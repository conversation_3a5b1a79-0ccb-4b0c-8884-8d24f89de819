package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ComfyuiWorkflowTemplateQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;

import java.util.List;

/**
 * comfyui模板 Service定义
 *
 * <AUTHOR>
 * @version ComfyuiWorkflowTemplateService.java v 0.1 2025-06-11 03:56:03
 */
public interface ComfyuiWorkflowTemplateService {
	
	/**
	 * 查询comfyui模板对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ComfyuiWorkflowTemplateVO selectById(Integer id);

	/**
	 * 删除comfyui模板对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加comfyui模板对象
	 * @param comfyuiWorkflowTemplate 对象参数
	 * @return 返回结果
	 */
	ComfyuiWorkflowTemplateVO insert(ComfyuiWorkflowTemplateVO comfyuiWorkflowTemplate);

	/**
	 * 修改comfyui模板对象
	 * @param comfyuiWorkflowTemplate 对象参数
	 */
	void updateByIdSelective(ComfyuiWorkflowTemplateVO comfyuiWorkflowTemplate);

	/**
	 * 带条件批量查询comfyui模板列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ComfyuiWorkflowTemplateVO> queryComfyuiWorkflowTemplateList(ComfyuiWorkflowTemplateQuery query);

	/**
	 * 带条件查询comfyui模板数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryComfyuiWorkflowTemplateCount(ComfyuiWorkflowTemplateQuery query);

	/**
	 * 带条件分页查询comfyui模板
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ComfyuiWorkflowTemplateVO> queryComfyuiWorkflowTemplateByPage(ComfyuiWorkflowTemplateQuery query);

	ComfyuiWorkflowTemplateVO queryTemplateByKeyAndVersion(String key, String version);

	ComfyuiWorkflowTemplateVO getActiveTemplateByKey(String key);

	ComfyuiWorkflowTemplateVO createActiveVersion(ComfyuiWorkflowTemplateVO tpl);
}