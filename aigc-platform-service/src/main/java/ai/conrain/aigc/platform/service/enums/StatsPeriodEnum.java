package ai.conrain.aigc.platform.service.enums;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import lombok.Getter;

@Getter
public enum StatsPeriodEnum {

    /**
     * 日
     */
    DAILY("DAILY", "日") {
        @Override
        public Date getStartDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            setTimeToStartOfDay(cal);
            return cal.getTime();
        }

        @Override
        public Date getEndDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            setTimeToEndOfDay(cal);
            return cal.getTime();
        }

        @Override
        public String getStartDateStr(Date date) {
            return formatYmdHmsStr(getStartDate(date));
        }

        @Override
        public String getEndDateStr(Date date) {
            return formatYmdHmsStr(getEndDate(date));
        }

        @Override
        public String getStorageDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }
    },
    /**
     * 周
     */
    WEEKLY("WEEKLY", "周") {
        @Override
        public Date getStartDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            setTimeToStartOfDay(cal);
            return cal.getTime();
        }

        @Override
        public Date getEndDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
            // 如果当前日期不是周日，需要加一周
            if (cal.getTime().before(date)) {
                cal.add(Calendar.WEEK_OF_YEAR, 1);
            }
            setTimeToEndOfDay(cal);
            return cal.getTime();
        }

        @Override
        public String getStartDateStr(Date date) {
            return formatYmdHmsStr(getStartDate(date));
        }

        @Override
        public String getEndDateStr(Date date) {
            return formatYmdHmsStr(getEndDate(date));
        }

        @Override
        public String getStorageDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }
    },
    /**
     * 月
     */
    MONTHLY("MONTHLY", "月") {
        @Override
        public Date getStartDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.DAY_OF_MONTH, 1); // 设置为1号
            setTimeToStartOfDay(cal);
            return cal.getTime();
        }

        @Override
        public Date getEndDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH)); // 设置为这个月的最后一天
            setTimeToEndOfDay(cal);
            return cal.getTime();
        }

        @Override
        public String getStartDateStr(Date date) {
            return formatYmdHmsStr(getStartDate(date));
        }

        @Override
        public String getEndDateStr(Date date) {
            return formatYmdHmsStr(getEndDate(date));
        }

        @Override
        public String getStorageDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }
    },
    /**
     * 季度
     */
    QUARTERLY("QUARTERLY", "季度") {
        @Override
        public Date getStartDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int currentMonth = cal.get(Calendar.MONTH);
            // 计算当前季度的第一个月 (0, 3, 6, 9)
            int quarterFirstMonth = (currentMonth / 3) * 3;
            cal.set(Calendar.MONTH, quarterFirstMonth);
            cal.set(Calendar.DAY_OF_MONTH, 1); // 设置为季度第一天
            setTimeToStartOfDay(cal);
            return cal.getTime();
        }

        @Override
        public Date getEndDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int currentMonth = cal.get(Calendar.MONTH);
            // 计算当前季度的最后一个月 (2, 5, 8, 11)
            int quarterLastMonth = (currentMonth / 3) * 3 + 2;
            cal.set(Calendar.MONTH, quarterLastMonth);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH)); // 设置为季度最后一天
            setTimeToEndOfDay(cal);
            return cal.getTime();
        }

        @Override
        public String getStartDateStr(Date date) {
            return formatYmdHmsStr(getStartDate(date));
        }

        @Override
        public String getEndDateStr(Date date) {
            return formatYmdHmsStr(getEndDate(date));
        }

        @Override
        public String getStorageDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }
    },
    /**
     * 年
     */
    YEARLY("YEARLY", "年") {
        @Override
        public Date getStartDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.MONTH, Calendar.JANUARY);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            setTimeToStartOfDay(cal);
            return cal.getTime();
        }

        @Override
        public Date getEndDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.MONTH, Calendar.DECEMBER);
            cal.set(Calendar.DAY_OF_MONTH, 31);
            setTimeToEndOfDay(cal);
            return cal.getTime();
        }

        @Override
        public String getStartDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }

        @Override
        public String getEndDateStr(Date date) {
            return formatYmdStr(getEndDate(date));
        }

        @Override
        public String getStorageDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }
    },
    /**
     * 总
     */
    TOTAL("TOTAL", "总") {
        @Override
        public Date getStartDate(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(Calendar.DAY_OF_MONTH, -1); // 获取昨天的日期
            setTimeToStartOfDay(cal);
            return cal.getTime();
        }

        @Override
        public Date getEndDate(Date date) {
            if (date == null) return null;
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            setTimeToStartOfDay(cal); // 设置为当天的开始时间
            return cal.getTime();
        }

        @Override
        public String getStartDateStr(Date date) {
            return formatYmdStr(getStartDate(date));
        }

        @Override
        public String getEndDateStr(Date date) {
            return formatYmdStr(getEndDate(date));
        }
        
        @Override
        public String getStorageDateStr(Date date) {
            return formatYmdStr(date);
        }
    },
    ;

    private String code;

    private String desc;

    StatsPeriodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取开始时间
     *
     * @param date 日期
     * @return 开始时间
     */
    public abstract Date getStartDate(Date date);

    /**
     * 获取结束时间
     *
     * @param date 日期
     * @return 结束时间
     */
    public abstract Date getEndDate(Date date);

    /** 
     * 获取开始时间字符串
     *
     * @param date 日期
     * @return 开始时间字符串
     */
    public abstract String getStartDateStr(Date date);

    /**
     * 获取结束时间字符串
     *
     * @param date 日期
     * @return 结束时间字符串
     */
    public abstract String getEndDateStr(Date date);

    /**
     * 获取存储日期字符串
     *
     * @param date 日期
     * @return 存储日期字符串
     */
    public abstract String getStorageDateStr(Date date);

    /**
     * 根据代码获取统计周期
     *
     * @param code 代码
     * @return 统计周期
     */
    public static StatsPeriodEnum getByCode(String code) {
        for (StatsPeriodEnum value : StatsPeriodEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 设置时间为一天的开始 (00:00:00.000)
     * 
     * @param cal 日历对象
     */
    protected static void setTimeToStartOfDay(Calendar cal) {
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
    }
    
    /**
     * 设置时间为一天的结束 (23:59:59.999)
     * 
     * @param cal 日历对象
     */
    protected static void setTimeToEndOfDay(Calendar cal) {
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
    }

    /**
     * 格式化年月日时分秒字符串 
     *
     * @param date 日期
     * @return 日期字符串
     */
    protected static String formatYmdHmsStr(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    /**
     * 格式化年月日字符串 
     *
     * @param date 日期
     * @return 日期字符串
     */
    protected static String formatYmdStr(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }

}
