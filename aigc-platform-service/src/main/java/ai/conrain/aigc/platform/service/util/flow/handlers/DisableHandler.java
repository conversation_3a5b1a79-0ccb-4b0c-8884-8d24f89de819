package ai.conrain.aigc.platform.service.util.flow.handlers;

import com.alibaba.fastjson.JSONObject;
import ai.conrain.aigc.platform.service.util.flow.JsonHandler;
import ai.conrain.aigc.platform.service.util.flow.FlowContext;
import ai.conrain.aigc.platform.service.util.flow.logger.FlowLogger;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Disable属性处理器
 * 负责处理JSON中的disable属性
 * 主要功能：
 * 1. 在prompt对象下为指定节点添加disable属性
 * 2. disable属性值使用模板表达式：${paramName?then('false','true')}
 * 3. 处理互斥节点，设置相反的disable逻辑
 * 4. 支持在已有disable属性的基础上通过&&进行条件拼接
 * 5. 避免重复添加相同参数
 */
public class DisableHandler implements JsonHandler {
    private final FlowContext context;
    private final FlowLogger logger;
    // 匹配模板表达式中的参数名正则，同时支持带括号和不带括号的格式
    private static final Pattern PARAM_PATTERN = Pattern.compile("\\$\\{(?:\\()?([^?]*)(?:\\))?\\?then\\('false','true'\\)\\}");

    public DisableHandler(FlowContext context, FlowLogger logger) {
        this.context = context;
        this.logger = logger;
    }

    @Override
    public void handle(JSONObject jsonObject) {
        logger.info("开始处理disable属性");
        logger.increaseLevel();
        
        JSONObject prompt = jsonObject.getJSONObject("prompt");
        if (prompt != null) {
            // 处理普通节点
            handleNodes(prompt, context.getNodeIds(), false);
            // 处理互斥节点
            handleNodes(prompt, context.getMutuallyNodeIds(), true);
            
            logger.success("所有节点处理完成");
        } else {
            logger.warn("未找到prompt对象，跳过处理");
        }
        
        logger.decreaseLevel();
        logger.success("disable属性处理完成");
    }

    private void handleNodes(JSONObject prompt, List<Integer> nodeIds, boolean reverse) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            logger.info(reverse ? "无互斥节点，跳过处理" : "无节点，跳过处理");
            return;
        }

        for (Integer nodeId : nodeIds) {
            logger.info("处理节点: " + nodeId + (reverse ? " (互斥节点)" : ""));
            logger.increaseLevel();
            
            String nodeIdStr = nodeId.toString();
            if (!prompt.containsKey(nodeIdStr)) {
                logger.info("节点不存在，跳过处理");
                logger.decreaseLevel();
                continue;
            }
            
            JSONObject nodePrompt = prompt.getJSONObject(nodeIdStr);
            
            // 获取新参数名（互斥节点需要在参数前加!）
            String newParamName = reverse ? "!" + context.getParamName() : context.getParamName();
            String baseParamName = context.getParamName(); // 不带!的原始参数名
            
            // 检查节点是否已有disable属性
            if (nodePrompt.containsKey("disable")) {
                String existingDisable = nodePrompt.getString("disable");
                logger.info("节点已有disable属性: " + existingDisable);
                
                // 从现有表达式中提取参数
                Matcher matcher = PARAM_PATTERN.matcher(existingDisable);
                if (matcher.find()) {
                    String existingParam = matcher.group(1);
                    logger.info("提取到现有参数: " + existingParam);
                    
                    // 检查当前参数是否已存在
                    if (parameterAlreadyExists(existingParam, baseParamName, reverse)) {
                        logger.info("参数 " + baseParamName + " 已存在于表达式中，跳过添加");
                        logger.decreaseLevel();
                        continue;
                    }
                    
                    // 组合新的参数条件，使用括号包裹
                    String combinedParam = "(" + existingParam + "&&" + newParamName + ")";
                    logger.info("组合新参数: " + combinedParam);
                    
                    // 设置新的disable属性
                    nodePrompt.put("disable", "${" + combinedParam + "?then('false','true')}");
                    logger.success("更新disable属性完成");
                } else {
                    logger.warn("无法解析现有disable属性，将进行替换");
                    nodePrompt.put("disable", "${" + newParamName + "?then('false','true')}");
                    logger.success("替换disable属性完成");
                }
            } else {
                nodePrompt.put("disable", "${" + newParamName + "?then('false','true')}");
                logger.success("新增disable属性完成");
            }
            
            logger.decreaseLevel();
        }
    }
    
    /**
     * 检查参数是否已存在于表达式中
     * 
     * @param existingParam 现有参数表达式
     * @param newParam 新参数名(不带!)
     * @param reverse 是否为互斥节点
     * @return 如果参数已存在返回true
     */
    private boolean parameterAlreadyExists(String existingParam, String newParam, boolean reverse) {
        // 去掉外层括号，如果有的话
        if (existingParam.startsWith("(") && existingParam.endsWith(")")) {
            existingParam = existingParam.substring(1, existingParam.length() - 1);
        }
        
        // 分割参数表达式，处理可能存在的多个参数（通过&&连接）
        String[] params = existingParam.split("&&");
        
        for (String param : params) {
            // 去除可能的空格
            param = param.trim();
            
            // 处理取反的情况 (去掉开头的!)
            String cleanParam = param.startsWith("!") ? param.substring(1) : param;
            
            // 检查参数名是否匹配
            if (cleanParam.equals(newParam)) {
                // 如果参数名相同，再检查取反状态是否一致
                boolean paramIsNegated = param.startsWith("!");
                // 如果取反状态一致，则认为是相同参数
                if (paramIsNegated == reverse) {
                    return true;
                }
            }
        }
        
        return false;
    }
} 