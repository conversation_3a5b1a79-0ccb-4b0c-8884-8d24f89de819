package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class VideoClipTaskStatusSyncJob extends JavaProcessor {

    private static final String LOCK_KEY = "VideoClipTaskStatusSyncJob_lock";
    private static final int EXPIRE_TIME = 60000;  // 锁的过期时间

    @Autowired
    private TairService tairService;

    @Autowired
    private CreativeBatchService creativeBatchService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));

        if (OperationContextHolder.getRoleType() == null) {
            OperationContextHolder.setContext(CommonUtil.mockAutoCreativeContext());
        }

        boolean lock = tairService.acquireLock(LOCK_KEY, "1", EXPIRE_TIME);

        if (lock) {
            try {
                CreativeBatchQuery query = new CreativeBatchQuery();
                query.setStatus(CreativeStatusEnum.PROCESSING.getCode());
                query.setType(CreativeTypeEnum.CREATE_VIDEO.getCode());

                List<CreativeBatchVO> list = creativeBatchService.queryCreativeBatchList(query);

                if (CollectionUtils.isNotEmpty(list)) {

                    list = list.stream().filter(b -> CollectionUtils.isNotEmpty(b.getUnCompletedVideoClipTasks())).collect(
                        Collectors.toList());

                    log.info("VideoClipTaskStatusSyncJob需要处理的任务数为{}", list.size());

                    for (CreativeBatchVO batch : list) {
                        MDC.put("traceId", uuid + "_" + batch.getId());
                        long t1 = System.currentTimeMillis();
                        creativeBatchService.syncStatus(batch);
                        long t2 = System.currentTimeMillis();
                        log.info("VideoClipTaskStatusSyncJob耗时统计modelId={}耗时{}ms", batch.getId(), t2 - t1);
                    }

                    MDC.put("traceId", uuid);
                }

            } catch (Throwable e) {
                log.error("VideoClipTaskStatusSyncJob error", e);
                return new ProcessResult(false);

            } finally {
                MDC.remove("env");
                MDC.remove("traceId");

                OperationContextHolder.clean();

                tairService.releaseLock(LOCK_KEY, "1");
            }
        } else {
            log.info("当前获取锁失败，忽略");
        }

        return new ProcessResult(true);
    }
}
