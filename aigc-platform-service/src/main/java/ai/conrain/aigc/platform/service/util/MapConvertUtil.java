package ai.conrain.aigc.platform.service.util;

import java.util.Map;

/**
 * Map转换工具类
 */
public class MapConvertUtil {


    /**
     * 从Map中获取数据，并转换为指定类型
     *
     * @param map Map对象
     * @param key 键名
     * @param clazz 目标类型的Class对象
     * @return 转换后的指定类型值
     */
    public static <T> T getValue(Map<String, Object> map, String key, Class<T> clazz) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        
        try {
            // 如果value的类型与目标类型相同，直接转换
            if (clazz.isInstance(value)) {
                return clazz.cast(value);
            }
            
            // 处理数值类型之间的转换
            if (Number.class.isAssignableFrom(clazz)) {
                if (value instanceof Number) {
                    Number numValue = (Number) value;
                    if (clazz == Integer.class) {
                        return clazz.cast(numValue.intValue());
                    } else if (clazz == Long.class) {
                        return clazz.cast(numValue.longValue());
                    } else if (clazz == Double.class) {
                        return clazz.cast(numValue.doubleValue());
                    } else if (clazz == Float.class) {
                        return clazz.cast(numValue.floatValue());
                    } else if (clazz == Short.class) {
                        return clazz.cast(numValue.shortValue());
                    } else if (clazz == Byte.class) {
                        return clazz.cast(numValue.byteValue());
                    }
                } else if (value instanceof String) {
                    // 处理字符串类型的数字转换
                    String strValue = (String) value;
                    if (strValue.isEmpty()) {
                        return null;
                    }
                    
                    try {
                        if (clazz == Integer.class) {
                            return clazz.cast(Integer.parseInt(strValue));
                        } else if (clazz == Long.class) {
                            return clazz.cast(Long.parseLong(strValue));
                        } else if (clazz == Double.class) {
                            return clazz.cast(Double.parseDouble(strValue));
                        } else if (clazz == Float.class) {
                            return clazz.cast(Float.parseFloat(strValue));
                        } else if (clazz == Short.class) {
                            return clazz.cast(Short.parseShort(strValue));
                        } else if (clazz == Byte.class) {
                            return clazz.cast(Byte.parseByte(strValue));
                        }
                    } catch (NumberFormatException e) {
                        // 字符串无法解析为数字
                        return null;
                    }
                }
            }
            
            // 字符串转换处理
            if (clazz == String.class && value != null) {
                return clazz.cast(value.toString());
            }
            
            // 尝试直接转换
            return clazz.cast(value);
        } catch (ClassCastException e) {
            // 记录日志或者用其他方式处理转换异常
            return null;
        }
    }
}