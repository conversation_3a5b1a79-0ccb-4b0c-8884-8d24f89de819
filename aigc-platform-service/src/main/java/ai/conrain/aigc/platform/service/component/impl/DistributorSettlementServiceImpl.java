package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.DistributorSettlementDAO;
import ai.conrain.aigc.platform.dal.dao.OrderSettlementDAO;
import ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO;
import ai.conrain.aigc.platform.dal.entity.OrderSettlementDO;
import ai.conrain.aigc.platform.dal.example.DistributorSettlementExample;
import ai.conrain.aigc.platform.service.component.DistributorSettlementService;
import ai.conrain.aigc.platform.service.component.OrderSettlementService;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ConfigStatusEnum;
import ai.conrain.aigc.platform.service.enums.DistributorSettleStatusEnum;
import ai.conrain.aigc.platform.service.enums.OrderSettleStatusEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.SettleTypeEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.DistributorSettlementConverter;
import ai.conrain.aigc.platform.service.model.converter.OrderSettlementConverter;
import ai.conrain.aigc.platform.service.model.query.DistributorSettlementQuery;
import ai.conrain.aigc.platform.service.model.query.OrderSettlementQuery;
import ai.conrain.aigc.platform.service.model.vo.DistributorSettlementVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * MerchantSettlementService实现
 *
 * <AUTHOR>
 * @version DistributorSettlementService.java v 0.1 2024-07-31 02:44:32
 */
@Slf4j
@Service
public class DistributorSettlementServiceImpl implements DistributorSettlementService {

	@Autowired
	public DistributorSettlementDAO distributorSettlementDAO;
	@Autowired
	private SystemConfigService systemConfigService;
	@Autowired
	private OrderSettlementDAO orderSettlementDAO;
	@Autowired
	private OrderSettlementService orderSettlementService;

	@Autowired
	private OrganizationService organizationService;

	@Override
	public DistributorSettlementVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		DistributorSettlementDO data = distributorSettlementDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return DistributorSettlementConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = distributorSettlementDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除MerchantSettlement失败");
	}

	@Override
	public DistributorSettlementVO insert(DistributorSettlementVO distributorSettlementVO) {
		AssertUtil.assertNotNull(distributorSettlementVO, ResultCode.PARAM_INVALID, "distributorSettlementVO is null");
		AssertUtil.assertTrue(distributorSettlementVO.getId() == null, ResultCode.PARAM_INVALID, "distributorSettlementVO.id is present");

		//创建时间、修改时间兜底
		if (distributorSettlementVO.getCreateTime() == null) {
			distributorSettlementVO.setCreateTime(new Date());
		}

		if (distributorSettlementVO.getModifyTime() == null) {
			distributorSettlementVO.setModifyTime(new Date());
		}

		DistributorSettlementDO data = DistributorSettlementConverter.vo2DO(distributorSettlementVO);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = distributorSettlementDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建MerchantSettlement失败");
		AssertUtil.assertNotNull(data.getId(), "新建MerchantSettlement返回id为空");
		distributorSettlementVO.setId(data.getId());
		return distributorSettlementVO;
	}


	@Override
	public void updateByIdSelective(DistributorSettlementVO distributorSettlementVO) {
		AssertUtil.assertNotNull(distributorSettlementVO, ResultCode.PARAM_INVALID, "distributorSettlementVO is null");
    	AssertUtil.assertTrue(distributorSettlementVO.getId() != null, ResultCode.PARAM_INVALID, "distributorSettlementVO.id is null");

		//修改时间必须更新
		distributorSettlementVO.setModifyTime(new Date());
		DistributorSettlementDO data = DistributorSettlementConverter.vo2DO(distributorSettlementVO);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = distributorSettlementDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MerchantSettlement失败，影响行数:" + n);
	}

	@Override
	public List<DistributorSettlementVO> queryMerchantSettlementList(DistributorSettlementQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		DistributorSettlementExample example = DistributorSettlementConverter.query2Example(query);

		List<DistributorSettlementDO> list = distributorSettlementDAO.selectByExample(example);
			return DistributorSettlementConverter.doList2VOList(list);
	}

	@Override
	public Long queryMerchantSettlementCount(DistributorSettlementQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		DistributorSettlementExample example = DistributorSettlementConverter.query2Example(query);
		long c = distributorSettlementDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询商户结算明细
	 */
	@Override
	public PageInfo<DistributorSettlementVO> queryMerchantSettlementByPage(DistributorSettlementQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<DistributorSettlementVO> page = new PageInfo<>();

		DistributorSettlementExample example = DistributorSettlementConverter.query2Example(query);
		long totalCount = distributorSettlementDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<DistributorSettlementDO> list = distributorSettlementDAO.selectByExample(example);
		page.setList(DistributorSettlementConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public void setServiceRate(Integer distributorCorpId, BigDecimal serviceRate, String effectTime) {
		AssertUtil.assertNotNull(distributorCorpId, ResultCode.PARAM_INVALID, "distributorCorpId is null");

		String key = SystemConstants.DISTRIBUTOR_SETTLE_RATE_PREFIX + distributorCorpId;
		SystemConfigVO config = systemConfigService.queryByKey(key);
		if (config == null) {
			config = new SystemConfigVO();

			config.setConfKey(key);
			config.setConfValue(serviceRate.toPlainString());
		}

		Date effectDate = DateUtils.parseShort(effectTime);
		if (DateUtils.beforeNow(effectDate)) {
			config.setStatus(ConfigStatusEnum.ACTIVE);
			config.setConfValue(serviceRate.toPlainString());
		} else {
			config.setStatus(ConfigStatusEnum.PENDING_CHANGE);
			config.setConfValueNext(serviceRate.toPlainString());
		}

		config.setMemo("渠道商结算费率");
		config.setEffectTime(DateUtils.parseShort(effectTime));
		config.setOperatorId(OperationContextHolder.getOperatorUserId());

		if (config.getId() != null) {
			systemConfigService.updateById(config);
		} else {
			systemConfigService.insert(config);
		}
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public Result<?> manualSettle(Integer id, String outBizNo) {
		//1.锁当前商家记录
		DistributorSettlementDO settlement = distributorSettlementDAO.lockById(id);
		if (null == settlement) {
			log.warn("手动结算失败，商家结算数据不存在id={}", id);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "商家结算订单不存在");
		}

		if (DistributorSettleStatusEnum.SETTLE_FINISH == DistributorSettleStatusEnum.getByCode(settlement.getStatus())) {
			log.warn("手动结算失败，该商家结算数据已完成id={}", id);
			return Result.failedWithMessage(ResultCode.SETTLE_ALREADY_FINISHED, "当前商户已完成结算");
		}

		//2.判断银行流水号是否重复
		DistributorSettlementExample example = new DistributorSettlementExample();
		example.createCriteria().andOutBizNoEqualTo(outBizNo);
		long count = distributorSettlementDAO.countByExample(example);
		if (count > 0) {
			log.warn("手动结算异常，银行流水号重复，id={}，outBizNo={}", id, outBizNo);
			throw new BizException(ResultCode.DUPLICATE_TRANSACTION_NO);
		}

		//3.查询所有当前商家记录单下的订单结算数据，并开始结算
		orderSettlementService.finishSettlement(settlement.getSettleId());

		//4.更新商家结算状态
		DistributorSettlementDO data = new DistributorSettlementDO();
		data.setId(settlement.getId());
		data.setStatus(DistributorSettleStatusEnum.SETTLE_FINISH.getCode());
		data.setSettleTime(new Date());
		data.setOutBizNo(outBizNo);

		int cnt = distributorSettlementDAO.updateByPrimaryKeySelective(data);
		if (cnt < 1) {
			log.error("手动结算异常，id={}商家结算失败，未完成更新", data.getId());
			throw new BizException(ResultCode.SYS_ERROR);
		}

		return Result.success();
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public void initSettlement(Integer distributorCorpId, String month) {
		log.info("执行渠道月结开始，当前执行distributorCorpId={}, month={}", distributorCorpId, month);
		AssertUtil.assertNotNull(distributorCorpId, ResultCode.PARAM_INVALID, "distributorCorpId is null");
		AssertUtil.assertNotBlank(month, ResultCode.PARAM_INVALID, "month is blank");

		//1.清理之前该商户未处理的结算
		List<OrderSettlementDO> oldList = clearAndFetchOld(distributorCorpId);

		String settleId = CommonUtil.genBizNo(distributorCorpId);

		//2.查询所有待清算记录
		List<OrderSettlementDO> orderSettleList = orderSettlementDAO.queryPendingSettle(distributorCorpId, month);
		//如果待清算记录为空时，理论上不会进到这个环节
		if (CollectionUtils.isEmpty(orderSettleList)) {
			log.error("执行日结异常，当前distributorCorpId={}的结算单未从数据库中查询到订单结算数据", distributorCorpId);
			throw new BizException(ResultCode.SYS_ERROR);
		}

		if (CollectionUtils.isNotEmpty(oldList)) {
			orderSettleList = ListUtils.union(oldList, orderSettleList);
		}

		BigDecimal totalAmount = BigDecimalUtils.newZero();
		BigDecimal settleAmount = BigDecimalUtils.newZero();

		//3.更新每条订单清算记录
		for (OrderSettlementDO order : orderSettleList) {
			OrderSettlementDO data = new OrderSettlementDO();
			data.setStatus(OrderSettleStatusEnum.SETTLING.getCode());
			data.setSettleId(settleId);
			data.setId(order.getId());

			int cnt = orderSettlementDAO.updateByPrimaryKeySelective(data);
			if (cnt < 1) {
				log.error("执行日结异常，distributorCorpId={}结算失败，id={}的订单结算单更新失败", distributorCorpId, data.getId());
				throw new BizException(ResultCode.SYS_ERROR);
			}

			totalAmount = totalAmount.add(order.getTotalAmount());
			settleAmount = settleAmount.add(order.getSettleAmount());
		}

		//4.插入商家结算数据
		DistributorSettlementDO detail = new DistributorSettlementDO();
		detail.setDistributorCorpId(distributorCorpId);

		OrganizationVO distOrg = organizationService.selectById(distributorCorpId);

		detail.setDistributorCorpName(distOrg != null && StringUtils.isNotBlank(distOrg.getName()) ? distOrg.getName() : orderSettleList.get(0).getDistributorCorpName());
		detail.setSettleId(settleId);
		detail.setDeleted(false);
		detail.setStatus(DistributorSettleStatusEnum.PENDING_SETTLE.getCode());
		detail.setSettleType(SettleTypeEnum.MANUAL_SETTLE.getCode());
		detail.setTotalAmount(totalAmount);
		detail.setSettleAmount(settleAmount);
		detail.setOrderNum(orderSettleList.size());

		distributorSettlementDAO.insertSelective(detail);

		log.info("执行日结完成，当前执行payeeId={}，总金额={},结算金额={}", distributorCorpId, totalAmount, settleAmount);
	}

	/**
	 * 带条件分页查询商户结算明细
	 */
	@Override
	public Result<PageInfo<DistributorSettlementVO>> queryByPage(DistributorSettlementQuery query) {
		if (query == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "query is null");
		}

		if (query.getPageNum() == null || query.getPageSize() == null || query.getPageNum() < 1
				|| query.getPageSize() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID,
					"pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());
		}

		if (RoleTypeEnum.DISTRIBUTOR == OperationContextHolder.getContext().getRoleType()) {
			query.setDistributorCorpId(OperationContextHolder.getCorpOrgId());
		}

		try {
			PageInfo<DistributorSettlementVO> page = new PageInfo<>();

			DistributorSettlementExample example = DistributorSettlementConverter.query2Example(query);
			long totalCount = distributorSettlementDAO.countByExample(example);
			if (totalCount == 0) {
				page.setList(new ArrayList<>());
				page.setSize(0);
				page.setTotalCount(0);
				page.setHasNextPage(false);

				return Result.success(page);
			}

			List<DistributorSettlementDO> list = distributorSettlementDAO.selectByExample(example);
			if (list != null) {
				page.setList(DistributorSettlementConverter.doList2VOList(list));
				page.setSize(list.size());
				page.setTotalCount(totalCount);
				page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

				return Result.success(page);
			} else {
				return Result.failedWithMessage(ResultCode.BIZ_FAIL, "分页查询MerchantSettlement失败，返回null");
			}

		} catch (Throwable t) {
			log.error("DistributorSettlementService.queryMerchantSettlementByPage异常:", t);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "MerchantSettlement分页查询异常:" + t.getMessage());
		}
	}

	/**
	 * 清理已经存在的商家结算单
	 *
	 * @param corpId 商户id
	 * @return 未结算订单列表
	 */
	private List<OrderSettlementDO> clearAndFetchOld(Integer corpId) {
		DistributorSettlementQuery query = new DistributorSettlementQuery();
		query.setDistributorCorpId(corpId);
		query.setStatus(OrderSettleStatusEnum.INIT.getCode());
		List<DistributorSettlementDO> list = distributorSettlementDAO.selectByExample(
				DistributorSettlementConverter.query2Example(query));

		if (CollectionUtils.isEmpty(list)) {
			log.info("清理已经存在的商家结算单结束，未查到历史存量结算单，corpId={}", corpId);
			return null;
		}

		if (list.size() > 1) {
			log.error("清理已经存在的商家结算单异常，历史存量结算单数{}>1，先清理第一笔", list.size());
		}

		DistributorSettlementDO target = list.get(0);
		log.warn("清理已经存在的商家结算单，删除历史存量结算单id={}", target.getId());

		//逻辑删除当前商户结算单
		distributorSettlementDAO.logicalDeleteByPrimaryKey(target.getId());

		//查询所有该商户结算单下的订单
		OrderSettlementQuery orderQuery = new OrderSettlementQuery();
		orderQuery.setSettleId(target.getSettleId());
		return orderSettlementDAO.selectByExample(OrderSettlementConverter.query2Example(orderQuery));
	}

	/**
	 * 生成结算id
	 *
	 * @param payeeId 商家id
	 * @return 结算id
	 */
	private static String genSettleId(Integer payeeId) {
		StringBuilder builder = new StringBuilder();
		//日期前缀，8位
		builder.append(DateUtils.getNowShort());

		//用户id,100000,当前6位,前置补齐0到8位
		builder.append(String.format("%08d", payeeId));

		//增加4位随机数
		Random random = new Random(System.currentTimeMillis());
		int randomValue = random.nextInt(10000);
		builder.append(String.format("%04d", randomValue));

		return builder.toString();
	}

}