package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;

/**
 * TrainParamQuery
 *
 * @version TrainParamService.java v 0.1 2024-11-19 08:51:42
 */
@Data
public class TrainParamQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(name = "id", value = "主键ID")
    private Integer id;

    /** 关联的训练计划ID */
    @ApiModelProperty(name = "trainPlanId", value = "关联的训练计划ID")
    private Integer trainPlanId;

    /** 训练分辨率 */
    @ApiModelProperty(name = "trainResolution", value = "训练分辨率")
    private String trainResolution;

    /** 学习内容或风格 */
    @ApiModelProperty(name = "contentOrStyle", value = "学习内容或风格")
    private String contentOrStyle;

    /** 关联的loraModelId */
    @ApiModelProperty(name = "relatedLoraModelId", value = "关联的loraModelId")
    private Integer relatedLoraModelId;

    /** 关联的loraModelName */
    @ApiModelProperty(name = "relatedLoraModelName", value = "关联的loraModelName")
    private String relatedLoraModelName;

    /** Rank值 */
    @ApiModelProperty(name = "loraRank", value = "Rank值")
    private Integer loraRank;

    /** Alpha值 */
    @ApiModelProperty(name = "alpha", value = "Alpha值")
    private Integer alpha;

    /** 训练步数 */
    @ApiModelProperty(name = "trainStep", value = "训练步数")
    private Integer trainStep;

    /** 学习率 */
    @ApiModelProperty(name = "lr", value = "学习率")
    private BigDecimal lr;

    /** Dropout值 */
    @ApiModelProperty(name = "dropout", value = "Dropout值")
    private BigDecimal dropout;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private String extInfo;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}