/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 创作图下载请求
 */
@Data
public class DownloadCreativeImageRequest implements Serializable {
    private static final long serialVersionUID = 9038388273793609246L;

    /** 批次id */
    @NotNull
    private Integer id;
    /** 图片ossUrl */
    private List<String> imageUrls;
}
