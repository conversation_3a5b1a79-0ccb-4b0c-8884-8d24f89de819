package ai.conrain.aigc.platform;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Controller
public class MainController {

    /**
     * 服务器启动探测使用
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/healthcheck")
    public String healthcheck(){
        return "success";
    }

    @GetMapping({"/"})
    public String index(HttpServletRequest request) {
        String fullPath = request.getRequestURL().toString();  // 获取完整的请求URL
        log.info("Current request path: {}", fullPath);  // 使用日志记录当前请求的完整路径
        return "../static/index";
    }
}