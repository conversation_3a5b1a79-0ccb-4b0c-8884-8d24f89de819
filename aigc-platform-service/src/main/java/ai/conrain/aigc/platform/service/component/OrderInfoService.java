package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.request.Topup2UserReq;
import ai.conrain.aigc.platform.service.model.vo.MerchantTopupVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单 Service定义
 *
 * <AUTHOR>
 * @version OrderInfoService.java v 0.1 2024-06-20 11:43:37
 */
public interface OrderInfoService {

    /**
     * 查询订单对象
     *
     * @param id 主键
     * @return 返回结果
     */
    OrderInfoVO selectById(Integer id);

    /**
     * 锁定订单
     *
     * @param id
     * @return
     */
    OrderInfoVO lockById(Integer id);

    /**
     * 删除订单对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加订单对象
     *
     * @param orderInfo 对象参数
     * @return 返回结果
     */
    OrderInfoVO insert(OrderInfoVO orderInfo);

    /**
     * 充值到用户（线下转账）
     *
     * @param orderInfo
     * @return
     */
    OrderInfoVO topup2User(Topup2UserReq orderInfo);

    /**
     * 修改订单对象
     *
     * @param orderInfo 对象参数
     */
    void updateByIdSelective(OrderInfoVO orderInfo);

    /**
     * 带条件批量查询订单列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<OrderInfoVO> queryOrderInfoList(OrderInfoQuery query);

    /**
     * 带条件查询订单数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryOrderInfoCount(OrderInfoQuery query);

    /**
     * 带条件分页查询订单
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<OrderInfoVO> queryOrderInfoByPage(OrderInfoQuery query);

    /**
     * 分页查询商家的充值记录
     *
     * @param query
     * @return
     */
    PageInfo<MerchantTopupVO> queryMerchantTopupByPage(OrderInfoQuery query);

    // 查询用户首单时间（要求充值金额3999以上）
    OrderInfoVO getFirstOrder(Integer userId, BigDecimal notLessThanAmount);

    /**
     * 查询用户充值金额小于3999的用户
     *
     * @param greaterThan 是否大于
     * @param userIdList  用户id列表
     * @return 用户 id 列表
     */
    List<Integer> findUsersWithMaxRechargeLessThan3999(Boolean greaterThan, List<Integer> userIdList);

    /**
     * 查询用户充值金额小于3999的用户
     *
     * @param greaterThan 是否大于
     * @param userIdList  用户id列表
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 用户 id 列表
     */
    List<Integer> findUsersWithMaxRechargeLessThan3999(Boolean greaterThan, List<Integer> userIdList, String startDate, String endDate);

    /**
     * 查询没有订单记录或者含有订单记录但是小于 3999 的用户
     * @param yesterdayDate 昨天时间
     * @param userIdList 用户id列表
     * @return 订单列表
     */
    List<Integer> selectNoOrderOrLessThan3999(String yesterdayDate, List<Integer> userIdList);

    /**
     * 查询满足最低充值金额的用户列表
     *
     * @param userIdList   用户ID列表
     * @param minAmount    最低充值金额
     * @param startDate    开始时间（可为null）
     * @param endDate      结束时间（可为null）
     * @return 满足条件的用户ID列表
     */
    List<Integer> findUsersWithMinRechargeAmount(List<Integer> userIdList, BigDecimal minAmount,
                                               String startDate, String endDate);

	/**
	 * 判断当前订单是否为新订单
	 */
	Boolean isNew(OrderInfoVO orderInfo);
}