/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import java.io.Serializable;
import lombok.Data;

/**
 * 服装拍摄角度模型
 *
 * <AUTHOR>
 * @version : ClothAngleDetail.java, v 0.1 2025/3/30 21:36 renxiao.wu Exp $
 */
@Data
public class ClothAngleDetail implements Serializable {
    private static final long serialVersionUID = -2266094475157265371L;
    /** 正面全身图片 */
    private String frontFullImg;
    /** 背面全身图片 */
    private String backFullImg;
    /** 正面半身图片 */
    private String frontHalfImg;
    /** 背面半身图片 */
    private String backHalfImg;
}

