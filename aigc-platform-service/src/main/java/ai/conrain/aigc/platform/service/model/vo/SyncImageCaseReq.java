package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;

/**
 * 图库同步请求入参
 */
@Data
public class SyncImageCaseReq {

    /**
     * 图片路径(新路径)
     */
    private String filePath;

    /**
     * 图片url（新地址）
     */
    private String url;


    /**
     * 服装模型id
     */
    private Integer id;

    /**
     * 文件路径(旧路径)
     */
    private String fileDir;

    /**
     * 文件名称(旧文件名称)
     */
    private String fileName;

    /**
     * 图片url（旧地址）
     */
    private String imgUrl;

    /**
     * 文本内容
     */
    private String textContent;

    /**
     * 图片类型
     */
    private String type;

    /**
     * 是否是训练材料
     */
    private Boolean trainingMaterial = false;

}
