/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.constants.CommonConstants;

import java.math.BigDecimal;

/**
 * BigDecimal工具类
 *
 * <AUTHOR>
 * @version : BigDecimalUtils.java, v 0.1 2023/9/13 09:51 renxiao.wu Exp $
 */
public abstract class BigDecimalUtils {
    /**
     * 新建一个0
     *
     * @return 0
     */
    public static BigDecimal newZero() {
        return BigDecimal.valueOf(0);
    }

    private static final BigDecimal oneCent = new BigDecimal("0.01");

    public static BigDecimal newOneCent() {
        return new BigDecimal("0.01");
    }

    public static boolean lessThanOneCent(BigDecimal amount) {
        return BigDecimalUtils.lessThan(amount, oneCent);
    }

    public static boolean greaterOrEqualsThanOneCent(BigDecimal target) {
        return !lessThanOneCent(target);
    }

    /**
     * 新建一个数值
     *
     * @return 新的数值
     */
    public static BigDecimal newVal(BigDecimal original) {
        if (null == original) {
            return newZero();
        }
        return original.add(newZero());
    }

    /**
     * 判断a与b是否相等
     *
     * @param a a
     * @param b b
     * @return true，a和b相等
     */
    public static boolean equals(BigDecimal a, BigDecimal b) {
        if (null == a) {
            return false;
        }

        if (null == b) {
            return true;
        }
        return a.compareTo(b) == 0;
    }

    /**
     * 判断data是否等于0
     *
     * @param data data
     * @return true等于0
     */
    public static boolean equalsZero(BigDecimal data) {
        if (null == data) {
            return false;
        }

        return equals(data, BigDecimal.ZERO);
    }

    /**
     * 判断a是否比b大
     *
     * @param a a
     * @param b b
     * @return true，a比b大
     */
    public static boolean greaterThan(BigDecimal a, BigDecimal b) {
        if (null == a) {
            return false;
        }

        if (null == b) {
            return true;
        }
        return a.compareTo(b) > 0;
    }

    /**
     * 判断目标数值是否比0大
     *
     * @param target 目标数值
     * @return true，比0大
     */
    public static boolean greaterThanZero(BigDecimal target) {
        return greaterThan(target, BigDecimal.ZERO);
    }

    /**
     * 判断目标数值是否比0大
     *
     * @param target 目标数值
     * @return true，比0大
     */
    public static boolean greaterThanOrEqualsZero(BigDecimal target) {
        return equalsZero(target) || greaterThan(target, BigDecimal.ZERO);
    }

    /**
     * 判断a是否比b小
     *
     * @param a a
     * @param b b
     * @return true，a比b小
     */
    public static boolean lessThan(BigDecimal a, BigDecimal b) {
        if (null == a) {
            return true;
        }

        if (null == b) {
            return false;
        }
        return a.compareTo(b) < 0;
    }

    /**
     * 判断目标数值是否比0小
     *
     * @param target 目标数值
     * @return true，比0小
     */
    public static boolean lessThanZero(BigDecimal target) {
        return lessThan(target, BigDecimal.ZERO);
    }

    /**
     * 两个数值取小
     *
     * @param a a
     * @param b b
     * @return 小的那个数值
     */
    public static BigDecimal min(BigDecimal a, BigDecimal b) {
        return greaterThan(a, b) ? b : a;
    }

    /**
     * 两个数值取大
     *
     * @param a a
     * @param b b
     * @return 大的那个数值
     */
    public static BigDecimal max(BigDecimal a, BigDecimal b) {
        return greaterThan(a, b) ? a : b;
    }

    /**
     * 金额格式化，保留小数2位，（超出时按第3位小数四舍五入后截断）
     *
     * @param amount 目标金额
     * @return 格式化后字符串
     */
    public static String formatWith2Digits(BigDecimal amount) {
        if (amount == null) {
            throw new NullPointerException();
        }
        return CommonConstants.DECIMAL_FORMAT.format(amount);
    }

    /**
     * 金额格式化，有小数时保留小数2位，（超出时按第3位小数四舍五入后截断），没小数时不保留小数
     *
     * @param amount 目标金额
     * @return 格式化后字符串
     */
    public static String formatWith2DigitsIfExist(BigDecimal amount) {
        if (amount == null) {
            throw new NullPointerException();
        }
        return CommonConstants.ROUND_TWO_DECIMAL_FORMAT.format(amount);
    }

    /**
     * 金额相加
     *
     * @param a a
     * @param b b
     * @return 相加后金额
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        if (null == a) {
            return newVal(b);
        }

        if (null == b) {
            return newVal(a);
        }

        return a.add(b);
    }

    /**
     * 金额 乘 费率
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
        if (null == a) {
            return newZero();
        }

        if (null == b) {
            return newZero();
        }

        return a.multiply(b);
    }

    /**
     * 是否等于1分钱
     *
     * @param m
     * @return
     */
    public static boolean equalsOneCent(BigDecimal m) {
        return equals(m, oneCent);
    }

    public static boolean greaterThanOneCent(BigDecimal m) {
        return greaterThan(m, oneCent);
    }

    /**
     * 金额相加
     *
     * @param a a
     * @param b b
     * @return 相加后金额
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        if (null == a) {
            return subtract(newZero(), b);
        }

        if (null == b) {
            return subtract(a, newZero());
        }

        return a.subtract(b);
    }
}
