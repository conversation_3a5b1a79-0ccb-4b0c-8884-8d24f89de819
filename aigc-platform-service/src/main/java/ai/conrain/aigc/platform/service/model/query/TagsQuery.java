package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * TagsQuery
 *
 * @version TagsService.java v 0.1 2024-05-22 08:28:41
 */
@Data
public class TagsQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 标签id */
    @ApiModelProperty(name = "id", value = "标签id")
    private Integer id;

    /** 标签类型 */
    @ApiModelProperty(name = "type", value = "标签类型")
    private String type;

    /** 标签名称 */
    @ApiModelProperty(name = "name", value = "标签名称")
    private String name;

    /** 是否默认选中，0不选中、1默认选中 */
    @ApiModelProperty(name = "defChecked", value = "是否默认选中，0不选中、1默认选中")
    private Boolean defChecked;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 标签详情 */
    @ApiModelProperty(name = "detail", value = "标签详情")
    private String detail;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}