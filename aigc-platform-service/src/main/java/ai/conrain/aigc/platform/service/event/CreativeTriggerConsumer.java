/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.event;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.model.event.CreativeTriggerEvent;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.EventConstants.GROUP_CREATIVE_TRIGGER;
import static ai.conrain.aigc.platform.service.constants.EventConstants.TOPIC_CREATIVE_TRIGGER;

/**
 * 批次任务触发消息消费者
 *
 * <AUTHOR>
 * @version : CreativeFinishedConsumer.java, v 0.1 2024/8/28 16:52 renxiao.wu Exp $
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = TOPIC_CREATIVE_TRIGGER, consumerGroup = GROUP_CREATIVE_TRIGGER)
@ConditionalOnProperty(prefix = "rocketmq", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CreativeTriggerConsumer implements RocketMQListener<CreativeTriggerEvent> {
    @Autowired
    private CreativeBatchService creativeBatchService;

    @Override
    public void onMessage(CreativeTriggerEvent event) {
        MDC.put("traceId", CommonUtil.uuid() + "_t_" + event.getId());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        long start = System.currentTimeMillis();

        if (event.getId() == null) {
            log.warn("【创作任务触发事件】状态同步，接收到消息{}，id为空，忽略", event);
            return;
        }

        log.info("【创作任务触发事件】状态同步，接收到消息: {}，开始调度", event);

        try {
            CreativeBatchVO record = creativeBatchService.selectById(event.getId());
            if (record == null) {
                log.warn("【创作任务触发事件】状态同步，对应的任务为空，忽略，event={}", event);
                return;
            }

            if (record.getStatus().isEnd()) {
                log.info("【创作任务触发事件】状态同步，当前任务已完结，跳过");
                return;
            }

            creativeBatchService.syncStatus(record);

            log.info("【创作任务触发事件】状态同步，成功,耗时={}ms,event={}", System.currentTimeMillis() - start, event);

        } catch (Exception e) {
            log.error("【创作任务触发事件】状态同步，异常,耗时=" + (System.currentTimeMillis() - start) + "ms，id="
                      + event.getId(), e);
            throw e;
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
        }
    }
}
