package ai.conrain.aigc.platform.web.config;

import ai.conrain.aigc.platform.web.filter.HttpContentCachingFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean httpContentCachingFilter() {
        FilterRegistrationBean r = new FilterRegistrationBean();
        r.setFilter(new HttpContentCachingFilter());
        r.addUrlPatterns("/*");
        r.setName("httpContentCachingFilter");
        r.setOrder(1);

        return r;
    }
}