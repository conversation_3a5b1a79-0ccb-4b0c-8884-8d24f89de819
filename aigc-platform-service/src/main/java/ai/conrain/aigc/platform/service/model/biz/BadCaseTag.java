/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import com.alibaba.schedulerx.shade.scala.Serializable;

import lombok.Data;

/**
 * 不良案例标签
 *
 * <AUTHOR>
 * @version : BadCaseTag.java, v 0.1 2024/12/9 18:53 renxiao.wu Exp $
 */
@Data
public class BadCaseTag implements Serializable {
    private static final long serialVersionUID = 4088822951863584724L;
    /** id */
    private Integer id;
    /** 标题 */
    private String title;
    /** 值 */
    private String prompt;
    /** 是否命中 */
    private boolean checked;

    public BadCaseTag() {}

    public BadCaseTag(Integer id, String title, boolean checked) {
        this.id = id;
        this.title = title;
        this.checked = checked;
    }
}
