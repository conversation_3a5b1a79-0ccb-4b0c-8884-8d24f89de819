package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.dal.example.MaterialModelExample;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.WorkScheduleService;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.NumberCounter;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 分配审核任务
 */
@Slf4j
@Component
public class CreateReviewTaskJob extends JavaProcessor {

    @Autowired
    private MaterialModelDAO materialModelDAO;

    @Autowired
    private WorkScheduleService workScheduleService;

    @Autowired
    private UserService userService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));
        if (params.getStartTime() == null) {
            // 默认取近1天的素材，避免第二天审核员不在排班中
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime twoDaysAgo = now.minusDays(1);
            Date dateTwoDaysAgo = Date.from(twoDaysAgo.atZone(ZoneId.systemDefault()).toInstant());
            params.setStartTime(dateTwoDaysAgo);
        }
        distributeReviewer(params);
        return new ProcessResult(true);
    }

    /**
     * 分配审核人，策略-均匀分配
     */
    private void distributeReviewer(Params params) {
        WorkScheduleQuery workScheduleQuery = new WorkScheduleQuery();
        workScheduleQuery.setStartTime(DateUtils.getFirstOfDay());
        workScheduleQuery.setEndTime(DateUtils.getLastOfDay());
        List<Integer> userIds = workScheduleService.queryWorkScheduleList(workScheduleQuery)
            .stream()
            .map(WorkScheduleVO::getUserId)
            .distinct()
            .collect(Collectors.toList());
        if (userIds.isEmpty()) {
            log.warn("审核人员未排班");
            return;
        }

        // 获取测试中的素材
        MaterialModelQuery materialModelQuery = new MaterialModelQuery();
        materialModelQuery.setStatusList(
            Arrays.asList(MaterialModelStatusEnum.IN_TRAINING.getCode(), MaterialModelStatusEnum.TESTING.getCode()));
        materialModelQuery.setOnlyExperimental(false);
        materialModelQuery.setMaterialType(MaterialType.cloth.name());
        materialModelQuery.setType(ModelTypeEnum.CUSTOM.getCode());
        materialModelQuery.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
        materialModelQuery.setStartCreateTime(params.getStartTime());
        MaterialModelExample materialModelExample = MaterialModelConverter.query2Example(materialModelQuery);
        List<MaterialModelDO> materialModelDOS = materialModelDAO.selectByExampleWithBLOBs(materialModelExample);
        List<Integer> allUserIds = materialModelDOS.stream()
            .map(MaterialModelDO::getUserId)
            .distinct()
            .collect(Collectors.toList());
        List<UserVO> userList = userService.batchQueryById(allUserIds);
        // 只有前台用户上传的素材需要审核
        List<MaterialModelVO> models = materialModelDOS
            .stream()
            .filter(item -> userList.stream()
                .filter(u -> u.getId().equals(item.getUserId()))
                .findFirst()
                .map(UserVO::getRoleType)
                .map(RoleTypeEnum::isFrontRoleOrOperator)
                .orElse(false))
            .map(m -> {
                // 避免转译干扰
                MaterialModelVO model = new MaterialModelVO();
                model.setId(m.getId());
                model.setExtInfo(
                    StringUtils.isBlank(m.getExtInfo()) ? new JSONObject() : JSONObject.parseObject(m.getExtInfo()));
                return model;
            })
            .collect(Collectors.toList());

        if (models.isEmpty()) {
            return;
        }

        // 筛选出未分配的素材
        // fix: 前一天未审核完的服装，第二天审核员不在排班表中，需要重新分配
        List<MaterialModelVO> unreviewedModels = models.stream()
            .peek(model -> {
                // fix: 历史数据包含逗号
                String reviewerId = model.getExtInfo(CommonConstants.KEY_REVIEWER_ID, String.class);
                if (StringUtils.isNotBlank(reviewerId)) {
                    model.addExtInfo(CommonConstants.KEY_REVIEWER_ID, reviewerId.replace(",", "").trim());
                }
            })
            .filter(model -> !userIds.contains(model.getExtInfo(CommonConstants.KEY_REVIEWER_ID, Integer.class)))
            .collect(Collectors.toList());

        if (unreviewedModels.isEmpty()) {
            return;
        }

        NumberCounter numberCounter = new NumberCounter(userIds);
        // 分组筛选出任务量最小的
        // 筛选出已经分配的素材，用于均匀分配策略
        // fix: 去除掉不在人员排班中的人，因为有可能是克隆产生的审核员
        models.stream()
            .map(model -> model.getExtInfo(CommonConstants.KEY_REVIEWER_ID, Integer.class))
            .filter(userIds::contains)
            .forEach(numberCounter::add);

        // 分配审核人
        // 多色服装分给同一个人
        Map<String, Integer> reviewerIdByBatchId = new HashMap<>(32);
        for (MaterialModelVO model : unreviewedModels) {
            Integer targetReviewerId = chooseReviewer(numberCounter, model, reviewerIdByBatchId);
            numberCounter.add(targetReviewerId);
            model.addExtInfo(CommonConstants.KEY_REVIEWER_ID, targetReviewerId);
            model.addExtInfo(CommonConstants.KEY_WORK_SCHEDULED_TIME, new Date());
            MaterialModelDO materialModelDO = new MaterialModelDO();
            materialModelDO.setId(model.getId());
            materialModelDO.setExtInfo(JSONObject.toJSONString(model.getExtInfo()));
            materialModelDAO.updateByPrimaryKeySelective(materialModelDO);
        }
    }

    /** 选择审核人 */
    private Integer chooseReviewer(NumberCounter numberCounter, MaterialModelVO model,
                                   Map<String, Integer> reviewerIdByBatchId) {
        String bizTag = model.getExtInfo(BizConstants.BIZ_TAG, String.class);
        String batchId = model.getExtInfo(CommonConstants.KEY_BATCH_ID, String.class);

        // 若不是多色服装，直接进行均匀分配
        if (!BizConstants.MULTI_COLOR_SPLIT.equals(bizTag) || StringUtils.isBlank(batchId)) {
            return numberCounter.getLeast();
        }

        // 检查批次 ID 是否已有对应的审核人
        Integer reviewerId = reviewerIdByBatchId.get(batchId);
        if (reviewerId != null) {
            return reviewerId;
        }

        // 若没有，选择任务量最小的审核人并记录
        reviewerId = numberCounter.getLeast();
        reviewerIdByBatchId.put(batchId, reviewerId);
        return reviewerId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        private Date startTime;
    }
}
