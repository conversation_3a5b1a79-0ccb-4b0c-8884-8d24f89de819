package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum SalesAssessTypeEnum {

    AGENT_EXCLUSIVE("AGENT_EXCLUSIVE", "独家代理"),
    AGENT_NORMAL("AGENT_NORMAL", "外部普通"),
    AGENT_RECTIFY("AGENT_RECTIFY", "代理整改期"),
    SALES_KA("SALES_KA", "大客户销售费率"),                // key account sales, 大客户销售
    SALES_LEADER("SALES_LEADER", "直营销售Leader"),
    SALES_SUB("SALES_SUB", "直营销售"),
    SALES_DEPT("SALES_DEPT", "销售下属部门/外部渠道"),
    CUSTOM("CUSTOM", "自定义类型"),
    ;

    private String code;

    private String desc;

    private SettleStrategyTypeEnum settleStrategy;

    SalesAssessTypeEnum(String code, String desc) {
        this(code, desc, null);
    }

    SalesAssessTypeEnum(String code, String desc, SettleStrategyTypeEnum settleStrategy) {
        this.code = code;
        this.desc = desc;
        this.settleStrategy = settleStrategy;
    }
    public static SalesAssessTypeEnum getByCode(String code) {
        for (SalesAssessTypeEnum type : SalesAssessTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
