package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * DistributorCustomerQuery
 *
 * @version DistributorCustomerService.java v 0.1 2024-07-15 04:11:37
 */
@Data
public class DistributorCustomerQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 客户主账号id */
    @ApiModelProperty(name = "customerMasterUserId", value = "客户主账号id")
    private Integer customerMasterUserId;

    private List<Integer> customerMasterUserIds;

    /**
     * 渠道商主体id
     */
    private Integer distributorCorpOrgId;

    /**
     * 渠道商主体名称（快照）
     */
    private String distributorCorpName;

    /** 渠道商主账号id */
    @ApiModelProperty(name = "distributorMasterUserId", value = "渠道商主账号id")
    private Integer distributorMasterUserId;

    /** 渠道商运营人员id */
    @ApiModelProperty(name = "distributorOperatorUserId", value = "渠道商运营人员id")
    private Integer distributorOperatorUserId;

    /** 渠道商运营人员id 列表*/
    @ApiModelProperty(name = "distributorOperatorUserIds", value = "渠道商运营人员id 列表")
    private List<Integer> distributorOperatorUserIds;

    /** 渠道商销售人员id */
    @ApiModelProperty(name = "distributorSalesUserId", value = "渠道商销售人员id")
    private Integer distributorSalesUserId;

    /** 渠道商销售人员id 列表*/
    @ApiModelProperty(name = "distributorSalesUserIds", value = "渠道商销售人员id 列表")
    private List<Integer> distributorSalesUserIds;

    /** 创建人id */
    @ApiModelProperty(name = "creatorId", value = "创建人id")
    private Integer creatorId;

    /** 创建人 id 列表*/
    @ApiModelProperty(name = "creatorIds", value = "创建人 id 列表")
    private List<Integer> creatorIds;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private String extInfo;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

    @ApiModelProperty(name = "statusNotIn", value = "状态集合，不包含")
    private List<String> statusNotIn;

    /** 是否包含创建的客户，默认是true */
    boolean includeCreatedCustomer = true;

    boolean needCustomerImagePoint = false;

    private String customerLike;

    private boolean onlyShow15DaysNotUsed;

    //查看muse点低于500的客户
    private boolean customerMusePointLessThan500;

    //推广注册码
    private String promotionCode;
}