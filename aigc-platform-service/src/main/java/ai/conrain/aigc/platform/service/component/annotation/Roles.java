package ai.conrain.aigc.platform.service.component.annotation;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;

import java.lang.annotation.*;
import java.time.LocalDateTime;
import org.joda.time.DateTime;

/**
 * 角色权限注解
 * <p>
 * 用于标记Controller接口，自动从@RequestMapping提取路径信息生成权限配置
 * </p>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Roles {
    /**
     * 允许访问的角色类型数组，默认管理员可访问
     */
    RoleTypeEnum[] value() default {RoleTypeEnum.ADMIN};

    /**
     * 版本号，默认为0，需要更新权限时递增版本号加1
     *
     * @return 版本号
     */
    long version() default 0L;
}
