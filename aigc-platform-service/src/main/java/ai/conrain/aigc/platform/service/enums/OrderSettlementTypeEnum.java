package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum OrderSettlementTypeEnum {
    COMMISSION("COMMISSION", "直接分佣"),
    COMMISSION_RELATED("COMMISSION_RELATED", "间接分佣"),
    ;

    private String code;

    private String desc;

    OrderSettlementTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OrderSettlementTypeEnum getByCode(String code) {
        for (OrderSettlementTypeEnum item : OrderSettlementTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
