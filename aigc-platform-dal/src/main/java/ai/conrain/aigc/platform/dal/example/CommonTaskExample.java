package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.CommonTaskDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CommonTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CommonTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CommonTaskExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CommonTaskExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CommonTaskExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdEqualTo(Integer value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotEqualTo(Integer value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThan(Integer value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThan(Integer value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIn(List<Integer> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotIn(List<Integer> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdBetween(Integer value1, Integer value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(String value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(String value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(String value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(String value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(String value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(String value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLike(String value) {
            addCriterion("task_type like", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotLike(String value) {
            addCriterion("task_type not like", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<String> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<String> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(String value1, String value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(String value1, String value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(String value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(String value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(String value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(String value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(String value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(String value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLike(String value) {
            addCriterion("task_status like", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotLike(String value) {
            addCriterion("task_status not like", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<String> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<String> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(String value1, String value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(String value1, String value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformIsNull() {
            addCriterion("out_task_platform is null");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformIsNotNull() {
            addCriterion("out_task_platform is not null");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformEqualTo(String value) {
            addCriterion("out_task_platform =", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformNotEqualTo(String value) {
            addCriterion("out_task_platform <>", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformGreaterThan(String value) {
            addCriterion("out_task_platform >", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("out_task_platform >=", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformLessThan(String value) {
            addCriterion("out_task_platform <", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformLessThanOrEqualTo(String value) {
            addCriterion("out_task_platform <=", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformLike(String value) {
            addCriterion("out_task_platform like", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformNotLike(String value) {
            addCriterion("out_task_platform not like", value, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformIn(List<String> values) {
            addCriterion("out_task_platform in", values, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformNotIn(List<String> values) {
            addCriterion("out_task_platform not in", values, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformBetween(String value1, String value2) {
            addCriterion("out_task_platform between", value1, value2, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskPlatformNotBetween(String value1, String value2) {
            addCriterion("out_task_platform not between", value1, value2, "outTaskPlatform");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdIsNull() {
            addCriterion("out_task_id is null");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdIsNotNull() {
            addCriterion("out_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdEqualTo(String value) {
            addCriterion("out_task_id =", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdNotEqualTo(String value) {
            addCriterion("out_task_id <>", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdGreaterThan(String value) {
            addCriterion("out_task_id >", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdGreaterThanOrEqualTo(String value) {
            addCriterion("out_task_id >=", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdLessThan(String value) {
            addCriterion("out_task_id <", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdLessThanOrEqualTo(String value) {
            addCriterion("out_task_id <=", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdLike(String value) {
            addCriterion("out_task_id like", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdNotLike(String value) {
            addCriterion("out_task_id not like", value, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdIn(List<String> values) {
            addCriterion("out_task_id in", values, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdNotIn(List<String> values) {
            addCriterion("out_task_id not in", values, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdBetween(String value1, String value2) {
            addCriterion("out_task_id between", value1, value2, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskIdNotBetween(String value1, String value2) {
            addCriterion("out_task_id not between", value1, value2, "outTaskId");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusIsNull() {
            addCriterion("out_task_status is null");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusIsNotNull() {
            addCriterion("out_task_status is not null");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusEqualTo(String value) {
            addCriterion("out_task_status =", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusNotEqualTo(String value) {
            addCriterion("out_task_status <>", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusGreaterThan(String value) {
            addCriterion("out_task_status >", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusGreaterThanOrEqualTo(String value) {
            addCriterion("out_task_status >=", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusLessThan(String value) {
            addCriterion("out_task_status <", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusLessThanOrEqualTo(String value) {
            addCriterion("out_task_status <=", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusLike(String value) {
            addCriterion("out_task_status like", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusNotLike(String value) {
            addCriterion("out_task_status not like", value, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusIn(List<String> values) {
            addCriterion("out_task_status in", values, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusNotIn(List<String> values) {
            addCriterion("out_task_status not in", values, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusBetween(String value1, String value2) {
            addCriterion("out_task_status between", value1, value2, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andOutTaskStatusNotBetween(String value1, String value2) {
            addCriterion("out_task_status not between", value1, value2, "outTaskStatus");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsIsNull() {
            addCriterion("req_biz_params is null");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsIsNotNull() {
            addCriterion("req_biz_params is not null");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsEqualTo(String value) {
            addCriterion("req_biz_params =", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsNotEqualTo(String value) {
            addCriterion("req_biz_params <>", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsGreaterThan(String value) {
            addCriterion("req_biz_params >", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsGreaterThanOrEqualTo(String value) {
            addCriterion("req_biz_params >=", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsLessThan(String value) {
            addCriterion("req_biz_params <", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsLessThanOrEqualTo(String value) {
            addCriterion("req_biz_params <=", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsLike(String value) {
            addCriterion("req_biz_params like", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsNotLike(String value) {
            addCriterion("req_biz_params not like", value, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsIn(List<String> values) {
            addCriterion("req_biz_params in", values, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsNotIn(List<String> values) {
            addCriterion("req_biz_params not in", values, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsBetween(String value1, String value2) {
            addCriterion("req_biz_params between", value1, value2, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andReqBizParamsNotBetween(String value1, String value2) {
            addCriterion("req_biz_params not between", value1, value2, "reqBizParams");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeIsNull() {
            addCriterion("related_biz_type is null");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeIsNotNull() {
            addCriterion("related_biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeEqualTo(String value) {
            addCriterion("related_biz_type =", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeNotEqualTo(String value) {
            addCriterion("related_biz_type <>", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeGreaterThan(String value) {
            addCriterion("related_biz_type >", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeGreaterThanOrEqualTo(String value) {
            addCriterion("related_biz_type >=", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeLessThan(String value) {
            addCriterion("related_biz_type <", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeLessThanOrEqualTo(String value) {
            addCriterion("related_biz_type <=", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeLike(String value) {
            addCriterion("related_biz_type like", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeNotLike(String value) {
            addCriterion("related_biz_type not like", value, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeIn(List<String> values) {
            addCriterion("related_biz_type in", values, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeNotIn(List<String> values) {
            addCriterion("related_biz_type not in", values, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeBetween(String value1, String value2) {
            addCriterion("related_biz_type between", value1, value2, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizTypeNotBetween(String value1, String value2) {
            addCriterion("related_biz_type not between", value1, value2, "relatedBizType");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdIsNull() {
            addCriterion("related_biz_id is null");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdIsNotNull() {
            addCriterion("related_biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdEqualTo(String value) {
            addCriterion("related_biz_id =", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdNotEqualTo(String value) {
            addCriterion("related_biz_id <>", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdGreaterThan(String value) {
            addCriterion("related_biz_id >", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("related_biz_id >=", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdLessThan(String value) {
            addCriterion("related_biz_id <", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdLessThanOrEqualTo(String value) {
            addCriterion("related_biz_id <=", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdLike(String value) {
            addCriterion("related_biz_id like", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdNotLike(String value) {
            addCriterion("related_biz_id not like", value, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdIn(List<String> values) {
            addCriterion("related_biz_id in", values, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdNotIn(List<String> values) {
            addCriterion("related_biz_id not in", values, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdBetween(String value1, String value2) {
            addCriterion("related_biz_id between", value1, value2, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andRelatedBizIdNotBetween(String value1, String value2) {
            addCriterion("related_biz_id not between", value1, value2, "relatedBizId");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeIsNull() {
            addCriterion("task_start_time is null");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeIsNotNull() {
            addCriterion("task_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeEqualTo(Date value) {
            addCriterion("task_start_time =", value, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeNotEqualTo(Date value) {
            addCriterion("task_start_time <>", value, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeGreaterThan(Date value) {
            addCriterion("task_start_time >", value, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("task_start_time >=", value, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeLessThan(Date value) {
            addCriterion("task_start_time <", value, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("task_start_time <=", value, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeIn(List<Date> values) {
            addCriterion("task_start_time in", values, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeNotIn(List<Date> values) {
            addCriterion("task_start_time not in", values, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeBetween(Date value1, Date value2) {
            addCriterion("task_start_time between", value1, value2, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("task_start_time not between", value1, value2, "taskStartTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeIsNull() {
            addCriterion("task_end_time is null");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeIsNotNull() {
            addCriterion("task_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeEqualTo(Date value) {
            addCriterion("task_end_time =", value, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeNotEqualTo(Date value) {
            addCriterion("task_end_time <>", value, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeGreaterThan(Date value) {
            addCriterion("task_end_time >", value, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("task_end_time >=", value, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeLessThan(Date value) {
            addCriterion("task_end_time <", value, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("task_end_time <=", value, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeIn(List<Date> values) {
            addCriterion("task_end_time in", values, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeNotIn(List<Date> values) {
            addCriterion("task_end_time not in", values, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeBetween(Date value1, Date value2) {
            addCriterion("task_end_time between", value1, value2, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andTaskEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("task_end_time not between", value1, value2, "taskEndTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(CommonTaskDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(CommonTaskDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}