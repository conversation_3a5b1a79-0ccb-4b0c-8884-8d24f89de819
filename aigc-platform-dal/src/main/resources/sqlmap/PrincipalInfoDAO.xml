<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.PrincipalInfoDAO">
  <resultMap id="ResultMap" type="ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="principal_type" jdbcType="VARCHAR" property="principalType" />
    <result column="principal_id" jdbcType="INTEGER" property="principalId" />
    <result column="info_key" jdbcType="VARCHAR" property="infoKey" />
    <result column="info_value" jdbcType="LONGVARCHAR" property="infoValue" />
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
    <result column="creator_user_id" jdbcType="INTEGER" property="creatorUserId" />
    <result column="modify_user_id" jdbcType="INTEGER" property="modifyUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Column_List">
    id, principal_type, principal_id, info_key, info_value, ext_info creator_user_id, modify_user_id, create_time,
    modify_time, deleted
  </sql>

  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.PrincipalInfoExample" resultMap="ResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Column_List" />
    from principal_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.PrincipalInfoExample" resultMap="ResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Column_List" />
    from principal_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMap">
    select 
    <include refid="Column_List" />
    from principal_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMap">
    select 
    <include refid="Column_List" />
    from principal_info
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from principal_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into principal_info (principal_type, principal_id, info_key, 
      creator_user_id, modify_user_id, create_time, 
      modify_time, deleted, info_value, 
      ext_info)
    values (#{principalType,jdbcType=VARCHAR}, #{principalId,jdbcType=INTEGER}, #{infoKey,jdbcType=VARCHAR}, 
      #{creatorUserId,jdbcType=INTEGER}, #{modifyUserId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, #{infoValue,jdbcType=LONGVARCHAR}, 
      #{extInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into principal_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        principal_type,
      </if>
      <if test="principalId != null">
        principal_id,
      </if>
      <if test="infoKey != null">
        info_key,
      </if>
      <if test="creatorUserId != null">
        creator_user_id,
      </if>
      <if test="modifyUserId != null">
        modify_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="infoValue != null">
        info_value,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        #{principalId,jdbcType=INTEGER},
      </if>
      <if test="infoKey != null">
        #{infoKey,jdbcType=VARCHAR},
      </if>
      <if test="creatorUserId != null">
        #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifyUserId != null">
        #{modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="infoValue != null">
        #{infoValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.PrincipalInfoExample" resultType="java.lang.Long">
    select count(*) from principal_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update principal_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.principalType != null">
        principal_type = #{record.principalType,jdbcType=VARCHAR},
      </if>
      <if test="record.principalId != null">
        principal_id = #{record.principalId,jdbcType=INTEGER},
      </if>
      <if test="record.infoKey != null">
        info_key = #{record.infoKey,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorUserId != null">
        creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="record.modifyUserId != null">
        modify_user_id = #{record.modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.infoValue != null">
        info_value = #{record.infoValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByExample" parameterType="map">
    update principal_info
    set id = #{record.id,jdbcType=INTEGER},
      principal_type = #{record.principalType,jdbcType=VARCHAR},
      principal_id = #{record.principalId,jdbcType=INTEGER},
      info_key = #{record.infoKey,jdbcType=VARCHAR},
      creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      modify_user_id = #{record.modifyUserId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      info_value = #{record.infoValue,jdbcType=LONGVARCHAR},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO">
    update principal_info
    <set>
      <if test="principalType != null">
        principal_type = #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        principal_id = #{principalId,jdbcType=INTEGER},
      </if>
      <if test="infoKey != null">
        info_key = #{infoKey,jdbcType=VARCHAR},
      </if>
      <if test="creatorUserId != null">
        creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifyUserId != null">
        modify_user_id = #{modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="infoValue != null">
        info_value = #{infoValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO">
    update principal_info
    set principal_type = #{principalType,jdbcType=VARCHAR},
      principal_id = #{principalId,jdbcType=INTEGER},
      info_key = #{infoKey,jdbcType=VARCHAR},
      creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      modify_user_id = #{modifyUserId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      info_value = #{infoValue,jdbcType=LONGVARCHAR},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="logicalDeleteByExample" parameterType="map">
    update principal_info set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update principal_info set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="insertOrUpdate" parameterType="ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into principal_info 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        principal_type,
      </if>
      <if test="principalId != null">
        principal_id,
      </if>
      <if test="infoKey != null">
        info_key,
      </if>
      <if test="creatorUserId != null">
        creator_user_id,
      </if>
      <if test="modifyUserId != null">
        modify_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="infoValue != null">
        info_value,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        #{principalId,jdbcType=INTEGER},
      </if>
      <if test="infoKey != null">
        #{infoKey,jdbcType=LONGVARCHAR},
      </if>
      <if test="creatorUserId != null">
        #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifyUserId != null">
        #{modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="infoValue != null">
        #{infoValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      modify_user_id = #{modifyUserId,jdbcType=INTEGER},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="infoValue != null">
        info_value = #{infoValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectCommissionRelatePrincipal" resultMap="ResultMap">
    select * from principal_info where
   info_key = "SETTLE_CONFIG" and
   JSON_CONTAINS_PATH(info_value, 'one', '$.beneficiaryTargetId') = 1 and
   JSON_EXTRACT(info_value, '$.beneficiaryTargetId') = #{principalId,jdbcType=INTEGER}
  </select>
  
</mapper>