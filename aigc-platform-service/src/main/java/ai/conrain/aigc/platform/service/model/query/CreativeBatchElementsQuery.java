package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * CreativeBatchElementsQuery
 *
 * @version CreativeBatchElementsService.java v 0.1 2024-05-08 03:35:57
 */
@Data
public class CreativeBatchElementsQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 批次id */
    @ApiModelProperty(name = "id", value = "批次id")
    private Integer id;

    /** 模型id */
    @ApiModelProperty(name = "batchId", value = "模型id")
    private Integer batchId;

    /** 元素id */
    @ApiModelProperty(name = "elementId", value = "元素id")
    private Integer elementId;

    @ApiModelProperty(name = "elementKey", value = "元素key")
    private String elementKey;

    /** 归属主账号id */
    @ApiModelProperty(name = "userId", value = "归属主账号id")
    private Integer userId;

    @ApiModelProperty(name = "operatorId", value = "操作人id")
    private Integer operatorId;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

    @ApiModelProperty(name = "includesTypes", value = "元素包含的元素类型")
    private List<String> includesTypes;

    @ApiModelProperty(name = "excludesTypes", value = "元素排除的元素类型")
    private List<String> excludesTypes;
}