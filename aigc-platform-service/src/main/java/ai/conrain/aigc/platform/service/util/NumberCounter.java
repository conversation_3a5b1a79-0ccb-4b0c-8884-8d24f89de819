package ai.conrain.aigc.platform.service.util;

import java.util.List;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数字计数器
 */
public class NumberCounter {
    private final List<Integer> numbers;

    public NumberCounter(List<Integer> numbers) {
        this.numbers = numbers;
    }

    /**
     * 添加数字
     *
     * @param number
     */
    public void add(Integer number) {
        numbers.add(number);
    }

    /**
     * 获取数量最少的数字
     *
     * @return 数字
     */
    public Integer getLeast() {
        return numbers.stream()
            .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
            .entrySet()
            .stream()
            .min(Entry.comparingByValue())
            .map(Entry::getKey)
            .orElse(null);
    }
}
