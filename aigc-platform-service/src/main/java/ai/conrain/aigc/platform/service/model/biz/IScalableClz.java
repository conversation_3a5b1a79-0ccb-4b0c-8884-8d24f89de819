/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import com.alibaba.fastjson.JSONObject;

/**
 * 扩展的模型接口
 *
 * <AUTHOR>
 * @version : IScalableClz.java, v 0.1 2024/6/26 11:48 renxiao.wu Exp $
 */
public interface IScalableClz {
    /** 获取标签列表 */
    String getTags();

    /** 设置标签列表 */
    void setTags(String tags);

    /** 获取扩展信息 */
    JSONObject getExtInfo();

    /** 通过key获取扩展信息 */
    Object getExtInfo(String key);
}
