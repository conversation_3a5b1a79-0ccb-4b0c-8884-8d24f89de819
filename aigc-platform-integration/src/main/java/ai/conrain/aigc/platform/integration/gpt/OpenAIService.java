package ai.conrain.aigc.platform.integration.gpt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class OpenAIService {

    private static final ApiConfig api = new ApiConfig("2025-01-01-preview",
            "1S6N1fjEp03RANWOT0vaeFfquX8WyJ9oPCjcFKt0diYlehKKtQuOJQQJ99BDACHYHv6XJ3w3AAAAACOGZFqi",
            "https://ai-admin0676ai986121648069.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview"
    );

    @Autowired
    @Qualifier("superLongRestTemplate")
    private RestTemplate extraLongRestTemplate;

    /**
     * 请求GPT
     * @param prompt
     * @param imgUrls
     * @return
     */
    public GptResponse requestGpt(String prompt, List<String> imgUrls) {

        if (StringUtils.isEmpty(prompt)) {
            throw new RuntimeException("prompt is empty");
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("api-key", api.getApiKey());

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("temperature", 0.1);
        payload.put("top_p", 0.95);
        payload.put("max_tokens", 1000);

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");

        JSONArray content = new JSONArray();
        content.add(createTextContent(prompt));

        if (!CollectionUtils.isEmpty(imgUrls)) {
            for (String url : imgUrls) {
                content.add(createImageContent(url));
            }
        }

        message.put("content", content);
        messages.add(message);
        payload.put("messages", messages);

        try {
            log.info("【请求GPT】，payload={}", payload);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);
            ResponseEntity<String> response = extraLongRestTemplate.exchange(
                    api.getEndpoint(),
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求GPT】，result={}", result);

            String text = result.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");

            return new GptResponse(
                    text,
                    Status.OK
            );
        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(
                    errorMsg,
                    Status.ERROR
            );
        }
    }

    public enum Status {
        OK, ERROR
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GptResponse {
        private String text;
        private Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ApiConfig {
        private String name;
        private String apiKey;
        private String endpoint;
    }

    private static JSONObject createTextContent(String text) {
        JSONObject content = new JSONObject();
        content.put("type", "text");
        content.put("text", text);
        return content;
    }

    private static JSONObject createImageContent(String imgUrl) {
        JSONObject imageUrlJson = new JSONObject();
        imageUrlJson.put("url", imgUrl);

        JSONObject content = new JSONObject();
        content.put("type", "image_url");
        content.put("image_url", imageUrlJson);
        return content;
    }
}