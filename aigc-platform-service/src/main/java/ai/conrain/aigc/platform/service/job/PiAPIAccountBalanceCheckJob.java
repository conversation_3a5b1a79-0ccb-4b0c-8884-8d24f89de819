package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.kling.KlingVideoServicePIAPI;
import ai.conrain.aigc.platform.integration.kling.VideoGeneratorService302API;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Slf4j
@Component
public class PiAPIAccountBalanceCheckJob extends JavaProcessor {

    //302api
    @Autowired
    private VideoGeneratorService302API videoGeneratorService302API;

    //piapi
    @Autowired
    private KlingVideoServicePIAPI klingVideoServicePIAPI;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));

        Float b = videoGeneratorService302API.getAccountBalanceUSD();
        //通知铁翅进行充值
        if (b != null && b < 10.0f) {
            DingTalkNoticeHelper.sendMsg2DevGroup( "视频账号余额不足，需要充值\n302api当前余额：" + Math.round(b * 100) / 100.0 + " USD", Collections.singletonList("***********"));
        }

        Float c = klingVideoServicePIAPI.getAccountBalanceUSD();
        //通知铁翅进行充值
        if (c != null && c < 10.0f) {
            DingTalkNoticeHelper.sendMsg2DevGroup( "视频账号余额不足，需要充值\npiapi当前余额：" + Math.round(c * 100) / 100.0 + " USD", Collections.singletonList("***********"));
        }

        return new ProcessResult(true);
    }
}
