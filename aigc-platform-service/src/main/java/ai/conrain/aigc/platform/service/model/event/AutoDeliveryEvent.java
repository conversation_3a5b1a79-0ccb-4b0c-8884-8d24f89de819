package ai.conrain.aigc.platform.service.model.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 自动交付事件
 *
 * <AUTHOR>
 * @version : AutoDeliveryEvent.java, v 0.1 2024/11/07 21:37 renxiao.wu Exp $
 */
@Slf4j
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AutoDeliveryEvent extends BaseEvent {
    private static final long serialVersionUID = 7690141523506544717L;
    private Integer modelId;

    public AutoDeliveryEvent() {
        super();
    }

    public AutoDeliveryEvent(Integer modelId) {
        this();
        this.modelId = modelId;
    }
}
