/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 水印工具类
 *
 * <AUTHOR>
 * @version : WaterMarkUtils.java, v 0.1 2024/12/31 13:38 renxiao.wu Exp $
 */
public abstract class WaterMarkUtils {
    /**
     * 解析水印位置
     * 包括topleft/bottomleft/topright/bottomright
     *
     * @param waterMarkDesc 水印描述
     * @return 水印位置
     */
    public static String parseOriginFromDesc(String waterMarkDesc) {
        if (StringUtils.isBlank(waterMarkDesc)) {
            return null;
        }

        if (StringUtils.contains(waterMarkDesc, "top left")) {
            return "topleft";
        }
        if (StringUtils.contains(waterMarkDesc, "top right")) {
            return "topright";
        }
        if (StringUtils.contains(waterMarkDesc, "bottom left")) {
            return "bottomleft";
        }
        if (StringUtils.contains(waterMarkDesc, "bottom right")) {
            return "bottomright";
        }
        return "bottomright";
    }
}
