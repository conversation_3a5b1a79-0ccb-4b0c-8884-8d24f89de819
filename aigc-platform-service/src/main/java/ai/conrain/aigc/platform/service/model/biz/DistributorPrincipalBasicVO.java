package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class DistributorPrincipalBasicVO {

    /** 销售渠道类型 */
    @ApiModelProperty(name = "salesType", value = "销售渠道类型")
    private String salesType;

    /** 结算主体类型 */
    @ApiModelProperty(name = "principalType", value = "结算主体类型")
    private String principalType;

    /** 主体 id */
    @ApiModelProperty(name = "userId", value = "用户id")
    private Integer principalId;

    /** 主体名称 */
    @ApiModelProperty(name = "principalName", value = "主体名称")
    private String principalName;

    /** 组织内角色 */
    @ApiModelProperty(name = "customerRole", value = "组织内角色")
    private String customRole;

    /** 主组织id */
    @ApiModelProperty(name = "masterCorpId", value = "主组织id")
    private Integer masterCorpId;

    /** 主组织名称 */
    @ApiModelProperty(name = "masterCorpName", value = "主组织名称")
    private String masterCorpName;

    /** 主账号id */
    @ApiModelProperty(name = "masterId", value = "主账号id")
    private Integer channelAdminId;

    /** 主账号昵称 */
    @ApiModelProperty(name = "masterNickName", value = "主账号昵称")
    private String channelAdminNickName;

    /** 子组织销售/渠道商类型 */
    @ApiModelProperty(name = "subSalesType", value = "子组织销售/渠道商类型")
    private String subSalesType;

    /** 子组织id */
    @ApiModelProperty(name = "subCorpId", value = "子组织id")
    private Integer subCorpId;

    /** 子组织名称 */
    @ApiModelProperty(name = "subCorpName", value = "子组织名称")
    private String subCorpName;

    /** 子组织管理员 id */
    @ApiModelProperty(name = "subChannelAdminId", value = "子组织管理员 id")
    private Integer subChannelAdminId;

    /** 子组织管理员昵称 */
    @ApiModelProperty(name = "subChannelAdminNickName", value = "子组织管理员昵称")
    private String subChannelAdminNickName;

    /** 签约时间 */
    @ApiModelProperty(name = "contractDate", value = "签约时间")
    private String contractDate;

    public boolean isChannelAdmin() {
        return StringUtils.equals(CustomRoleEnum.CHANNEL_ADMIN.getCode(), customRole);
    }

    public boolean isSecondChannelAdmin() {
        return StringUtils.equals(CustomRoleEnum.SECOND_CHANNEL_ADMIN.getCode(), customRole);
    }
}
