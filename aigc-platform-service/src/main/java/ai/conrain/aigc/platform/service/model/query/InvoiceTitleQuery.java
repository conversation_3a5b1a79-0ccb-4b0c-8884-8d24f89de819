package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * InvoiceTitleQuery
 *
 * @version InvoiceTitleService.java v 0.1 2024-06-27 01:42:09
 */
@Data
public class InvoiceTitleQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 关联的主用户id */
    @ApiModelProperty(name = "masterUserId", value = "关联的主用户id")
    private Integer masterUserId;

    /** 关联的操作员用户id */
    @ApiModelProperty(name = "operatorUserId", value = "关联的操作员用户id")
    private Integer operatorUserId;

    /** 发票类型，普票｜专票 */
    @ApiModelProperty(name = "invoiceType", value = "发票类型，普票｜专票")
    private String invoiceType;

    /** 发票抬头类型，个人｜企业 */
    @ApiModelProperty(name = "subjectType", value = "发票抬头类型，个人｜企业")
    private String subjectType;

    /** 发票抬头 */
    @ApiModelProperty(name = "subjectName", value = "发票抬头")
    private String subjectName;

    /** 统一社会信用代码 */
    @ApiModelProperty(name = "creditCode", value = "统一社会信用代码")
    private String creditCode;

    /** 办公地址 */
    @ApiModelProperty(name = "businessAddress", value = "办公地址")
    private String businessAddress;

    /** 办公电话 */
    @ApiModelProperty(name = "businessPhone", value = "办公电话")
    private String businessPhone;

    /** 开户银行 */
    @ApiModelProperty(name = "bankName", value = "开户银行")
    private String bankName;

    /** 银行账号 */
    @ApiModelProperty(name = "bankAccount", value = "银行账号")
    private String bankAccount;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private String extInfo;


    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}