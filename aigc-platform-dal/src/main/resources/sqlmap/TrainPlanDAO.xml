<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.TrainPlanDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.TrainPlanDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="clothing_id" jdbcType="INTEGER" property="clothingId" />
    <result column="clothing_name" jdbcType="VARCHAR" property="clothingName" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="images_per_combination" jdbcType="INTEGER" property="imagesPerCombination" />
    <result column="face_models" jdbcType="VARCHAR" property="faceModels" />
    <result column="scenes" jdbcType="VARCHAR" property="scenes" />
    <result column="sizes" jdbcType="VARCHAR" property="sizes" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="creator_user_id" jdbcType="INTEGER" property="creatorUserId" />
    <result column="creator_user_name" jdbcType="VARCHAR" property="creatorUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_plan
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.TrainPlanDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_plan (plan_name, clothing_id, clothing_name, 
      remarks, images_per_combination, face_models, 
      scenes, sizes, ext_info, 
      creator_user_id, creator_user_name, create_time, 
      modify_time)
    values (#{planName,jdbcType=VARCHAR}, #{clothingId,jdbcType=INTEGER}, #{clothingName,jdbcType=VARCHAR}, 
      #{remarks,jdbcType=VARCHAR}, #{imagesPerCombination,jdbcType=INTEGER}, #{faceModels,jdbcType=VARCHAR}, 
      #{scenes,jdbcType=VARCHAR}, #{sizes,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, 
      #{creatorUserId,jdbcType=INTEGER}, #{creatorUserName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.TrainPlanDO">
    update train_plan
    set plan_name = #{planName,jdbcType=VARCHAR},
      clothing_id = #{clothingId,jdbcType=INTEGER},
      clothing_name = #{clothingName,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      images_per_combination = #{imagesPerCombination,jdbcType=INTEGER},
      face_models = #{faceModels,jdbcType=VARCHAR},
      scenes = #{scenes,jdbcType=VARCHAR},
      sizes = #{sizes,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      creator_user_name = #{creatorUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select id, plan_name, clothing_id, clothing_name, remarks, images_per_combination, 
    face_models, scenes, sizes, ext_info, creator_user_id, creator_user_name, create_time, 
    modify_time
    from train_plan
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, plan_name, clothing_id, clothing_name, remarks, images_per_combination, 
    face_models, scenes, sizes, ext_info, creator_user_id, creator_user_name, create_time, 
    modify_time
    from train_plan
    order by id desc
  </select>
</mapper>