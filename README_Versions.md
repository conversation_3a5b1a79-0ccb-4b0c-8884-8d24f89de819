# Versions 工具类使用指南

## 概述

`Versions` 是一个用于管理日期+自增号格式版本的工具类，版本格式为 `YYYYMMDD.N`（如：`20250619.0`）。

## 主要功能

### 1. 版本验证
```java
// 验证版本格式
boolean isValid = Versions.isValid("20250619.0"); // true
boolean isInvalid = Versions.isValid("invalid");   // false
```

### 2. 版本解析
```java
// 解析版本
Versions.Version version = Versions.parse("20250619.5");
String datePart = version.getDatePart();      // "20250619"
Integer increment = version.getIncrementPart(); // 5
String fullVersion = version.getFullVersion(); // "20250619.5"
```

### 3. 版本比较
```java
// 比较版本
int result = Versions.compare("20250619.5", "20250619.3"); // > 0
boolean isGreater = Versions.isGreaterThan("20250619.5", "20250619.3"); // true
boolean isLess = Versions.isLessThan("20250619.3", "20250619.5");       // true
boolean isEqual = Versions.isEqual("20250619.5", "20250619.5");         // true
```

### 4. 版本生成
```java
// 获取当前日期的初始版本
String currentInitial = Versions.getCurrentDateInitial(); // "20250619.0"

// 获取指定日期的初始版本
LocalDate date = LocalDate.of(2025, 6, 19);
String dateInitial = Versions.getDateInitial(date); // "20250619.0"

// 构建版本
String built = Versions.build(date, 5);           // "20250619.5"
String builtFromString = Versions.build("20250619", 3); // "20250619.3"
```

### 5. 获取下一个版本
```java
// 获取下一个版本（基于当前日期）
String next = Versions.getNext("20250619.5"); // 如果今天是2025-06-19，返回"20250619.6"

// 获取指定日期的下一个版本
LocalDate targetDate = LocalDate.of(2025, 6, 20);
String nextWithDate = Versions.getNext("20250619.5", targetDate); // "20250620.0"
```

### 6. 版本信息提取
```java
String version = "20250619.5";

// 获取各部分信息
String datePart = Versions.getDatePart(version);      // "20250619"
Integer increment = Versions.getIncrementPart(version); // 5
LocalDate date = Versions.getDate(version);           // LocalDate.of(2025, 6, 19)
```

### 7. 版本检查
```java
// 检查是否为今天的版本
boolean isToday = Versions.isToday("20250619.0");

// 检查是否为指定日期的版本
LocalDate checkDate = LocalDate.of(2025, 6, 19);
boolean isSpecificDate = Versions.isDate("20250619.5", checkDate); // true
```

### 8. 版本格式化
```java
String version = "20250619.5";

// 获取可读描述
String description = Versions.getDescription(version); // "2025年06月19日第6个版本"

// 格式化显示
String display = Versions.formatDisplay(version); // "2025-06-19 v5"
```

## 实际使用场景

### 软件版本管理
```java
// 今天发布第一个版本
String firstVersion = Versions.getCurrentDateInitial(); // "20250619.0"

// 发现bug，发布修复版本
String bugFixVersion = Versions.getNext(firstVersion); // "20250619.1"

// 再次修复
String anotherFix = Versions.getNext(bugFixVersion);   // "20250619.2"

// 明天发布新功能
LocalDate tomorrow = LocalDate.now().plusDays(1);
String newFeature = Versions.getNext(anotherFix, tomorrow); // "20250620.0"
```

### 版本比较和排序
```java
List<String> versions = Arrays.asList("20250619.5", "20250619.3", "20250620.0");
versions.sort(Versions::compare);
// 结果：["20250619.3", "20250619.5", "20250620.0"]
```

## API 变更说明

相比原来的 `VersionUtils`，新的 `Versions` 类有以下变更：

1. **类名**：`VersionUtils` → `Versions`
2. **方法名简化**（去除 `version` 后缀）：
   - `isValidVersion()` → `isValid()`
   - `parseVersion()` → `parse()`
   - `compareVersions()` → `compare()`
   - `getCurrentDateInitialVersion()` → `getCurrentDateInitial()`
   - `getDateInitialVersion()` → `getDateInitial()`
   - `getNextVersion()` → `getNext()`
   - `buildVersion()` → `build()`
   - `getVersionDate()` → `getDate()`
   - `isTodayVersion()` → `isToday()`
   - `isDateVersion()` → `isDate()`
   - `getVersionDescription()` → `getDescription()`
   - `formatVersionDisplay()` → `formatDisplay()`

这些变更使API更加简洁和易用，同时保持了所有原有功能。
