package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.biz.DistributorEnhancedVO;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;
import ai.conrain.aigc.platform.service.model.query.AssessmentPlanQuery;
import ai.conrain.aigc.platform.service.component.AssessmentPlanService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AssessmentPlan控制器
 *
 * <AUTHOR>
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
@Slf4j
@Api("销售考核计划")
@Validated
@RestController
@RequestMapping("/assessmentPlan")
public class AssessmentPlanController {

	/** assessmentPlanService */
	@Autowired
	private AssessmentPlanService assessmentPlanService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询销售考核计划", notes = "根据id查询销售考核计划[assessmentPlan]")
	public Result<AssessmentPlanVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(assessmentPlanService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建销售考核计划", notes = "新建销售考核计划[assessmentPlan]")
	public Result<Integer> create(@Valid @RequestBody AssessmentPlanVO assessmentPlan){
		try {
			AssessmentPlanVO data = assessmentPlanService.insert(assessmentPlan);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加销售考核计划失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建销售考核计划失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除销售考核计划", notes = "删除销售考核计划")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		assessmentPlanService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新销售考核计划", notes = "修改销售考核计划[assessmentPlan]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody AssessmentPlanVO assessmentPlan){
		assessmentPlanService.updateByIdSelective(assessmentPlan);
		return Result.success();
	}

	@ApiOperation(value = "批量查询销售考核计划", notes = "批量查询[assessmentPlan]")
	@PostMapping("/queryList")
	public Result<List<AssessmentPlanVO>> queryAssessmentPlanList(@Valid @RequestBody AssessmentPlanQuery query){
		return Result.success(assessmentPlanService.queryAssessmentPlanList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<AssessmentPlanDO>]")
	public Result<PageInfo<AssessmentPlanVO>> getAssessmentPlanByPage(@Valid @RequestBody AssessmentPlanQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(assessmentPlanService.queryAssessmentPlanByPage(query));
	}

	@PostMapping("/modifyAssessmentPlan")
	@ApiOperation(value = "修改考核计划", notes = "修改考核计划")
	public Result<?> modifyAssessmentPlan(@Valid @RequestBody AssessmentPlanVO assessmentPlan) {
		// 参数检查
		AssertUtil.assertNotNull(assessmentPlan, ResultCode.PARAM_INVALID, "[修改考核计划]考核计划不能为空");

		assessmentPlanService.modifyAssessmentPlan(assessmentPlan);
		return Result.success();
	}

	@GetMapping("/getAllDistributorAssessment")
	@ApiOperation(value = "查询所有渠道商的当前考核计划", notes = "查询所有渠道商的当前考核计划")
	public Result<List<DistributorEnhancedVO>> getAllDistributorAssessment() {
		return Result.success(assessmentPlanService.queryAllDistributorAssessment());
	}

	@PostMapping("/reviewAssessmentPlan")
	@ApiOperation(value = "查询所有渠道商的当前考核计划", notes = "查询所有渠道商的当前考核计划")
	public Result<?> reviewAssessmentPlan(@RequestBody JSONObject request) {
		if (request == null || request.isEmpty() || !request.containsKey("planId")) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数不完整");
		}
		Integer planId = request.getInteger("planId");
		AssertUtil.assertNotNull(planId, ResultCode.PARAM_INVALID, "考核计划ID不能为空");
		SettleConfigModel newSettleConfig = request.getObject("newSettleConfig", SettleConfigModel.class);

		assessmentPlanService.reviewAssessmentPlan(planId, newSettleConfig);
		return Result.success();
	}

	@GetMapping("/manualRefresh")
	@ApiOperation(value = "手动刷新考核计划", notes = "手动刷新考核计划")
	public Result<Boolean> manualRefresh() {
		assessmentPlanService.processAssessment();
		return Result.success(true);
	}
}