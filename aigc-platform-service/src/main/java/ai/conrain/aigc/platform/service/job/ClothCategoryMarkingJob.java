package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.dao.CreativeElementDAO;
import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.integration.gpt.OpenAIService;
import ai.conrain.aigc.platform.integration.gpt.OpenAIService.GptResponse;
import ai.conrain.aigc.platform.integration.gpt.OpenAIService.Status;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ClothCategoryEnum;
import ai.conrain.aigc.platform.service.enums.ClothCategoryEnum.Subcategory;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.biz.CommonMaterialDetail;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.converter.CreativeElementConverter;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ModelTrainDetailVO;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 服装款式标记任务
 * <p>
 * 该任务负责对系统中的服装进行自动款式标记和分类处理。通过 gpt 对服装款式分类，
 * 自动为服装添加款式标签，便于后续的搜索、推荐和展示。
 * </p>
 */
@Slf4j
@Component
public class ClothCategoryMarkingJob extends JavaProcessor {
    private static final int IMAGE_NUM = 5;

    @Autowired
    private CreativeElementDAO creativeElementDAO;

    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private MaterialModelDAO materialModelDAO;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));
        log.info("[服装款式标记任务] 开始执行，参数：{}", params);
        SystemConfigVO promptConfig = systemConfigService.queryByKey(SystemConstants.CLOTH_CATEGORY_PROMPT);
        String prompt = Optional.ofNullable(promptConfig)
            .map(SystemConfigVO::getConfValue)
            .filter(StringUtils::isNotBlank)
            .map(this::replacePrompt)
            .orElse(null);
        if (StringUtils.isBlank(prompt)) {
            return new ProcessResult(false, "未配置 CLOTH_CATEGORY_PROMPT");
        }
        ExecutorService executor = Executors.newFixedThreadPool(5);

        try {
            executeElementJob(params, executor, prompt);
            executeModelJob(params, executor, prompt);
            log.info("[服装款式标记任务] 执行结束");
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("[服装款式标记任务] 执行过程中发生异常", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            MDC.remove("traceId");
            MDC.remove("env");
            OperationContextHolder.clean();
            executor.shutdown();
        }
    }

    private void executeElementJob(Params params, ExecutorService executor, String prompt) {
        if (params.types != null && !params.types.contains("element")) {
            return;
        }
        CreativeElementQuery query = new CreativeElementQuery();
        query.setStartCreateTime(params.getElementStartTime());
        query.setIds(params.getElementIds());
        query.setConfigKey(ElementConfigKeyEnum.SCENE.name());
        query.setNeedInitClothCategory(!params.isForceRefresh());
        query.setIsLora(true);
        query.setLevel(2);
        query.setPageSize(params.getElementPageSize());
        query.setPageNum(1);

        PageInfo<CreativeElementVO> scenes = creativeElementService.queryByPage(query);
        log.info("[服装款式标记任务] 获取到待处理的场景元素数量：{}", scenes.getList().size());

        scenes.getList().forEach(element -> this.markClothCategories(executor, params, prompt,
            element.getLoraModelId(),
            detail -> Optional.ofNullable(detail.toJavaObject(CommonMaterialDetail.class))
                .map(CommonMaterialDetail::getImgUrls)
                .orElse(Collections.emptyList()),
            categories -> {
                // 重新查一遍，尽量避免脏数据覆盖
                // 其他类别记录时间戳，防止重复处理
                CreativeElementVO latestElement = creativeElementService.selectById(element.getId());
                if (CollectionUtils.isNotEmpty(categories)) {
                    latestElement.addExtInfo(CommonConstants.KEY_CLOTH_CATEGORY, categories);
                    latestElement.getExtInfo().remove(CommonConstants.KEY_CLOTH_CATEGORY_OTHER);
                } else {
                    latestElement.addExtInfo(CommonConstants.KEY_CLOTH_CATEGORY_OTHER, new Date());
                }
                CreativeElementVO updateCreativeElement = new CreativeElementVO();
                updateCreativeElement.setId(latestElement.getId());
                updateCreativeElement.setExtInfo(latestElement.getExtInfo());
                creativeElementDAO.updateByPrimaryKeySelective(CreativeElementConverter.vo2DO(updateCreativeElement));
            }));
    }

    private void executeModelJob(Params params, ExecutorService executor, String prompt) {
        if (params.types != null && !params.types.contains("model")) {
            return;
        }
        MaterialModelQuery materialModelQuery = new MaterialModelQuery();
        materialModelQuery.setMaterialType(MaterialType.cloth.name());
        materialModelQuery.setStartCreateTime(params.getModelStartTime());
        materialModelQuery.setNeedInitClothCategory(!params.isForceRefresh());
        materialModelQuery.setIds(params.getModelIds());
        materialModelQuery.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
        materialModelQuery.setPageNum(1);
        materialModelQuery.setPageSize(params.getModelPageSize());
        List<MaterialModelDO> models = materialModelDAO.selectIdByExample(MaterialModelConverter.query2Example(materialModelQuery));
        log.info("[服装款式标记任务] 获取到待处理的服装模型数量：{}", models.size());
        models.forEach(model -> this.markClothCategories(executor, params, prompt,
            model.getId(),
            detail -> Optional.ofNullable(detail.toJavaObject(ClothMaterialDetail.class))
                .map(ClothMaterialDetail::getFullShotImgList)
                .orElse(Collections.emptyList())
                .stream()
                .map(ClothMaterialImg::getImgUrl)
                .collect(Collectors.toList()),
            categories -> {
                // 重新查一遍，尽量避免脏数据覆盖
                // 其他类别记录时间戳，防止重复处理
                // 和服装类型匹配的保留
                MaterialModelVO latestModel = materialModelService.lockById(model.getId());
                String clothType = latestModel.getExtInfo().getString(CommonConstants.CLOTHE_TYPE);
                if (ClothTypeEnum.Tops.getCode().equals(clothType)) {
                    categories = categories.stream()
                        .filter(c -> !c.startsWith("bottom_"))
                        .collect(Collectors.toList());
                }
                if (ClothTypeEnum.Bottoms.getCode().equals(clothType)) {
                    categories = categories.stream()
                       .filter(c ->!c.startsWith("top_"))
                       .collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(categories)) {
                    latestModel.addExtInfo(CommonConstants.KEY_CLOTH_CATEGORY, categories);
                    latestModel.getExtInfo().remove(CommonConstants.KEY_CLOTH_CATEGORY_OTHER);
                } else {
                    latestModel.addExtInfo(CommonConstants.KEY_CLOTH_CATEGORY_OTHER, new Date());
                }
                materialModelService.updateExtInfoByIdSelective(latestModel);
            }));
    }

    private void markClothCategories(ExecutorService executor,
                                     Params params,
                                     String prompt,
                                     int modelId,
                                     Function<JSONObject, List<String>> imgUrlsFunction,
                                     Consumer<List<String>> categoryConsumer) {
        ModelTrainDetailVO trainDetail;
        try {
            trainDetail = materialModelService.getTrainDetail(modelId);
        } catch (Exception e) {
            log.warn("[服装款式标记任务] modelId={} 获取训练详情失败：{}", modelId, e.getMessage());
            return;
        }
        JSONObject materialDetail = trainDetail.getMaterialDetail();
        if (materialDetail == null) {
            log.warn("[服装款式标记任务] modelId={} 未获取到素材详情，跳过该任务", modelId);
            return;
        }

        List<String> imgUrls = Optional.ofNullable(imgUrlsFunction.apply(materialDetail))
            .orElse(Collections.emptyList())
            .stream()
            .limit(IMAGE_NUM)
            .collect(Collectors.toList());
        if (imgUrls.size() < IMAGE_NUM) {
            log.warn("[服装款式标记任务] modelId={} 未获取到足够的图片，跳过该任务", modelId);
            return;
        }
        if (params.isRandom()) {
            Collections.shuffle(imgUrls);
        }

        // 使用 CompletableFuture 进行异步请求
        List<CompletableFuture<String>> futures = imgUrls.stream()
            .map(imgUrl -> CompletableFuture.supplyAsync(() -> {
                GptResponse gptResponse = openAIService.requestGpt(prompt, Collections.singletonList(imgUrl));
                if (gptResponse.getStatus() == Status.ERROR) {
                    log.warn("[服装款式标记任务] modelId={} 调用 gpt 接口失败，错误信息：{}",
                        modelId, gptResponse.getText());
                    return null;
                }
                log.info("[服装款式标记任务] modelId={} gptResponse={}", modelId, gptResponse.getText());
                return gptResponse.getText();
            }, executor))
            .collect(Collectors.toList());

        // 等待所有异步任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        // 获取所有任务的结果
        List<String> imgTexts = allFutures.thenApply(v -> futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList()))
            .join();

        // 提取出所有的款式，筛选出每个款式出现次数大于等于3次的
        List<String> clothCategories = imgTexts.stream()
            .map(StringUtils::trim)
            .filter(StringUtils::isNotBlank)
            .flatMap(array -> JSONObject.parseArray(array, String.class)
                .stream()
                .map(ClothCategoryEnum::fromSubcategoryName)
                .filter(Objects::nonNull)
                .map(Subcategory::getSubcategory))
            .collect(Collectors.groupingBy(
                Function.identity(),
                HashMap::new,
                Collectors.counting()))
            .entrySet()
            .stream()
            .filter(e -> e.getValue() >= 3)
            .map(Entry::getKey)
            .collect(Collectors.toList());
        categoryConsumer.accept(clothCategories);
    }

    private String replacePrompt(String prompt) {
        List<String> categoryList = Arrays.stream(ClothCategoryEnum.values())
            .map(ClothCategoryEnum::getChildren)
            .flatMap(List::stream)
            .map(ClothCategoryEnum.Subcategory::getSubcategoryName)
            .collect(Collectors.toList());

        return prompt.replace("{{categoryList}}", JSONObject.toJSONString(categoryList));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        private List<Integer> elementIds;
        private List<Integer> modelIds;
        private Date elementStartTime = new Date();
        private Date modelStartTime = new Date();
        private int elementPageSize = 10;
        private int modelPageSize = 10;
        private List<String> types;
        private boolean random;
        private boolean forceRefresh;
    }
}
