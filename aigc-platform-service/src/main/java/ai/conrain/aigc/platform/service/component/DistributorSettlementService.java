package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorPrincipalBasicVO;
import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.query.DistributorSettlementQuery;
import ai.conrain.aigc.platform.service.model.vo.DistributorSettlementVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 渠道商结算明细 Service定义
 *
 * <AUTHOR>
 * @version DistributorSettlementService.java v 0.1 2025-05-22 05:39:41
 */
public interface DistributorSettlementService {
	
	/**
	 * 查询渠道商结算明细对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	DistributorSettlementVO selectById(Integer id);

	/**
	 * 删除渠道商结算明细对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加渠道商结算明细对象
	 * @param distributorSettlement 对象参数
	 * @return 返回结果
	 */
	DistributorSettlementVO insert(DistributorSettlementVO distributorSettlement);

	/**
	 * 修改渠道商结算明细对象
	 * @param distributorSettlement 对象参数
	 */
	void updateByIdSelective(DistributorSettlementVO distributorSettlement);

	/**
	 * 带条件批量查询渠道商结算明细列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<DistributorSettlementVO> queryDistributorSettlementList(DistributorSettlementQuery query);

	/**
	 * 变更服务费率
	 * @param distributorCorpId 渠道商id
	 * @param serviceRate 目标服务费率
	 * @param effectTime  生效时间
	 */
	void setServiceRate(Integer distributorCorpId, BigDecimal serviceRate, String effectTime);

	/**
	 * 初始化渠道结算
	 *
	 * @param principal      销售渠道 id
	 * @param settleConfigVO 结算配置
	 * @param startDate      结算周期开始日期
	 * @param endDate        结算周期结束日期
	 * @return 结果
	 */
	DistributorSettlementVO initSettlement(PrincipalModel principal, DistributorSettleConfigVO settleConfigVO, Date startDate, Date endDate);

	/**
	 * 带条件分页查询商户结算明细
	 *
	 * @param query 查询条件
	 */
	Result<PageInfo<DistributorSettlementVO>> queryByPage(DistributorSettlementQuery query);

	/**
	 * 手动结算
	 *
	 * @param id       结算单id
	 * @param outBizNo 银行流水号
	 * @return 结果
	 */
	Result<?> manualSettle(Integer id, String outBizNo);

	/**
	 * 获取结算配置 model
	 */
	SettleConfigModel getSettleConfig(PrincipalModel principal);

	/**
	 * 查询渠道商结算配置
	 * @param principal 结算主体
	 * @return 结果
	 */
    DistributorSettleConfigVO queryDistributorSettleConfig(PrincipalModel principal);

	DistributorPrincipalBasicVO queryDistributorPrincipalBasicInfo(PrincipalModel principal);

	/**
	 * 修改渠道商结算配置
	 */
    void modifyDistributorSettlementConfig(PrincipalTypeEnum principalType, Integer principalId, SettleConfigModel config);

	List<DistributorPrincipalBasicVO> queryAllDistributorPrincipalBasicInfo();
}