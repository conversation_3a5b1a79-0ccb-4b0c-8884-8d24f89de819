/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import java.io.Serializable;

import lombok.Data;

/**
 * 决策后的服务结果
 *
 * <AUTHOR>
 * @version : DeclareServiceResult.java, v 0.1 2024/6/15 17:13 renxiao.wu Exp $
 */
@Data
public class DeclareServiceResult implements Serializable {
    private static final long serialVersionUID = 4326314207115023053L;

    /** 是否可用 */
    private boolean available = false;

    /** 空闲线程数量 */
    private int idleNumber = 0;

    /** 当前服务地址 */
    private String serviceUrl;

    /** ws服务地址 */
    private String wsServiceUrl;
}
