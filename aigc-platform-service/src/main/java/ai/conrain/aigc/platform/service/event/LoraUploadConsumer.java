/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.event;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.event.LoraUploadEvent;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_MD5;
import static ai.conrain.aigc.platform.service.constants.EventConstants.GROUP_UPLOAD_LORA_TO_OSS;
import static ai.conrain.aigc.platform.service.constants.EventConstants.TOPIC_UPLOAD_LORA_TO_OSS;

/**
 * 上传lora到oss消息监听
 *
 * <AUTHOR>
 * @version : LoraUploadConsumer.java, v 0.1 2024/9/14 14:14 renxiao.wu Exp $
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = TOPIC_UPLOAD_LORA_TO_OSS, consumerGroup = GROUP_UPLOAD_LORA_TO_OSS,
    consumeTimeout = 5 * 1000L)
@ConditionalOnProperty(prefix = "rocketmq", name = "enabled", havingValue = "true", matchIfMissing = true)
public class LoraUploadConsumer implements RocketMQListener<LoraUploadEvent> {
    private static final String LOCK_KEY_PREFIX = "_lora_upload_lock_";
    private static final int LOCK_EXPIRE_TIME = 20 * 60 * 1000;
    /** loraMd5上线时间 */
    private static final Date LORA_MD_PUBLIC_TIME = DateUtils.parseSimpleDate("2025-02-01");
    /** 线程池配置 */
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 1, 60L, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(10), new ThreadPoolExecutor.DiscardPolicy());// 丢弃新任务，避免资源耗尽
    @Autowired
    private LoraUploadConsumer self;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private OssService ossService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private TairService tairService;
    @Autowired
    private FileDispatch fileDispatch;

    @Autowired
    private CommonTaskService commonTaskService;

    @Override
    public void onMessage(LoraUploadEvent event) {
        String traceId = CommonUtil.uuid();
        String env = StringUtils.upperCase(EnvUtil.getEnv());
        MDC.put("traceId", traceId);
        MDC.put("env", env);

        if (event.getModelId() == null) {
            log.error("【Lora上传oss事件】模型id为空，直接丢弃该事件: {}", event);
            return;
        }

        log.info("【Lora上传oss事件】接收到消息: {}", event);

        MaterialModelVO model = materialModelService.selectById(event.getModelId());
        if (alreadyUploaded(event, model)) {

            String fileServerUrl = serverHelper.getFileServerUrl(event.getFileServerUrl());
            String filePath = event.getFilePath();

            String md5 = model.getExtInfo(KEY_LORA_MD5, String.class);
            String loraUrl = model.getClothLoraTrainDetail().getLoraRetFileUrl();

            log.info("【Lora上传oss事件】文件已上传，直接通知lora文件同步事件,taskId={},fileServerUrl={},filePath={}",
                model.getId(), fileServerUrl, filePath);

            fileDispatch.notifyFileSync(fileServerUrl, filePath, null, md5, event.getModelId());

            return;
        }

        threadPoolExecutor.execute(() -> self.process(event, traceId, env));

        log.info("【Lora上传oss事件】发起调度lora上传异步任务，中断当前消息消费，由异步线程继续执行");
        throw new RuntimeException("【Lora上传oss事件】发起调度lora上传异步任务，中断当前消息消费，由异步线程继续执行");
    }

    /**
     * lora下载和上传处理
     *
     * @param event   事件
     * @param traceId traceId
     * @param env     环境
     */
    @Transactional(rollbackFor = Exception.class)
    public void process(LoraUploadEvent event, String traceId, String env) {
        long start = System.currentTimeMillis();
        MDC.put("traceId", traceId);
        MDC.put("env", env);

        String lockKey = LOCK_KEY_PREFIX + event.getModelId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);
        if (!lock) {
            log.info("【Lora上传oss事件】抢锁失败，由下一次事件重新发起调度，event={}", event);
            throw new BizException("【Lora上传oss事件】抢锁失败");
        }

        log.info("【Lora上传oss事件】异步处理启动");

        try {
            MaterialModelVO model = materialModelService.selectById(event.getModelId());

            //做个兜底，防止传入的url非文件服务
            String fileServerUrl = serverHelper.getFileServerUrl(event.getFileServerUrl());
            String filePath = event.getFilePath();

            String md5 = null;
            String loraUrl = null;

            //如果oss上文件已存在且是最新的，则只检查GPU机器上是否存在且是最新
            if (alreadyUploaded(event, model)) {

                log.info("【Lora上传oss事件】文件已上传: {}", event);

                md5 = model.getExtInfo(KEY_LORA_MD5, String.class);
                loraUrl = model.getClothLoraTrainDetail().getLoraRetFileUrl();

            } else { //如果oss上文件不存在，则先上传文件，之后再检查GPU机器上文件是否存在

                if (StringUtils.isBlank(fileServerUrl)) {
                    log.error("【Lora上传oss事件】文件服务地址为空，不进行处理,{},event={}", fileServerUrl, event);
                    throw new BizException("error");
                }

                log.info("【Lora上传oss事件】文件未上传，开始上传文件: {}", event);

                ByteArrayOutputStream os = comfyUIService.downloadFile(filePath, fileServerUrl);
                if (os == null) {
                    log.warn("【Lora上传oss事件】下载lora文件失败，event={}", event);
                    throw new BizException("Lora上传oss异常");
                }

                byte[] fileBytes = os.toByteArray();

                try (ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes)) {
                    inputStream.mark(fileBytes.length);  // 标记当前位置，并指定最多允许读取的字节数

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd/HHmmss");

                    File file = new File(filePath);
                    String fileName = String.format("lora/%s_%s_%s", sdf.format(new Date()),
                        CommonUtil.generateRandomNumberString(4), file.getName());

                    // 上传到 OSS
                    loraUrl = ossService.upload(fileName, inputStream);

                    log.info("lora上传oss成功：{}", loraUrl);

                    // 设置任务结果（重新查一次，上面的下载和上传耗时可能很长，model可能已经有更新）
                    model = materialModelService.lockById(event.getModelId());
                    AssertUtil.assertNotNull(model, "lora训练成功但未抢到锁");

                    LoraTrainDetail detail = model.getClothLoraTrainDetail();
                    detail.setLoraRetFileUrl(loraUrl);

                    //计算文件的md5
                    md5 = CommonUtil.calculateMD5(fileBytes);
                    model.addExtInfo(KEY_LORA_MD5, md5);
                }

                //小于1M的文件，不进行处理
                if (fileBytes.length < 10 * 1024 * 1024) {
                    log.error("【Lora上传oss事件】文件大小异常，不进行处理,{},event={}", fileServerUrl, event);
                    throw new BizException("Lora上传oss异常");
                }

                materialModelService.innerUpdate(model);
            }

            //通知其他文件服务器拉取文件
            log.info("【Lora上传oss事件】开始通知lora文件同步事件,taskId={},fileServerUrl={},filePath={}", model.getId(),
                fileServerUrl, filePath);

            fileDispatch.notifyFileSync(fileServerUrl, filePath, null, md5, event.getModelId());

            log.info("【Lora上传oss事件】成功,耗时={}ms,event={}", System.currentTimeMillis() - start, event);

            syncLoraUploadSuccess4AutoGenImgTask(event, model);

        } catch (Exception e) {
            log.error("【Lora上传oss事件】异常,耗时=" + (System.currentTimeMillis() - start) + "ms，event=" + event, e);
            throw new RuntimeException(e);
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            tairService.releaseLock(lockKey);
        }
    }

    private void syncLoraUploadSuccess4AutoGenImgTask(LoraUploadEvent event, MaterialModelVO model) {
        Integer modelId = event.getModelId();

        if (model.getMainType() == MainTypeEnum.SUB) {
            modelId = model.getMainId();

            List<MaterialModelVO> list = materialModelService.querySubModel(modelId);
            list = list.stream().filter(item -> !item.getId().equals(event.getModelId())).collect(Collectors.toList());

            if (!list.stream().allMatch(e -> MaterialModelUtils.alreadyUploadedLora(e.getClothLoraTrainDetail()))) {
                log.info("【Lora上传oss事件】子模型未全部上传oss，不变更自动出图任务状态,mainId={},event={}", modelId,
                    event);
                return;
            }

            log.info("【Lora上传oss事件】子模型全部上传oss，变更自动出图任务状态,mainId={},event={}", modelId, event);
        }

        CommonTaskQuery commonTaskQuery = new CommonTaskQuery();
        commonTaskQuery.setTaskType(CommonTaskEnums.TaskType.AUTO_GEN_IMGS_BEFORE_DELIVER.name());
        commonTaskQuery.setRelatedBizId(modelId.toString());
        commonTaskQuery.setRelatedBizType(CommonTaskEnums.RelatedBizType.LORA_MODEL.getCode());

        List<CommonTaskVO> commonTaskVOS = commonTaskService.queryCommonTaskList(commonTaskQuery);

        //自动出图任务
        if (CollectionUtils.isNotEmpty(commonTaskVOS)) {
            CommonTaskVO task = commonTaskVOS.get(0);
            JSONObject ext = task.getExtInfo();
            if (ext == null) {
                ext = new JSONObject();
            }
            ext.put(CommonConstants.KEY_LORA_UPLOAD_OSS_SUCCESS, CommonConstants.YES);

            task.setExtInfo(ext);
            commonTaskService.updateByIdSelective(task);
        }
    }

    /**
     * 判断是否已经上传oss
     *
     * @param event lora上传oss事件
     * @param model 模型
     * @return true，已上传
     */
    private static boolean alreadyUploaded(LoraUploadEvent event, MaterialModelVO model) {
        if (model == null) {
            log.error("【Lora上传oss事件】模型数据未找到，直接丢弃该事件: {}", event);
            return true;
        }

        String md5 = model.getExtInfo(KEY_LORA_MD5, String.class);

        if (StringUtils.isBlank(md5) && DateUtils.compare(model.getCreateTime(), LORA_MD_PUBLIC_TIME) > 0) {
            log.info("【Lora上传oss事件】lora md5为空，判定为未上传lora: {}", event);
            return false;
        }

        LoraTrainDetail detail = model.getClothLoraTrainDetail();

        return MaterialModelUtils.alreadyUploadedLora(detail);
    }
}
