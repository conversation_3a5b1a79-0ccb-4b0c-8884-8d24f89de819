/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.job;


import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 渠道商结算定时任务
 *
 * <AUTHOR>
 * @version : DistributorSettleJob.java, v 0.1 2023/9/20 19:41 renxiao.wu Exp $
 */
@Slf4j
@Component
public class DistributorSettleJob extends JavaProcessor {

    @Autowired
    private DistributorSettlementService distributorSettlementService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private DistributorService distributorService;

    @Override
    public ProcessResult process(JobContext context) {
        // 1. 初始化日志上下文
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        
        log.info("[渠道商结算定时任务]任务开始");
        long startTime = System.currentTimeMillis();

        try {
            // 2. 解析任务参数
            String params = context.getInstanceParameters();
            SettleTaskParams taskParams = parseParams(params);
            
            // 3. 根据参数执行结算
            if (taskParams != null) {
                if (StringUtils.isNotEmpty(taskParams.getSettleDate())) {
                    // 手动指定结算日期
                    Date settleDate = DateUtils.parseSimpleDate(taskParams.getSettleDate());
                    Date executionDate = DateUtils.getDateAfterNDays(settleDate, 1);
                    log.info("[渠道商结算定时任务]手动指定结算日: {}, 执行日: {}", 
                            taskParams.getSettleDate(), DateUtils.formatSimpleDate(executionDate));
                    processDistributorSettlement(executionDate, taskParams.getUserIds());
                } else if (StringUtils.isNotEmpty(taskParams.getExecutionDate())) {
                    // 手动指定执行日期
                    Date executionDate = DateUtils.parseSimpleDate(taskParams.getExecutionDate());
                    log.info("[渠道商结算定时任务]手动指定执行日: {}", taskParams.getExecutionDate());
                    processDistributorSettlement(executionDate, taskParams.getUserIds());
                } else {
                    // 只指定了结算主体ID，使用今天作为执行日期
                    log.info("[渠道商结算定时任务]指定结算主体ID: {}", taskParams.getUserIds());
                    processDistributorSettlement(new Date(), taskParams.getUserIds());
                }
            } else {
                // 常规执行，使用今天作为执行日期
                log.info("[渠道商结算定时任务]常规执行，使用今天日期");
                processDistributorSettlement(new Date(), null);
            }

            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("[渠道商结算定时任务]执行异常", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            log.info("[渠道商结算定时任务]任务结束，耗时{}ms", System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 执行结算处理
     */
    private void processDistributorSettlement(Date executionDate, List<Integer> specificUserIds) {
        // 获取所有渠道组织
        List<OrganizationVO> rootCorpList = organizationService.queryDistributorCorps();
        int successCount = 0;
        int skipCount = 0;
        int failCount = 0;

        for (OrganizationVO rootCorp : rootCorpList) {
            log.info("[渠道商结算定时任务]处理渠道组织: {}", rootCorp.getName());

            // 获取所有子账号
            List<PrincipalModel> principals = new ArrayList<>();
            distributorService.querySettlePrincipalExclusive(principals, rootCorp, CustomRoleEnum.OPS_MEMBER);

            for (PrincipalModel principal : principals) {
                // 如果指定了结算主体ID列表且当前结算主体不在列表中，则跳过
                if (CollectionUtils.isNotEmpty(specificUserIds) && !specificUserIds.contains(principal.getId())) {
                    continue;
                }

                try {
                    log.info("[渠道商结算定时任务]处理结算主体: {}", principal.getName());

                    // 获取结算配置
                    DistributorSettleConfigVO settleConfigVO =
                            distributorSettlementService.queryDistributorSettleConfig(principal);

                    if (settleConfigVO == null || settleConfigVO.getSettleConfig() == null) {
                        log.warn("[渠道商结算定时任务]结算主体{}没有结算配置，跳过", principal.getName());
                        skipCount++;
                        continue;
                    }

                    SettleConfigModel settleConfigModel = settleConfigVO.getSettleConfig();

                    // 如果settleDate为空，设置默认值为0（月底结算）
                    if (ObjectUtils.isEmpty(settleConfigModel.getCalculationDate())) {
                        settleConfigModel.setCalculationDate(0);
                        log.warn("[渠道商结算定时任务]结算主体{}结算日期为空，默认设置为月底结算", principal.getName());
                    }

                    // 判断是否应该执行结算
                    if (shouldSettleOnDate(settleConfigModel, executionDate)) {
                        // 计算结算周期
                        SettlePeriod period = calculateSettlePeriod(settleConfigModel, executionDate);

                        log.info("[渠道商结算定时任务]开始执行结算，结算主体:{}, 结算周期:{} 至 {}",
                                principal.getName(),
                                DateUtils.formatSimpleDate(period.getStartDate()),
                                DateUtils.formatSimpleDate(period.getEndDate()));

                        // 执行结算
                        distributorSettlementService.initSettlement(
                                principal,
                                settleConfigVO,
                                period.getStartDate(),
                                period.getEndDate()
                        );
                        log.info("[渠道商结算定时任务]结算完成，结算主体:{}", principal.getName());
                        successCount++;
                    } else {
                        log.info("[渠道商结算定时任务]当前日期{}不是结算日期的次日，跳过结算主体:{}",
                                DateUtils.formatSimpleDate(executionDate), principal.getName());
                        skipCount++;
                    }
                } catch (Exception e) {
                    log.error("[渠道商结算定时任务]结算异常，结算主体:{}", principal.getName(), e);
                    failCount++;
                }
            }
        }

        log.info("[渠道商结算定时任务]处理完成, 成功:{}, 跳过:{}, 失败:{}", successCount, skipCount, failCount);
    }
    
    /**
     * 解析任务参数
     * 格式1: {"settleDate":"2023-05-25","userIds":[100001,100002]}
     * 格式2: {"executionDate":"2023-05-26","userIds":[100001,100002]}
     */
    private SettleTaskParams parseParams(String params) {
        if (StringUtils.isEmpty(params)) {
            return null;
        }
        
        try {
            return JSON.parseObject(params, SettleTaskParams.class);
        } catch (Exception e) {
            log.error("[渠道商结算定时任务]解析任务参数失败: {}", params, e);
            return null;
        }
    }
    
    /**
     * 判断指定日期是否应该执行结算（即前一天是否为结算日）
     */
    private boolean shouldSettleOnDate(SettleConfigModel config, Date executionDate) {
        // 获取前一天的日期（结算日）
        Date settleDate = DateUtils.getDateAfterNDays(executionDate, -1);
        
        // 获取结算日的日期信息
        Calendar cal = Calendar.getInstance();
        cal.setTime(settleDate);
        int settleDayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
        int maxDaysInMonth = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        
        // 处理月底特殊情况
        if (config.getCalculationDate() == 0) {
            // 判断结算日是否为月底最后一天
            return settleDayOfMonth == maxDaysInMonth;
        } else {
            // 判断结算日是否为有效结算日
            return settleDayOfMonth == config.getCalculationDate();
        }
    }
    
    /**
     * 计算结算周期的起止日期
     */
    private SettlePeriod calculateSettlePeriod(SettleConfigModel config, Date executionDate) {
        SettlePeriod period = new SettlePeriod();
        // 结算日为执行日的前一天
        Date settleDate = DateUtils.getDateAfterNDays(executionDate, -1);
        
        if (config.getCalculationDate() == 0) {
            // 月底结算：结算周期为当月1号到月底
            Calendar cal = Calendar.getInstance();
            cal.setTime(settleDate);
            cal.set(Calendar.DAY_OF_MONTH, 1); // 当月1号
            period.setStartDate(cal.getTime());
            period.setEndDate(settleDate); // 结算日就是月底
        } else {
            // 指定日期结算：结算周期为上月(settleDate+1)号到本月settleDate号
            // 获取上月对应的(settleDate+1)号
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(settleDate);
            
            // 确定上个结算周期的月份
            startCal.add(Calendar.MONTH, -1);
            // 设置为上月的(settleDate+1)号
            startCal.set(Calendar.DAY_OF_MONTH, config.getCalculationDate() + 1);
            period.setStartDate(startCal.getTime());
            period.setEndDate(settleDate); // 结算日
        }
        return period;
    }

    /**
     * 结算任务参数类
     */
    @Data
    private static class SettleTaskParams {
        private String settleDate;
        private String executionDate;
        private List<Integer> userIds;
    }

    /**
     * 结算周期类
     */
    @Data
    private static class SettlePeriod {
        private Date startDate;
        private Date endDate;
    }
}

