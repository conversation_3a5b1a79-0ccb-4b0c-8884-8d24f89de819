package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DistributorCustomerTopupVO extends MerchantTopupVO {

    //运营
    private Integer distributorOperatorUserId;
    private String distributorOperatorNickName;
    private String distributorOperatorMobile;

    //销售
    private Integer distributorSalesUserId;
    private String distributorSalesNickName;
    private String distributorSalesMobile;
}
