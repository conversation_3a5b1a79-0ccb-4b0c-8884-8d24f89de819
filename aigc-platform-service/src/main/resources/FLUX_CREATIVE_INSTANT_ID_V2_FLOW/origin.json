{"client_id": "${clientId}", "prompt": {"177": {"_meta": {"title": "Text String"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["283", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "216": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "232": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "235": {"_meta": {"title": "Inspyrenet Rembg"}, "class_type": "InspyrenetRembg", "inputs": {"image": ["236", 0], "torchscript_jit": "default"}, "disable": "${isPureBg?then('false','true')}"}, "236": {"_meta": {"title": "🔍 CR Upscale Image"}, "class_type": "CR Upscale Image", "inputs": {"image": ["410", 0], "mode": "rescale", "resampling_method": "lanc<PERSON>s", "rescale_factor": 2, "resize_width": "${height}", "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}, "disable": "${isPureBg?then('false','true')}"}, "248": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": "${pureRgb}", "height": ["261", 5], "width": ["261", 4]}, "disable": "${isPureBg?then('false','true')}"}, "258": {"_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}, "class_type": "ImageRGBA2RGB", "inputs": {"image": ["235", 0]}, "disable": "${isPureBg?then('false','true')}"}, "261": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["236", 0]}, "disable": "${isPureBg?then('false','true')}"}, "263": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["316", 0], "scale_by": ["266", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isPureBg?then('false','true')}"}, "266": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["316", 0], "target_size": ["367", 0]}, "disable": "${isPureBg?then('false','true')}"}, "268": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["283", 0]}}, "269": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "272": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "${baseModelDType!'default'}"}}, "273": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["277", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "${samplerName!'euler'}"}}, "275": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "${scheduleName!'beta'}", "steps": "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}"}}, "276": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "277": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["268", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(378,296)}", 0], "width": ["352", 0]}}, "282": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["${isPromptCorrect?then(552,282)}", 0]}}, "284": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"_meta": {"title": "服装lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${((isPureBg||!isAntiBlueLora)&&!isLoraFace)?then(0,1)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,377),298)}", 0], "strength_clip": 1, "strength_model": "${loraStrength}"}}, "297": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "298": {"_meta": {"title": "风格lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isLoraFace?then(297,271)}", "${isLoraFace?then(1,0)?number}"], "lora_name": "${sceneLora}", "model": ["${isLoraFace?then(297,377)}", 0], "strength_clip": 1, "strength_model": "${sceneLoraStrength}"}, "disable": "${(isPureBg||!isAntiBlueLora)?then('true','false')}"}, "316": {"_meta": {"title": "conrain image composite masked"}, "class_type": "ConrainImageCompositeMasked", "inputs": {"destination": ["248", 0], "mask": ["235", 1], "resize_source": false, "source": ["258", 0], "x": 0, "y": 0}, "disable": "${isPureBg?then('false','true')}"}, "320": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "331": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1335}}, "349": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,410)}", 0], "width": ["285", 0], "x": 0, "y": 0}}, "365": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["410", 0]}}, "367": {"_meta": {"title": "Integer Maximum"}, "class_type": "JWIntegerMax", "inputs": {"a": ["365", 4], "b": ["365", 5]}}, "373": {"_meta": {"title": "推理加速开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${speedUpSwitch?then(1,2)}"}}, "375": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}}, "377": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}}, "378": {"_meta": {"title": "合并PW和flux模型"}, "class_type": "ModelMergeFlux1", "inputs": {"double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "final_layer.": 1, "guidance_in": 1, "img_in.": 1, "model1": ["508", 0], "model2": ["379", 0], "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.2.": 1, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.3.": 1, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "time_in.": 1, "txt_in.": 1, "vector_in.": 1}, "disable": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then('false','true')}"}, "379": {"_meta": {"title": "PW模型"}, "class_type": "UNETLoader", "inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "disable": "${(isPWModel)?then('false','true')}"}, "388": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["271", 0], "text": ""}}, "389": {"_meta": {"title": "Load InstantID Model"}, "class_type": "InstantIDModelLoader", "inputs": {"instantid_file": "ip-adapter.bin"}}, "390": {"_meta": {"title": "InstantID Face Analysis"}, "class_type": "InstantIDFaceAnalysis", "inputs": {"provider": "CUDA"}}, "391": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}}, "392": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["402", 0], "negative": ["412", 2], "noise_mask": true, "pixels": ["414", 0], "positive": ["412", 1], "vae": ["416", 2]}}, "397": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": ["331", 0]}}, "398": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"}}, "399": {"_meta": {"title": "Ordered Face Filter"}, "class_type": "OrderedFaceFilter", "inputs": {"criteria": "area", "faces": ["436", 0], "order": "descending", "take_count": 1, "take_start": 0}}, "400": {"_meta": {"title": "MaskComposite"}, "class_type": "MaskComposite", "inputs": {"destination": ["404", 0], "operation": "subtract", "source": ["401", 0], "x": 0, "y": 0}}, "401": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": -24, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["402", 0]}}, "402": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 4, "enabled": true, "grow": 32, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["414", 1]}}, "403": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 40, "enabled": true, "grow": 72, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["414", 1]}}, "404": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": 24, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["402", 0]}}, "405": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["271", 0], "text": ["331", 0]}}, "406": {"_meta": {"title": "VAE Encode"}, "class_type": "VAEEncode", "inputs": {"pixels": ["413", 0], "vae": ["270", 0]}}, "407": {"_meta": {"title": "Set Latent Noise Mask"}, "class_type": "SetLatentNoiseMask", "inputs": {"mask": ["400", 0], "samples": ["406", 0]}}, "408": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["454", 0], "vae": ["270", 0]}}, "409": {"_meta": {"title": "Image Color Match"}, "class_type": "easy imageColorMatch", "inputs": {"image_output": "<PERSON>de", "image_ref": ["414", 0], "image_target": ["408", 0], "method": "adain", "save_prefix": "ComfyUI"}}, "410": {"_meta": {"title": "<PERSON>p Faces Back"}, "class_type": "WarpFacesBack", "inputs": {"crop": ["409", 0], "face": ["399", 0], "images": ["422", 0], "mask": ["403", 0], "warp": ["414", 2]}}, "411": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.6, "latent_image": ["392", 2], "model": ["412", 0], "negative": ["392", 1], "positive": ["392", 0], "sampler_name": "euler", "scheduler": "kl_optimal", "seed": "${seed}", "steps": 5}}, "412": {"_meta": {"title": "Apply InstantID"}, "class_type": "ApplyInstantID", "inputs": {"control_net": ["391", 0], "end_at": 1, "image": ["446", 0], "image_kps": ["414", 0], "insightface": ["390", 0], "instantid": ["389", 0], "model": ["416", 0], "negative": ["398", 0], "positive": ["397", 0], "start_at": 0, "weight": 1}}, "413": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["411", 0], "vae": ["416", 2]}}, "414": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 1.5, "crop_size": 1024, "faces": ["399", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "416": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"}}, "417": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["269", 0], "target_size": ["420", 0]}}, "418": {"_meta": {"title": "Int"}, "class_type": "easy int", "inputs": {"value": 2048}}, "420": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["421", 0], "b": ["418", 0], "mode": false}}, "421": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["453", 0], "b": ["453", 1], "mode": true}}, "422": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["${isReactorAndInstantId?then('499','269')}", 0], "scale_by": ["417", 0], "upscale_method": "bicubic"}}, "429": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["435", 0], "b": ["435", 1], "mode": true}}, "430": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["434", 0], "b": ["429", 0], "mode": false}}, "431": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["469", 0], "target_size": ["430", 0]}}, "433": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["469", 0], "scale_by": ["431", 0], "upscale_method": "bicubic"}}, "434": {"_meta": {"title": "Int"}, "class_type": "easy int", "inputs": {"value": 2048}}, "435": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["469", 0]}}, "436": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["422", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "446": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["447", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "447": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["433", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "453": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["269", 0]}}, "454": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.3, "latent_image": ["407", 0], "model": ["491", 0], "negative": ["388", 0], "positive": ["405", 0], "sampler_name": "euler", "scheduler": "sgm_uniform", "seed": 17, "steps": 8}}, "462": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}}, "469": {"_meta": {"title": "Batch Images One or More"}, "class_type": "ImageBatchOneOrMore", "inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}}, "474": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["547", 0], "image1": ["475", 0]}}, "475": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "476": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "477": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["548", 0], "image1": ["476", 0]}}, "491": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "${fillModel!'fluxFillFP8_v10.safetensors'}", "weight_dtype": "default"}}, "498": {"_meta": {"title": "Load ConrainReactor Models"}, "class_type": "LoadConrainReactorModels", "inputs": {"face_restore_model": "GFPGANv1.4.pth", "facedetection_model": "retinaface_resnet50", "parse_model": "parsenet", "swap_model": "inswapper_128.onnx"}, "disable": "${isReactorAndInstantId?then('false','true')}"}, "499": {"_meta": {"title": "ConrainReactor Fast Face Swap"}, "class_type": "ConrainReActorFaceSwap", "inputs": {"codeformer_weight": 0.7, "console_log_level": 1, "detect_gender_input": "no", "detect_gender_source": "no", "enabled": true, "face_restore_model": ["498", 2], "face_restore_visibility": 0.7, "facedetection": ["498", 1], "faceparse_model": ["498", 3], "input_faces_index": "0", "input_image": ["269", 0], "keep_largest": "yes", "source_faces_index": "0", "source_image": ["462", 0], "swap_model": ["498", 0]}, "disable": "${isReactorAndInstantId?then('false','true')}"}, "508": {"_meta": {"title": "Apply PuLID Flux"}, "class_type": "ApplyPulidFlux", "inputs": {"end_at": 1, "eva_clip": ["513", 0], "face_analysis": ["515", 0], "image": ["536", 0], "model": ["296", 0], "pulid_flux": ["512", 0], "start_at": 0, "weight": 0.8}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "512": {"_meta": {"title": "Load PuLID Flux Model"}, "class_type": "PulidFluxModelLoader", "inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "513": {"_meta": {"title": "Load <PERSON> (PuLID Flux)"}, "class_type": "PulidFluxEvaClipLoader", "inputs": {}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "515": {"_meta": {"title": "Load InsightFace (PuLID Flux)"}, "class_type": "PulidFluxInsightFaceLoader", "inputs": {"provider": "CUDA"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "525": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["462", 0], "mask_components": "1,3", "method": "selfie_multiclass_256x256"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "536": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["462", 0], "mask": ["525", 1], "padding_bottom": 96, "padding_left": 96, "padding_right": 96, "padding_top": 96}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "547": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"}}, "548": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"}}, "549": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": ": ", "text_a": ["550", 0], "text_b": ["282", 0]}, "disable": "${isPromptCorrect?then('false','true')}"}, "550": {"_meta": {"title": "🔤 CR Text"}, "class_type": "CR Text", "inputs": {"text": "Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"}, "disable": "${isPromptCorrect?then('false','true')}"}, "552": {"_meta": {"title": "llmodel"}, "class_type": "LLModel", "inputs": {"llm_model": "default", "prompt": ["549", 0]}, "disable": "${isPromptCorrect?then('false','true')}"}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [-449.15328894933793, 1635.2220944710177], "scale": 0.7247295000000037}}, "groups": [{"id": 3, "bounding": [5945.7255859375, -1753.57763671875, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 5, "bounding": [641.1348876953125, -1689.5174560546875, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 6, "bounding": [5943.24755859375, -1050.6004638671875, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 7, "bounding": [-1031.672119140625, -1730.5921630859375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "模型加载"}, {"id": 8, "bounding": [3672.7900390625, -967.8936767578125, 1904.325927734375, 793.1136474609375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"id": 9, "bounding": [3659.67236328125, -107.16871643066406, 1918.953857421875, 535.1592407226562], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "精修脸部边缘"}, {"id": 10, "bounding": [3677.690185546875, -1795.876953125, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "人脸原图"}, {"id": 11, "bounding": [3131.479248046875, -1191.9888916015625, 364.15802001953125, 850.4071655273438], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "限制最大尺寸"}], "last_link_id": 990, "last_node_id": 556, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [672, 412, 1, 392, 0, "CONDITIONING"], [673, 412, 2, 392, 1, "CONDITIONING"], [674, 416, 2, 392, 2, "VAE"], [675, 414, 0, 392, 3, "IMAGE"], [676, 402, 0, 392, 4, "MASK"], [684, 436, 0, 399, 0, "FACE"], [685, 404, 0, 400, 0, "MASK"], [686, 401, 0, 400, 1, "MASK"], [687, 402, 0, 401, 0, "MASK"], [688, 414, 1, 402, 0, "MASK"], [689, 414, 1, 403, 0, "MASK"], [690, 402, 0, 404, 0, "MASK"], [693, 413, 0, 406, 0, "IMAGE"], [695, 406, 0, 407, 0, "LATENT"], [696, 400, 0, 407, 1, "MASK"], [697, 454, 0, 408, 0, "LATENT"], [699, 414, 0, 409, 0, "IMAGE"], [700, 408, 0, 409, 1, "IMAGE"], [701, 422, 0, 410, 0, "IMAGE"], [702, 399, 0, 410, 1, "FACE"], [703, 409, 0, 410, 2, "IMAGE"], [704, 403, 0, 410, 3, "MASK"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [707, 392, 0, 411, 1, "CONDITIONING"], [708, 392, 1, 411, 2, "CONDITIONING"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [717, 414, 0, 412, 7, "IMAGE"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [722, 420, 0, 417, 1, "INT"], [724, 421, 0, 420, 0, "*"], [725, 418, 0, 420, 1, "*"], [726, 453, 0, 421, 0, "*"], [727, 453, 1, 421, 1, "*"], [729, 417, 0, 422, 1, "FLOAT"], [739, 435, 0, 429, 0, "*"], [740, 435, 1, 429, 1, "*"], [741, 434, 0, 430, 0, "*"], [742, 429, 0, 430, 1, "*"], [744, 430, 0, 431, 1, "INT"], [748, 431, 0, 433, 1, "FLOAT"], [750, 422, 0, 436, 0, "IMAGE"], [760, 447, 0, 446, 0, "FACE"], [769, 405, 0, 454, 1, "CONDITIONING"], [770, 388, 0, 454, 2, "CONDITIONING"], [771, 407, 0, 454, 3, "LATENT"], [774, 455, 0, 453, 0, "IMAGE"], [776, 455, 0, 417, 0, "IMAGE"], [777, 269, 0, 455, 0, "*"], [781, 410, 0, 456, 0, "*"], [782, 456, 0, 236, 0, "IMAGE"], [783, 456, 0, 365, 0, "IMAGE"], [786, 331, 0, 457, 0, "*"], [787, 457, 0, 405, 1, "STRING"], [788, 457, 0, 397, 1, "STRING"], [803, 462, 0, 469, 0, "IMAGE"], [806, 474, 0, 469, 1, "IMAGE"], [807, 469, 0, 435, 0, "IMAGE"], [808, 469, 0, 431, 0, "IMAGE"], [809, 469, 0, 433, 0, "IMAGE"], [812, 433, 0, 447, 0, "IMAGE"], [813, 475, 0, 474, 0, "IMAGE"], [814, 477, 0, 469, 2, "IMAGE"], [815, 476, 0, 477, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [854, 491, 0, 454, 0, "MODEL"], [856, 355, 0, 216, 0, "IMAGE"], [859, 462, 0, 499, 5, "IMAGE"], [860, 498, 0, 499, 1, "FACE_MODEL"], [861, 498, 1, 499, 2, "FACE_MODEL"], [862, 498, 2, 499, 3, "FACE_MODEL"], [863, 498, 3, 499, 4, "FACE_MODEL"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [951, 269, 0, 499, 0, "IMAGE"], [953, 499, 0, 422, 0, "IMAGE"], [954, 416, 0, 412, 4, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [968, 386, 0, 541, 0, "*"], [969, 541, 0, 405, 0, "CLIP"], [970, 541, 0, 388, 0, "CLIP"], [971, 270, 0, 542, 0, "*"], [972, 542, 0, 406, 1, "VAE"], [973, 542, 0, 408, 1, "VAE"], [978, 547, 0, 474, 2, "INT"], [979, 548, 0, 477, 2, "INT"], [980, 550, 0, 549, 0, "STRING"], [981, 282, 0, 549, 1, "STRING"], [982, 549, 0, 552, 2, "STRING"], [983, 552, 0, 553, 0, "*"], [989, 553, 0, 283, 0, "STRING"], [990, 283, 0, 287, 0, "*"]], "nodes": [{"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [392, 394], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [386, 539], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [6533.8974609375, -787.8736572265625], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 74, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7660.8974609375, -641.8736572265625], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6160.8974609375, -574.8736572265625], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6138.8974609375, -768.8736572265625], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 216, "flags": {}, "inputs": [{"label": "images", "link": 856, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 392, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 386, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 117, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [8225.5751953125, -914.8041381835938], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 34, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7015.8974609375, -820.8736572265625], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6526.8974609375, -916.8736572265625], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 114, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [6518.8974609375, -1330.87353515625], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 782, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 112, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [6062.8974609375, -1399.87353515625], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [], "pos": [6948.8974609375, -1650.87353515625], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {"collapsed": false}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 118, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6973.8974609375, -1469.87353515625], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 116, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6959.8974609375, -1252.87353515625], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 119, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [6524.8974609375, -1556.87353515625], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 120, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8283.8857421875, -1574.87353515625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}, {"link": 615, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": "${isPureBg?then(0,4)}", "order": 121, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [7969.8974609375, -1462.87353515625], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 268, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 513, "name": "clip", "type": "CLIP"}, {"link": 453, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 72, "outputs": [{"links": [464], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1393.572998046875, -1348.140869140625], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 54], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 85, "outputs": [{"links": [611, 777, 951], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2622.702880859375, -1422.1380615234375], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"links": [455, 520, 971], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [2478.3408203125, -1170.58984375], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"links": [622, 664, 664], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-961.8040771484375, -912.8571166992188], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [621, 647, 647, 944], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-949.96533203125, -1104.5126953125], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "${baseModelDType!'default'}"]}, {"id": 273, "flags": {}, "inputs": [{"link": 456, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 84, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [2432.************, -1611.155517578125], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [292.29705810546875, 127.71533966064453], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [2010.49560546875, -1139.160888671875], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["${samplerName!'euler'}"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 81, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [2031.17724609375, -971.8941040039062], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["${scheduleName!'beta'}", "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}", 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 82, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [1994.201171875, -1380.5081787109375], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 277, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [{"links": [456], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [2059.2626953125, -1607.************], "properties": {"Node name for S&R": "RandomNoise"}, "size": [317.5343933105469, 84.33126831054688], "type": "RandomNoise", "widgets_values": ["${seed}", "fixed"]}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 464, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 76, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [1756.1866455078125, -1359.78466796875], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 65, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [1718.************, -1133.7596435546875], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 651, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 79, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [1701.7969970703125, -884.855712890625], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [{"links": [981, 473], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [789.1341552734375, -968.331787109375], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [400, 200], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "fixed"]}, {"id": 283, "flags": {}, "inputs": [{"link": 989, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 67, "outputs": [{"links": [453, 990], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [1400.793701171875, -1160.6016845703125], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 226], "type": "ShowText|pysssss", "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [587, 589], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1345.7989501953125, -503.8546447753906], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [590, 591], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1331.7989501953125, -689.8560791015625], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [798.8578491210938, -683.801025390625], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [411.6590881347656, 124], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 990, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 70, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2535.691650390625, -838.6723022460938], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 40, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2500.691650390625, -707.6724243164062], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 68, "outputs": [{"links": [514, 654, 947], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [513, 510, 513, 630, 631], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [740.1643676757812, -1196.1575927734375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [499.25970458984375, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", "${loraStrength}", 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 946, "name": "model", "type": "MODEL"}, {"link": 665, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 59, "outputs": [{"links": [627], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [628, 625], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [726.767822265625, -1590.7061767578125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [477.3377990722656, 128.31455993652344], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 627, "name": "model", "type": "MODEL"}, {"link": 628, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 64, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [724.9411010742188, -1389.706298828125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [491.7470703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 122, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7391.8974609375, -1376.87353515625], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 78, "outputs": [], "pos": [8231.892578125, -569.8736572265625], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [575, 786, 575], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2460.299072265625, -1021.8446655273438], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [319.1407165527344, 134.37188720703125], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1335, "fixed"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 39, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1679.1009521484375, -613.5169067382812], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 38, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1702.960693359375, -512.515625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 52, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1911.972900390625, -635.5166625976562], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 61, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2161.5029296875, -639.5166625976562], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 51, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1982.985107421875, -505.5156555175781], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 60, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2215.380859375, -484.5157165527344], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 123, "outputs": [{"links": [856], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7702.8974609375, -944.8736572265625], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 365, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 783, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 113, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [613], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [614], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [7407.1708984375, -1615.321533203125], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 367, "flags": {}, "inputs": [{"link": 613, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 614, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 115, "outputs": [{"links": [615], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [7701.1708984375, -1540.321533203125], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 373, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [644, 644], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-205.92677307128906, -1002.7108764648438], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "推理加速开关", "type": "JWStringToInteger", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 375, "flags": {}, "inputs": [{"link": 647, "name": "model", "type": "MODEL"}], "mode": 0, "order": 37, "outputs": [{"links": [642], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-483.5779724121094, -705.3702392578125], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "flags": {"collapsed": false}, "inputs": [{"link": 642, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 944, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 644, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 50, "outputs": [{"links": [946], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [118.73208618164062, -698.2780151367188], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [257.191650390625, 78.78076171875], "type": "CR Model Input Switch", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 378, "flags": {"collapsed": true}, "inputs": [{"link": 948, "name": "model1", "type": "MODEL"}, {"link": 663, "name": "model2", "type": "MODEL"}], "mode": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(0,4)}", "order": 75, "outputs": [{"links": [651], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [209.58755493164062, -1070.8203125], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isPWModel)?then(0,4)}", "order": 14, "outputs": [{"links": [662, 662], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-951.9925537109375, -1307.653076171875], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "title": "PW模型", "type": "UNETLoader", "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"]}, {"id": 385, "flags": {}, "inputs": [{"link": 662, "name": "", "type": "*"}], "mode": 0, "order": 42, "outputs": [{"links": [663], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-374.7147216796875, -1127.6103515625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 386, "flags": {}, "inputs": [{"link": 664, "name": "", "type": "*"}], "mode": 0, "order": 36, "outputs": [{"links": [665, 968], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [415.603759765625, -950.938232421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 388, "flags": {"collapsed": true}, "inputs": [{"link": 970, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 58, "outputs": [{"links": [770], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [4144.9189453125, 189.60214233398438], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [400, 200], "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 389, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"label": "INSTANTID", "links": [710], "name": "INSTANTID", "shape": 3, "slot_index": 0, "type": "INSTANTID"}], "pos": [4438.3916015625, -877.1947631835938], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDModelLoader", "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 16, "outputs": [{"label": "FACEANALYSIS", "links": [711], "name": "FACEANALYSIS", "shape": 3, "slot_index": 0, "type": "FACEANALYSIS"}], "pos": [4439.4443359375, -806.9871826171875], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDFaceAnalysis", "widgets_values": ["CUDA"]}, {"id": 391, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"label": "CONTROL_NET", "links": [712], "name": "CONTROL_NET", "shape": 3, "slot_index": 0, "type": "CONTROL_NET"}], "pos": [4440.44482421875, -736.9521484375], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [378.708740234375, 58], "type": "ControlNetLoader", "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 392, "flags": {}, "inputs": [{"link": 672, "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"link": 673, "name": "negative", "type": "CONDITIONING"}, {"link": 674, "name": "vae", "type": "VAE"}, {"link": 675, "name": "pixels", "type": "IMAGE"}, {"link": 676, "name": "mask", "type": "MASK"}], "mode": 0, "order": 99, "outputs": [{"links": [707], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"links": [708], "name": "negative", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"links": [709], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [4969.63232421875, -848.127685546875], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 397, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 955, "name": "clip", "type": "CLIP"}, {"link": 788, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 53, "outputs": [{"links": [715], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [4107.203125, -887.3280639648438], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 96], "type": "CLIPTextEncode", "widgets_values": ["a 5 year girl"]}, {"id": 398, "bgcolor": "#353", "color": "#232", "flags": {"collapsed": true}, "inputs": [{"link": 956, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 43, "outputs": [{"links": [716], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [4115.85693359375, -718.8209228515625], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [239.4051971435547, 91.89370727539062], "type": "CLIPTextEncode", "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"]}, {"id": 399, "flags": {}, "inputs": [{"link": 684, "name": "faces", "type": "FACE"}], "mode": 0, "order": 94, "outputs": [{"links": [702, 720], "name": "filtered", "slot_index": 0, "type": "FACE"}, {"name": "rest", "type": "FACE"}], "pos": [3702.7900390625, -367.8946228027344], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "size": [227.9144744873047, 169.93338012695312], "type": "OrderedFaceFilter", "widgets_values": ["area", "descending", 0, 1]}, {"id": 400, "flags": {"collapsed": false}, "inputs": [{"link": 685, "name": "destination", "type": "MASK"}, {"link": 686, "name": "source", "type": "MASK"}], "mode": 0, "order": 103, "outputs": [{"links": [696], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [5103.9833984375, -364.9097900390625], "properties": {"Node name for S&R": "MaskComposite"}, "size": [210, 126], "type": "MaskComposite", "widgets_values": [0, 0, "subtract"]}, {"id": 401, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 687, "name": "mask", "type": "MASK"}], "mode": 0, "order": 100, "outputs": [{"links": [686], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4809.3330078125, -373.89111328125], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, -24, 0, 0, 1, true]}, {"id": 402, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 688, "name": "mask", "type": "MASK"}], "mode": 0, "order": 97, "outputs": [{"links": [676, 687, 690], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4282.978515625, -370.7340087890625], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 32, 4, 0, 1, true]}, {"id": 403, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 689, "name": "mask", "type": "MASK"}], "mode": 0, "order": 98, "outputs": [{"links": [704], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4285.263671875, -604.95166015625], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 72, 40, 0, 1, true]}, {"id": 404, "bgcolor": "#653", "color": "#432", "flags": {"collapsed": false}, "inputs": [{"link": 690, "name": "mask", "type": "MASK"}], "mode": 0, "order": 101, "outputs": [{"links": [685], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4543.36572265625, -369.82025146484375], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 24, 0, 0, 1, true]}, {"id": 405, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 969, "name": "clip", "type": "CLIP"}, {"link": 787, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 57, "outputs": [{"links": [769], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [4151.38916015625, 26.29731559753418], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [221.6905059814453, 76], "type": "CLIPTextEncode", "widgets_values": ["woman"]}, {"id": 406, "flags": {}, "inputs": [{"link": 693, "name": "pixels", "type": "IMAGE"}, {"link": 972, "name": "vae", "type": "VAE"}], "mode": 0, "order": 105, "outputs": [{"links": [695], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [4149.90771484375, 297.87554931640625], "properties": {"Node name for S&R": "VAEEncode"}, "size": [140, 46], "type": "VAEEncode", "widgets_values": []}, {"id": 407, "flags": {}, "inputs": [{"link": 695, "name": "samples", "type": "LATENT"}, {"link": 696, "name": "mask", "type": "MASK"}], "mode": 0, "order": 106, "outputs": [{"links": [771], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [4388.75, 306.1544494628906], "properties": {"Node name for S&R": "SetLatentNoiseMask"}, "size": [176.39999389648438, 46], "type": "SetLatentNoiseMask", "widgets_values": []}, {"id": 408, "flags": {}, "inputs": [{"link": 697, "name": "samples", "type": "LATENT"}, {"link": 973, "name": "vae", "type": "VAE"}], "mode": 0, "order": 108, "outputs": [{"links": [700], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5099.669921875, 2.831263303756714], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 409, "flags": {"collapsed": false}, "inputs": [{"link": 699, "name": "image_ref", "type": "IMAGE"}, {"link": 700, "name": "image_target", "type": "IMAGE"}], "mode": 0, "order": 109, "outputs": [{"links": [703], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [5351.24267578125, -17.744340896606445], "properties": {"Node name for S&R": "easy imageColorMatch"}, "size": [210, 102], "type": "easy imageColorMatch", "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 410, "flags": {}, "inputs": [{"link": 701, "name": "images", "type": "IMAGE"}, {"link": 702, "name": "face", "type": "FACE"}, {"link": 703, "name": "crop", "type": "IMAGE"}, {"link": 704, "name": "mask", "type": "MASK"}, {"link": 705, "name": "warp", "type": "WARP"}], "mode": 0, "order": 110, "outputs": [{"links": [781], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [5105.2197265625, 177.44003295898438], "properties": {"Node name for S&R": "WarpFacesBack"}, "size": [182.46627807617188, 157.38844299316406], "type": "WarpFacesBack", "widgets_values": []}, {"id": 411, "flags": {}, "inputs": [{"link": 706, "name": "model", "type": "MODEL"}, {"link": 707, "name": "positive", "type": "CONDITIONING"}, {"link": 708, "name": "negative", "type": "CONDITIONING"}, {"link": 709, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 102, "outputs": [{"links": [718], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [5263.43359375, -803.8001708984375], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [261.8017578125, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": ["${seed}", "fixed", 5, 1, "euler", "kl_optimal", 0.6]}, {"id": 412, "flags": {}, "inputs": [{"label": "instantid", "link": 710, "name": "instantid", "slot_index": 0, "type": "INSTANTID"}, {"label": "insightface", "link": 711, "name": "insightface", "slot_index": 1, "type": "FACEANALYSIS"}, {"label": "control_net", "link": 712, "name": "control_net", "slot_index": 2, "type": "CONTROL_NET"}, {"label": "image", "link": 713, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 954, "name": "model", "slot_index": 4, "type": "MODEL"}, {"label": "positive", "link": 715, "name": "positive", "slot_index": 5, "type": "CONDITIONING"}, {"label": "negative", "link": 716, "name": "negative", "slot_index": 6, "type": "CONDITIONING"}, {"label": "image_kps", "link": 717, "name": "image_kps", "shape": 7, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 96, "outputs": [{"label": "MODEL", "links": [706], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "positive", "links": [672], "name": "positive", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"label": "negative", "links": [673], "name": "negative", "shape": 3, "slot_index": 2, "type": "CONDITIONING"}], "pos": [4692.7900390625, -867.8936767578125], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [210, 266], "type": "ApplyInstantID", "widgets_values": [1, 0, 1]}, {"id": 413, "flags": {"collapsed": false}, "inputs": [{"link": 718, "name": "samples", "type": "LATENT"}, {"link": 719, "name": "vae", "type": "VAE"}], "mode": 0, "order": 104, "outputs": [{"links": [693], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5388.3720703125, -468.2469482421875], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 414, "flags": {}, "inputs": [{"link": 720, "name": "faces", "type": "FACE"}], "mode": 0, "order": 95, "outputs": [{"links": [675, 699, 717], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [688, 689], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [705], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [3977.688720703125, -353.6644287109375], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 1.5, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 416, "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [{"links": [954], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [955, 956], "name": "CLIP", "slot_index": 1, "type": "CLIP"}, {"links": [674, 719], "name": "VAE", "slot_index": 2, "type": "VAE"}], "pos": [3710.78271484375, -853.4059448242188], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [254.65628051757812, 146.35491943359375], "type": "CheckpointLoaderSimple", "widgets_values": ["${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"]}, {"id": 417, "flags": {}, "inputs": [{"link": 776, "name": "image", "type": "IMAGE"}, {"link": 722, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 91, "outputs": [{"links": [729], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [3201.89208984375, -509.83837890625], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 113.94757080078125], "type": "UpscaleSizeCalculator", "widgets_values": [2048]}, {"id": 418, "flags": {}, "inputs": [], "mode": 0, "order": 19, "outputs": [{"links": [725], "name": "int", "slot_index": 0, "type": "INT"}], "pos": [3206.6103515625, -773.5169067382812], "properties": {"Node name for S&R": "easy int"}, "size": [210, 63.99684524536133], "type": "easy int", "widgets_values": [2048]}, {"id": 420, "flags": {}, "inputs": [{"link": 724, "name": "a", "type": "*"}, {"link": 725, "name": "b", "type": "*"}], "mode": 0, "order": 90, "outputs": [{"links": [722], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3206.6103515625, -653.5173950195312], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 85.42294311523438], "type": "ImpactMinMax", "widgets_values": [false]}, {"id": 421, "flags": {}, "inputs": [{"link": 726, "name": "a", "type": "*"}, {"link": 727, "name": "b", "type": "*"}], "mode": 0, "order": 89, "outputs": [{"links": [724], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3206.6103515625, -923.5164794921875], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 78], "type": "ImpactMinMax", "widgets_values": [true]}, {"id": 422, "flags": {"collapsed": false}, "inputs": [{"link": 953, "name": "image", "type": "IMAGE"}, {"link": 729, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 92, "outputs": [{"links": [701, 750], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [3702.7900390625, -517.89453125], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [210, 82], "type": "ImageScaleBy", "widgets_values": ["bicubic", 0.5]}, {"id": 429, "flags": {"collapsed": true}, "inputs": [{"link": 739, "name": "a", "type": "*"}, {"link": 740, "name": "b", "type": "*"}], "mode": 0, "order": 66, "outputs": [{"links": [742], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [4968.1240234375, -1306.3111572265625], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 82.59356689453125], "type": "ImpactMinMax", "widgets_values": [true]}, {"id": 430, "flags": {"collapsed": true}, "inputs": [{"link": 741, "name": "a", "type": "*"}, {"link": 742, "name": "b", "type": "*"}], "mode": 0, "order": 69, "outputs": [{"links": [744], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [4968.1240234375, -1246.3109130859375], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 85.89134216308594], "type": "ImpactMinMax", "widgets_values": [false]}, {"id": 431, "flags": {"collapsed": true}, "inputs": [{"link": 808, "name": "image", "type": "IMAGE"}, {"link": 744, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 73, "outputs": [{"links": [748], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [4838.1240234375, -1166.310302734375], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 94], "type": "UpscaleSizeCalculator", "widgets_values": [2048]}, {"id": 433, "flags": {"collapsed": true}, "inputs": [{"link": 809, "name": "image", "type": "IMAGE"}, {"link": 748, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 77, "outputs": [{"links": [812], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4838.1240234375, -1096.3096923828125], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [210, 82], "type": "ImageScaleBy", "widgets_values": ["bicubic", 0.5]}, {"id": 434, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 20, "outputs": [{"links": [741], "name": "int", "slot_index": 0, "type": "INT"}], "pos": [4828.1240234375, -1236.3109130859375], "properties": {"Node name for S&R": "easy int"}, "size": [210, 63.99684524536133], "type": "easy int", "widgets_values": [2048]}, {"id": 435, "flags": {"collapsed": true}, "inputs": [{"link": 807, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 62, "outputs": [{"links": [739], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [740], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "slot_index": 2, "type": "INT"}], "pos": [4788.1240234375, -1296.3111572265625], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 436, "flags": {}, "inputs": [{"link": 750, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 93, "outputs": [{"links": [684], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [3979.617431640625, -585.685791015625], "properties": {"Node name for S&R": "DetectFaces"}, "size": [210, 126], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 446, "flags": {}, "inputs": [{"link": 760, "name": "faces", "type": "FACE"}], "mode": 0, "order": 83, "outputs": [{"links": [713, 762], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [5336.8212890625, -1654.697998046875], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "flags": {}, "inputs": [{"link": 812, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 80, "outputs": [{"links": [760], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [5061.73974609375, -1651.956298828125], "properties": {"Node name for S&R": "DetectFaces"}, "size": [216.65777587890625, 143.53131103515625], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 453, "flags": {}, "inputs": [{"link": 774, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 88, "outputs": [{"links": [726], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [727], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [3206.6103515625, -1043.5166015625], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 454, "flags": {}, "inputs": [{"link": 854, "name": "model", "type": "MODEL"}, {"link": 769, "name": "positive", "type": "CONDITIONING"}, {"link": 770, "name": "negative", "type": "CONDITIONING"}, {"link": 771, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 107, "outputs": [{"links": [697], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [4614.1865234375, 3.83551025390625], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [246.57957458496094, 275.6719970703125], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": [17, "fixed", 8, 1, "euler", "sgm_uniform", 0.3]}, {"id": 455, "flags": {}, "inputs": [{"link": 777, "name": "", "type": "*"}], "mode": 0, "order": 86, "outputs": [{"links": [774, 776, 789], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [2931.47607421875, -1439.1119384765625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 456, "flags": {}, "inputs": [{"link": 781, "name": "", "type": "*"}], "mode": 0, "order": 111, "outputs": [{"links": [782, 783], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [5453.69775390625, 132.83119201660156], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 457, "flags": {}, "inputs": [{"link": 786, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 41, "outputs": [{"links": [787, 788], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [3763.99609375, -649.8289184570312], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 462, "flags": {}, "inputs": [], "mode": 0, "order": 21, "outputs": [{"links": [803, 859, 899, 957], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [3781.813232421875, -1685.504150390625], "properties": {"Node name for S&R": "LoadImage"}, "size": [235.8109893798828, 314], "type": "LoadImage", "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 469, "flags": {}, "inputs": [{"link": 803, "name": "image1", "type": "IMAGE"}, {"link": 806, "name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 814, "name": "image3", "shape": 7, "type": "IMAGE"}, {"name": "image4", "shape": 7, "type": "IMAGE"}, {"name": "image5", "shape": 7, "type": "IMAGE"}, {"name": "image6", "shape": 7, "type": "IMAGE"}], "mode": 0, "order": 55, "outputs": [{"links": [807, 808, 809], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4541.1953125, -1226.8348388671875], "properties": {"Node name for S&R": "ImageBatchOneOrMore"}, "size": [201.60000610351562, 126], "type": "ImageBatchOneOrMore", "widgets_values": []}, {"id": 474, "flags": {}, "inputs": [{"link": 813, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 978, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 46, "outputs": [{"links": [806], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [4142.31494140625, -1245.5076904296875], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 475, "flags": {}, "inputs": [], "mode": 0, "order": 22, "outputs": [{"links": [813], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4156.66015625, -1686.3446044921875], "properties": {"Node name for S&R": "LoadImage"}, "size": [249.60922241210938, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 476, "flags": {}, "inputs": [], "mode": 0, "order": 23, "outputs": [{"links": [815], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4496.2197265625, -1667.8482666015625], "properties": {"Node name for S&R": "LoadImage"}, "size": [234.48504638671875, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 477, "flags": {}, "inputs": [{"link": 815, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 979, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 47, "outputs": [{"links": [814], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [4132.31494140625, -1105.5076904296875], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 491, "flags": {}, "inputs": [], "mode": 0, "order": 24, "outputs": [{"links": [854], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [3706.533447265625, -4.770148277282715], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["${fillModel!'fluxFillFP8_v10.safetensors'}", "default"]}, {"id": 498, "flags": {}, "inputs": [], "mode": "${isReactorAndInstantId?then('0','4')}", "order": 25, "outputs": [{"links": [860], "name": "faceswapper_model", "slot_index": 0, "type": "FACE_MODEL"}, {"links": [861], "name": "facedetection_model", "slot_index": 1, "type": "FACE_MODEL"}, {"links": [862], "name": "facerestore_model", "slot_index": 2, "type": "FACE_MODEL"}, {"links": [863], "name": "faceparse_model", "slot_index": 3, "type": "FACE_MODEL"}], "pos": [2758.498046875, -2102.38818359375], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "size": [327.5999755859375, 190], "type": "LoadConrainReactorModels", "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 499, "flags": {}, "inputs": [{"link": 951, "name": "input_image", "type": "IMAGE"}, {"link": 860, "name": "swap_model", "type": "FACE_MODEL"}, {"link": 861, "name": "facedetection", "type": "FACE_MODEL"}, {"link": 862, "name": "face_restore_model", "type": "FACE_MODEL"}, {"link": 863, "name": "faceparse_model", "type": "FACE_MODEL"}, {"link": 859, "name": "source_image", "shape": 7, "type": "IMAGE"}, {"name": "face_model", "shape": 7, "type": "FACE_MODEL"}], "mode": "${isReactorAndInstantId?then('0','4')}", "order": 87, "outputs": [{"links": [953], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "FACE_MODEL", "type": "FACE_MODEL"}], "pos": [3216.62744140625, -2119.385498046875], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "size": [367.79998779296875, 370], "type": "ConrainReActorFaceSwap", "widgets_values": [true, 0.7, 0.7, "no", "no", "0", "0", 1, "yes"]}, {"id": 508, "flags": {}, "inputs": [{"link": 947, "name": "model", "type": "MODEL"}, {"link": 879, "name": "pulid_flux", "type": "PULIDFLUX"}, {"link": 882, "name": "eva_clip", "type": "EVA_CLIP"}, {"link": 883, "name": "face_analysis", "type": "FACEANALYSIS"}, {"link": 966, "name": "image", "type": "IMAGE"}, {"name": "attn_mask", "shape": 7, "type": "MASK"}, {"name": "options", "shape": 7, "type": "OPTIONS"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 71, "outputs": [{"links": [948], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [183.51483154296875, -1463.5994873046875], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "size": [315, 226], "type": "ApplyPulidFlux", "widgets_values": [0.8, 0, 1]}, {"id": 512, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 31, "outputs": [{"links": [879], "name": "PULIDFLUX", "type": "PULIDFLUX"}], "pos": [-466.6873779296875, -1493.7525634765625], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "size": [315, 58], "type": "PulidFluxModelLoader", "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "flags": {"collapsed": false}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 32, "outputs": [{"links": [882], "name": "EVA_CLIP", "type": "EVA_CLIP"}], "pos": [-468.8070068359375, -1372.1922607421875], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "size": [327.5999755859375, 26], "type": "PulidFluxEvaClipLoader", "widgets_values": []}, {"id": 515, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 33, "outputs": [{"links": [883], "name": "FACEANALYSIS", "type": "FACEANALYSIS"}], "pos": [-479.9522705078125, -1278.173095703125], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "size": [365.4000244140625, 58], "type": "PulidFluxInsightFaceLoader", "widgets_values": ["CUDA"]}, {"id": 525, "flags": {}, "inputs": [{"link": 899, "name": "image", "type": "IMAGE"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 44, "outputs": [{"name": "image", "type": "IMAGE"}, {"links": [960], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "type": "BBOX"}], "pos": [-841.5623779296875, -1652.26416015625], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "size": [300, 260], "type": "easy humanSegmentation", "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 536, "flags": {}, "inputs": [{"link": 959, "name": "image", "type": "IMAGE"}, {"link": 960, "name": "mask", "type": "MASK"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 54, "outputs": [{"links": [966], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [-54.86212158203125, -1627.6336669921875], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [236.27499389648438, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [96, 96, 96, 96]}, {"id": 537, "flags": {}, "inputs": [{"link": 957, "name": "", "type": "*"}], "mode": 0, "order": 45, "outputs": [{"links": [959], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-288.80596923828125, -1664.228271484375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 541, "flags": {}, "inputs": [{"link": 968, "name": "", "type": "*"}], "mode": 0, "order": 49, "outputs": [{"links": [969, 970], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [3816.75244140625, 151.26307678222656], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 542, "flags": {}, "inputs": [{"link": 971, "name": "", "type": "*"}], "mode": 0, "order": 35, "outputs": [{"links": [972, 973], "name": "", "slot_index": 0, "type": "VAE"}], "pos": [3828.2685546875, 279.6264953613281], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 547, "flags": {}, "inputs": [], "mode": 0, "order": 26, "outputs": [{"links": [978], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3736.19287109375, -1245.133544921875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 548, "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [979], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3715.51171875, -1090.682373046875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 549, "flags": {}, "inputs": [{"link": 980, "name": "text_a", "shape": 7, "type": "STRING", "widget": {"name": "text_a"}}, {"link": 981, "name": "text_b", "shape": 7, "type": "STRING", "widget": {"name": "text_b"}}, {"name": "text_c", "shape": 7, "type": "STRING", "widget": {"name": "text_c"}}, {"name": "text_d", "shape": 7, "type": "STRING", "widget": {"name": "text_d"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 48, "outputs": [{"links": [982], "name": "STRING", "slot_index": 0, "type": "STRING"}], "pos": [1418.9630126953125, -1600.443359375], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [213.69569396972656, 179.2432861328125], "type": "Text Concatenate", "widgets_values": [": ", "true", "", "", "", ""]}, {"id": 550, "flags": {}, "inputs": [], "mode": "${isPromptCorrect?then('0','4')}", "order": 30, "outputs": [{"links": [980], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [777.7445678710938, -1800.5491943359375], "properties": {"Node name for S&R": "CR Text"}, "size": [415.5107116699219, 126.7047348022461], "type": "CR Text", "widgets_values": ["Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"]}, {"id": 552, "flags": {}, "inputs": [{"name": "image_list", "shape": 7, "type": "IMAGE"}, {"name": "ref_image", "shape": 7, "type": "IMAGE"}, {"link": 982, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 56, "outputs": [{"links": [983], "name": "result_text", "slot_index": 0, "type": "STRING"}, {"name": "result_detail", "type": "STRING"}], "pos": [1760.8935546875, -1600.6900634765625], "properties": {"Node name for S&R": "LLModel"}, "size": [254.6305389404297, 154.52664184570312], "type": "LLModel", "widgets_values": ["你能干嘛", "default"]}, {"id": 553, "flags": {}, "inputs": [{"link": 983, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 63, "outputs": [{"links": [989], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [1281.9537353515625, -1206.7755126953125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}], "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "454": 0}, "version": 0.4}}}}