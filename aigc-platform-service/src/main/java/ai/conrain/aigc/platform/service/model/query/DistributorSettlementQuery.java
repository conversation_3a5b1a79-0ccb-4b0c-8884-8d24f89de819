package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;

/**
 * DistributorSettlementQuery
 *
 * @version DistributorSettlementService.java v 0.1 2025-05-22 05:39:41
 */
@Data
public class DistributorSettlementQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 结算主体类型 */
    @ApiModelProperty(name = "principalType", value = "结算主体类型")
    private String principalType;

    /** 结算主体 id */
    @ApiModelProperty(name = "principalId", value = "结算主体 id")
    private Integer principalId;

    /** 结算id，前8位是YYYYMMDD */
    @ApiModelProperty(name = "settleId", value = "结算id，前8位是YYYYMMDD")
    private String settleId;

    /** 状态，0初始化、1交易关闭、2待结算、3结算中、4结算成功、5结算失败 */
    @ApiModelProperty(name = "status", value = "状态，0初始化、1交易关闭、2待结算、3结算中、4结算成功、5结算失败")
    private Integer status;

    /** 结算类型，1系统结算，1手工结算 */
    @ApiModelProperty(name = "settleType", value = "结算类型，1系统结算，1手工结算")
    private Integer settleType;

    /** 总金额 */
    @ApiModelProperty(name = "totalAmount", value = "总金额")
    private BigDecimal totalAmount;

    /** 结算金额，结算给渠道商的总金额 */
    @ApiModelProperty(name = "settleAmount", value = "结算金额，结算给渠道商的总金额")
    private BigDecimal settleAmount;

    /** 结算订单笔数 */
    @ApiModelProperty(name = "orderNum", value = "结算订单笔数")
    private Integer orderNum;

    /** 外部业务单号，如银行流水号 */
    @ApiModelProperty(name = "outBizNo", value = "外部业务单号，如银行流水号")
    private String outBizNo;

    /** 渠道商实体id */
    @ApiModelProperty(name = "distributorCorpId", value = "渠道商实体id")
    private Integer distributorCorpId;

    /** 渠道商实体名称 */
    @ApiModelProperty(name = "distributorCorpName", value = "渠道商实体名称")
    private String distributorCorpName;

    /** 结算日期 */
    @ApiModelProperty(name = "settleTime", value = "结算日期")
    private Date settleTime;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private String extInfo;


    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

    /** 结算完成时间开始，yyyyMMdd格式 */
    @ApiModelProperty(name = "settleTimeStart", value = "结算完成日期开始")
    private String settleTimeStart;

    /** 结算完成时间结束（包含），yyyyMMdd格式 */
    @ApiModelProperty(name = "settleTimeEnd", value = "结算完成日期结束")
    private String settleTimeEnd;

}