package ai.conrain.aigc.platform.service.util.flow;

import com.alibaba.fastjson.JSONObject;

/**
 * JSON处理器接口
 * 定义了JSON处理的标准接口，所有具体的JSON处理器都需要实现这个接口
 * 采用策略模式，使得不同的处理逻辑可以互相替换
 */
public interface JsonHandler {
    /**
     * 处理JSON对象
     * @param jsonObject 需要处理的JSON对象
     */
    void handle(JSONObject jsonObject);

    static JSONObject getNodeById(JSONObject jsonObject, Integer nodeId) {
        return jsonObject.getJSONObject("extra_data")
                .getJSONObject("extra_pnginfo")
                .getJSONObject("workflow")
                .getJSONArray("nodes")
                .getJSONObject(nodeId);
    }
} 