/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 模型版本枚举
 * v_1、v_2等用于前台脱敏
 *
 * <AUTHOR>
 * @version : ModelVersionEnum.java, v 0.1 2024/9/12 14:59 renxiao.wu Exp $
 */
@Getter
public enum ModelVersionEnum {
    SDXL("v_1", "sdxl版本", "sdxl"),

    FLUX("v_2", "flux版本", "flux"),

    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /** lora类型 */
    private final String loraType;

    private ModelVersionEnum(String code, String desc, String loraType) {
        this.code = code;
        this.desc = desc;
        this.loraType = loraType;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static ModelVersionEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (ModelVersionEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param loraType lora类型
     * @return 对应枚举
     */
    public static ModelVersionEnum getByLoraType(String loraType) {
        if (StringUtils.isBlank(loraType)) {
            return ModelVersionEnum.SDXL;
        }

        for (ModelVersionEnum item : values()) {
            if (StringUtils.equals(item.getLoraType(), loraType)) {
                return item;
            }
        }

        return null;
    }
}
