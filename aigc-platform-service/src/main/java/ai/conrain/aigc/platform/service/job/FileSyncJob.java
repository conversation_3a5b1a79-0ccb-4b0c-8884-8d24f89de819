package ai.conrain.aigc.platform.service.job;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.component.ImageCaseSyncRecordService;
import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.ImageCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseSyncRecordVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FileSyncJob extends JavaProcessor {

    @Value("${comfyui.output.path}")
    private String comfyuiOutputPath;

    @Autowired
    private ImageCaseService imageCaseService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ImageCaseSyncRecordService imageCaseSyncRecordService;

    @Autowired
    private CreativeBatchService creativeBatchService;

    @Autowired
    private PromptDictService promptDictService;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private ServerHelper serverHelper;

    // 是否同步到 imageCase
    private static final String IS_SYNC_TO_IMAGE_CASE = "isSyncToImageCase";
    // 服务器IP信息
    private static final String TARGET_SERVER = "targetServer";
    // 服务器路径
    private static final String TARGET_PATH = "targetPath";
    // 最大上传次数
    private static final Integer MAX_SYNC_COUNT = 10;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));

        // 远程同步 BadCase 图片
        remoteSyncBadCase();

        // 远程同步 GoodCase 图片
        remoteSyncGoodCase();

        // 远程同步 manualReplacement 图片
        remoteSyncManualReplacementCase();

        // 远程同步 lowQualityImage 图片
        remoteLowQualityImageCase();

        // 释放内存
        return new ProcessResult(true);
    }

    /**
     * 远程同步 BadCase 图片
     */
    private void remoteSyncBadCase() {
        remoteSync(ImageCaseTypeEnum.BAD_CASE, Boolean.FALSE);
    }

    /**
     * 远程同步 GoodCase 图片
     */
    private void remoteSyncGoodCase() {
        // 进行同步操作之前先去将精选图拉取至 imageCase 中
        syncGoodCaseToImageCase();
        remoteSync(ImageCaseTypeEnum.GOOD_CASE, Boolean.FALSE);
    }

    /**
     * 远程同步 manualReplacement 图片
     */
    private void remoteSyncManualReplacementCase() {
        remoteSync(ImageCaseTypeEnum.MANUAL_REPLACEMENT, Boolean.TRUE);
    }

    /**
     * 远程同步 lowQualityImage 图片
     */
    private void remoteLowQualityImageCase() {
        remoteSync(ImageCaseTypeEnum.LOW_QUALITY, Boolean.FALSE);
    }

    /**
     * 通用远程同步方法
     *
     * @param imageCaseTypeEnum 图片案例类型
     * @param isPartUpload      是否部分上传
     */
    private void remoteSync(ImageCaseTypeEnum imageCaseTypeEnum, Boolean isPartUpload) {
        // 获取目标服务器信息
        Map<String, String> targetServerInfo = getTargetServerInfoByType(imageCaseTypeEnum.getCode());
        // 目标服务器配置信息为空则直接返回
        if (targetServerInfo == null ||
            targetServerInfo.get(TARGET_SERVER) == null ||
            targetServerInfo.get(TARGET_PATH) == null) {
            log.error("[文件同步][{}]FileSyncJob::remoteSync::目标服务器配置信息为空，同步操作终止...",
                imageCaseTypeEnum.getCode());
            return;
        }

        // 目标服务器参数提取
        String targetServer = serverHelper.getFileServerUrl(targetServerInfo.get(TARGET_SERVER));
        String targetPath = concatenatePaths(comfyuiOutputPath, targetServerInfo.get(TARGET_PATH));

        // 待同步的图片案例列表
        List<ImageCaseVO> imageCaseVOList = selectImageCase(imageCaseTypeEnum, isPartUpload);

        log.info("[文件同步][{}]FileSyncJob::remoteSync::开始同步，共有{}条数据需要进行同步...",
            imageCaseTypeEnum.getCode(), imageCaseVOList.size());

        // 获取当前日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // 拼装时间目录（eg:20250307）
        String format = sdf.format(new Date());

        // 同步成功数量
        AtomicInteger successCount = new AtomicInteger(0);

        // 遍历图片案例列表，同步图片
        imageCaseVOList.forEach(imageCaseVO -> {
            // 遍历标签详情进行同步
            imageCaseVO.getTagDetails().forEach(tag -> {
                String prompt = replaceSpacesAndLowerCase(tag.getPrompt());
                if (prompt != null) {
                    // 根据图片类型选择目标路径 （BadCase 需要拼接标签）
                    String storePath = imageCaseTypeEnum.equals(ImageCaseTypeEnum.BAD_CASE) ? concatenatePaths(
                        targetPath, prompt) : targetPath;

                    // 执行远程同步
                    boolean result = doRemoteFileSync(imageCaseVO, targetServer, storePath + '/' + format,
                        imageCaseTypeEnum);
                    if (result) {
                        successCount.incrementAndGet();
                    }
                }
            });
        });

        log.info("[文件同步][{}]FileSyncJob::remoteSync::同步结束，成功同步 {} 条数据(不同标签会有重复部分)...",
            imageCaseTypeEnum.getCode(), successCount.get());
    }

    /**
     * 执行远程同步
     *
     * @param imageCaseVO  图片案例对象
     * @param targetServer 目标服务器地址
     * @param targetPath   目标服务器路径
     */
    private Boolean doRemoteFileSync(ImageCaseVO imageCaseVO, String targetServer, String targetPath,
                                     ImageCaseTypeEnum imageCaseTypeEnum) {
        // 执行结果
        boolean result = false;

        // 检查目标文件是否存在
        if (checkPathIsExist(imageCaseVO)) {
            log.info("[文件同步][{}]FileSyncJob::doRemoteFileSync::源文件存在，将从源服务器拉取文件...",
                imageCaseTypeEnum.getCode());

            // 获取文件服务地址
            String fileServerUrl = serverHelper.getFileServerUrl(imageCaseVO.getStoreServer());

            // 远程同步图片（服务器内部图片）
            result = comfyUIService.remoteFileSync(fileServerUrl,
                concatenatePaths(comfyuiOutputPath, imageCaseVO.getStorePath()), targetServer, targetPath);

            // 拼装 url 地址
            String imageUrl = fileServerUrl + "/" + imageCaseVO.getStorePath();

            // 记录同步结果
            updateBadCaseInfo(imageCaseVO, targetServer, targetPath, result, imageUrl, imageCaseTypeEnum);
        } else {
            log.info(
                "[文件同步][{}]FileSyncJob::doRemoteFileSync::源文件不存在或配置错误，将从 OSS 中拉取文件...",
                imageCaseTypeEnum.getCode());

            // 同步Url图片（阿里云 OSS 图片）
            result = comfyUIService.remoteFileSync(imageCaseVO.getUrl(), targetServer, targetPath);

            // 记录同步结果
            updateBadCaseInfo(imageCaseVO, targetServer, targetPath, result, imageCaseVO.getUrl(),
                imageCaseTypeEnum);
        }

        // 返回结果
        return result;
    }

    /**
     * 同步精选图到 imageCase 中
     */
    private void syncGoodCaseToImageCase() {
        // 待同步的精选图列表
        List<CreativeBatchVO> creativeBatchVOList = selectNotSyncGoodCaseList();
        if (creativeBatchVOList.isEmpty()) {
            log.info("[文件同步][GoodCase]FileSyncJob::syncGoodCaseToImageCase::无需同步的精选图数据");
            return;
        }

        log.info(
            "[文件同步][GoodCase]FileSyncJob::syncGoodCaseToImageCase::精选图数据迁移开始，共有{}条数据需要进行迁移...",
            creativeBatchVOList.size());

        // 查询精选图标签
        PromptDictVO promptDictVO = promptDictService.queryByTypeAndTag(DictTypeEnum.IMAGE_TAGS,
            ImageCaseTypeEnum.GOOD_CASE.getCode());
        if (promptDictVO == null) {
            log.error(
                "[文件同步][GoodCase]FileSyncJob::syncGoodCaseToImageCase::精选图标签未配置或配置错误，数据同步终止");
            return;
        }

        // 统计处理结果
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 遍历精选图列表，同步图片
        creativeBatchVOList.forEach(creativeBatchVO -> {
            try {
                // 处理单个批次的图片同步
                processSingleBatch(creativeBatchVO, promptDictVO, successCount, failCount);
            } catch (Exception e) {
                log.error("[文件同步][GoodCase]FileSyncJob::syncGoodCaseToImageCase::批次[{}]处理失败，错误信息: {}",
                    creativeBatchVO.getId(), e.getMessage());
                failCount.incrementAndGet();
            }
        });

        log.info("[文件同步][GoodCase]FileSyncJob::syncGoodCaseToImageCase::迁移结束，成功: {}，失败: {}",
            successCount.get(), failCount.get());
    }

    /**
     * 处理单个批次的图片同步
     *
     * @param creativeBatchVO 创意批次对象
     * @param promptDictVO    标签字典对象
     * @param successCount    成功计数器
     * @param failCount       失败计数器
     */
    private void processSingleBatch(CreativeBatchVO creativeBatchVO, PromptDictVO promptDictVO,
                                    AtomicInteger successCount, AtomicInteger failCount) {
        // 获取批次扩展信息 
        JSONObject extInfo = creativeBatchVO.getExtInfo();
        // 获取批次结果图片
        List<String> resultImages = creativeBatchVO.getResultImages();

        // 如果批次结果图片为空则直接返回
        if (resultImages == null || resultImages.isEmpty()) {
            log.warn("[文件同步][GoodCase]FileSyncJob::processSingleBatch::批次[{}]无图片数据",
                creativeBatchVO.getId());
            return;
        }

        boolean batchSuccess = true;
        try {
            // 批量处理图片
            for (String imageUrl : resultImages) {
                if (StringUtils.isBlank(imageUrl)) {
                    log.warn("[文件同步][GoodCase]FileSyncJob::processSingleBatch::批次[{}]存在空图片地址",
                        creativeBatchVO.getId());
                    continue;
                }

                // 同步单张图片
                if (!syncSingleImage(imageUrl, creativeBatchVO, promptDictVO)) {
                    // 同步失败则标记批次失败
                    batchSuccess = false;
                    // 失败计数器加1
                    failCount.incrementAndGet();
                } else {
                    // 成功计数器加1
                    successCount.incrementAndGet();
                }
            }

            // 只有当批次内所有图片处理成功时，才标记整个批次为已同步
            if (batchSuccess) {
                // 标记批次已同步
                extInfo.put(IS_SYNC_TO_IMAGE_CASE, true);
                // 更新批次扩展信息
                creativeBatchVO.setExtInfo(extInfo);
                // 更新批次信息
                creativeBatchService.updateByIdSelective(creativeBatchVO);
            }
        } catch (Exception e) {
            log.error("[文件同步][GoodCase]FileSyncJob::processSingleBatch::批次[{}]处理异常，错误信息: {}",
                creativeBatchVO.getId(), e.getMessage());
            // 移除批次扩展信息中的同步状态
            extInfo.remove(IS_SYNC_TO_IMAGE_CASE);
            // 更新批次扩展信息
            creativeBatchVO.setExtInfo(extInfo);
            // 更新批次信息
            creativeBatchService.updateByIdSelective(creativeBatchVO);
        }
    }

    /**
     * 同步单张图片
     *
     * @param imageUrl        图片URL
     * @param creativeBatchVO 创意批次对象
     * @param promptDictVO    标签字典对象
     * @return 同步是否成功
     */
    private boolean syncSingleImage(String imageUrl, CreativeBatchVO creativeBatchVO, PromptDictVO promptDictVO) {
        try {
            ImageCaseVO imageCaseVO = new ImageCaseVO();
            imageCaseVO.setUrl(imageUrl);
            imageCaseVO.setMiniUrl(imageUrl);
            imageCaseVO.setBatchId(creativeBatchVO.getId());
            imageCaseVO.setStatus(MaterialModelStatusEnum.ENABLED);
            imageCaseVO.setOperatorId(OperationContextHolder.getOperatorUserId());
            imageCaseVO.setMemo("精选图同步迁移数据");
            imageCaseVO.setCreateTime(creativeBatchVO.getCreateTime());
            imageCaseVO.setModifyTime(creativeBatchVO.getModifyTime());
            imageCaseVO.setSyncStatus(false);
            imageCaseVO.setReSyncCount(0);

            // 插入图片案例
            imageCaseService.insert(imageCaseVO);
            // 添加关联标签
            imageCaseService.addBadCaseTag(imageCaseVO.getUrl(), null, promptDictVO.getId(), true, null, null);
            return true;
        } catch (Exception e) {
            log.error("[文件同步][GoodCase]FileSyncJob::syncSingleImage::图片[{}]同步失败，错误信息: {}",
                imageUrl, e.getMessage());
            return false;
        }
    }

    /**
     * 查询待同步的精选图
     *
     * @return 精选图列表
     */
    private List<CreativeBatchVO> selectNotSyncGoodCaseList() {
        // 封装查询条件
        CreativeBatchQuery query = new CreativeBatchQuery();

        // 查询精选图
        query.setBizTag("exampleImages");

        List<CreativeBatchVO> creativeBatchVOList = creativeBatchService.queryCreativeBatchList(query);

        return creativeBatchVOList.stream().filter(creativeBatchVO -> {
            return creativeBatchVO.getExtInfo().get(IS_SYNC_TO_IMAGE_CASE) == null;
        }).collect(Collectors.toList());
    }

    /**
     * 记录同步结果
     *
     * @param imageCaseVO       图片案例对象
     * @param targetServer      目标服务器地址
     * @param targetPath        目标服务器路径
     * @param syncResult        同步状态
     * @param imageUrl          图片地址
     * @param imageCaseTypeEnum 图片案例类型
     */
    private void updateBadCaseInfo(ImageCaseVO imageCaseVO, String targetServer, String targetPath, Boolean syncResult,
                                   String imageUrl, ImageCaseTypeEnum imageCaseTypeEnum) {
        try {
            // 更新图片同步状态
            imageCaseVO.setSyncStatus(syncResult);
            imageCaseVO.setSyncTime(new Date());
            imageCaseVO.setReSyncCount(imageCaseVO.getReSyncCount() + 1);
            // 更新图片案例信息
            imageCaseService.updateByIdSelective(imageCaseVO);

            ImageCaseSyncRecordVO imageCaseSyncRecordVO = new ImageCaseSyncRecordVO();
            imageCaseSyncRecordVO.setCaseId(imageCaseVO.getId());
            imageCaseSyncRecordVO.setTargetServer(targetServer);
            imageCaseSyncRecordVO.setTargetStorePath(targetPath);
            imageCaseSyncRecordVO.setImageUrl(imageUrl);
            imageCaseSyncRecordVO.setUploadTime(new Date());
            imageCaseSyncRecordVO.setIsSuccess(syncResult);
            imageCaseSyncRecordVO.setSyncType(imageCaseTypeEnum.getCode());

            // 记录同步结果
            imageCaseSyncRecordService.insert(imageCaseSyncRecordVO);

        } catch (Exception e) {
            log.error("[文件同步][{}]FileSyncJob::updateBadCaseInfo::记录同步结果出现异常,错误信息：{}...",
                imageCaseTypeEnum.getCode(), e.getMessage());
        }
    }

    /**
     * 查询待同步的图片（每次查询 100条记录）
     *
     * @param imageCaseTypeEnum 图片案例类型
     * @param isPartUpload      是否部分上传
     * @return 图片案例列表
     */
    private List<ImageCaseVO> selectImageCase(ImageCaseTypeEnum imageCaseTypeEnum, Boolean isPartUpload) {
        // 封装查询条件
        ImageCaseQuery query = new ImageCaseQuery();
        // 查询未同步的图片
        query.setSyncStatus(false);
        query.setMaxSyncCount(MAX_SYNC_COUNT);

        // 每次查询 100条数据
        query.setPageNum(1);
        query.setPageSize(100);

        // 查询图片列表
        return imageCaseService.queryImageCaseListByType(query, DictTypeEnum.IMAGE_TAGS, imageCaseTypeEnum,
            isPartUpload);
    }

    /**
     * 根据图片类型获取目标服务器信息
     *
     * @param imageCaseType 图片案例类型
     * @return 目标服务器信息
     */
    private Map<String, String> getTargetServerInfoByType(String imageCaseType) {
        // 存储进入 Map 集合中
        Map<String, String> map = new HashMap<>();

        // 获取配置信息
        String imageCaseSyncConfig = systemConfigService.queryValueByKey(SystemConstants.IMAGE_CASE_SYNC_CONFIG);

        if (imageCaseSyncConfig == null) {
            log.error(
                "[文件同步]FileSyncJob::getTargetServerInfoByType::imageCaseSyncConfig配置为空，请先进行配置...");
            return null;
        }

        // 将配置信息转换为 JSONObject
        JSONObject jsonObject = JSONArray.parseObject(imageCaseSyncConfig);
        // 获取枚举类型中对应的配置信息
        Object imageCaseTypeConfig = jsonObject.get(imageCaseType);
        // 获取目标服务器配置信息
        if (imageCaseTypeConfig != null) {
            JSONObject badCaseJsonObject = (JSONObject)imageCaseTypeConfig;
            map.put(TARGET_SERVER, badCaseJsonObject.getString(TARGET_SERVER));
            map.put(TARGET_PATH, badCaseJsonObject.getString(TARGET_PATH));
        }

        // 返回目标服务器信息
        return map;
    }

    /**
     * 检查目标路径是否存在/合法
     *
     * @param imageCaseVO 图片案例对象
     * @return true 存在，false 不存在
     */
    private boolean checkPathIsExist(ImageCaseVO imageCaseVO) {
        // 参数提取
        String storePath = imageCaseVO.getStorePath();
        String storeServer = serverHelper.getFileServerUrl(imageCaseVO.getStoreServer());

        // 如果为空则直接返回 false
        if (storeServer == null || storePath == null) {
            return false;
        }

        // 校验 storePath 图片路径 内容是否合法（后缀是否为文件）/ 且校验文件是否存在
        return isValidFilePath(storePath) && comfyUIService.checkFileExists(
            concatenatePaths(comfyuiOutputPath, storePath), storeServer);
    }

    /**
     * 检查文件路径是否有效
     *
     * @param storePath 文件路径
     * @return true 含有文件名，false 不含有文件名
     */
    private boolean isValidFilePath(String storePath) {
        // 检查 storePath 是否为空或不包含 "/"
        if (storePath == null || !storePath.contains("/")) {
            return false;
        }

        // 获取文件名称
        String fileName = storePath.substring(storePath.lastIndexOf("/") + 1);

        // 检查文件名称是否包含 "."
        return fileName.contains(".");
    }

    /**
     * 连接路径
     *
     * @param prefixPath 前置路径
     * @param suffixPath 后置路径
     */
    private String concatenatePaths(String prefixPath, String suffixPath) {
        if (StringUtils.isBlank(prefixPath) && StringUtils.isBlank(suffixPath)) {
            return "";
        }

        if (StringUtils.isBlank(prefixPath)) {
            return suffixPath;
        }

        if (StringUtils.isBlank(suffixPath)) {
            return prefixPath;
        }

        // 移除basePath末尾的斜杠和subPath开头的斜杠
        prefixPath = StringUtils.stripEnd(prefixPath, "/");
        suffixPath = StringUtils.stripStart(suffixPath, "/");

        return prefixPath + "/" + suffixPath;
    }

    /**
     * 将字符串中的一个多个空格、制表符、- 、换行符登登替换为_
     *
     * @param str 字符串
     * @return 处理后的字符串
     */
    private String replaceSpacesAndLowerCase(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("[\\s-]+", "_").toLowerCase();
    }

}
