package ai.conrain.aigc.platform.service.util.flow.handlers;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import ai.conrain.aigc.platform.service.util.flow.JsonHandler;
import ai.conrain.aigc.platform.service.util.flow.FlowContext;
import ai.conrain.aigc.platform.service.util.flow.logger.FlowLogger;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 工作流模式处理器
 * 负责处理JSON中workflow节点的mode属性
 * 主要功能：
 * 1. 在workflow.nodes数组中查找指定ID的节点
 * 2. 为匹配的节点添加mode属性
 * 3. mode属性值使用模板表达式：${paramName?then(0,4)}
 * 4. 支持在已有mode属性的基础上通过&&进行条件拼接
 * 5. 避免重复添加相同参数
 * 
 * JSON路径：extra_data.extra_pnginfo.workflow.nodes[].mode
 */
public class WorkflowModeHandler implements JsonHandler {
    private final FlowContext context;
    private final FlowLogger logger;
    // 匹配模板表达式中的参数名正则，同时支持带括号和不带括号的格式
    private static final Pattern PARAM_PATTERN = Pattern.compile("\\$\\{(?:\\()?([^?]*)(?:\\))?\\?then\\(0,4\\)\\}");

    public WorkflowModeHandler(FlowContext context, FlowLogger logger) {
        this.context = context;
        this.logger = logger;
    }

    @Override
    public void handle(JSONObject jsonObject) {
        logger.info("开始处理workflow mode属性");
        logger.increaseLevel();
        
        try {
            JSONObject workflow = jsonObject.getJSONObject("extra_data")
                                         .getJSONObject("extra_pnginfo")
                                         .getJSONObject("workflow");
            JSONArray nodes = workflow.getJSONArray("nodes");
            logger.info("获取到workflow nodes，共" + nodes.size() + "个节点");
            
            // 处理普通节点
            handleNodes(nodes, context.getNodeIds(), false);
            // 处理互斥节点
            handleNodes(nodes, context.getMutuallyNodeIds(), true);
            
        } catch (Exception e) {
            logger.error("处理workflow mode时发生异常: " + e.getMessage());
            throw e;
        }
        
        logger.decreaseLevel();
        logger.success("workflow mode处理完成");
    }

    private void handleNodes(JSONArray nodes, List<Integer> nodeIds, boolean reverse) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            logger.info(reverse ? "无互斥节点，跳过处理" : "无节点，跳过处理");
            return;
        }

        int modifiedCount = 0;
        int skippedCount = 0;
        logger.increaseLevel();
        
        for (int i = 0; i < nodes.size(); i++) {
            JSONObject node = nodes.getJSONObject(i);
            if (node.containsKey("id") && nodeIds.contains(node.getInteger("id"))) {
                Integer nodeId = node.getInteger("id");
                logger.info("处理节点: " + nodeId + (reverse ? " (互斥节点)" : ""));
                
                // 获取新参数名（互斥节点需要在参数前加!）
                String newParamName = reverse ? "!" + context.getParamName() : context.getParamName();
                String baseParamName = context.getParamName(); // 不带!的原始参数名
                
                // 检查节点是否已有mode属性
                if (node.containsKey("mode") && node.get("mode") instanceof String) {
                    String existingMode = node.getString("mode");
                    logger.info("节点已有mode属性: " + existingMode);
                    
                    // 从现有表达式中提取参数
                    Matcher matcher = PARAM_PATTERN.matcher(existingMode);
                    if (matcher.find()) {
                        String existingParam = matcher.group(1);
                        logger.info("提取到现有参数: " + existingParam);
                        
                        // 检查当前参数是否已存在
                        if (parameterAlreadyExists(existingParam, baseParamName, reverse)) {
                            logger.info("参数 " + baseParamName + " 已存在于表达式中，跳过添加");
                            continue;
                        }
                        
                        // 组合新的参数条件，使用括号包裹
                        String combinedParam = "(" + existingParam + "&&" + newParamName + ")";
                        logger.info("组合新参数: " + combinedParam);
                        
                        // 设置新的mode属性
                        node.put("mode", "${" + combinedParam + "?then(0,4)}");
                        logger.success("更新mode属性完成");
                    } else {
                        logger.warn("无法解析现有mode属性，将进行替换");
                        node.put("mode", "${" + newParamName + "?then(0,4)}");
                        logger.success("替换mode属性完成");
                    }
                } else {
                    node.put("mode", "${" + newParamName + "?then(0,4)}");
                    logger.success("新增mode属性完成");
                }
                
                modifiedCount++;
            } else if (node.containsKey("id")) {
                // 记录被跳过的节点，提高日志可读性
                skippedCount++;
            }
        }
        
        logger.decreaseLevel();
        logger.success("共修改" + modifiedCount + "个节点，跳过" + skippedCount + "个节点");
    }
    
    /**
     * 检查参数是否已存在于表达式中
     * 
     * @param existingParam 现有参数表达式
     * @param newParam 新参数名(不带!)
     * @param reverse 是否为互斥节点
     * @return 如果参数已存在返回true
     */
    private boolean parameterAlreadyExists(String existingParam, String newParam, boolean reverse) {
        // 分割参数表达式，处理可能存在的多个参数（通过&&连接）
        String[] params = existingParam.split("&&");
        
        for (String param : params) {
            // 去除可能的空格
            param = param.trim();
            
            // 处理取反的情况 (去掉开头的!)
            String cleanParam = param.startsWith("!") ? param.substring(1) : param;
            
            // 检查参数名是否匹配
            if (cleanParam.equals(newParam)) {
                // 如果参数名相同，再检查取反状态是否一致
                boolean paramIsNegated = param.startsWith("!");
                // 如果取反状态一致，则认为是相同参数
                if (paramIsNegated == reverse) {
                    return true;
                }
            }
        }
        
        return false;
    }
} 