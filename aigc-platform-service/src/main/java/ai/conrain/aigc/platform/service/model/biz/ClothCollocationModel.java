/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import java.io.Serializable;

import lombok.Data;

/**
 * 服装配饰模型
 *
 * <AUTHOR>
 * @version : ClothCollocationModel.java, v 0.1 2024/9/4 20:10 renxiao.wu Exp $
 */
@Data
public class ClothCollocationModel implements Serializable {
    private static final long serialVersionUID = -7423555343801410820L;
    /** 鞋子配饰描述，如果有多个值用逗号连接 */
    private String shoe;
    /** 上装配饰描述，如果有多个值用逗号连接 */
    private String tops;
    /** 下装配饰描述，如果有多个值用逗号连接 */
    private String bottoms;
    /** 其他配饰描述，如果有多个值用逗号连接 */
    private String others;
    /** 道具描述，如果有多个值用逗号连接 */
    private String props;
}
