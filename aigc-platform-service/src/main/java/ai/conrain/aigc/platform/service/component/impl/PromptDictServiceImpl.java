package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.PromptDictDAO;
import ai.conrain.aigc.platform.dal.entity.PromptDictDO;
import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.enums.DictTagsEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.PromptDictConverter;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * PromptDictService实现
 *
 * <AUTHOR>
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
@Slf4j
@Service
public class PromptDictServiceImpl extends AbstractCachedService<PromptDictVO, String, String>
    implements PromptDictService {

    /** DAO */
    @Autowired
    private PromptDictDAO promptDictDAO;

    @Override
    public PromptDictVO selectByIdFromDB(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        PromptDictDO data = promptDictDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return PromptDictConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = promptDictDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除PromptDict失败");

        //强制刷新
        forceRefresh();
    }

    @Override
    public PromptDictVO insert(PromptDictVO promptDict) {
        AssertUtil.assertNotNull(promptDict, ResultCode.PARAM_INVALID, "promptDict is null");
        AssertUtil.assertTrue(promptDict.getId() == null, ResultCode.PARAM_INVALID, "promptDict.id is present");

        //创建时间、修改时间兜底
        if (promptDict.getCreateTime() == null) {
            promptDict.setCreateTime(new Date());
        }

        if (promptDict.getModifyTime() == null) {
            promptDict.setModifyTime(new Date());
        }

        PromptDictDO data = PromptDictConverter.vo2DO(promptDict);
        int n = promptDictDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建PromptDict失败");
        AssertUtil.assertNotNull(data.getId(), "新建PromptDict返回id为空");
        promptDict.setId(data.getId());

        refresh();

        return promptDict;
    }

    @Override
    public void updateById(PromptDictVO promptDict) {
        AssertUtil.assertNotNull(promptDict, ResultCode.PARAM_INVALID, "promptDict is null");
        AssertUtil.assertTrue(promptDict.getId() != null, ResultCode.PARAM_INVALID, "promptDict.id is null");
        //修改时间必须更新
        promptDict.setModifyTime(new Date());

        PromptDictDO data = PromptDictConverter.vo2DO(promptDict);
        int n = promptDictDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新PromptDict失败，影响行数:" + n);

        refresh();
    }

    @Override
    public void batchInsert(List<PromptDictVO> target) {
        if (CollectionUtils.isEmpty(target)) {
            return;
        }

        promptDictDAO.batchInsert(PromptDictConverter.voList2DOList(target));
        refresh();
    }

    @Override
    public List<PromptDictVO> querySystemCollocation() {
        List<PromptDictVO> list = queryAll();
        return list.stream().filter(item -> item.getType() == DictTypeEnum.CLOTH_COLLOCATION && item.getTags()
            .contains(DictTagsEnum.SYSTEM.getCode())).collect(Collectors.toList());
    }

    @Override
    public List<PromptDictVO> querySystemScene() {
        List<PromptDictVO> list = queryAll();
        return list.stream().filter(item -> item.getType() == DictTypeEnum.TOPICAL_SCENE && item.getTags()
            .contains(DictTagsEnum.SYSTEM.getCode())).collect(Collectors.toList());
    }

    @Override
    public List<PromptDictVO> queryGarmentList() {
        List<PromptDictVO> list = queryAll();
        return list.stream().filter(item -> item.getType() == DictTypeEnum.GARMENT_TYPE && item.getTags()
            .contains(DictTagsEnum.SYSTEM.getCode())).collect(Collectors.toList());
    }

    @Override
    public List<PromptDictVO> queryListByType(DictTypeEnum type, ImageCaseTypeEnum imageCaseTypeEnum) {
        List<PromptDictVO> list = queryAll();
        return list.stream().filter(item -> item.getType() == type && item.getTags()
            .contains(imageCaseTypeEnum.getCode())).collect(Collectors.toList());
    }

    @Override
    public List<PromptDictVO> queryListByType(DictTypeEnum type, String tagName) {
        List<PromptDictVO> list = queryAll();
        return list.stream().filter(item -> item.getType() == type && item.getTags()
            .contains(tagName)).collect(Collectors.toList());
    }

    @Override
    public PromptDictVO queryByTypeAndPrompt(DictTypeEnum type, String value) {
        return queryAll().stream().filter(item -> item.getType() == type && item.getPrompt().equals(value)).findFirst()
            .orElse(null);
    }

    @Override
    public List<PromptDictVO> queryByTypeAndTags(DictTypeEnum type, List<DictTagsEnum> tags) {
        List<String> tagsList = tags.stream().map(DictTagsEnum::getCode).collect(Collectors.toList());

        return queryAll().stream().filter(item -> item.getType() == type && new HashSet<>(item.getTags()).containsAll(
            tagsList)).collect(Collectors.toList());
    }


    @Override
    public PromptDictVO queryByTypeAndTag(DictTypeEnum type, String tagName) {
        return queryAll().stream().filter(item -> item.getType() == type && new HashSet<>(item.getTags()).contains(
            tagName)).findFirst().orElse(null);
    }

    @Override
    public List<PromptDictVO> queryByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        return queryAll().stream().filter(item -> ids.contains(item.getId())).collect(Collectors.toList());
    }

    @Override
    protected List<PromptDictVO> loadAll() {
        List<PromptDictDO> list = promptDictDAO.selectAll();
        return PromptDictConverter.doList2VOList(list);
    }

    @Override
    protected String getKey(PromptDictVO cache) {
        return cache.getWord();
    }

    @Override
    protected String getValue(PromptDictVO cache) {
        return cache.getPrompt();
    }
}