package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * PermissionVO
 *
 * @version PermissionService.java v 0.1 2024-01-20 01:21:37
 */
@Slf4j
@Data
public class PermissionVO implements ModifyTimeClz, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 执行动作 */
    @ApiModelProperty(name = "action", value = "执行动作")
    private String action;

    /** 权限名称 */
    @ApiModelProperty(name = "name", value = "权限名称")
    private String name;

    /** 权限配置 */
    @ApiModelProperty(name = "config", value = "权限配置")
    private String config;

    /** 是否允许子账号执行 */
    @ApiModelProperty(name = "allowedSub", value = "是否允许子账号执行")
    private boolean allowedSub;

    /** 权限角色配置 */
    @ApiModelProperty(name = "permissionRoles", value = "权限角色配置")
    private List<RoleTypeEnum> permissionRoles;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 版本号 */
    @JsonIgnore
    private long version;

    public void setConfig(String config) {
        this.config = config;

        if (StringUtils.isNotBlank(config)) {
            String[] split = StringUtils.split(config, ",");
            List<RoleTypeEnum> roles = new ArrayList<>(split.length);
            for (String each : split) {
                //兼容一下数据和代码不一致的情况
                if (RoleTypeEnum.getByCode(each) != null) {
                    roles.add(RoleTypeEnum.getByCode(each));
                } else {
                    log.warn("PermissionVO.setConfig() config={} is not valid", config);
                }
            }

            this.permissionRoles = roles;
        }
    }

}