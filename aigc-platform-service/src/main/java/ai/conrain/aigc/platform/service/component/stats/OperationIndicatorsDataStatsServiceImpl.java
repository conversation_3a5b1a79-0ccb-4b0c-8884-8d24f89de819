package ai.conrain.aigc.platform.service.component.stats;


import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.enums.StatsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "operationIndicatorsDataStatsServiceImpl")
public class OperationIndicatorsDataStatsServiceImpl extends AbstractDataStatsServiceImpl {
    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.OPERATION_INDICATORS;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum, boolean isTotal) {
        return 0;
    }


}
