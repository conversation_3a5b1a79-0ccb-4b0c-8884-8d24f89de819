package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 权限配置表
 * 对应数据表：permission
 */
@Data
public class PermissionDO implements Serializable {
    private static final long serialVersionUID = 5919627296591908778L;
    /**
     * id
     */
    private Integer id;

    /**
     * 执行动作
     */
    private String action;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限配置
     */
    private String config;

    /**
     * 是否允许子账号执行
     */
    private Boolean allowedSub;

    /**
     * 备注
     */
    private String memo;

    /**
     * 版本号
     */
    private long version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}