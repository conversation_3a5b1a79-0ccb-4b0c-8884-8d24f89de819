package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * InvoiceTitleVO
 *
 * @version InvoiceTitleService.java v 0.1 2024-06-27 01:42:09
 */
@Data
public class InvoiceTitleVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 关联的主用户id */
	@ApiModelProperty(name = "masterUserId", value = "关联的主用户id")
	private Integer masterUserId;

	/** 关联的操作员用户id */
	@ApiModelProperty(name = "operatorUserId", value = "关联的操作员用户id")
	private Integer operatorUserId;

	/** 发票类型，普票｜专票 */
	@ApiModelProperty(name = "invoiceType", value = "发票类型，普票｜专票")
	private String invoiceType;

	/** 发票抬头类型，个人｜企业 */
	@ApiModelProperty(name = "subjectType", value = "发票抬头类型，个人｜企业")
	private String subjectType;

	/** 发票抬头 */
	@ApiModelProperty(name = "subjectName", value = "发票抬头")
	private String subjectName;

	/** 统一社会信用代码 */
	@ApiModelProperty(name = "creditCode", value = "统一社会信用代码")
	private String creditCode;

	/** 办公地址 */
	@ApiModelProperty(name = "businessAddress", value = "办公地址")
	private String businessAddress;

	/** 办公电话 */
	@ApiModelProperty(name = "businessPhone", value = "办公电话")
	private String businessPhone;

	/** 开户银行 */
	@ApiModelProperty(name = "bankName", value = "开户银行")
	private String bankName;

	/** 银行账号 */
	@ApiModelProperty(name = "bankAccount", value = "银行账号")
	private String bankAccount;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private String extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;
}