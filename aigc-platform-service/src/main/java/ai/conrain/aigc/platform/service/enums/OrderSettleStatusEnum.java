/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

/**
 * 结算状态枚举
 *
 * <AUTHOR>
 * @version : OrderSettleStatusEnum.java, v 0.1 2023/9/2 09:54 renxiao.wu Exp $
 */
@Getter
public enum OrderSettleStatusEnum {
//    INIT(0, "初始化"),
//    TRADE_CLOSED(1, "交易关闭"),
//    PENDING_SETTLE(2, "待结算"),
    SETTLING(3, "结算中"),
    SUCCESS(4, "已结算"),
//    FAIL(5, "结算失败"),
    ;

    /** 枚举码 */
    private Integer code;

    /** 枚举描述 */
    private String desc;

    private OrderSettleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static OrderSettleStatusEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }

        for (OrderSettleStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }

        return null;
    }
}
