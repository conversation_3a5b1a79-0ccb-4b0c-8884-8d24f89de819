package ai.conrain.aigc.platform.web.controller;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.ComfyuiTaskService;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.MerchantPreferenceService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.WorkflowTaskService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.biz.UserClothMatchingPreference;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.query.WorkflowTaskQuery;
import ai.conrain.aigc.platform.service.model.request.AutoGenImgParam;
import ai.conrain.aigc.platform.service.model.request.ConfirmLoraReq;
import ai.conrain.aigc.platform.service.model.vo.LabelFileEditReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ModelTrainDetailVO;
import ai.conrain.aigc.platform.service.model.vo.SyncImageCaseReq;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.model.vo.WorkflowTaskVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.OrderUtils;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_RANGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_COLOR_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_NUM;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_TYPE_CONFIGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CUTOUT_AGAIN_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_CUTOUT_AGAIN;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_OP_VERSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REVIEWER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_WORK_SCHEDULED_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.clothStyleType;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.disableOperatorNick;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.disableReason;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.highResModelShowImgUrl;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SYSTEM_LORA_ORDER_CFG;

/**
 * MaterialModel控制器
 *
 * <AUTHOR>
 * @version MaterialModelService.java v 0.1 2024-05-09 02:06:17
 */
@Slf4j
@Api("素材模型")
@Validated
@RestController
@RequestMapping("/materialModel")
public class MaterialModelController {
    /** 允许输出的扩展字段白名单 */
    public static final List<String> outputKeyWhitelist = Arrays.asList(clothStyleType, CommonConstants.KEY_CLOTH_MARK,
        disableReason, disableOperatorNick, highResModelShowImgUrl, KEY_AGE_RANGE, KEY_CLOTH_NUM, KEY_CLOTH_CATEGORY);
    private static final List<String> ADMIN_WHITE_LIST = Arrays.asList("13732280808", "18610655771", "19932600928",
        "18966484977");//登登实际在后台配置的报警账户是18717831174，要替换成操作员的账号18966484977

    @Autowired
    private MaterialInfoService materialInfoService;

    /** materialModelService */
    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private ComfyuiTaskService comfyuiTaskService;

    @Autowired
    private UserService userService;

    @Autowired
    private OssService ossService;

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private MerchantPreferenceService merchantPreferenceService;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ServerHelper serverHelper;

    @Autowired
    private DistributorService distributorService;

    @Autowired
    private ImageCaseService imageCaseService;

    @Autowired
    private WorkflowTaskService workflowTaskService;

    @Autowired
    private UserProfileService userProfileService;

    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据id查询素材模型", notes = "根据id查询素材模型[materialModel]")
    public Result<MaterialModelVO> getById(@NotNull @PathVariable("id") Integer id) {
        MaterialModelVO data = materialModelService.selectById(id);

        // 数据不存在直接返回 null
        if (data == null) {
            return Result.success(null);
        }

        if (data.getClothTypeConfigs() == null) {
            data.setClothTypeConfigs(MaterialModelConverter.convert2ClothTypeConfig(data));
        }
        Integer opVersion = data.getExtInfo(KEY_OP_VERSION, Integer.class);
        data.setOpVersion(opVersion != null ? opVersion : 1);
        return Result.success(data);
    }

    @PostMapping("/getTrainDetail")
    @ApiOperation(value = "根据id查询模型训练详情")
    public Result<ModelTrainDetailVO> getTrainDetail(@JsonArg @NotNull Integer id) {
        ModelTrainDetailVO model = materialModelService.getTrainDetail(id);
        if (model == null) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "不存在的模型id:" + id);
        }
        WorkflowTaskQuery workflowTaskQuery = new WorkflowTaskQuery();
        workflowTaskQuery.setBizId(model.getId());
        List<WorkflowTaskVO> tasks = workflowTaskService.queryWorkflowTaskList(workflowTaskQuery);
        model.setTasks(tasks);
        return Result.success(model);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新建素材模型", notes = "新建素材模型[materialModel]")
    public Result<Integer> create(@Valid @RequestBody MaterialModelVO materialModel) {
        try {
            MaterialModelVO data = materialModelService.insert(materialModel);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加素材模型失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建素材模型失败");
    }

    @PostMapping("/cloneLora")
    @ApiOperation("管理员后台服装克隆")
    public Result<?> cloneLora(@JsonArg @NotNull Integer modelId, @JsonArg Boolean fullCopy) {
        MaterialModelVO newLora = materialModelService.cloneLora(modelId, null, null,
            fullCopy != null ? fullCopy : false);
        return Result.success(newLora.getId());
    }

    @PostMapping("/copyToSystem")
    @ApiOperation("管理员后台服装克隆")
    public Result<?> copyToSystem(@JsonArg @NotNull Integer modelId) {
        MaterialModelVO newLora = materialModelService.copyToSystem(modelId);
        return Result.success(newLora.getId());
    }

    @PostMapping("/retrainLora")
    @ApiOperation("重新训练")
    public Result<?> retrainLora(@JsonArg @NotNull Integer modelId, @JsonArg String labelType,
                                 @JsonArg String cut4ScaleUp) {
        materialModelService.retrainLora(modelId, true, labelType, cut4ScaleUp);
        return Result.success();
    }

    @PostMapping("/relabelLora")
    @ApiOperation("重新抠图打标")
    public Result<?> relabelLora(@JsonArg @NotNull Integer modelId, @JsonArg String labelType,
                                 @JsonArg String cut4ScaleUp) {
        materialModelService.retrainLora(modelId, false, labelType, cut4ScaleUp);
        return Result.success();
    }

    @PostMapping("/add/system")
    @ApiOperation(value = "新建官方素材模型", notes = "新建官方素材模型[materialModel]")
    public Result<Integer> addSystem(@Valid @RequestBody MaterialModelVO materialModel) {
        materialModel.setType(ModelTypeEnum.SYSTEM);
        materialModel.setOperatorId(OperationContextHolder.getOperatorUserId());
        AssertUtil.assertTrue(StringUtils.isNotBlank(materialModel.getShowImage()), ResultCode.PARAM_INVALID,
            "图片未上传");
        try {
            MaterialModelVO data = materialModelService.insert(materialModel);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加官方素材模型失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建素材模型失败");
    }

    @PostMapping("/deleteById")
    @ApiOperation(value = "删除素材模型", notes = "删除素材模型")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        materialModelService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    @ApiOperation(value = "更新素材模型", notes = "修改素材模型[materialModel]，id必填，其余字段非空时才更新")
    public Result<?> updateByIdSelective(@Valid @RequestBody MaterialModelVO materialModel) {
        MaterialModelVO current = materialModelService.selectById(materialModel.getId());
        AssertUtil.assertNotNull(current, "模型不存在");

        //bugfix：将新旧两个ext info进行merge，避免丢失key
        JSONObject extInfo = current.getExtInfo() != null ? current.getExtInfo() : new JSONObject();

        //由其它状态改为审核不通过状态时，记录操作信息到扩展字段
        if (!MaterialModelStatusEnum.DISABLED.getCode().equals(current.getStatus())
            && MaterialModelStatusEnum.DISABLED.getCode().equals(materialModel.getStatus())) {
            log.info("模型被禁用modelId={}", materialModel.getId());
            extInfo.put(CommonConstants.disableOperatorId, OperationContextHolder.getOperatorUserId().toString());
            extInfo.put(CommonConstants.disableOperatorNick, OperationContextHolder.getOperatorNick());
            extInfo.put(CommonConstants.disableTime, DateUtils.formatTime(new Date()));
        }

        if (materialModel.getExtInfo() != null) {
            extInfo.putAll(materialModel.getExtInfo());
        }
        materialModel.setExtInfo(extInfo);

        if (CollectionUtils.isNotEmpty(materialModel.getClothTypeConfigs())) {
            materialModel.addExtInfo(KEY_CLOTH_TYPE_CONFIGS, materialModel.getClothTypeConfigs());
        }

        if (!MaterialModelStatusEnum.ENABLED.getCode().equals(current.getStatus())
            && MaterialModelStatusEnum.ENABLED.getCode().equals(materialModel.getStatus())) {
            log.info("模型被启用modelId={},operator={}", materialModel.getId(),
                OperationContextHolder.getOperatorUserId());
            materialModel.addExtInfo(KEY_DELIVERY_OPERATOR, OperationContextHolder.getOperatorUserId());
        }

        if (materialModel.getExtInfo(KEY_RELATED_OPERATOR, Integer.class) == null
            && OperationContextHolder.isBackRole()) {
            materialModel.addExtInfo(KEY_RELATED_OPERATOR, OperationContextHolder.getOperatorUserId());
        }

        if (materialModel.getClothLoraTrainDetail() != null) {
            AutoGenImgParam targetParam = materialModel.getClothLoraTrainDetail().getAutoGenImgParam();
            if (targetParam != null) {
                log.info("更新训练详情，只变更自动生成图片参数id={},targetParam={}", materialModel.getId(), targetParam);
                materialModel.setClothLoraTrainDetail(current.getClothLoraTrainDetail());
                materialModel.getClothLoraTrainDetail().setAutoGenImgParam(targetParam);
            } else {
                materialModel.setClothLoraTrainDetail(null);
            }
        }

        materialModelService.updateByIdSelective(materialModel);
        return Result.success();
    }

    @PostMapping("/deliver")
    @ApiOperation(value = "交付素材模型", notes = "交付素材模型[materialModel]，id必填，其余字段非空时才更新")
    public Result<?> deliver(@Valid @RequestBody MaterialModelVO materialModel) {
        materialModelService.deliver(materialModel);
        return Result.success();
    }

    @PostMapping("/addLora2Vip")
    public Result<?> add2LoraVip(@JsonArg @NotNull Integer loraId) {
        AssertUtil.assertTrue(OperationContextHolder.isAdmin(), "非管理员不能操作");
        MaterialModelVO model = materialModelService.selectById(loraId);
        if (model == null) {
            return Result.error("lora不存在");
        }

        SystemConfigVO loraVipCfg = systemConfigService.queryByKey(SystemConstants.LORA_VIP);
        AssertUtil.assertNotNull(loraVipCfg, "LORA_VIP配置不存在");

        JSONArray loraVip = JSONArray.parseArray(loraVipCfg.getConfValue());
        if (loraVip == null) {
            loraVip = new JSONArray();
        }
        if (!loraVip.contains(loraId)) {
            loraVip.add(loraId);
        }

        if (model.getMainType() == MainTypeEnum.MAIN) {
            List<MaterialModelVO> subList = materialModelService.querySubModel(loraId);

            if (CollectionUtils.isNotEmpty(subList)) {
                loraVip.addAll(subList.stream().map(MaterialModelVO::getId).collect(Collectors.toList()));
            }
        }

        loraVipCfg.setConfValue(loraVip.toJSONString());
        systemConfigService.updateById(loraVipCfg);

        return Result.success();
    }

    @PostMapping("/queryAllModelCreators")
    @ApiOperation(value = "查询当前登录者同组织的所有素材模型创建者")
    public Result<List<UserVO>> queryAllModelCreators() {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setUserId(OperationContextHolder.getMasterUserId());

        List<MaterialModelVO> models = materialModelService.queryMaterialModelList(query);
        if (models != null) {
            Map<Integer, UserVO> map = Maps.newHashMap();
            for (MaterialModelVO model : models) {
                AssertUtil.assertNotNull(model.getOperatorId(), "模型的操作人id为空");
                if (!map.containsKey(model.getOperatorId())) {
                    UserVO u = new UserVO();
                    u.setId(model.getOperatorId());
                    u.setNickName(model.getOperatorNick());

                    map.put(model.getOperatorId(), u);
                }
            }

            return Result.success(new ArrayList<>(map.values()));
        }

        return Result.success();
    }

    @ApiOperation(value = "批量查询素材模型", notes = "批量查询[materialModel]")
    @PostMapping("/querySystemList")
    public Result<PageInfo<MaterialModelVO>> querySystemList(@Valid @RequestBody MaterialModelQuery query) {
        Integer originPageSize = query.getPageSize();
        query.setType(ModelTypeEnum.SYSTEM.getCode());
        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        if (OperationContextHolder.isBackRole() && StringUtils.isNotBlank(query.getStatus())) {
            query.setStatusList(Lists.newArrayList(query.getStatus(), MaterialModelStatusEnum.TESTING.getCode()));
            query.setStatus(null);
        }
        //TODO by半泉:临时代码，对体验服装进行排序
        JSONArray array = systemConfigService.queryJsonArrValue(SYSTEM_LORA_ORDER_CFG);
        boolean needOrder = array != null && !array.isEmpty();

        if (needOrder) {
            query.setPageSize(20);
        }

        PageInfo<MaterialModelVO> pageInfo = materialModelService.queryMaterialModelByPage(query);
        List<MaterialModelVO> list = pageInfo.getList();
        if (needOrder && CollectionUtils.isNotEmpty(list)) {
            OrderUtils.sortListByJsonArray(list, array);
            pageInfo.setHasNextPage(list.size() > originPageSize);

            list = list.subList(0, originPageSize > list.size() ? list.size() : originPageSize);
            query.setPageSize(originPageSize);
            pageInfo.setSize(originPageSize);
        }
        pageInfo.setList(pruneForFront(list));

        return Result.success(pageInfo);
    }

    @ApiOperation(value = "批量查询素材模型", notes = "批量查询[materialModel]")
    @PostMapping("/queryList")
    public Result<List<MaterialModelVO>> queryMaterialModelList(@Valid @RequestBody MaterialModelQuery query) {
        if (!OperationContextHolder.isBackRole()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        query.setType(ModelTypeEnum.CUSTOM.getCode());
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }
        return Result.success(materialModelService.queryMaterialModelList(query));
    }

    @ApiOperation(value = "后台批量查询素材模型", notes = "批量查询[materialModel]")
    @PostMapping("/queryListWithBlogs")
    public Result<PageInfo<MaterialModelVO>> queryListWithBlogs(@Valid @RequestBody MaterialModelQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        if (!OperationContextHolder.isBackRole()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        if (query.getDistributorMasterId() != null) {
            List<UserVO> customers = distributorService.queryAllCustomersByDistributorMasterId(
                query.getDistributorMasterId());
            if (CollectionUtils.isNotEmpty(customers)) {
                query.setUserIds(customers.stream().map(UserVO::getId).collect(Collectors.toList()));
            } else {
                query.setUserIds(new ArrayList<>());
            }
        }

        fillRelatedToMeSearchCondition(query);

        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);

        //临时兼容10张精选图的用户
        PageInfo<MaterialModelVO> pageInfo = materialModelService.queryPageWithBlobs(query);
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            WorkflowTaskQuery workflowTaskQuery = new WorkflowTaskQuery();
            workflowTaskQuery.setBizIds(
                pageInfo.getList().stream().map(MaterialModelVO::getId).collect(Collectors.toList()));
            List<WorkflowTaskVO> tasks = workflowTaskService.queryWorkflowTaskList(workflowTaskQuery);
            String value = systemConfigService.queryValueByKey(SystemConstants.TEN_EXAMPLE_IMAGES_USERS);
            if (StringUtils.isNotBlank(value)) {
                List<Integer> userIds = JSONArray.parseArray(value, Integer.class);
                pageInfo.getList().forEach(model -> {
                    if (userIds.contains(model.getUserId())) {
                        model.addExtInfo("TEN_EXAMPLE_IMAGES", true);
                    }
                });
            }

            //兼容处理
            pageInfo.getList().forEach(model -> {
                if (model.getClothTypeConfigs() == null) {
                    model.setClothTypeConfigs(MaterialModelConverter.convert2ClothTypeConfig(model));
                }
                List<WorkflowTaskVO> filteredTasks = tasks.stream().filter(
                    task -> task.getBizId().equals(model.getId())).collect(Collectors.toList());
                model.setTasks(filteredTasks);
            });
        }
        return Result.success(pageInfo);
    }

    /**
     * 前台用户查询「全部资产」
     *
     * @param query
     * @return
     */
    @PostMapping("/queryByPage")
    @ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<MaterialModelDO>]")
    public Result<PageInfo<MaterialModelVO>> getMaterialModelByPage(@Valid @RequestBody MaterialModelQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        if (StringUtils.isBlank(query.getOrderBy())) {
            if (OperationContextHolder.isBackRole()) {
                query.setOrderBy(
                    "if(id in (select model_id from (select model_id from (select model_id,max(id) id from "
                    + "creative_batch where user_id = " + OperationContextHolder.getOperatorUserId()
                    + " and deleted != 1 group by model_id) t order by id desc " + "limit 4) s) , 1, 0) desc, id desc");
            } else {
                query.setOrderBy("id desc");
            }
        }

        //演示账号
        if (OperationContextHolder.getRoleType() == RoleTypeEnum.DEMO_ACCOUNT) {
            query.setOnlyShowDemo(true);
            query.setIsOwner(null);
        } else if (!OperationContextHolder.isBackRole() && !systemConfigService.isInJsonArray(
            SystemConstants.SEE_ALL_MODELS_AND_HISTORY, OperationContextHolder.getOperatorUserId())) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        } else {
            if (StringUtils.isNotBlank(query.getStatus())) {
                query.setStatusList(Lists.newArrayList(query.getStatus(), MaterialModelStatusEnum.TESTING.getCode()));
                query.setStatus(null);
            }

            String operatorMobile = OperationContextHolder.getContext().getOperationSession().getLoginUser()
                .getMobile();

            // 后台用户《我的服装》，同时返回跟进操作员的服装
            if (query.getIsOwner() != null && query.getIsOwner() && ADMIN_WHITE_LIST.contains(operatorMobile)) {
                query.setIsOwner(null);
                query.setRelatedOperatorType(
                    StringUtils.equals("18966484977", operatorMobile) ? "18717831174" : operatorMobile);

                query.setRelatedOrOperatorId(OperationContextHolder.getOperatorUserId());
            }
        }

        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);

        PageInfo<MaterialModelVO> pageInfo = materialModelService.queryMaterialModelByPage(query);
        pageInfo.setList(pruneForFront(pageInfo.getList()));

        return Result.success(pageInfo);
    }

    @PostMapping("/confirmTrainLora")
    public Result<?> confirmTrainLora(@RequestBody @Valid ConfirmLoraReq req) {
        materialModelService.confirmTrainLora(req, OperationContextHolder.getOperatorUserId(),
            OperationContextHolder.getOperatorNick());
        return Result.success();
    }

    @PostMapping("/confirmCanDeliver")
    public Result<?> confirmCanDeliver(@JsonArg @NotNull Integer loraId) {
        materialModelService.confirmCanDeliver(loraId, OperationContextHolder.getOperatorUserId(),
            OperationContextHolder.getOperatorNick());
        return Result.success();
    }

    /**
     * 重新抠图打标
     */
    @PostMapping("/cutoutAgain")
    public Result<?> cutoutAgain(@JsonArg @NotNull Integer id, @JsonArg String cutoutKeyword,
                                 @JsonArg Boolean prepareViewAgainWhenCutoutAgain, @JsonArg String imageSize,
                                 @JsonArg String captionPrompt, @JsonArg String waterMarkDesc,
                                 @JsonArg String preprocessCensoredFace, @JsonArg String cut4ScaleUp,
                                 @JsonArg Boolean isSquare, @JsonArg String cutoutOnlyUpscale,
                                 @JsonArg String clothDetailsPrompt, @JsonArg String labelType,
                                 @JsonArg Integer trainRepeatTimes, @JsonArg String noshowFace) {

        MaterialModelVO model = materialModelService.selectById(id);
        if (model != null) {
            MaterialModelVO target = new MaterialModelVO();
            target.setId(model.getId());
            target.setExtInfo(model.getExtInfo());

            LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();

            trainDetail.setCutoutAgain(true);
            trainDetail.setCutoutKeyword(cutoutKeyword);
            trainDetail.setCutoutAgainOperatorId(OperationContextHolder.getOperatorUserId());
            trainDetail.setCutoutAgainOperatorNick(OperationContextHolder.getOperatorNick());
            trainDetail.setCutoutAgainTime(DateUtils.formatTime(new Date()));
            if (StringUtils.isNotBlank(imageSize)) {
                trainDetail.setImageSize(imageSize);
            }
            trainDetail.setIsSquare(Boolean.TRUE.equals(isSquare) ? "1" : "0");

            if (StringUtils.isNotBlank(captionPrompt)) {
                captionPrompt = CommonUtil.unescapeLineBreak(captionPrompt);
                captionPrompt = ComfyUIUtils.parseParams(captionPrompt);
                trainDetail.setCaptionPrompt(captionPrompt);
            }
            if (StringUtils.isNotBlank(waterMarkDesc)) {
                trainDetail.setWaterMarkDesc(CommonUtil.unescapeLineBreak(waterMarkDesc));
            }

            if (Boolean.TRUE.equals(prepareViewAgainWhenCutoutAgain)) {
                trainDetail.setPrepareView(null);
            }

            //风格lora，不抠图，只缩放处理
            if (StringUtils.isNotBlank(cutoutOnlyUpscale)) {
                trainDetail.setCutoutOnlyUpscale(cutoutOnlyUpscale);
            }

            //衣服lora，抠图环节是否通过剪裁放大衣服比例
            if (StringUtils.isNotBlank(cut4ScaleUp)) {
                trainDetail.setCut4ScaleUp(cut4ScaleUp);
            }

            if (StringUtils.isNotBlank(clothDetailsPrompt)) {
                clothDetailsPrompt = CommonUtil.unescapeLineBreak(clothDetailsPrompt);
                clothDetailsPrompt = ComfyUIUtils.parseParams(clothDetailsPrompt);
                trainDetail.setClothDetailsPrompt(clothDetailsPrompt);
            }

            if (trainRepeatTimes != null) {
                Integer originRepeatTimes = trainDetail.getRepeatTimes();
                trainDetail.setRepeatTimes(trainRepeatTimes);
                String labelRetDir = trainDetail.getLabelRetDir();
                String materialType = trainDetail.getMaterialType();

                originRepeatTimes = originRepeatTimes == null ? (StringUtils.equals(materialType,
                    MaterialType.face.name()) ? 15 : 5) : originRepeatTimes;

                if (!originRepeatTimes.equals(trainRepeatTimes)) {
                    fileDispatch.renameFile(
                        MaterialModelUtils.buildLabelSubPath(labelRetDir, materialType, originRepeatTimes),
                        MaterialModelUtils.buildLabelSubPath(labelRetDir, materialType, trainRepeatTimes));
                }
            }

            trainDetail.setCutout(null);
            trainDetail.setLabel(null);
            trainDetail.setLora(null);

            trainDetail.setLoraConfirmed(null);
            trainDetail.setLoraConfirmedTime(null);

            trainDetail.setPreprocessCensoredFace(preprocessCensoredFace);

            //模特极简打标，激活码为空
            if (LabelTypeEnum.getByCode(labelType) == LabelTypeEnum.MINI && StringUtils.equals(
                trainDetail.getMaterialType(), MaterialType.face.name())) {
                trainDetail.setActivateKey("");
            } else {
                trainDetail.setActivateKey(
                    materialInfoService.buildActivateKey(trainDetail.getMaterialType(), labelType,
                        StringUtils.equals(YES, cutoutOnlyUpscale)));
            }

            //场景去水印（走的抠图流程）源目录
            trainDetail.setCutoutSourceDir(
                getCutoutSourceDir(trainDetail.getMaterialType(), preprocessCensoredFace, trainDetail));

            //打标去水印（走的预处理流程）源目录
            trainDetail.setLabelSourceDir(
                getLabelSourceDir(trainDetail.getMaterialType(), preprocessCensoredFace, waterMarkDesc, trainDetail));

            if (StringUtils.isNotBlank(noshowFace)) {
                trainDetail.setNoshowFace(noshowFace);
                MaterialModelUtils.initAppendInfo(trainDetail);
            }

            target.setClothLoraTrainDetail(trainDetail);
            target.addExtInfo(KEY_LABEL_TYPE, labelType);
            target.addExtInfo(KEY_IS_CUTOUT_AGAIN, YES);
            target.addExtInfo(KEY_CUTOUT_AGAIN_OPERATOR, OperationContextHolder.getOperatorUserId());

            if (model.getExtInfo(KEY_RELATED_OPERATOR, Integer.class) == null) {
                target.addExtInfo(KEY_RELATED_OPERATOR, OperationContextHolder.getOperatorUserId());
            }
            materialModelService.updateByIdSelective(target);
        }

        return Result.success();
    }

    private static String getLabelSourceDir(String materialType, String preprocessCensoredFace, String wasterMarkDesc,
                                            LoraTrainDetail trainDetail) {
        //目前只给场景在用
        if (!StringUtils.equals(materialType, MaterialType.scene.name())) {
            return null;
        }
        String labelSourceDir = trainDetail.getClothDir();
        if (StringUtils.equals(preprocessCensoredFace, CommonConstants.YES)) {
            labelSourceDir = trainDetail.getPrepareViewRetDir();
        }
        if (StringUtils.isNotBlank(wasterMarkDesc)) {
            labelSourceDir = trainDetail.getCutoutRetDir();
        }
        return labelSourceDir;
    }

    private static String getCutoutSourceDir(String materialType, String preprocessCensoredFace,
                                             LoraTrainDetail trainDetail) {
        //目前只给场景在用
        if (!StringUtils.equals(materialType, MaterialType.scene.name())) {
            return null;
        }
        String cutoutSourceDir = trainDetail.getClothDir();
        //如果白头处理，则使用views目录，否则是原图目录
        if (StringUtils.equals(preprocessCensoredFace, CommonConstants.YES)) {
            cutoutSourceDir = trainDetail.getPrepareViewRetDir();
        }
        return cutoutSourceDir;
    }

    /**
     * 为服装训练指定平台运营
     */
    @PostMapping("/assignPlatformOperator")
    public Result<?> assignPlatformOperator(@NotNull @JsonArg Integer id, @NotNull @JsonArg String operatorMobile) {
        MaterialModelVO model = materialModelService.selectById(id);
        if (model != null) {
            MaterialModelVO target = new MaterialModelVO();
            target.setId(model.getId());
            target.setExtInfo(model.getExtInfo());

            LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();

            UserQuery query = new UserQuery();
            query.setMobile(operatorMobile);
            List<UserVO> users = userService.queryUsers(query);
            if (CollectionUtils.isEmpty(users)) {
                return Result.failedWithMessage(ResultCode.BIZ_FAIL, "平台运营不存在");
            }

            trainDetail.setRelatedOperatorId(users.get(0).getId());
            trainDetail.setRelatedOperatorMobile(users.get(0).getMobile());
            trainDetail.setRelatedOperatorNick(users.get(0).getNickName());
            target.setClothLoraTrainDetail(trainDetail);

            materialModelService.updateByIdSelective(target);
        }

        return Result.success();
    }

    @PostMapping("/updateLabelFiles")
    public Result<Boolean> updateLabelFiles(@Valid @RequestBody LabelFileEditReq req) {

        materialModelService.updateLabelFiles(req);

        return Result.success(true);
    }

    /**
     * 更新服装模型扩展信息(该接口数据处理不影响正常业务)
     *
     * @param req 请求入参
     * @return 是否更新成功
     */
    @PostMapping("/updateExtInfo")
    public Result<Boolean> updateExtInfo(@Valid @RequestBody MaterialModelReq req) {
        // 更新扩展信息
        materialModelService.updateExtInfo(req);

        // 返回成功
        return Result.success();
    }

    /**
     * 更新审核员
     *
     * @return 是否更新成功
     */
    @PostMapping("/updateReviewer")
    public Result<Boolean> updateExtInfo(@JsonArg Integer id, @JsonArg Integer reviewerId) {
        MaterialModelVO materialModelVO = materialModelService.selectById(id);
        materialModelVO.addExtInfo(KEY_REVIEWER_ID, reviewerId);
        materialModelVO.addExtInfo(KEY_WORK_SCHEDULED_TIME, new Date());

        materialModelService.updateExtInfoByIdSelective(materialModelVO);
        return Result.success();
    }

    /**
     * 同步服装模型到图库中
     *
     * @param req 请求入参
     * @return 是否更新成功
     */
    @PostMapping("/syncToImageCase")
    public Result<Boolean> syncToImageCase(@Valid @RequestBody SyncImageCaseReq req) {
        // 同步服装模型到图库中
        materialModelService.syncToImageCase(req);

        // 返回成功
        return Result.success();
    }

    @PostMapping("/assignTo")
    public Result<?> assignTo(@NotNull @JsonArg Integer modelId, @NotNull @JsonArg Integer userId) {
        materialModelService.assignTo(modelId, userId);
        return Result.success();
    }

    /**
     * 将模特或场景lora，转移给指定客户
     */
    @PostMapping("/assignElementModelToUser")
    public Result<?> assignElementModelToUser(@NotNull @JsonArg Integer modelId, @NotNull @JsonArg Integer userId,
                                              @NotNull @JsonArg Boolean exclusive, @NotNull @JsonArg Boolean free) {
        materialModelService.assignElementModelToUser(modelId, userId, exclusive, free);
        return Result.success();
    }

    @PostMapping("/changeExampleImages")
    public Result<?> changeExampleImages(@Valid @RequestBody MaterialModelVO materialModel) {
        AssertUtil.assertNotNull(materialModel, "数据不能为空");
        AssertUtil.assertNotNull(materialModel.getId(), "id不能为空");
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(materialModel.getExampleImages()), "精选图不能为空");

        materialModelService.changeExampleImages(materialModel);
        return Result.success();
    }

    @PostMapping("/clearExampleImages")
    public Result<?> clearExampleImages(@NotNull @JsonArg Integer modelId) {
        materialModelService.clearExampleImages(modelId);
        return Result.success();
    }

    @PostMapping("/markCloth")
    public Result<?> markCloth(@JsonArg @NotNull Integer modelId, @JsonArg @NotBlank String markKey,
                               @JsonArg @NotBlank String markValue) {
        MaterialModelVO modelVO = materialModelService.selectById(modelId);
        if (modelVO == null) {
            return Result.failedWithMessage(ResultCode.BIZ_FAIL, "服装模型不存在");
        }
        JSONObject clothMark = modelVO.getExtInfo().getJSONObject(CommonConstants.KEY_CLOTH_MARK);
        if (clothMark == null) {
            clothMark = new JSONObject();
        }
        clothMark.put(markKey, markValue);
        modelVO.addExtInfo(CommonConstants.KEY_CLOTH_MARK, clothMark.toJSONString());
        materialModelService.updateByIdSelective(modelVO);
        return Result.success();
    }

    @PostMapping("/queryDetailShowImage")
    public Result<?> queryDetailShowImage(@NotNull @JsonArg Integer modelId) {
        return Result.success(materialModelService.queryDetailShowImage(modelId));
    }

    @PostMapping("/updateColorImage")
    public Result<?> updateColorImage(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer index,
                                      @NotNull @JsonArg String imgUrl) {
        materialModelService.updateColorImage(id, index, imgUrl);
        return Result.success();
    }

    @PostMapping("/enableColor")
    public Result<?> enableColor(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer index,
                                 @NotNull @JsonArg Boolean enable) {
        materialModelService.enableColor(id, index, enable);
        return Result.success();
    }

    @PostMapping("/batchModifyGarment")
    public Result<?> batchModifyGarment(@NotNull @JsonArg String ids) {
        //TODO by半泉:临时批量订正使用
        materialModelService.batchModifyGarment(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/supplyColor")
    public Result<?> supplyColor(@NotNull @JsonArg String ids) {
        //TODO by半泉:临时批量订正使用
        materialModelService.supplyColor(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchUploadOss")
    public Result<?> batchUploadOss(@NotNull @JsonArg String ids) {
        materialModelService.batchUploadOss(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchCreateTestImages")
    public Result<?> batchCreateTestImages(@NotNull @JsonArg String ids) {
        materialModelService.batchCreateTestImages(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchRelabelLora")
    public Result<?> batchRelabelLora(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            materialModelService.retrainLora(Integer.parseInt(s), false, null, null);
        }
        return Result.success();
    }

    @PostMapping("/batchInitTrainedModel")
    public Result<?> batchInitTrainedModel(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            materialModelService.reInitTrainedModel(Integer.parseInt(s));
        }
        return Result.success();
    }

    @PostMapping("/modifyModelName")
    public Result<?> modifyModelName(@NotNull @JsonArg Integer id, @NotNull @JsonArg String name) {
        MaterialModelVO model = new MaterialModelVO();
        model.setId(id);
        model.setName(name);
        materialModelService.innerUpdate(model);
        return Result.success();
    }

    @PostMapping("/addDemoTag")
    @ApiOperation(value = "添加演示标记", notes = "添加演示标记")
    public Result<?> addDemoTag(@JsonArg @NotNull Integer id) {
        materialModelService.addDemoTag(id);
        return Result.success();
    }

    @PostMapping("/updateExperimental")
    @ApiOperation(value = "更新素材实验状态", notes = "更新素材实验状态")
    public Result<?> updateExperimental(@JsonArg Integer id, @JsonArg boolean experimental) {
        materialModelService.updateExperimental(id, experimental);
        return Result.success();
    }

    @PostMapping("/querySubIds")
    @ApiOperation(value = "查询所有子模型id", notes = "查询所有子模型id")
    public Result<?> querySubIds(@JsonArg Integer id) {
        return Result.success(materialModelService.querySubIds(id));
    }

    @GetMapping("/statsQueuedModel")
    @ApiOperation(value = "统计等待队列中的模型数据", notes = "统计等待队列中的模型数据")
    public Result<?> statsQueuedModel() {
        return Result.success(materialModelService.statsQueuedModel());
    }

    /**
     * 前台用户数据剪枝
     *
     * @param list 数据列表
     * @return 剪枝后数据
     */
    @SuppressWarnings({"unchecked"})
    public static <T extends MaterialModelVO> List<T> pruneForFront(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        list.forEach(e -> {
            String userPreferFeaturesStr = e.getExtInfo(CommonConstants.userPreferFeatures, String.class);
            if (StringUtils.isNotBlank(userPreferFeaturesStr)) {
                UserClothMatchingPreference preference = JSONObject.parseObject(userPreferFeaturesStr,
                    UserClothMatchingPreference.class);
                e.setClothCollocation(preference != null ? preference.getOriginClothCollocation() : null);
            }
            if (e.getClothLoraTrainDetail() != null && e.getClothLoraTrainDetail().getOriginalMaterialId() != null) {
                e.setMaterialInfoId(e.getClothLoraTrainDetail().getOriginalMaterialId());
            }

            e.setMaterialType(null);

            List<ClothTypeConfig> clothTypeConfigs = e.getClothTypeConfigs() != null ? e.getClothTypeConfigs()
                : MaterialModelConverter.convert2ClothTypeConfig(e);
            List<String> colorImages = e.getExtInfo(KEY_CLOTH_COLOR_IMAGES, List.class);

            fillClothTypeConfigInfo(e, clothTypeConfigs, colorImages);

            e.setTags(null);
            e.setClothLoraTrainDetail(null);
            e.setExtInfo(SecurityUtils.clearWithWhiteList(e.getExtInfo(), outputKeyWhitelist));
            e.setLoraName(null);
            //bugfix: 【这几个字段需要保留】，在前端的「全部资产」，资产详情页展示时需要使用，因此注释掉这两行
            //e.setMaterialInfoId(null);
            //e.setOperatorNick(null);
            //e.setUserId(null);
            e.setOperatorId(null);
            if (!OperationContextHolder.isDistributorRole()) {
                e.setUserNick(null);
            }
        });

        return list;
    }

    /**
     * 填充服装类型配置信息
     *
     * @param model            模型
     * @param clothTypeConfigs 服装类型配置
     * @param colorImages      颜色图片列表
     */
    private static void fillClothTypeConfigInfo(MaterialModelVO model, List<ClothTypeConfig> clothTypeConfigs,
                                                List<String> colorImages) {
        //设置颜色列表
        if (CollectionUtils.isEmpty(clothTypeConfigs)) {
            return;
        }

        List<ClothTypeConfig> result = new ArrayList<>();
        for (ClothTypeConfig each : clothTypeConfigs) {
            List<ClothColorDetail> filter = each.getColorList().stream().filter(ClothColorDetail::isEnable).collect(
                Collectors.toList());
            if (CollectionUtils.isEmpty(filter)) {
                continue;
            }

            ClothTypeConfig config = new ClothTypeConfig();
            config.setType(each.getType());

            List<ClothColorDetail> colorList = new ArrayList<>();

            filter.forEach(color -> {
                ClothColorDetail detail = new ClothColorDetail(color.getIndex(), null, null);

                int index = detail.getIndex();
                if (CollectionUtils.isNotEmpty(colorImages) && index <= CollectionUtils.size(colorImages)) {
                    detail.setShowImg(colorImages.get(index - 1));
                }
                colorList.add(detail);
            });

            config.setColorList(colorList);

            result.add(config);
        }
        model.setClothTypeConfigs(result);

        for (ClothTypeConfig clothTypeConfig : clothTypeConfigs) {
            //在2024-10-26 13:00:00之前创建的模型，默认只支持全身
            if (!OperationContextHolder.isBackRole() && !DateUtils.after(
                Objects.requireNonNull(DateUtils.parseSimple("2024-10-23 14:00:00")), model.getCreateTime(), 0)) {
                break;
            }

            CameraAngleEnum bodyPosition = CameraAngleEnum.getBodyPositionByStr(clothTypeConfig.getType());
            if (StringUtils.isBlank(model.getHalfBody()) && bodyPosition != CameraAngleEnum.WHOLE_BODY) {
                model.setHalfBody(bodyPosition.getCode());
            }
        }
    }

    /**
     * 审核员和工程师筛选项
     *
     * @param query 查询条件
     */
    private void fillRelatedToMeSearchCondition(MaterialModelQuery query) {
        if (query.isRelatedToMe()) {
            query.setReviewerId(OperationContextHolder.getOperatorUserId());
            query.setPromptUserId(OperationContextHolder.getOperatorUserId());
        }
        if (query.getPromptUserId() != null) {
            // 筛选工程师是当前用户的，工程师绑定客户，根据客户筛选
            UserProfileQuery userProfileQuery = new UserProfileQuery();
            userProfileQuery.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
            userProfileQuery.setProfileVal(query.getPromptUserId().toString());
            List<UserProfileVO> profiles = userProfileService.queryUserProfileList(userProfileQuery);
            List<Integer> userIds = profiles.stream().map(UserProfileVO::getUid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                query.setId(0);
            } else {
                query.setUserIds(userIds);
            }
        }
    }
}