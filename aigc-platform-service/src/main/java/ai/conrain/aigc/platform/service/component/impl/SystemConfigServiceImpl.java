package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.SystemConfigDAO;
import ai.conrain.aigc.platform.dal.entity.SystemConfigDO;
import ai.conrain.aigc.platform.service.acm.AcmConfigService;
import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ConfigStatusEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.biz.MachineInfo;
import ai.conrain.aigc.platform.service.model.biz.MachinePort;
import ai.conrain.aigc.platform.service.model.biz.MachineRoom;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.SystemConfigConverter;
import ai.conrain.aigc.platform.service.model.vo.ClothTypeScopeItem;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.AUTO_TRAIN_MERCHANT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.COMFYUI_PORT_CFG;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.COMFYUI_PORT_CFG_LORA;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.COMFYUI_PORT_CFG_LORA_MATERIAL_ID;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.MODERATION_FORBID_LABEL;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.NIGHTTIME_AUTO_TRAIN_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TEXT_MODERATION_FORBID_LABEL;

/**
 * SystemConfigService实现
 *
 * <AUTHOR>
 * @version SystemConfigService.java v 0.1 2024-01-20 01:21:37
 */
@Slf4j
@Service
public class SystemConfigServiceImpl extends AbstractCachedService<SystemConfigVO, String, String>
    implements SystemConfigService {
    /** DAO */
    @Autowired
    private SystemConfigDAO systemConfigDAO;
    @Lazy
    @Autowired
    private AcmConfigService acmConfigService;
    @Lazy
    @Autowired
    private UserService userService;
    @Lazy
    @Autowired
    private ServerService serverService;

    @Override
    public SystemConfigVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        SystemConfigDO data = systemConfigDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return SystemConfigConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = systemConfigDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除SystemConfig失败");

        //强制刷新
        forceRefresh();
    }

    @Override
    public SystemConfigVO insert(SystemConfigVO systemConfig) {
        AssertUtil.assertNotNull(systemConfig, ResultCode.PARAM_INVALID, "systemConfig is null");
        AssertUtil.assertTrue(systemConfig.getId() == null, ResultCode.PARAM_INVALID, "systemConfig.id is present");

        //创建时间、修改时间兜底
        if (systemConfig.getCreateTime() == null) {
            systemConfig.setCreateTime(new Date());
        }

        if (systemConfig.getModifyTime() == null) {
            systemConfig.setModifyTime(new Date());
        }

        SystemConfigDO data = SystemConfigConverter.vo2DO(systemConfig);
        int n = systemConfigDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建SystemConfig失败");
        AssertUtil.assertNotNull(data.getId(), "新建SystemConfig返回id为空");
        systemConfig.setId(data.getId());

        refresh();

        return systemConfig;
    }

    @Override
    public void updateById(SystemConfigVO systemConfig) {
        AssertUtil.assertNotNull(systemConfig, ResultCode.PARAM_INVALID, "systemConfig is null");
        AssertUtil.assertTrue(systemConfig.getId() != null, ResultCode.PARAM_INVALID, "systemConfig.id is null");
        //修改时间必须更新
        systemConfig.setModifyTime(new Date());

        SystemConfigDO data = SystemConfigConverter.vo2DO(systemConfig);
        int n = systemConfigDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新SystemConfig失败，影响行数:" + n);

        refresh();
    }

    @Override
    public boolean queryBoolValue(String key, boolean defaultValue) {
        String config = queryValueByKey(key);
        return StringUtils.isBlank(config) ? defaultValue : Boolean.parseBoolean(config);
    }

    @Override
    public int queryIntValue(String key, int defaultValue) {
        String config = queryValueByKey(key);
        return StringUtils.isBlank(config) ? defaultValue : Integer.parseInt(config);
    }

    @Override
    public BigDecimal queryBigDecimalValue(String key, BigDecimal defaultValue) {
        String config = queryValueByKey(key);
        return StringUtils.isBlank(config) ? defaultValue : new BigDecimal(config);
    }

    @Override
    public long queryLongValue(String key, long defaultValue) {
        String config = queryValueByKey(key);
        return StringUtils.isBlank(config) ? defaultValue : Long.parseLong(config);
    }

    @Override
    public JSONObject queryJsonValue(String key) {
        String config = queryValueByKey(key);
        return StringUtils.isBlank(config) ? new JSONObject() : JSONObject.parseObject(config);
    }

    @Override
    public JSONArray queryJsonArrValue(String key) {
        String config = queryValueByKey(key);
        return StringUtils.isBlank(config) ? new JSONArray() : JSONArray.parseArray(config);
    }

    @Override
    public boolean isInJsonArray(String key, Object value) {
        JSONArray array = queryJsonArrValue(key);
        return array.contains(value);
    }

    @Override
    public String queryComfyUIPort(Integer userId) {
        String config = queryValueByKey(COMFYUI_PORT_CFG);

        if (StringUtils.isBlank(config)) {
            return null;
        }
        JSONObject json = JSONObject.parseObject(config);

        return json.getString(userId.toString());
    }

    @Override
    public String queryComfyUILoraPort(Integer userId, Integer clothMaterialId, boolean isLoraTask) {

        //优先使用 clothMaterialId
        if (clothMaterialId != null) {
            //素材id有指定端口时，以该端口为准
            String portCfgByMaterialId = queryValueByKey(COMFYUI_PORT_CFG_LORA_MATERIAL_ID);
            if (StringUtils.isNotBlank(portCfgByMaterialId) && CommonUtil.isValidJson(portCfgByMaterialId)) {
                JSONObject materialIdCfg = JSONObject.parseObject(portCfgByMaterialId);

                //衣服id vip规则
                if (materialIdCfg.containsKey(clothMaterialId.toString())) {
                    log.info("queryComfyUILoraPort,按配置的素材id对应端口为准，clothMaterialId:{}", clothMaterialId);
                    return materialIdCfg.getString(clothMaterialId.toString());

                    //普通衣服
                } else {

                    //短任务，按短任务分流 short_task_port_0/short_task_port_1
                    if (!isLoraTask) {
                        int mod = clothMaterialId % 2;
                        String portId = "short_task_port_" + mod;
                        if (materialIdCfg.containsKey(portId)) {
                            log.info(
                                "queryComfyUILoraPort,按配置的素材id余数规则走对应端口，clothMaterialId:{}, portId:{}",
                                clothMaterialId, portId);
                            return materialIdCfg.getString(portId);
                        }
                    }

                    //lora任务走port_0/port_1，同时为短作业未配置端口时兼容兜底
                    int mod = clothMaterialId % 2;
                    String portId = "port_" + mod;
                    if (materialIdCfg.containsKey(portId)) {
                        log.info("queryComfyUILoraPort,按配置的素材id余数规则走对应端口，clothMaterialId:{}, portId:{}",
                            clothMaterialId, portId);
                        return materialIdCfg.getString(portId);
                    }
                }
            }
        }

        //旧的userId配置
        String config = queryValueByKey(COMFYUI_PORT_CFG_LORA);

        if (StringUtils.isBlank(config)) {
            return null;
        }
        JSONObject json = JSONObject.parseObject(config);

        return json.getString(userId.toString());
    }

    @Override
    public List<Integer> querySamePortUser(Integer userId) {
        JSONObject json = getComfyuiPortJson();
        if (json == null) {return null;}

        if (!json.containsKey(userId.toString())) {
            return null;
        }

        List<Integer> userIds = new ArrayList<>();
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            if (entry.getValue().equals(json.getString(userId.toString()))) {
                userIds.add(Integer.valueOf(entry.getKey()));
            }
        }
        return userIds;
    }

    @Override
    public List<List<Integer>> querySamePortUsers() {
        JSONObject json = getComfyuiPortJson();
        if (json == null) {return null;}

        Map<String, List<Integer>> portMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            String port = entry.getValue().toString();
            portMap.putIfAbsent(port, new ArrayList<>());
            portMap.get(port).add(Integer.valueOf(entry.getKey()));
        }
        return new ArrayList<>(portMap.values());

    }

    @Override
    public List<String> queryTextModerationForbidLabel() {
        String config = queryValueByKey(TEXT_MODERATION_FORBID_LABEL);
        if (StringUtils.isBlank(config)) {
            return null;
        }
        String[] split = config.split(",");
        return Arrays.asList(split);
    }

    @Override
    public List<String> queryModerationForbidLabel() {
        String config = queryValueByKey(MODERATION_FORBID_LABEL);
        if (StringUtils.isBlank(config)) {
            return null;
        }

        String[] split = config.split(",");
        return Arrays.asList(split);
    }

    @Override
    public SystemConfigVO queryByKey(String key) {
        SystemConfigVO item = super.queryByKey(key);

        if (null == item) {
            log.error("查询系统参数异常，请检查配置，key={}", key);
            return null;
        }

        ConfigStatusEnum status = item.getStatus();

        // 待变更状态且已超过生效时间，取待变更值，同时变更配置值和状态
        if (status == ConfigStatusEnum.PENDING_CHANGE && DateUtils.beforeNow(item.getEffectTime())) {

            //变更本地缓存状态
            item.setConfValue(item.getConfValueNext());
            item.setStatus(ConfigStatusEnum.ACTIVE);

            //变更数据库状态
            SystemConfigDO systemConfigDO = SystemConfigConverter.vo2DO(item);
            systemConfigDAO.updateByPrimaryKey(systemConfigDO);

            return item;
        }

        return item;
    }

    @Override
    protected List<SystemConfigVO> loadAll() {
        List<SystemConfigDO> list = systemConfigDAO.selectAll();

        List<SystemConfigVO> result = new ArrayList<>(list.size());

        for (SystemConfigDO each : list) {
            ConfigStatusEnum status = ConfigStatusEnum.getByCode(each.getStatus());

            if (ConfigStatusEnum.INVALID == status) {
                continue;
            }

            result.add(SystemConfigConverter.do2VO(each));
        }

        return result;
    }

    @Override
    protected String getKey(SystemConfigVO cache) {
        return cache.getConfKey();
    }

    @Override
    protected String getValue(SystemConfigVO cache) {
        if (cache != null) {
            return cache.getConfValue();
        }

        return null;
    }

    @Nullable
    private JSONObject getComfyuiPortJson() {
        String config = queryValueByKey(COMFYUI_PORT_CFG);
        if (StringUtils.isBlank(config)) {
            return null;
        }

        JSONObject json = JSONObject.parseObject(config);
        if (MapUtils.isEmpty(json)) {
            return null;
        }
        return json;
    }

    /**
     * 获取分销商结算费率
     *
     * @param distributorCorpId 分销商id
     * @return 分销商结算费率
     */
    @Override
    public BigDecimal querySettleRate(Integer distributorCorpId) {
        AssertUtil.assertNotNull(distributorCorpId, ResultCode.PARAM_INVALID, "distributorCorpId is null");
        String serviceRate = queryValueByKey(SystemConstants.DISTRIBUTOR_SETTLE_RATE_PREFIX + distributorCorpId);
        if (StringUtils.isNotBlank(serviceRate)) {
            return new BigDecimal(serviceRate);
        }
        DingTalkNoticeHelper.sendMsg2DevGroup(
            String.format("获取结算费率失败,distributorCorpId:%s,traceId:%s", distributorCorpId, MDC.get("traceId")));
        throw new RuntimeException("获取结算费率失败distributorCorpId：" + distributorCorpId);
    }

    /**
     * 获取分销商结算费率配置
     *
     * @param distributorCorpId
     * @return
     */
    @Override
    public SystemConfigVO querySettleRateCfgByCorpId(Integer distributorCorpId) {
        AssertUtil.assertNotNull(distributorCorpId, ResultCode.PARAM_INVALID, "distributorCorpId is null");
        return queryByKey(SystemConstants.DISTRIBUTOR_SETTLE_RATE_PREFIX + distributorCorpId);
    }

    /**
     * 查询服装类型范围配置
     *
     * @return
     */
    @Override
    public List<ClothTypeScopeItem> queryClothTypeScopeCfg() {
        JSONArray arr = this.queryJsonArrValue(SystemConstants.SCENE_CLOTH_SCOPE_CFG);
        if (arr != null) {
            return arr.toJavaList(ClothTypeScopeItem.class);
        }
        return null;
    }

    @Override
    public List<MachineRoom> queryDeviceInfo() {
        return queryJsonArrValue(SystemConstants.DEVICE_INFO_CONFIG).toJavaList(MachineRoom.class);
    }

    @Override
    public void updateDeviceInfo(List<MachineRoom> rooms) {
        if (CollectionUtils.isEmpty(rooms)) {
            return;
        }

        //校验并生成各级编号
        for (MachineRoom room : rooms) {
            initMachineId(room);
        }

        SystemConfigVO systemConfig = queryByKey(SystemConstants.DEVICE_INFO_CONFIG);
        systemConfig.setConfValue(JSON.toJSONString(rooms));
        updateById(systemConfig);

        log.info("更新设备信息后，强制刷新服务器信息");
        serverService.forceRefresh();
    }

    @Override
    public String queryServerUrlByPort(String portId, boolean isPublic) {
        List<MachineRoom> machineRooms = queryDeviceInfo();

        String[] split = StringUtils.split(portId, "-");

        MachineRoom room = machineRooms.stream().filter(e -> StringUtils.equals(e.getId(), split[0])).findFirst()
            .orElse(null);

        if (room == null) {
            return null;
        }

        if (CollectionUtils.isEmpty(room.getMachines())) {
            return null;
        }

        MachineInfo machine = room.getMachines().stream().filter(
            e -> StringUtils.equals(e.getId(), split[0] + "-" + split[1])).findFirst().orElse(null);

        if (machine == null) {
            return null;
        }

        if (CollectionUtils.isEmpty(machine.getPorts())) {
            return null;
        }

        MachinePort port = machine.getPorts().stream().filter(e -> StringUtils.equals(portId, e.getId())).findFirst()
            .orElse(null);

        if (port == null) {
            return null;
        }

        return "http://" + (isPublic ? machine.getPublicAddress() : machine.getInternalAddress()) + ":"
               + port.getPort();
    }

    @Override
    public boolean isAutoTrain(Integer userId) {
        if (userId == null) {
            return false;
        }

        if (isInJsonArray(AUTO_TRAIN_MERCHANT, userId)) {
            log.info("判断是否进行自动训练，命中商家自动训练白名单，{}，返回true", userId);
            return true;
        }

        //晚8点半以后到早9，根据夜间运行的开关执行白名单
        Date now = new Date();
        //当天的20:30点之后，或者当天09点之前
        if (DateUtils.compare(now, DateUtils.parseSimple(DateUtils.formatSimpleDate(now) + " 20:30:00")) >= 0
            || DateUtils.compare(now, DateUtils.parseSimple(DateUtils.formatSimpleDate(now) + " 09:00:00")) < 0) {

            String value = queryValueByKey(NIGHTTIME_AUTO_TRAIN_SWITCH);
            if (StringUtils.equalsIgnoreCase("all", value)) {
                log.info("判断是否进行自动训练，命中夜间自动训练白名单，{},all，返回true", userId);
                return true;
            }

            if (StringUtils.equalsIgnoreCase("vip", value)) {
                boolean vip = userService.isVipWithoutBackUser(userId);
                if (vip) {
                    log.info("判断是否进行自动训练，命中夜间自动训练白名单，{},vip，返回true", userId);
                }
                return vip;
            }

        }

        log.info("判断是否进行自动训练，未命中自动训练，{}，返回false", userId);
        return false;
    }

    /**
     * 初始化设备的id
     *
     * @param room 设备机房
     */
    private void initMachineId(MachineRoom room) {
        //校验并生成各级编号
        if (StringUtils.isBlank(room.getId())) {
            //生成6位包含大小写字母和数字的字符串
            room.setId(buildMachineUUID(null));
        }

        if (CollectionUtils.isEmpty(room.getMachines())) {
            return;
        }

        for (MachineInfo machine : room.getMachines()) {
            if (StringUtils.isBlank(machine.getId())) {
                //生成6位包含大小写字母和数字的字符串
                machine.setId(buildMachineUUID(room.getId()));
            }

            if (CollectionUtils.isEmpty(machine.getPorts())) {
                continue;
            }

            for (MachinePort port : machine.getPorts()) {
                if (StringUtils.isBlank(port.getId())) {
                    port.setId(buildMachineUUID(machine.getId()));
                }
            }
        }
    }

    /**
     * 生成机器的UUID
     *
     * @param parentId 父节点Id
     * @return 机器的UUID
     */
    private String buildMachineUUID(String parentId) {
        return (StringUtils.isNotBlank(parentId) ? parentId + "-" : "") + RandomStringUtils.randomAlphanumeric(6);
    }
}