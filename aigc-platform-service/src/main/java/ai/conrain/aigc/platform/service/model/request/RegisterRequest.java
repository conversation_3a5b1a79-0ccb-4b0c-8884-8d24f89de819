package ai.conrain.aigc.platform.service.model.request;

import javax.validation.constraints.NotBlank;

import ai.conrain.aigc.platform.service.validation.Mobile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 注册请求
 */
@Data
public class RegisterRequest {

    /** 企业名称 */
    @ApiModelProperty(name = "corpName", value = "企业名称")
    @NotBlank(message = "企业名称不能为空！")
    private String corpName;

    /** 手机号 */
    @ApiModelProperty(name = "mobile", value = "手机号")
    @NotBlank(message = "手机号不能为空！")
    @Mobile
    private String mobile;

    /** 昵称 */
    @ApiModelProperty(name = "nickName", value = "昵称")
    private String nickName;

    /** 验证码 */
    @ApiModelProperty(name = "code", value = "短信验证码")
    @NotBlank(message = "短信验证码不能为空！")
    private String code;

}
