<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.UserDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.UserDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="login_id" jdbcType="VARCHAR" property="loginId" />
    <result column="pswd" jdbcType="VARCHAR" property="pswd" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="role_type" jdbcType="VARCHAR" property="roleType" />
    <result column="custom_role" jdbcType="VARCHAR" property="customRole" />
    <result column="corp_org_id" jdbcType="INTEGER" property="corpOrgId" />
    <result column="user_review_info" jdbcType="VARCHAR" property="userReviewInfo"/>
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="master_id" jdbcType="INTEGER" property="masterId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="register_from" jdbcType="VARCHAR" property="registerFrom" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="login_fail_count" jdbcType="INTEGER" property="loginFailCount" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="last_visit_date" jdbcType="VARCHAR" property="lastVisitDate" />
    <result column="contract_date" jdbcType="VARCHAR" property="contractDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, nick_name, real_name, login_id, pswd, mobile, role_type, user_type, master_id, 
    status, operator_id, register_from, memo, login_fail_count, last_login_time, deleted,
    create_time, modify_time, custom_role, corp_name, corp_org_id, user_review_info, last_visit_date
  </sql>
  <sql id="View_Column_List">
    muse_point,total_topup_amount,visit_in_15days,related_distributor_master_userId,prompt_engineer_user_id, related_corp_name,
    related_distributor_sales_userId, related_sales_user_name, related_distributor_corp_id, related_distributor_corp_name,
    contract_date
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />,<include refid="View_Column_List" />
    from user_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />,<include refid="View_Column_List" />
    from user_view
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />,<include refid="View_Column_List" />
    from user_view
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.UserDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user (nick_name, real_name, login_id, 
      pswd, mobile, role_type, custom_role, corp_org_id, user_review_info,
      user_type, master_id, status, 
      operator_id, register_from, memo,
      login_fail_count, last_login_time, deleted, 
      create_time, modify_time, last_visit_date)
    values (#{nickName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, #{loginId,jdbcType=VARCHAR}, 
      #{pswd,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{roleType,jdbcType=VARCHAR}, #{customRole}, #{corpOrgId}, #{userReviewInfo},
      #{userType,jdbcType=VARCHAR}, #{masterId,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{operatorId,jdbcType=INTEGER}, #{registerFrom,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR},
      #{loginFailCount,jdbcType=INTEGER}, #{lastLoginTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{lastVisitDate})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="loginId != null">
        login_id,
      </if>
      <if test="pswd != null">
        pswd,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="customRole != null">
        custom_role,
      </if>
      <if test="corpOrgId != null">
        corp_org_id,
      </if>
      <if test="userReviewInfo != null">
        user_review_info,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="masterId != null">
        master_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="registerFrom != null">
        register_from,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="loginFailCount != null">
        login_fail_count,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="lastVisitDate != null">
        last_visit_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="pswd != null">
        #{pswd,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="customRole != null">
        #{customRole},
      </if>
      <if test="corpOrgId != null">
        #{corpOrgId,jdbcType=INTEGER},
      </if>
      <if test="userReviewInfo != null">
        #{userReviewInfo},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="masterId != null">
        #{masterId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="registerFrom != null">
        #{registerFrom,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="loginFailCount != null">
        #{loginFailCount,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastVisitDate != null">
        #{lastVisitDate},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserExample" resultType="java.lang.Long">
    select count(*) from user_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.nickName != null">
        nick_name = #{record.nickName,jdbcType=VARCHAR},
      </if>
      <if test="record.realName != null">
        real_name = #{record.realName,jdbcType=VARCHAR},
      </if>
      <if test="record.loginId != null">
        login_id = #{record.loginId,jdbcType=VARCHAR},
      </if>
      <if test="record.pswd != null">
        pswd = #{record.pswd,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=VARCHAR},
      </if>
      <if test="record.customRole != null">
        custom_role = #{record.customRole},
      </if>
      <if test="record.corpOrgId != null">
        corp_org_id = #{record.corpOrgId,jdbcType=INTEGER},
      </if>
      <if test="record.userReviewInfo != null">
        user_review_info = #{record.userReviewInfo},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.masterId != null">
        master_id = #{record.masterId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.registerFrom != null">
        register_from = #{record.registerFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.loginFailCount != null">
        login_fail_count = #{record.loginFailCount,jdbcType=INTEGER},
      </if>
      <if test="record.lastLoginTime != null">
        last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastVisitDate != null">
        last_visit_date = #{record.lastVisitDate},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user
    set id = #{record.id,jdbcType=INTEGER},
      nick_name = #{record.nickName,jdbcType=VARCHAR},
      real_name = #{record.realName,jdbcType=VARCHAR},
      login_id = #{record.loginId,jdbcType=VARCHAR},
      pswd = #{record.pswd,jdbcType=VARCHAR},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      role_type = #{record.roleType,jdbcType=VARCHAR},
      custom_role = #{record.customRole},
      corp_org_id = #{record.corpOrgId,jdbcType=INTEGER},
      user_review_info = #{record.userReviewInfo},
      user_type = #{record.userType,jdbcType=VARCHAR},
      master_id = #{record.masterId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      register_from = #{record.registerFrom,jdbcType=VARCHAR},
      memo = #{record.memo,jdbcType=VARCHAR},
      login_fail_count = #{record.loginFailCount,jdbcType=INTEGER},
      last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      last_visit_date = #{record.lastVisitDate}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserDO">
    update user
    <set>
      <if test="nickName != null">
        nick_name = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        login_id = #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="pswd != null">
        pswd = #{pswd,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="customRole != null">
        custom_role = #{customRole},
      </if>
      <if test="corpOrgId != null">
        corp_org_id = #{corpOrgId,jdbcType=INTEGER},
      </if>
      <if test="userReviewInfo != null">
        user_review_info = #{userReviewInfo},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="masterId != null">
        master_id = #{masterId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="registerFrom != null">
        register_from = #{registerFrom,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="loginFailCount != null">
        login_fail_count = #{loginFailCount,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastVisitDate != null">
        last_visit_date = #{lastVisitDate},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.UserDO">
    update user
    set nick_name = #{nickName,jdbcType=VARCHAR},
      real_name = #{realName,jdbcType=VARCHAR},
      login_id = #{loginId,jdbcType=VARCHAR},
      pswd = #{pswd,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      role_type = #{roleType,jdbcType=VARCHAR},
      custom_role = #{customRole},
      corp_org_id = #{corpOrgId,jdbcType=INTEGER},
      user_review_info = #{userReviewInfo},
      user_type = #{userType,jdbcType=VARCHAR},
      master_id = #{masterId,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      register_from = #{registerFrom,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      login_fail_count = #{loginFailCount,jdbcType=INTEGER},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      last_visit_date = #{lastVisitDate}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update user set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryAllMasterMetaInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select a.id, a.role_type, nick_name, org.name as relatedCorpName, corp_org_id
    from user a left join
      (
        select user_id, max(create_time) create_time
        from material_model group by user_id
      ) b on a.id = b.user_id
    left join `organization` org on a.corp_org_id = org.`id`
    where a.deleted = 0
        and a.user_type = 'MASTER'
    <if test="roleTypes == null">
        and a.role_type = 'MERCHANT'
    </if>
        and a.role_type in
        <foreach collection="roleTypes" item="role" open="(" separator="," close=")">
          #{role,jdbcType=VARCHAR}
        </foreach>
    order by b.create_time desc;
  </select>

  <select id="queryAllByRoleTypes"  parameterType="java.lang.String" resultMap="BaseResultMap">
    select a.id, a.role_type, nick_name, org.name as relatedCorpName, corp_org_id
    from user a left join `organization` org on a.corp_org_id = org.`id`
    where a.deleted = 0
    <if test="roleTypes == null">
      and a.role_type = 'MERCHANT'
    </if>
    and a.role_type in
    <foreach collection="roleTypes" item="role" open="(" separator="," close=")">
      #{role,jdbcType=VARCHAR}
    </foreach>
    order by a.create_time desc;
  </select>


  <select id="selectIdByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserExample" resultType="java.lang.Integer">
    select
    <if test="distinct">
      distinct
    </if>
    id
    from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>

  <select id="queryAllVipOrPaidMasterUsers" resultType="ai.conrain.aigc.platform.dal.entity.UserDO">
    select distinct <include refid="Base_Column_List" /> from `user`
    where user_type = 'MASTER' and deleted = 0 and (memo like '%VIP%' or `id` in (
      select distinct(master_user_id) from `order_info` where deleted = 0
    ))
  </select>

  <select id="isVipOrPaidMasterUser" parameterType="java.lang.Integer" resultType="java.lang.Boolean">
    select if(count(*)>0,1,0)
    from `user`
    where user_type = 'MASTER' and deleted = 0
      and id = #{id,jdbcType=INTEGER}
      and (memo like '%VIP%' or `id` in (
        select distinct(master_user_id) from `order_info` where deleted = 0 and master_user_id = #{id,jdbcType=INTEGER}
      ))
  </select>

  <select id="isCustomer" parameterType="java.lang.Integer" resultType="java.lang.Boolean">
    select if(count(*)>0,1,0)
    from `user_vip_view`
    where user_id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectHighPayUser" resultType="java.lang.Integer">
    SELECT
      user_id
    FROM
      user_vip_view
    WHERE
      vip_3999 = 1
  </select>

  <select id="queryBefore60Days" parameterType="string" resultType="java.util.Map">
    select related_distributor_sales_userId sales_id, related_distributor_corp_name channel_name, count(*) sleep_cnt
    from user_view a
           left join user_vip_view b on a.id = b.user_id
    where deleted != 1 and status = 'ENABLED'
    and user_type = 'MASTER'
    and DATEDIFF(STR_TO_DATE(#{endDate}, '%Y-%m-%d %H:%i:%s'), create_time) > 60
    and b.vip = 0
    group by related_distributor_sales_userId, related_distributor_corp_name
  </select>

  <select id="queryActiveUser" resultType="java.util.Map">
      select a.user_id,
             if(batch_cnt > 0 or cloth_cnt > 0, true, false) is_active,
             related_distributor_sales_userId                sales_id,
             related_distributor_corp_name                   channel_name
      from user_vip_view a
               LEFT JOIN (select user_id, count(*) `batch_cnt`
                          from `creative_batch`
                          where `user_id` in (select user_id from user_vip_view)
                            and create_time &gt;= #{startDate}
                            and create_time &lt; #{endDate}
                          group by `user_id`) b on a.user_id = b.user_id
               LEFT JOIN (select user_id, count(*) `cloth_cnt`
                          from `material_model`
                          where `user_id` in (select user_id from user_vip_view)
                            and create_time &gt;= #{startDate}
                            and create_time &lt; #{endDate}
                          group by `user_id`) c on a.user_id = c.user_id
               LEFT JOIN user_view d on a.user_id = d.id
  </select>
    <select id="fetch3999UserIdList" resultType="java.lang.Integer">
        select a.id
        from user_view a
                 left join user_vip_view b on a.id = b.user_id
        where deleted != 1
        and status = 'ENABLED'
        and vip_3999 = 1
        and related_distributor_sales_userId = #{userId}
    </select>
  <select id="queryBefore60DaysByOperate" parameterType="string" resultType="java.util.Map">
    select id user_id, count(*) sleep_cnt
    from user_view a
           left join user_vip_view b on a.id = b.user_id
    where deleted != 1
          and status = 'ENABLED'
          and user_type = 'MASTER'
          and DATEDIFF(STR_TO_DATE(#{endDate}, '%Y-%m-%d %H:%i:%s'), create_time) > 60
          and b.vip = 0
    group by id
  </select>
  <select id="queryAll3999VIPOrPaidCustomer" resultType="ai.conrain.aigc.platform.dal.entity.UserDO">
      select *
      from user_view a
      left join user_vip_view b on a.id = b.user_id
      where deleted != 1
      and status = 'ENABLED'
      and vip_3999 = 1
  </select>

  <select id="queryAll3999VIPOrPaidCustomerWithCreateTime" resultType="ai.conrain.aigc.platform.dal.entity.UserDO">
      select *
      from user_view a
               left join user_vip_view b on a.id = b.user_id
      where deleted != 1
        and create_time &lt; #{endDate}
        and status = 'ENABLED'
        and vip_3999 = 1
  </select>


  <select id="batchSelectChannelAdminByOrgId" resultMap="BaseResultMap">
    select user.*
    from user
    left join user_organization uo on user.id = uo.user_id
    where user.deleted = 0
      and user.status = 'ENABLED'
      and user.custom_role in ('CHANNEL_ADMIN', 'SECOND_CHANNEL_ADMIN')
      and uo.org_id in
      <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
        #{orgId,jdbcType=INTEGER}
      </foreach>
  </select>
  <select id="findDirectChannelAdmin" resultType="ai.conrain.aigc.platform.dal.entity.UserDO">
    select user.*
    from user
    left join user_organization uo on user.id = uo.user_id
    where user.deleted = 0
      and user.status = 'ENABLED'
      and user.custom_role in ('CHANNEL_ADMIN', 'SECOND_CHANNEL_ADMIN')
      and uo.org_id in (
          select org_id from user_organization where user_id = #{userId,jdbcType=INTEGER}
      )
  </select>

</mapper>