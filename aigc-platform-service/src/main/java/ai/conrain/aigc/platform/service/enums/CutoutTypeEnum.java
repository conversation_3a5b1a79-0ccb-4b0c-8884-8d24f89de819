/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_FLOW_CUTOUT_ONLY_UPSCALE;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_FLOW_CUTOUT_WITH_MANNEQUIN;

/**
 * 抠图类型枚举
 *
 * <AUTHOR>
 * @version : CutoutTypeEnum.java, v 0.1 2025/5/26 23:30 renxiao.wu Exp $
 */
@Getter
public enum CutoutTypeEnum {
    DEFAULT("default", "默认，只抠服装", "TRAIN_FLOW_CUTOUT"),
    CLOTH_AND_MANNEQUIN("clothAndMannequin", "抠服装和人台", TRAIN_FLOW_CUTOUT_WITH_MANNEQUIN),
    UNCUT("uncut", "不抠图", TRAIN_FLOW_CUTOUT_ONLY_UPSCALE),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    private final String flowKey;

    private CutoutTypeEnum(String code, String desc, String flowKey) {
        this.code = code;
        this.desc = desc;
        this.flowKey = flowKey;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static CutoutTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (CutoutTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
