/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import javax.imageio.ImageIO;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.creative.async.BatchToAsyncExecutor;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.AgeRangeEnum;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.helper.ClothCollocationHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModelExt;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.IScalableClz;
import ai.conrain.aigc.platform.service.model.biz.IScalableTagsClz;
import ai.conrain.aigc.platform.service.model.biz.StyleScene.ModelDetail;
import ai.conrain.aigc.platform.service.model.biz.UserClothMatchingPreference;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.ContextUtils;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.FlowUtils;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.PromptUtils;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.BizConstants.SYSTEM_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.FLUX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_RANGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_BIZTAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_BODY_TYPE_UNLIMITED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CAMERA_ANGLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CFG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_LORA_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_COLOR_INDEX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DEFAULT_SCENE_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ENABLE_ANTI_BLUR_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ENABLE_NEW_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_EXPRESSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_EXT_TAGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_AFTER;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_AFTER_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_CFG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_LORA_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FEATURES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_GARMENT_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_HAIRSTYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_INCLUDES_BRA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_INCREASE_STEPS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_PURE_BG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_PURE_COLOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_SWAP_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NOSHOW_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PURE_RGB;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REPAIR_AFTER_SWAP;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REPAIR_FACE_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SAMPLER_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCENE_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SEED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_SCENE_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SUB_LORA_NAMES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SWAP_MODEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_CONTEXT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_GROUP_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_PARAMS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_ROUND_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRANS_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_UNLIMITED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_VERSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NOW_SHOW_FACE_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.USE_HAIRSTYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.clothStyleType;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.features;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.unisex;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.CREATIVE_PURE_BG_FLOW_PARAMS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.EXCLUDE_SYSTEM_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FACE_NEW_FLOW_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_NEW_FACE_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_NEW_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_STYLE_EXP_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SHOE_SUFFIX_DICT;

/**
 * 生成图片创作服务
 *
 * <AUTHOR>
 * @version : CreateImageCreativeService.java, v 0.1 2024/7/18 15:21 renxiao.wu Exp $
 */
@Slf4j
@Service
public class CreateImageCreativeService extends AbstractCreativeService<AddCreativeRequest> {
    private final static List<String> COPY_TO_TASK_KEY = Arrays.asList(KEY_TRANS_CLOTH_COLLOCATION,
        KEY_ORIGIN_CLOTH_COLLOCATION, KEY_CAMERA_ANGLE, KEY_COLOR_INDEX, KEY_ENABLE_ANTI_BLUR_LORA,
        KEY_ENABLE_NEW_MODEL, KEY_IS_PURE_BG, clothStyleType, KEY_TEST_GROUP_ID, KEY_CLOTH_TYPE, KEY_TEST_CONTEXT,
        KEY_GARMENT_TYPE, KEY_EXPRESSION, KEY_BODY_TYPE_UNLIMITED, KEY_BIZTAG);

    @Autowired
    private UserProfileService userProfileService;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private OssService ossService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private BatchToAsyncExecutor batchToAsyncExecutor;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.CREATE_IMAGE;
    }

    @Override
    protected CreativeBatchVO buildData(AddCreativeRequest request, MaterialModelVO modelVO) {
        CreativeBatchVO data = CreativeBatchConverter.request2VO(request, modelVO.getShowImage());

        batchToAsyncExecutor.storeSync(request, data);
        // 处理服装搭配
        if (!ClothCollocationHelper.isEmpty(request.getTransClothCollocation())) {
            data.addExtInfo(KEY_TRANS_CLOTH_COLLOCATION, request.getTransClothCollocation());
        }

        if (StringUtils.equals(request.getBodyType(), KEY_UNLIMITED)) {
            data.addExtInfo(KEY_BODY_TYPE_UNLIMITED, YES);
        }
        List<String> cameraAngle = buildCameraAngle(request);
        data.addExtInfo(clothStyleType, parseClothStyleType(request, modelVO));
        data.addExtInfo(KEY_CAMERA_ANGLE, cameraAngle);
        if (request.getColorIndex() != null && StringUtils.equalsIgnoreCase(
            modelVO.getClothLoraTrainDetail().getMultiColors(), YES)) {
            data.addExtInfo(KEY_COLOR_INDEX, request.getColorIndex());
        }

        //打上标
        ModelVersionEnum version = ModelVersionEnum.SDXL;
        if (modelVO.getClothLoraTrainDetail() != null && StringUtils.equals(FLUX,
            modelVO.getClothLoraTrainDetail().getLoraType())) {
            version = ModelVersionEnum.FLUX;
        }

        data.addExtInfo(KEY_VERSION, version.getCode());
        data.addExtInfo(KEY_ENABLE_ANTI_BLUR_LORA, request.getEnableAntiBlurLora());
        data.addExtInfo(KEY_ENABLE_NEW_MODEL, request.getEnableNewModel());
        if (request.getEnableNewModel() && !OperationContextHolder.isBackRole()) {
            log.info("接收到创建图片请求enableNewModel=true");
        }

        if (StringUtils.isNotBlank(request.getExpression())) {
            log.info("用户自定义表情:{}", request.getExpression());
            data.addExtInfo(KEY_EXPRESSION, request.getExpression());
        } else {
            String ageRange = modelVO.getExtInfo(KEY_AGE_RANGE, String.class);
            //童装时默认微笑和大笑
            if (!(StringUtils.isBlank(ageRange) || StringUtils.equals(AgeRangeEnum.ADULT.getCode(), ageRange))) {
                data.addExtInfo(KEY_EXPRESSION, "{smile|laugh}");
            }
        }

        //填充AB测试参数
        if (request.getTestGroupId() != null && OperationContextHolder.isBackRole()) {
            data.addExtInfo(KEY_TEST_GROUP_ID, request.getTestGroupId());
            data.addExtInfo(KEY_TEST_PARAMS, request.getTestParams());
            if (request.getTestContext() != null) {
                data.addExtInfo(KEY_TEST_CONTEXT, request.getTestContext());
            }
        }

        //防止前台传入的尺寸有错误
        ProportionTypeEnum proportion = ProportionTypeEnum.getByCode(data.getImageProportion());
        if (proportion != null && proportion.isLagerProportion() && modelVO.getVersion() == ModelVersionEnum.SDXL) {
            log.info("sdxl生成图片的尺寸有误{}，自动修正为{}", proportion, proportion.getSmallerProportion());
            data.setImageProportion(proportion.getSmallerProportion().getCode());
        }

        String garmentType = modelVO.getExtInfo(KEY_GARMENT_TYPE, String.class);
        if (StringUtils.isNotBlank(garmentType)) {
            data.addExtInfo(KEY_GARMENT_TYPE, garmentType);
        }

        data.addExtInfo(KEY_CLOTH_TYPE, modelVO.getClothLoraTrainDetail().getOriginClothType());

        data.addExtInfo(KEY_CLOTH_LORA_PATH, modelVO.getLoraName());

        return data;
    }

    @Override
    protected Map<Integer, List<Integer>> getConfigs(AddCreativeRequest request) {
        return request.getConfigs();
    }

    @Override
    protected CreativeElementVO fetchElementById(Integer id, AddCreativeRequest request, MaterialModelVO modelVO) {
        CreativeElementVO element = super.fetchElementById(id, request, modelVO);

        //0.风格lora走随机流程,所以这里返回父节点
        if (ElementUtils.isStyleScene(element)) {
            log.info("命中风格lora，直接返回当前二级节点");
            return element;
        }

        //设置过滤条件
        List<String> matchTypes = new ArrayList<>();
        //1.版本过滤，flux or sdxl
        matchTypes.add(modelVO.getVersion().getCode());

        //2.男/女过滤(模特本身就区分男女，直接跳过)
        String clothStyleType = null;
        if (!StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {

            clothStyleType = parseClothStyleType(request, modelVO);

            matchTypes.add(clothStyleType);
        }

        //3.正面/背面过滤
        CameraAngleEnum position = CameraAngleEnum.getByCode(request.getPosition()) != null ? CameraAngleEnum.getByCode(
            request.getPosition()) : null;
        if (position != null) {
            matchTypes.add(position.getCode());
        }

        List<CreativeElementVO> filter = element.getChildren().stream().filter(
            e -> new HashSet<>(e.getType()).containsAll(matchTypes)).collect(Collectors.toList());

        //4.场景：上下半身/双人过滤
        if (StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
            CameraAngleEnum bodyType = CameraAngleEnum.getByCode(request.getBodyType(), CameraAngleEnum.WHOLE_BODY);

            List<CreativeElementVO> temp = filter.stream().filter(
                e -> new HashSet<>(e.getType()).contains(bodyType.getCode())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(temp)) {
                filter = temp;
            } else {

                //如果取不到时再查找一次WHOLE_BODY
                temp = filter.stream().filter(
                    e -> new HashSet<>(e.getType()).contains(CameraAngleEnum.WHOLE_BODY.getCode())).collect(
                    Collectors.toList());

                if (CollectionUtils.isNotEmpty(temp)) {
                    filter = temp;
                }
            }
        }

        log.info("获取到过滤条件{},filterSize={},id={}", JSONObject.toJSONString(matchTypes),
            CollectionUtils.size(filter), element.getId());

        if (CollectionUtils.isNotEmpty(filter)) {
            return filter.get(0);
        }

        //4.兜底逻辑，如果没有如flux的配置，则模特的男女必须要正确
        if (!StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.FACE.name()) && StringUtils.isNotBlank(
            clothStyleType)) {

            String finalClothStyleType = clothStyleType;
            CreativeElementVO find = element.getChildren().stream().filter(
                e -> e.getType().contains(finalClothStyleType)).findFirst().orElse(null);

            log.info("获取到过滤条件{},走兜底逻辑获取到当前性别{}的模特，结果={}", JSONObject.toJSONString(matchTypes),
                clothStyleType, find != null);

            if (find != null) {
                return find;
            }
        }

        if (CollectionUtils.isEmpty(element.getChildren())) {
            log.error("当前创作元素没有配置{}的配置,id={},name={}", element.getConfigKey(), element.getId(),
                element.getName());
            throw new BizException(ResultCode.BIZ_FAIL);
        }

        return element.getChildren().get(0);
    }

    @Override
    protected void postProcess(CreativeBatchVO data, AddCreativeRequest request) {
        if (data.getModelType() == ModelTypeEnum.CUSTOM) {
            userProfileService.initRetractNotice(data.getOperatorId());
        }
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        super.fillTaskExt(target, batch, elements, idx);
        JSONObject extInfo = batch.getExtInfo();
        for (String key : COPY_TO_TASK_KEY) {
            Object value = extInfo.get(key);
            if (value != null) {
                target.addExtInfo(key, value);
            }
        }

        //noinspection unchecked
        List<Object> testParams = batch.getExtValue(KEY_TEST_PARAMS, List.class);
        if (CollectionUtils.isNotEmpty(testParams)) {
            CreateTestParams params = null;
            Object jsonObject = testParams.get(idx);
            if (jsonObject instanceof CreateTestParams) {
                params = (CreateTestParams)jsonObject;
            } else {
                params = ((JSONObject)jsonObject).toJavaObject(CreateTestParams.class);
            }

            target.addExtInfo(KEY_TEST_PARAMS, params);
            target.addExtInfo(KEY_TEST_ROUND_ID, idx);
        }

        batchToAsyncExecutor.restoreTask(target, batch);

        elements.forEach(e -> {
            if (StringUtils.equals(e.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                boolean pureBgScene = isPureBgScene(e);
                target.addExtInfo(KEY_IS_PURE_BG, pureBgScene);
            }
        });
    }

    @Override
    protected Integer getModelId(AddCreativeRequest request) {
        return request.getLoraId();
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        //初始化各种seed
        String faceSeed = RandomStringUtils.randomNumeric(15);
        long promptSeed = Long.parseLong(RandomStringUtils.randomNumeric(15));//1-2048的随机数字,无法发挥随机数seed，调整成15位
        CreateTestParams testParams = task.getExtValue(KEY_TEST_PARAMS, CreateTestParams.class);
        if (testParams != null) {
            context.put(KEY_SEED, testParams.getSeed());
            faceSeed = testParams.getFaceSeed();
            promptSeed = Long.parseLong(testParams.getPromptSeed());
        }

        //1.异步执行的参数解析
        batchToAsyncExecutor.asyncExecAndStore(task, elements);

        //2.进行数据修正
        dataCorrect(elements, modelVO, context, task, promptSeed);

        CreativeElementVO sceneElement = (CreativeElementVO)context.get(ElementConfigKeyEnum.SCENE.name());
        CreativeElementVO faceElement = (CreativeElementVO)context.get(ElementConfigKeyEnum.FACE.name());

        if (sceneElement != null) {
            //将场景id灌入task
            task.addExtInfo(KEY_SCENE_ID,
                sceneElement.getLevel() > 2 ? sceneElement.getParentId() : sceneElement.getId());

            //解决纯色背景cfg需要单独设置问题，场景cfg优先级>服装cfg
            String sceneCfg = sceneElement.getExtInfo(KEY_CFG, String.class);
            if (sceneCfg != null) {
                modelVO.addExtInfo(KEY_CFG, sceneCfg);
            }
        }

        //默认写死30步，兼容逻辑，修改流程后可优化 TODO
        modelVO.addExtInfo(KEY_INCREASE_STEPS, 30);

        //3.补齐数据
        context.put("faceSeed", faceSeed);

        String faceCfg = faceElement.getExtInfo(KEY_FACE_CFG) != null ? faceElement.getExtInfo(KEY_FACE_CFG).toString()
            : String.valueOf(1.5); // 默认3.0
        context.put("faceCfg", faceCfg);

        context.put("promptSeed", promptSeed);
        context.put("loraStrength", 1);

        CreativeElementVO pureBgScene = sceneElement == null ? null : getPureBgScene(
            Collections.singletonList(sceneElement));
        if (pureBgScene != null) {
            String pureRGB = fetchPureRGB(pureBgScene);

            context.put(KEY_PURE_RGB, pureRGB);
        }

        //4.基于个性化需求，调整部分参数，如角度、性别、搭配、表情、发型、场景等

        //设置服装款式个性化配置（正、背、全身、下半身）
        //注意：要放在resetClothCollocation之前，服装搭配依赖于补充激活词，extTags
        resetCameraAngle(task, modelVO, promptSeed, faceElement, sceneElement);

        //设置服装搭配
        resetClothCollocation(task, modelVO, sceneElement, faceElement, context, promptSeed);

        //重置表情
        resetExpression(faceElement, promptSeed, modelVO, task);

        //重置发型
        resetHairstyle(faceElement, sceneElement, modelVO);

        //重置场景lora信息
        resetSceneLora(sceneElement, context);

        // 背面切换，1是需要换脸 todo 待删除，先兼容
        context.put("switchFace", isBackViewFlow(task) ? 0 : 1);
        context.put("repairFaceDenoise", "0.55");

        // 加速开关，默认不开启
        boolean speedUpSwitch = false;
        JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
        float flag = switchJson.getFloatValue("speedUp");
        if (GrayscaleTestUtils.isHit(flag, "出图-推理加速灰度")) {
            log.info("出图-推理加速灰度命中,taskId={},batchId={}", task.getId(), task.getBatchId());
            speedUpSwitch = true;
            task.addExtInfo("speedUp", "Y");
        }
        context.put("speedUpSwitch", speedUpSwitch);
        context.put("faceDetailerMaxSize", 1785);
        context.put("faceDetailerCropFactor", 1.5);
        // instantId换脸模型
        context.put("instantIdModel", "sdxl/Epicrealismxl_Hades.safetensors");
    }

    @Override
    protected String postParse(String prompt) {
        prompt = prompt.replaceAll("\r\n|\r|\n", "");
        prompt = prompt.replaceAll("\\\\n", "\\\\n");
        return prompt;
    }

    @Override
    protected String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        CreativeElementVO sceneElement = (CreativeElementVO)context.get(ElementConfigKeyEnum.SCENE.name());
        CreativeElementVO faceElement = (CreativeElementVO)context.get(ElementConfigKeyEnum.FACE.name());

        //1.正/背
        boolean isBackView = isBackViewFlow(task);
        //2.lora/换脸
        boolean isLoraFace = ElementUtils.isLoraFace(faceElement);
        //3.纯色/非纯色
        boolean isPureBg = isPureBgScene(sceneElement);

        String repairFaceType = faceElement.getExtInfo(KEY_REPAIR_FACE_TYPE, String.class);
        boolean isNeedRepair = !StringUtils.equals(NO, repairFaceType);

        boolean isFluxRepair = isNeedRepair && !StringUtils.equals(ModelVersionEnum.SDXL.getCode(), repairFaceType);

        boolean isLoraSwapFace = isLoraFace && StringUtils.equals(YES,
            faceElement.getExtInfo(KEY_LORA_SWAP_FACE, String.class));

        Boolean enableAntiBlueLora = task.getExtValue(KEY_ENABLE_ANTI_BLUR_LORA, Boolean.class);
        boolean isAntiBlueLora = ElementUtils.isStyleScene(sceneElement) || (enableAntiBlueLora != null
            ? enableAntiBlueLora : true);

        boolean isForcePW = context.get("isForcePW") != null && (Boolean)context.get("isForcePW");

        boolean isPWModel = task.getExtInfo(KEY_ENABLE_NEW_MODEL, Boolean.class) != null && task.getExtInfo(
            KEY_ENABLE_NEW_MODEL, Boolean.class);
        isPWModel = (isLoraFace && (isPWModel || StringUtils.equals("PW", repairFaceType))) || isForcePW;

        boolean isFaceAfter = isFaceAfter(context) && isLoraFace && isFluxRepair && isNeedRepair;

        //风格场景不展示人脸时，将模特lora、换脸、修脸等都进行关闭，同时清除模特信息
        if (isNoshowFace(context, sceneElement)) {
            isLoraSwapFace = false;
            isNeedRepair = false;
            isFluxRepair = false;
            isPWModel = false;
            isFaceAfter = false;
            // 清除模特特征信息
            faceElement.setExtTags(null);
            faceElement.setTags(null);
            faceElement.addExtInfo(KEY_FACE_LORA_STRENGTH, 0);
            faceElement.getExtInfo().remove(KEY_EXPRESSION);
            faceElement.getExtInfo().remove(KEY_HAIRSTYLE);
        } else if (sceneElement != null) {
            //兼容处理，展示人脸时将关键词下掉
            sceneElement.setTags(StringUtils.replaceIgnoreCase(sceneElement.getTags(), NOW_SHOW_FACE_PROMPT + ",", ""));
        }

        //冗余加一份
        context.put("isBackView", isBackView);
        context.put("isLoraFace", isLoraFace);
        context.put("isPureBg", isPureBg);
        context.put("isNeedRepair", isNeedRepair);
        context.put("isFluxRepair", isFluxRepair);
        context.put("isLoraSwapFace", isLoraSwapFace);
        context.put("isAntiBlueLora", isAntiBlueLora);
        context.put("isPWModel", isPWModel);
        context.put("isFaceAfter", isFaceAfter);

        //采样器实验专用 TODO 后续明确类型后废弃
        //${(lora.extInfo.increaseStrength=='Y')?then('deis',isPWModel?then('dpmpp_2m','euler'))}
        //${(lora.extInfo.increaseStrength=='Y')?then('beta',isPWModel?then('sgm_uniform','beta'))}
        String samplerName = (String)context.get(KEY_SAMPLER_NAME);
        String scheduleName = StringUtils.equals("dpmpp_2m", samplerName) ? "sgm_uniform" : "beta";
        if (StringUtils.isBlank(samplerName)) {
            samplerName = "dpmpp_2m"; //默认统一改为deis
            scheduleName = "beta"; //默认统一改成beta
            //MaterialModelVO model = (MaterialModelVO)context.get("lora");
            //if (StringUtils.equals(model.getExtInfo(KEY_INCREASE_STRENGTH, String.class), YES)) {
            //    samplerName = "deis";
            //} else if (isPWModel) {
            //    samplerName = "dpmpp_2m";
            //} else {
            //    samplerName = "euler";
            //}
        } else if (StringUtils.contains(samplerName, "|")) {
            String[] split = StringUtils.split(samplerName, "|");
            AssertUtil.assertTrue(split.length == 2, "samplerName|scheduleName格式错误");
            samplerName = split[0];
            scheduleName = split[1];
        }

        context.put(KEY_SAMPLER_NAME, samplerName);
        context.put(KEY_SCHEDULE_NAME, scheduleName);

        //4.获取对应的流程配置
        return FlowUtils.correctFlow(flow, flowKey, isBackView, isLoraFace, isPureBg, isFluxRepair, isLoraSwapFace,
            isAntiBlueLora, isPWModel, isFaceAfter, isNeedRepair, isForcePW);
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        JSONObject testContext = task.getExtValue(KEY_TEST_CONTEXT, JSONObject.class);
        if (testContext != null) {
            log.info("识别到测试场景,需变更上下文,id={},testContext={}", task.getBatchId(), testContext);
            ContextUtils.updateContext(context, testContext);
        }

        CreativeElementVO sceneElement = (CreativeElementVO)context.get(ElementConfigKeyEnum.SCENE.name());

        if (modelVO.getVersion() == ModelVersionEnum.FLUX) {
            CreativeElementVO face = (CreativeElementVO)context.get(ElementConfigKeyEnum.FACE.name());
            if (face != null) {

                if (StringUtils.equals("instantId", face.getExtInfo(KEY_SWAP_MODEL_TYPE, String.class))) {
                    log.info("识别到flux新换脸流程,id={}", task.getBatchId());
                    return FLUX_CREATIVE_NEW_FACE_FLOW;
                }

                //临时逻辑，部分代码写死，236、355和365节点，后续修改可参考FLUX_CREATIVE_STYLE_EXP_FLOW中的配置
                Integer faceId = face.getLevel().equals(2) ? face.getId() : face.getParentId();
                if (!isNoshowFace(context, sceneElement) && (systemConfigService.isInJsonArray(FACE_NEW_FLOW_SWITCH,
                    faceId) || StringUtils.equals(YES, face.getExtInfo(KEY_REPAIR_AFTER_SWAP, String.class)))) {
                    log.info("识别到flux先换脸后修脸场景,id={}", task.getBatchId());
                    return FLUX_CREATIVE_NEW_FLOW;
                }
            }

            log.info("识别到flux场景,id={}", task.getBatchId());
            return FLUX_CREATIVE_STYLE_EXP_FLOW;
            //return FLUX_CREATIVE_FLOW todo 优化成 FLUX_CREATIVE_FLOW
        }

        if (getPureBgScene(elements) != null) {
            log.info("识别到纯色背景场景");
            return CREATIVE_PURE_BG_FLOW_PARAMS;
        }

        return super.getFlowKey(elements, modelVO, task, context);
    }

    @Override
    protected boolean otherWithoutDeduction(AddCreativeRequest request) {
        boolean result = StringUtils.equals(request.getBizTag(), SYSTEM_IMAGES) && request.isWithoutDeduction();

        if (result) {
            log.info("识别到系统出图且打标为不扣点数,loraId={},operatorUser={}", request.getLoraId(),
                OperationContextHolder.getOperatorUserId());
        }
        return result;
    }

    /**
     * 数据修正
     *
     * @param elements   元素列表
     * @param modelVO    模型数据
     * @param context    配置map上下文
     * @param task       任务
     * @param promptSeed prompt的种子
     */
    private void dataCorrect(List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context,
                             CreativeTaskVO task, long promptSeed) {
        CreativeElementVO originFaceElement = elements.stream().filter(
            element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())).findFirst().orElse(null);
        String needReplaceModelKeys = systemConfigService.queryValueByKey(SystemConstants.NEED_REPLACE_MODEL_KEYS);

        CreativeElementVO originSceneElement = elements.stream().filter(
            element -> element.getConfigKey().equals(ElementConfigKeyEnum.SCENE.name())).findFirst().orElse(null);

        //对于风格lora场景，随机选择一个
        if (ElementUtils.isStyleScene(originSceneElement)) {
            originSceneElement = fetchStyleSceneElement(modelVO, task, promptSeed, originSceneElement);
        }

        CreativeElementVO faceElement = CommonUtil.deepCopy(originFaceElement);
        CreativeElementVO sceneElement = CommonUtil.deepCopy(originSceneElement);

        // lora模特，默认修脸强度1
        if (ElementUtils.isLoraFace(faceElement) && null == faceElement.getExtInfo(KEY_FACE_AFTER_STRENGTH,
            Double.class)) {
            faceElement.addExtInfo(KEY_FACE_AFTER_STRENGTH, 1);
        }

        //如果不需要替换模型，直接返回原有的设置
        if (StringUtils.isBlank(needReplaceModelKeys) || !needReplaceFaceFeatures(faceElement, sceneElement, context)) {
            context.put("lora", modelVO);
            context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
            context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
            return;
        }

        String faceFeatures = getFaceFeatures(faceElement);

        String[] split = StringUtils.split(needReplaceModelKeys, ",");

        for (String key : split) {
            replaceFaceFeatures(modelVO, key, faceFeatures);
            replaceFaceFeatures(faceElement, key, faceFeatures);
            if (sceneElement != null) {
                replaceFaceFeatures(sceneElement, key, faceFeatures);
            }
        }

        context.put("lora", modelVO);
        context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
        context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
    }

    /**
     * 获取模特特征
     *
     * @param faceElement 模特元素
     * @return 模特特征
     */
    private static String getFaceFeatures(CreativeElementVO faceElement) {
        String faceFeatures = faceElement.getExtInfo(KEY_FEATURES, String.class);
        if (StringUtils.endsWith(faceFeatures, ", ")) {
            faceFeatures = StringUtils.substring(faceFeatures, 0, faceFeatures.length() - 2);
        }
        if (StringUtils.endsWith(faceFeatures, ",")) {
            faceFeatures = StringUtils.substring(faceFeatures, 0, faceFeatures.length() - 1);
        }
        return faceFeatures;
    }

    /**
     * 是否需要替换模特特征
     *
     * @param face    模特配置
     * @param scene   场景元素
     * @param context 上下文
     * @return true, 需要替换
     */
    private static boolean needReplaceFaceFeatures(CreativeElementVO face, CreativeElementVO scene,
                                                   Map<String, Object> context) {
        if (isNoshowFace(context, scene)) {
            return false;
        }
        return face != null && StringUtils.isNotBlank(StringUtils.trim(face.getExtInfo(KEY_FEATURES, String.class)));
    }

    /**
     * 获取风格场景元素
     *
     * @param modelVO            模型vo
     * @param task               热舞你
     * @param promptSeed         seed
     * @param originSceneElement 原场景元素
     * @return 风格元素
     */
    private CreativeElementVO fetchStyleSceneElement(MaterialModelVO modelVO, CreativeTaskVO task, long promptSeed,
                                                     CreativeElementVO originSceneElement) {

        List<List<String>> angleList = MaterialModelUtils.fetchAllCameraAngleCode(modelVO);
        List<CreativeElementVO> children = originSceneElement.getChildren();
        if (originSceneElement.getLevel() == 3) {
            log.warn("识别到风格lora场景，当前level=3，获取parent,batchId={},elementId={}", task.getBatchId(),
                originSceneElement.getId());
            CreativeElementVO parent = creativeElementService.selectByIdWithChildren(originSceneElement.getParentId());
            children = parent.getChildren();
        }
        List<String> cameraAngles = getCameraAngles(task);

        //兼容逻辑，官方试用角度根据风格场景的角度来
        boolean isBodyTypeUnlimited = StringUtils.equals(YES, task.getExtInfo(KEY_BODY_TYPE_UNLIMITED, String.class));

        if (modelVO.getType() == ModelTypeEnum.SYSTEM || isBodyTypeUnlimited) {
            //不限时，正面和背面的约束要生效
            CameraAngleEnum orientation = isBodyTypeUnlimited ? CameraAngleEnum.getOrientationByStr(cameraAngles)
                : null;

            //过滤满足所有角度的children
            List<CreativeElementVO> filter = children.stream().filter(
                e -> orientation == null || e.getType().contains(orientation.getCode())).filter(
                e -> angleList.stream().anyMatch(angles -> CollectionUtils.containsAll(e.getType(), angles))).collect(
                Collectors.toList());

            if (CollectionUtils.isEmpty(filter)) {
                filter = children.stream().filter(
                    e -> orientation == null || e.getType().contains(orientation.getCode())).collect(
                    Collectors.toList());

                if (CollectionUtils.isEmpty(filter)) {
                    log.warn(
                        "识别到风格lora场景，未匹配到服装可用角度的元素1，全局进行随机,batchId={},服装角度={},orientation={}",
                        task.getBatchId(), angleList, orientation);
                    filter = children;
                } else {
                    log.info(
                        "识别到风格lora场景，未匹配到服装可用角度的元素2，但匹配到正面/背面，进行随机,batchId={},服装角度={},orientation={}",
                        task.getBatchId(), angleList, orientation);
                }
            } else {
                log.info("识别到风格lora场景，匹配到服装可用角度的元素1，进行随机,batchId={},orientation={}",
                    task.getBatchId(), orientation);
            }
            Collections.shuffle(filter, new Random(promptSeed));

            originSceneElement = filter.get(0);

            //重置人物的角度
            if (!isBodyTypeUnlimited) {
                CameraAngleEnum styleOrientation = CameraAngleEnum.getOrientationByStr(originSceneElement.getType());
                CameraAngleEnum.replaceOrientation(styleOrientation, cameraAngles);
            }
            CameraAngleEnum styleBodyPosition = CameraAngleEnum.getBodyPositionByStr(originSceneElement.getType());
            CameraAngleEnum.replaceBodyPosition(styleBodyPosition, cameraAngles);

            log.info("识别到风格lora场景，重置构图的角度,batchId={},taskId={},cameraAngles={}", task.getBatchId(),
                task.getId(), cameraAngles);
            task.addExtInfo(KEY_CAMERA_ANGLE, cameraAngles);
            task.addExtInfo(KEY_STYLE_SCENE_ID, originSceneElement.getId());
            return originSceneElement;
        }

        CameraAngleEnum orientation = CameraAngleEnum.getOrientationByStr(cameraAngles);
        String orientationStr = orientation != null ? orientation.getCode() : CameraAngleEnum.FRONT_VIEW.getCode();
        //过滤满足所有角度的children
        List<CreativeElementVO> filter = children.stream().filter(
                element -> angleList.stream().anyMatch(angles -> CollectionUtils.containsAll(element.getType(),
                    angles)))
            .filter(element -> CollectionUtils.containsAll(element.getType(), cameraAngles)).collect(
                Collectors.toList());

        if (CollectionUtils.isEmpty(filter)) {
            //当朝向和角度都匹配不到，则只筛选朝向一致的数据
            filter = children.stream().filter(element -> element.getType().contains(orientationStr)).filter(
                element -> CollectionUtils.containsAll(element.getType(), cameraAngles)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filter)) {
                log.warn("识别到风格lora场景，未匹配到服装可用角度的元素2，全局进行随机,batchId={}", task.getBatchId());
                filter = children;
            } else {
                log.info("识别到风格lora场景，未匹配到服装可用角度的元素2，命中同一朝向的数据，进行随机,batchId={}",
                    task.getBatchId());
            }
        } else {
            log.info("识别到风格lora场景，匹配到服装可用角度的元素2，进行随机,batchId={}", task.getBatchId());
        }

        Collections.shuffle(filter, new Random(promptSeed));

        if (CollectionUtils.isEmpty(filter)) {
            log.error("识别到风格lora场景，未匹配到任何子元素,batchId={},elementId={}", task.getBatchId(),
                originSceneElement.getId());
        }

        originSceneElement = filter.get(0);

        log.info("识别到风格lora场景，设置角度,batchId={},taskId={},cameraAngles={}", task.getBatchId(), task.getId(),
            cameraAngles);
        task.addExtInfo(KEY_STYLE_SCENE_ID, originSceneElement.getId());
        return originSceneElement;
    }

    /**
     * 替换模特特征
     *
     * @param data         数据
     * @param key          key
     * @param faceFeatures 模特特征
     */
    private void replaceFaceFeatures(IScalableClz data, String key, String faceFeatures) {
        if (StringUtils.containsIgnoreCase(data.getTags(), key)) {
            data.setTags(StringUtils.replaceIgnoreCase(data.getTags(), key, faceFeatures));
        }

        if (MapUtils.isNotEmpty(data.getExtInfo())) {
            data.getExtInfo().forEach((k, v) -> {
                if (v instanceof String && StringUtils.containsIgnoreCase(v.toString(), key)) {
                    data.getExtInfo().put(k, StringUtils.replaceIgnoreCase(v.toString(), key, faceFeatures));
                }
            });
        }

        if (data instanceof IScalableTagsClz) {
            IScalableTagsClz tagsClz = (IScalableTagsClz)data;
            if (StringUtils.containsIgnoreCase(tagsClz.getExtTags(), key)) {
                tagsClz.setExtTags(StringUtils.replaceIgnoreCase(tagsClz.getExtTags(), key, faceFeatures));
            }
        }
    }

    /**
     * 是否纯色背景场景
     *
     * @param elements 元素列表
     * @return true, 纯色背景
     */
    private CreativeElementVO getPureBgScene(List<CreativeElementVO> elements) {
        CreativeElementVO scene = elements.stream().filter(
                e -> e != null && ElementConfigKeyEnum.valueOf(e.getConfigKey()) == ElementConfigKeyEnum.SCENE).findFirst()
            .orElse(null);

        return isPureBgScene(scene) ? scene : null;
    }

    /**
     * 是否纯色背景场景
     *
     * @param scene 场景
     * @return true，纯色背景
     */
    private boolean isPureBgScene(CreativeElementVO scene) {
        //当没有人脸时，则是背部，这里做简单处理
        if (scene == null) {
            return false;
        }

        Boolean isPureColor = scene.getExtInfo(KEY_IS_PURE_COLOR, Boolean.class);
        if (isPureColor != null && isPureColor) {
            return true;
        }

        if (scene.getLevel() > 2) {
            CreativeElementVO parent = creativeElementService.selectById(scene.getParentId());
            isPureColor = parent.getExtInfo(KEY_IS_PURE_COLOR, Boolean.class);
            return isPureColor != null && isPureColor;
        }

        return false;
    }

    /**
     * 获取纯色图的背景色，如无则解析图片并保存到数据库
     *
     * @param pureBgScene 纯色背景场景数据
     * @return 背景色
     */
    private String fetchPureRGB(CreativeElementVO pureBgScene) {
        String pureRGB = pureBgScene.getExtInfo(KEY_PURE_RGB, String.class);
        if (StringUtils.isNotBlank(pureRGB)) {
            return pureRGB;
        }

        log.info("进入纯色背景解析RGB色值流程，id={}", pureBgScene.getId());

        String showImage = pureBgScene.getShowImage();
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(showImage);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        BufferedImage image = null;
        try {
            image = ImageIO.read(new File(tmpUrl));
        } catch (IOException e) {
            log.error("读取纯色图片获取色值异常", e);
            throw new BizException(ResultCode.SYS_ERROR);
        }

        // 取图片左上角第一个像素的RGB值（适用于纯色图片）
        int pixel = image.getRGB(image.getWidth() / 2, image.getHeight() / 2); // (0, 0) 是左上角坐标

        // 将 RGB 值拆分为各个颜色分量
        Color color = new Color(pixel, true);
        int red = color.getRed();
        int green = color.getGreen();
        int blue = color.getBlue();

        // 色值计算，色值 = 红 * 256* 256 + 绿 * 256 + 蓝
        pureRGB = String.valueOf(red * 256 * 256 + green * 256 + blue);

        pureBgScene.addExtInfo(KEY_PURE_RGB, pureRGB);

        //保存场景数据
        CreativeElementVO data = new CreativeElementVO();
        data.setId(pureBgScene.getId());
        data.setExtInfo(pureBgScene.getExtInfo());
        creativeElementService.updateById(data);

        return pureRGB;
    }

    /**
     * 重置服装搭配
     *
     * @param task         任务模型
     * @param modelVO      模型数据
     * @param sceneElement 场景元素
     * @param faceElement  模特元素
     * @param context      上下文
     * @param promptSeed   提示种子
     */
    private void resetClothCollocation(CreativeTaskVO task, MaterialModelVO modelVO, CreativeElementVO sceneElement,
                                       CreativeElementVO faceElement, Map<String, Object> context, long promptSeed) {
        //优先级：出图时用户设置的搭配 > 用户上传素材时的搭配 > 调用gpt生成的搭配
        ClothCollocationModelExt target; //调用gpt生成的搭配，兜底
        ClothCollocationModel origin;
        ClothCollocationModel userSet; //用户设置的搭配
        boolean isUpperBody = isUpperBodyFlow(task);

        String aiGenFeaturesJson = modelVO.getExtInfo(CommonConstants.aiGenFeatures, String.class);

        if (isUpperBody && systemConfigService.isInJsonArray(EXCLUDE_SYSTEM_COLLOCATION, modelVO.getUserId())) {
            log.info("当前用户在不使用系统搭配白名单中，且当前场景是上半身，直接返回空搭配,id={}", modelVO.getId());
            origin = new ClothCollocationModel();
        } else if (CommonUtil.isValidJson(aiGenFeaturesJson) && !JSONObject.parseObject(aiGenFeaturesJson).isEmpty()) {
            origin = JSONObject.parseObject(aiGenFeaturesJson, ClothCollocationModel.class);
        } else {
            log.warn("进入服装搭配重置老流程，说明该逻辑还在生效，不能删除");
            //兼容老逻辑
            String originFeatures = modelVO.getExtInfo(features, String.class);
            if (StringUtils.isNotBlank(originFeatures)) {
                String fileServerUrl = serverHelper.getFileServerUrlByUser(task.getUserId());
                JSONObject json = comfyUIService.extractClothCollocation(originFeatures, fileServerUrl);

                UserClothMatchingPreference userClothMatchingPreference = ComfyUIUtils.parseFeaturesFromMatchSuggestion(
                    json.toString(), false);

                if (userClothMatchingPreference != null) {
                    origin = userClothMatchingPreference.getTransClothCollocation();
                    //设置到模型的扩展信息中，防止重复调用gpt
                    MaterialModelVO current = materialModelService.lockById(modelVO.getId());
                    current.addExtInfo(CommonConstants.aiGenFeatures, JSONObject.toJSONString(origin));
                    materialModelService.innerUpdate(current);
                } else {
                    log.error("获取模型原始特征失败，调用gpt返回的结果非json={}，modelId={}", json, modelVO.getId());
                    origin = new ClothCollocationModel();
                }
            } else {
                log.error("获取模型原始特征失败，即无aiGenFeatures也无features，modelId={}", modelVO.getId());
                origin = new ClothCollocationModel();
            }
        }

        target = new ClothCollocationModelExt(origin);

        //todo 由于用户在上传服装时填写的文案翻译准确性，暂时将该功能关闭
        //String userPreferFeatures = modelVO.getExtInfo(CommonConstants.userPreferFeatures, String.class);
        //if (StringUtils.isNotBlank(userPreferFeatures)) {
        //    UserClothMatchingPreference preference = JSONObject.parseObject(userPreferFeatures,
        //        UserClothMatchingPreference.class);
        //
        //    if (preference != null) {
        //        //合并 用户上传素材时的搭配
        //        CreativeBatchConverter.mergeCollocation(target, preference.getTransClothCollocation(), false);
        //    }
        //}

        //业务处理逻辑：如果包含拉链和扣子关上的单词时，则清空上衣搭配 暂时去掉这个逻辑
        //String extTags = modelVO.getExtInfo(KEY_EXT_TAGS, String.class);
        //if ((StringUtils.contains(extTags, "zipped") && !StringUtils.contains(extTags, "unzipped")) || (
        //    StringUtils.contains(extTags, "buttoned") && !StringUtils.contains(extTags, "unbuttoned"))) {
        //    log.info("当前补充激活词包含zipped或buttoned，清空系统设置的上衣搭配{}，extTags={},,,batchId={}",
        //        target.getTops(), extTags, task.getBatchId());
        //    target.setTops(null);
        //}

        //如果是下半身的流程，则清空系统设置的上装搭配
        //if (isLowerBodyFlow(task)) {
        //    log.info("当前是下半身流程，清除系统设置的上装搭配{},batchId={}", target.getTops(), task.getBatchId());
        //    target.setTops(null);
        //}

        //如果是风格lora场景，直接使用风格搭配
        ClothCollocationModel styleCollocation = ClothCollocationHelper.fetchStyleOutfit(sceneElement, modelVO, target);
        if (styleCollocation != null) {
            log.info("当前是风格lora场景，覆盖ai推荐的搭配,batchId={},搭配={}", task.getBatchId(), styleCollocation);
            target = new ClothCollocationModelExt(styleCollocation);
        }

        userSet = task.getExtInfo(KEY_TRANS_CLOTH_COLLOCATION, ClothCollocationModel.class);
        if (userSet != null) {
            //合并 生图时用户设置的搭配
            CreativeBatchConverter.mergeCollocation(target, userSet, true);
        }

        if (isUpperBody) {
            log.info("当前是上半身流程，清除所有鞋相关的搭配{},batchId={}", target.getShoe(), task.getBatchId());
            target.setShoe(null);
        }

        JSONObject shoeSuffixDict = systemConfigService.queryJsonValue(SHOE_SUFFIX_DICT);

        String clothType = modelVO.getClothLoraTrainDetail().getClothType();
        boolean clothIncludesBra = StringUtils.equals(modelVO.getExtInfo(KEY_INCLUDES_BRA, String.class), YES);

        //替换特征中的模特关键词
        log.info("构建搭配信息，collocation={},batchId={}", target, task.getBatchId());
        String features = CreativeBatchConverter.buildFeatures(target, shoeSuffixDict, promptSeed, clothIncludesBra,
            clothType);

        if (StringUtils.isNotBlank(features) && needReplaceFaceFeatures(faceElement, sceneElement, context)) {
            String needReplaceModelKeys = systemConfigService.queryValueByKey(SystemConstants.NEED_REPLACE_MODEL_KEYS);
            String faceFeatures = faceElement.getExtInfo(KEY_FEATURES, String.class);
            if (StringUtils.isNotBlank(needReplaceModelKeys) && StringUtils.isNotBlank(faceFeatures)) {
                String[] split = needReplaceModelKeys.split(",");
                for (String key : split) {
                    features = StringUtils.replaceIgnoreCase(features, key, faceFeatures);
                }
            }
        }

        //替换原来的补充特征
        modelVO.addExtInfo(KEY_FEATURES, features);
    }

    /**
     * 重置相机角度
     *
     * @param task         创作任务
     * @param modelVO      模型
     * @param promptSeed   prompt随机数
     * @param faceElement  模特元素
     * @param sceneElement 背景元素
     */
    private static void resetCameraAngle(CreativeTaskVO task, MaterialModelVO modelVO, Long promptSeed,
                                         CreativeElementVO faceElement, CreativeElementVO sceneElement) {
        List<ClothTypeConfig> clothTypeConfigs = MaterialModelConverter.convert2ClothTypeConfig(modelVO);
        if (CollectionUtils.isEmpty(clothTypeConfigs)) {
            log.error("未找到服装类型配置，无法重置相机角度，modelId={},batchId={}", modelVO.getId(), task.getBatchId());
            return;
        }

        List<String> cameraAngles = getCameraAngles(task);

        ClothTypeConfig clothTypeConfig = clothTypeConfigs.stream().filter(
            config -> new HashSet<>(config.getType()).containsAll(cameraAngles)).findFirst().orElse(null);
        if (clothTypeConfig == null) {

            CameraAngleEnum orientation = CameraAngleEnum.getOrientationByStr(cameraAngles);
            clothTypeConfig = clothTypeConfigs.stream().filter(
                config -> new HashSet<>(config.getType()).contains(orientation.getCode())).findFirst().orElse(null);

            if (clothTypeConfig == null) {
                log.warn("未找到服装类型配置，使用默认配置，cameraAngles={},batchId={}", cameraAngles, task.getBatchId());
                clothTypeConfig = clothTypeConfigs.get(0);
            } else {
                log.warn("未找到服装类型配置，但找到同样朝向的配置，使用该配置，cameraAngles={},hit={},batchId={}",
                    cameraAngles, clothTypeConfig.getType(), task.getBatchId());
            }
        }

        //激活词先进行随机
        String modelTags = PromptUtils.random(ComfyUIUtils.parseJsonStr(clothTypeConfig.getTags()), promptSeed);
        if (ElementUtils.isStyleScene(sceneElement)) {

            String activateKey = StringUtils.contains(modelTags, "(linrun2111:1.3)") ? "(linrun2111:1.3), "
                : (StringUtils.contains(modelTags, "linrun2111") ? "linrun2111, " : "");
            modelTags = activateKey + sceneElement.getExtInfo(KEY_LENS, String.class) + (StringUtils.contains(modelTags,
                "mid shot") ? "mid shot," : "") + (StringUtils.contains(modelTags, "close shot") ? "close shot," : "");
            sceneElement.getExtInfo().remove(KEY_LENS);
        }
        modelVO.setTags(modelTags);

        JSONObject extInfo = MapUtils.isEmpty(clothTypeConfig.getExtInfo()) ? new JSONObject()
            : clothTypeConfig.getExtInfo();

        extInfo.forEach((k, v) -> {
            v = ComfyUIUtils.parseJsonStr(k, v != null ? v.toString() : null);
            extInfo.put(k, v);
        });

        //颜色选择
        if (CollectionUtils.isNotEmpty(clothTypeConfig.getColorList())) {
            Integer colorIndex = task.getExtValue(KEY_COLOR_INDEX, Integer.class);
            if (colorIndex == null) {
                colorIndex = 0;
            }

            if (colorIndex > clothTypeConfig.getColorList().size()) {
                colorIndex = 0;
            }

            ClothColorDetail colorDetail = getColorDetail(clothTypeConfig.getColorList(), colorIndex, promptSeed);
            String extTags = colorDetail.getValue();
            if (StringUtils.isNotBlank(extTags)) {
                String randomExtTags = PromptUtils.random(ComfyUIUtils.parseJsonStr(extTags), promptSeed);
                extInfo.put(KEY_EXT_TAGS, randomExtTags);
            }

            if (modelVO.getMainType() == MainTypeEnum.MAIN) {
                String subLoraNamesStr = modelVO.getExtInfo(KEY_SUB_LORA_NAMES, String.class);
                JSONArray subLoraNames = JSONArray.parseArray(subLoraNamesStr);
                modelVO.setLoraName(subLoraNames.getString(colorDetail.getIndex() - 1));

                log.info("识别到主模型，使用对应子模型的lora，batchId={}，taskId={}，modelId={}，index={}，替换后loraName={}",
                    task.getBatchId(), task.getId(), modelVO.getId(), colorDetail.getIndex(), modelVO.getLoraName());
            }
        }

        modelVO.getExtInfo().putAll(extInfo);

        //如果是下半身情况的话，清除模型元素中的表情等信息
        if (faceElement != null && (isLowerBodyFlow(task) || ElementUtils.isNoshowFace(sceneElement))) {
            log.info("识别到{}，删除模特特征/表情/发型/修复信息,batchId={}",
                isLowerBodyFlow(task) ? "下半身场景" : "风格场景不展示人脸", task.getBatchId());

            if (!ElementUtils.isNoshowFace(sceneElement)) {
                String clothType = task.getExtValue(clothStyleType, String.class);
                //模特区分男女
                String tags = "a female model. ";
                if (StringUtils.equalsIgnoreCase("male", clothType)) {
                    tags = "a male model. ";
                }

                faceElement.setTags(tags);
            }
            faceElement.getExtInfo().remove(KEY_EXPRESSION);
            faceElement.getExtInfo().remove(KEY_HAIRSTYLE);
            faceElement.getExtInfo().remove(KEY_EXT_TAGS);
        }
    }

    /**
     * 获取相机角度
     *
     * @param task 任务
     * @return 相机角度列表
     */
    private static List<String> getCameraAngles(CreativeTaskVO task) {
        //noinspection unchecked
        return (List<String>)task.getExtInfo(KEY_CAMERA_ANGLE, List.class);
    }

    /**
     * 重置表情
     *
     * @param face       模特数据
     * @param promptSeed 随机种子
     * @param modelVO    模型数据
     * @param task       创作任务
     */
    private void resetExpression(CreativeElementVO face, Long promptSeed, MaterialModelVO modelVO,
                                 CreativeTaskVO task) {
        //只针对flux模型处理
        if (modelVO.getVersion() == ModelVersionEnum.SDXL) {
            return;
        }

        //如果是背面场景，则表情设置为空
        if (isBackViewFlow(task)) {
            log.info("识别到flux背面场景，删除模特表情");
            face.addExtInfo(KEY_EXPRESSION, "");
            return;
        }

        if (StringUtils.isNotBlank(task.getExtInfo(KEY_EXPRESSION, String.class))) {
            face.addExtInfo(KEY_EXPRESSION, task.getExtInfo(KEY_EXPRESSION, String.class) + ", ");
            return;
        }

        String expression = face.getExtInfo(KEY_EXPRESSION, String.class);
        if (StringUtils.isBlank(expression)) {
            log.warn("未找到表情数据，不处理");
            return;
        }

        String random = PromptUtils.random(expression, promptSeed);

        //回填face.extInfo["expression"]
        face.addExtInfo(KEY_EXPRESSION, random);
    }

    /**
     * 构建镜头角度
     *
     * @param request 请求
     * @return 镜头角度列表
     */
    private List<String> buildCameraAngle(AddCreativeRequest request) {
        List<String> cameraAngle = new ArrayList<>();
        CameraAngleEnum bodyType = CameraAngleEnum.getByCode(request.getBodyType());
        cameraAngle.add((bodyType == null ? CameraAngleEnum.WHOLE_BODY : bodyType).getCode());
        CameraAngleEnum position = CameraAngleEnum.getByCode(request.getPosition());
        cameraAngle.add(position != null ? position.getCode() : CameraAngleEnum.FRONT_VIEW.getCode());
        return cameraAngle;
    }

    /**
     * 解析服装款式
     *
     * @param request 请求
     * @param modelVO 模型
     * @return 服装款式
     */
    private String parseClothStyleType(AddCreativeRequest request, MaterialModelVO modelVO) {
        String clothStyleType;
        clothStyleType = modelVO.getExtInfo(CommonConstants.clothStyleType, String.class);

        //如果服装是男女同款时，则需要从选择的模特来判断性别
        if (StringUtils.equals(clothStyleType, unisex)) {
            clothStyleType = parseSelectedFaceType(request);
        } else if (StringUtils.isNotBlank(clothStyleType)) {
            //做个特殊处理，Scene里的男女type是首字母大小写的
            clothStyleType = clothStyleType.substring(0, 1).toUpperCase() + clothStyleType.substring(1);
        }

        return clothStyleType;
    }

    /**
     * 是否是背面流程
     *
     * @param task 创作任务
     * @return true是背面流程
     */
    private static boolean isBackViewFlow(CreativeTaskVO task) {
        List<String> cameraAngles = getCameraAngles(task);
        return CollectionUtils.isNotEmpty(cameraAngles) && cameraAngles.contains(CameraAngleEnum.BACK_VIEW.getCode());
    }

    /**
     * 是否是下半身流程
     *
     * @param task 创作任务
     * @return true是下半身流程
     */
    private static boolean isLowerBodyFlow(CreativeTaskVO task) {
        List<String> cameraAngles = getCameraAngles(task);
        return CollectionUtils.isNotEmpty(cameraAngles) && cameraAngles.contains(CameraAngleEnum.LOWER_BODY.getCode());
    }

    /**
     * 是否是上半身流程
     *
     * @param task 创作任务
     * @return true是上半身流程
     */
    private static boolean isUpperBodyFlow(CreativeTaskVO task) {
        List<String> cameraAngles = getCameraAngles(task);
        return CollectionUtils.isNotEmpty(cameraAngles) && cameraAngles.contains(CameraAngleEnum.UPPER_BODY.getCode());
    }

    /**
     * 解析选择的模特类型
     *
     * @param request 请求
     * @return 模特类型
     */
    private String parseSelectedFaceType(AddCreativeRequest request) {
        final String[] clothStyleType = {null};

        request.getConfigs().forEach((k, v) -> {
            Integer each = v.get(0);
            CreativeElementVO elementVO = creativeElementService.selectById(each);
            if (StringUtils.equals(elementVO.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                clothStyleType[0] = elementVO.getType().stream().anyMatch(t -> t.equalsIgnoreCase("female-model"))
                    ? "Female" : "Male";
            }
        });

        return clothStyleType[0];
    }

    /**
     * 获取当前颜色的详情
     *
     * @param colorList  颜色列表
     * @param colorIndex 颜色索引
     * @param promptSeed 种子
     * @return 颜色详情
     */
    private static ClothColorDetail getColorDetail(List<ClothColorDetail> colorList, Integer colorIndex,
                                                   Long promptSeed) {
        List<ClothColorDetail> filter = colorList.stream().filter(ClothColorDetail::isEnable).collect(
            Collectors.toList());
        if (filter.size() == 1) {
            log.info("识别到颜色选择:随机颜色但只有一个颜色，返回对应的激活词");
            return filter.get(0);
        }

        //如果是选择了颜色，则返回对应的激活词
        if (colorIndex > 0) {
            ClothColorDetail hit = filter.stream().filter(e -> e.getIndex() == colorIndex).findFirst().orElse(null);
            if (hit != null) {
                log.info("识别到颜色选择:颜色{}，返回对应的激活词", colorIndex);
                return hit;
            } else {
                log.info("识别到颜色选择:颜色{}，但未找到对应的颜色，colorList={}", colorIndex, colorList);
            }
        }

        //如果选择了随机颜色，则需要对颜色列表进行随机
        Collections.shuffle(filter, new Random(promptSeed));
        log.info("识别到颜色选择:随机颜色index={}，返回对应的激活词", filter.get(0).getIndex());
        return filter.get(0);
    }

    /**
     * 当连衣帽hood up的时候，需要清空发型
     *
     * @param faceElement  模特元素
     * @param sceneElement 场景元素
     * @param modelVO      模型
     */
    private void resetHairstyle(CreativeElementVO faceElement, CreativeElementVO sceneElement,
                                MaterialModelVO modelVO) {
        String extTags = modelVO.getExtInfo(KEY_EXT_TAGS, String.class);
        if (StringUtils.contains(extTags, "hood up")) {
            log.info("连衣帽hood up的时候，清空发型,extTags={}", extTags);
            faceElement.getExtInfo().remove(KEY_HAIRSTYLE);

            return;
        }

        //风格场景使用风格中的发型
        if (ElementUtils.isStyleScene(sceneElement) && StringUtils.equals(YES,
            sceneElement.getExtInfo(USE_HAIRSTYLE, String.class))) {
            String styleModelStr = sceneElement.getExtInfo(KEY_STYLE_MODEL, String.class);
            JSONObject json = JSONObject.parseObject(styleModelStr);
            if (json != null) {
                ModelDetail styleModel = json.toJavaObject(ModelDetail.class);
                if (styleModel != null && StringUtils.isNotBlank(styleModel.getHairstyle())) {
                    log.info("风格场景启用发型，填充到模特发型");
                    faceElement.getExtInfo().put(KEY_HAIRSTYLE, styleModel.getHairstyle());
                }
            }
        }
    }

    /**
     * 重置场景lora
     *
     * @param sceneElement 场景元素
     * @param context      上下文
     */
    private void resetSceneLora(CreativeElementVO sceneElement, Map<String, Object> context) {
        String sceneLora = KEY_DEFAULT_SCENE_LORA;
        String sceneLoraStrength = "1.5";

        if (ElementUtils.isStyleScene(sceneElement)) {
            String loraPath = sceneElement.getExtInfo(KEY_LORA_PATH, String.class);
            if (StringUtils.isNotBlank(loraPath)) {
                sceneLora = loraPath;
                String preset = sceneElement.getExtInfo(KEY_LORA_STRENGTH, String.class);
                sceneLoraStrength = StringUtils.isNotBlank(preset) ? preset : "0.8";

                log.info("重置lora场景，设置当前场景lora为{},设置强度为={},预设强度={}", loraPath, sceneLoraStrength,
                    preset);
            } else {
                log.error("重置lora场景时未配置lora地址，id={}", sceneElement.getId());
            }
        }

        context.put("sceneLoraStrength", sceneLoraStrength);
        context.put("sceneLora", sceneLora);
    }

    @Override
    protected boolean exemption(CreativeElementVO element, AddCreativeRequest request) {
        return StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.SCENE.name()) && StringUtils.isNotBlank(
            request.getCustomScene());
    }

    /**
     * 是否后置修脸流程
     *
     * @param context 上下文
     * @return true, 后置
     */
    private static boolean isFaceAfter(Map<String, Object> context) {
        boolean isFaceAfter = false;
        Object faceAfter = context.get(KEY_FACE_AFTER);
        if (faceAfter != null && Boolean.parseBoolean(faceAfter.toString())) {
            isFaceAfter = true;
        } else {
            CreativeElementVO faceElement = (CreativeElementVO)context.get(ElementConfigKeyEnum.FACE.name());

            Double faceAfterStrength = faceElement.getExtInfo(KEY_FACE_AFTER_STRENGTH, Double.class);
            Double faceLoraStrength = faceElement.getExtInfo(KEY_FACE_LORA_STRENGTH, Double.class);
            if (faceAfterStrength != null && faceLoraStrength != null && !faceAfterStrength.equals(faceLoraStrength)) {
                isFaceAfter = true;
            }
        }

        return isFaceAfter;
    }

    /**
     * 是否不展示人脸
     *
     * @param context      上下文
     * @param sceneElement 场景元素
     * @return true，不展示˚
     */
    private static boolean isNoshowFace(Map<String, Object> context, CreativeElementVO sceneElement) {
        return ElementUtils.isStyleScene(sceneElement) && (context.get(KEY_NOSHOW_FACE) != null && (Boolean)context.get(
            KEY_NOSHOW_FACE) || ElementUtils.isNoshowFace(sceneElement));
    }
}
