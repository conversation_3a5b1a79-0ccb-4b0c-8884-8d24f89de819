package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.PrincipalInfoVO;
import ai.conrain.aigc.platform.service.model.query.PrincipalInfoQuery;
import ai.conrain.aigc.platform.service.component.PrincipalInfoService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PrincipalInfo控制器
 *
 * <AUTHOR>
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
@Slf4j
@Api("通用主体属性")
@Validated
@RestController
@RequestMapping("/principalInfo")
public class PrincipalInfoController {

	/** principalInfoService */
	@Autowired
	private PrincipalInfoService principalInfoService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询通用主体属性", notes = "根据id查询通用主体属性[principalInfo]")
	public Result<PrincipalInfoVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(principalInfoService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建通用主体属性", notes = "新建通用主体属性[principalInfo]")
	public Result<Integer> create(@Valid @RequestBody PrincipalInfoVO principalInfo){
		try {
			PrincipalInfoVO data = principalInfoService.insert(principalInfo);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加通用主体属性失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建通用主体属性失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除通用主体属性", notes = "删除通用主体属性")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		principalInfoService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新通用主体属性", notes = "修改通用主体属性[principalInfo]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody PrincipalInfoVO principalInfo){
		principalInfoService.updateByIdSelective(principalInfo);
		return Result.success();
	}

	@ApiOperation(value = "批量查询通用主体属性", notes = "批量查询[principalInfo]")
	@PostMapping("/queryList")
	public Result<List<PrincipalInfoVO>> queryPrincipalInfoList(@Valid @RequestBody PrincipalInfoQuery query){
		return Result.success(principalInfoService.queryPrincipalInfoList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<PrincipalInfoDO>]")
	public Result<PageInfo<PrincipalInfoVO>> getPrincipalInfoByPage(@Valid @RequestBody PrincipalInfoQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(principalInfoService.queryPrincipalInfoByPage(query));
	}

	@PostMapping("/modifyContractDate")
	@ApiOperation(value = "录入合同日期", notes = "录入合同日期")
	public Result<Boolean> modifyContractDate(@RequestBody @Valid JSONObject req) {
		Integer userId = req.getInteger("userId");
		String contractDate = req.getString("contractDate");

		AssertUtil.assertTrue(userId != null && StringUtils.isNotBlank(contractDate), ResultCode.PARAM_INVALID, "参数错误");
		principalInfoService.modifyContractDate(userId, contractDate);
		return Result.success();
	}
}