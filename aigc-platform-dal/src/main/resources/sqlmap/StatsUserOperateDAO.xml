<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.StatsUserOperateDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="stats_type" jdbcType="VARCHAR" property="statsType" />
    <result column="stats_date" jdbcType="VARCHAR" property="statsDate" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="material_id" jdbcType="INTEGER" property="materialId" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="create_count" jdbcType="INTEGER" property="createCount" />
    <result column="download_count" jdbcType="INTEGER" property="downloadCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, stats_type, stats_date, user_id, material_id, user_type, create_count, download_count, 
    create_time, modify_time
  </sql>
  <sql id="Blob_Column_List">
    ext_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.StatsUserOperateExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from stats_user_operate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.StatsUserOperateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from stats_user_operate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from stats_user_operate
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from stats_user_operate
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stats_user_operate (stats_type, stats_date, user_id, 
      material_id, user_type, create_count, 
      download_count, create_time, modify_time, 
      ext_info)
    values (#{statsType,jdbcType=VARCHAR}, #{statsDate,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{materialId,jdbcType=INTEGER}, #{userType,jdbcType=VARCHAR}, #{createCount,jdbcType=INTEGER}, 
      #{downloadCount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, 
      #{extInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stats_user_operate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statsType != null">
        stats_type,
      </if>
      <if test="statsDate != null">
        stats_date,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="createCount != null">
        create_count,
      </if>
      <if test="downloadCount != null">
        download_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statsType != null">
        #{statsType,jdbcType=VARCHAR},
      </if>
      <if test="statsDate != null">
        #{statsDate,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="createCount != null">
        #{createCount,jdbcType=INTEGER},
      </if>
      <if test="downloadCount != null">
        #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.StatsUserOperateExample" resultType="java.lang.Long">
    select count(*) from stats_user_operate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stats_user_operate
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.statsType != null">
        stats_type = #{record.statsType,jdbcType=VARCHAR},
      </if>
      <if test="record.statsDate != null">
        stats_date = #{record.statsDate,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=INTEGER},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.createCount != null">
        create_count = #{record.createCount,jdbcType=INTEGER},
      </if>
      <if test="record.downloadCount != null">
        download_count = #{record.downloadCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update stats_user_operate
    set id = #{record.id,jdbcType=INTEGER},
      stats_type = #{record.statsType,jdbcType=VARCHAR},
      stats_date = #{record.statsDate,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=INTEGER},
      material_id = #{record.materialId,jdbcType=INTEGER},
      user_type = #{record.userType,jdbcType=VARCHAR},
      create_count = #{record.createCount,jdbcType=INTEGER},
      download_count = #{record.downloadCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stats_user_operate
    set id = #{record.id,jdbcType=INTEGER},
      stats_type = #{record.statsType,jdbcType=VARCHAR},
      stats_date = #{record.statsDate,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=INTEGER},
      material_id = #{record.materialId,jdbcType=INTEGER},
      user_type = #{record.userType,jdbcType=VARCHAR},
      create_count = #{record.createCount,jdbcType=INTEGER},
      download_count = #{record.downloadCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    update stats_user_operate
    <set>
      <if test="statsType != null">
        stats_type = #{statsType,jdbcType=VARCHAR},
      </if>
      <if test="statsDate != null">
        stats_date = #{statsDate,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="createCount != null">
        create_count = #{createCount,jdbcType=INTEGER},
      </if>
      <if test="downloadCount != null">
        download_count = #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    update stats_user_operate
    set stats_type = #{statsType,jdbcType=VARCHAR},
      stats_date = #{statsDate,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      material_id = #{materialId,jdbcType=INTEGER},
      user_type = #{userType,jdbcType=VARCHAR},
      create_count = #{createCount,jdbcType=INTEGER},
      download_count = #{downloadCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO">
    update stats_user_operate
    set stats_type = #{statsType,jdbcType=VARCHAR},
      stats_date = #{statsDate,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      material_id = #{materialId,jdbcType=INTEGER},
      user_type = #{userType,jdbcType=VARCHAR},
      create_count = #{createCount,jdbcType=INTEGER},
      download_count = #{downloadCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsertOrUpdate" parameterType="java.util.List">
    INSERT INTO stats_user_operate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      stats_type, stats_date, user_id, material_id, user_type,
      <if test="list[0].createCount != null">create_count,</if>
      <if test="list[0].downloadCount != null">download_count,</if>
      <if test="list[0].createTime != null">create_time,</if>
      <if test="list[0].modifyTime != null">modify_time,</if>
      ext_info,
    </trim>
    VALUES
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.statsType,jdbcType=VARCHAR},
        #{item.statsDate,jdbcType=VARCHAR},
        #{item.userId,jdbcType=INTEGER},
        #{item.materialId,jdbcType=INTEGER},
        <choose>
          <when test="item.userType != null">#{item.userType,jdbcType=VARCHAR},</when>
          <otherwise>'MASTER',</otherwise>
        </choose>
        <if test="item.createCount != null">#{item.createCount,jdbcType=INTEGER},</if>
        <if test="item.downloadCount != null">#{item.downloadCount,jdbcType=INTEGER},</if>
        <if test="item.createTime != null">#{item.createTime,jdbcType=TIMESTAMP},</if>
        <if test="item.modifyTime != null">#{item.modifyTime,jdbcType=TIMESTAMP},</if>
        <choose>
          <when test="item.extInfo != null">#{item.extInfo,jdbcType=LONGVARCHAR},</when>
          <otherwise>NULL,</otherwise>
        </choose>
      </trim>
    </foreach>
    ON DUPLICATE KEY UPDATE
    <trim suffixOverrides=",">
      user_type = VALUES(user_type),
      <if test="list[0].createCount != null">create_count = VALUES(create_count),</if>
      <if test="list[0].downloadCount != null">download_count = VALUES(download_count),</if>
      <if test="list[0].modifyTime != null">modify_time = VALUES(modify_time),</if>
      ext_info = VALUES(ext_info),
    </trim>
  </insert>
  <select id="selectModelCount" resultType="java.lang.Integer">
      SELECT COUNT(DISTINCT material_id) FROM stats_user_operate
      WHERE stats_type = 'DAILY' and `material_id` != -1 and user_id IN
      <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
          #{userId}
      </foreach>
  </select>
  <select id="selectCreativeCount" resultType="java.lang.Integer">
      SELECT SUM(JSON_LENGTH(JSON_EXTRACT(ext_info, '$.batchIdList'))) FROM stats_user_operate
      WHERE stats_type = 'DAILY' and user_id IN
      <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
          #{userId}
      </foreach>
      AND JSON_EXTRACT(ext_info, '$.batchIdList') IS NOT NULL
  </select>
  <select id="selectTopMaterialsByUsage" resultType="java.util.Map">
      SELECT
      material_id AS materialId,
      SUM(create_count + download_count) AS totalUsage,
      SUM(create_count) AS totalCreateCount,
      SUM(download_count) AS totalDownloadCount
      FROM stats_user_operate
      WHERE stats_type = 'DAILY' and user_id IN
      <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
          #{userId}
      </foreach>
      and material_id != 0 and material_id != -1
      GROUP BY material_id
      ORDER BY totalUsage DESC
      LIMIT #{size}
  </select>
  <select id="selectImageCount" resultType="java.lang.Integer">
    SELECT SUM(create_count) FROM stats_user_operate
    WHERE  stats_type = 'DAILY' and user_id IN
    <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
      #{userId}
    </foreach>
  </select>
  <select id="selectDownloadCount" resultType="java.lang.Integer">
    SELECT SUM(download_count) FROM stats_user_operate
    WHERE stats_type = 'DAILY' and user_id IN
    <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
      #{userId}
    </foreach>
  </select>
  <select id="selectUserOperateData" resultType="java.util.Map">
    SELECT
      user_id AS userId,
      SUM(create_count + download_count) AS totalUsage,
      SUM(create_count) AS totalCreateCount,
      SUM(download_count) AS totalDownloadCount
    FROM stats_user_operate
    WHERE stats_type = 'DAILY' and user_id IN
    <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
      #{userId}
    </foreach>
    GROUP BY user_id
    ORDER BY totalUsage DESC
  </select>
  
</mapper>