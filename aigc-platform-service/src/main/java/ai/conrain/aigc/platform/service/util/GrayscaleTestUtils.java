/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import java.util.concurrent.ThreadLocalRandom;

import lombok.extern.slf4j.Slf4j;

/**
 * 灰度实验工具类
 *
 * <AUTHOR>
 * @version : GrayscaleTestUtils.java, v 0.1 2025/2/27 15:25 renxiao.wu Exp $
 */
@Slf4j
public abstract class GrayscaleTestUtils {

    /**
     * 判断是否命中灰度实验
     *
     * @param probability 配置的标记
     * @param sceneName   场景名称，用于日志打印
     * @return true命中
     */
    public static boolean isHit(float probability, String sceneName) {
        if (probability <= 0) {
            log.info("当前{}场景，未命中灰度实验,{}", sceneName, probability);
            return false;
        }

        if (probability >= 1) {
            log.info("当前{}场景，命中灰度实验,{}", sceneName, probability);
            return true;
        }

        float random = ThreadLocalRandom.current().nextFloat();
        boolean result = random <= probability;

        log.info("当前{}场景，{}命中灰度实验,{},random={}", sceneName, result ? "" : "未", probability, random);
        return result;
    }

}
