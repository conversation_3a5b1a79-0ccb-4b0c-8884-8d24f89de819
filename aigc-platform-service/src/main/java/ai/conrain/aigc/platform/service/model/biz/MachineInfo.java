/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 机器信息
 *
 * <AUTHOR>
 * @version : MachineInfo.java, v 0.1 2025/3/8 11:11 renxiao.wu Exp $
 */
@Data
public class MachineInfo implements Serializable {
    private static final long serialVersionUID = -1856457069212898476L;
    /** 机器编号 */
    private String id;
    /** 机器名称 */
    private String name;
    /** 公网地址 */
    private String publicAddress;
    /** 内网ip */
    private String internalAddress;
    /** 状态 */
    private ServerStatusEnum status;
    /** 端口列表 */
    private List<MachinePort> ports;
}
