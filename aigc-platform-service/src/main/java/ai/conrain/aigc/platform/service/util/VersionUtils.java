package ai.conrain.aigc.platform.service.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本工具类
 * <p>
 * 支持日期+自增号格式的版本管理，版本格式为：YYYYMMDD.N
 * 例如：20250619.0, 20250619.1, 20250620.0
 * </p>
 * <p>
 * 主要功能：
 * <ul>
 *   <li>版本格式验证</li>
 *   <li>版本比较（支持 Comparable 接口）</li>
 *   <li>版本解析和构建</li>
 *   <li>获取下一个版本号</li>
 *   <li>获取当前日期的初始版本</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-19
 */
@Slf4j
public class VersionUtils {

    /**
     * 版本格式正则表达式：YYYYMMDD.N
     */
    private static final Pattern VERSION_PATTERN = Pattern.compile("^(\\d{8})\\.(\\d+)$");

    /**
     * 日期格式化器
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 版本信息类
     */
    @Data
    @AllArgsConstructor
    public static class Version implements Comparable<Version> {
        /**
         * 日期部分（格式：YYYYMMDD）
         */
        private String datePart;

        /**
         * 自增号部分
         */
        private Integer incrementPart;

        /**
         * 完整版本字符串
         */
        private String fullVersion;

        /**
         * 构造函数
         */
        public Version(String datePart, Integer incrementPart) {
            this.datePart = datePart;
            this.incrementPart = incrementPart;
            this.fullVersion = datePart + "." + incrementPart;
        }

        @Override
        public int compareTo(Version other) {
            if (other == null) {
                return 1;
            }

            // 首先比较日期部分
            int dateComparison = this.datePart.compareTo(other.datePart);
            if (dateComparison != 0) {
                return dateComparison;
            }

            // 日期相同时比较自增号
            return this.incrementPart.compareTo(other.incrementPart);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            Version version = (Version) obj;
            return Objects.equals(datePart, version.datePart) &&
                   Objects.equals(incrementPart, version.incrementPart);
        }

        @Override
        public int hashCode() {
            return Objects.hash(datePart, incrementPart);
        }

        @Override
        public String toString() {
            return fullVersion;
        }
    }

    /**
     * 验证版本格式是否正确
     *
     * @param version 版本字符串
     * @return true 如果格式正确，false 否则
     */
    public static boolean isValidVersion(String version) {
        if (StringUtils.isBlank(version)) {
            return false;
        }

        Matcher matcher = VERSION_PATTERN.matcher(version.trim());
        if (!matcher.matches()) {
            return false;
        }

        // 验证日期部分是否为有效日期
        String datePart = matcher.group(1);
        try {
            LocalDate.parse(datePart, DATE_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            log.warn("版本中的日期部分无效: {}", datePart);
            return false;
        }
    }
