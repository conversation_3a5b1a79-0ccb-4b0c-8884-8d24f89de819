package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.OrderInfoDAO;
import ai.conrain.aigc.platform.dal.entity.OrderInfoDO;
import ai.conrain.aigc.platform.dal.example.OrderInfoExample;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.InvoiceInfoService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.OrderSettlementService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.InvoiceStatus;
import ai.conrain.aigc.platform.service.enums.OrderStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.OrderNoGenHelper;
import ai.conrain.aigc.platform.service.helper.PricePlanHelper;
import ai.conrain.aigc.platform.service.model.biz.PricePlanCode;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.OrderInfoConverter;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.request.Topup2UserReq;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MerchantTopupVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.PricePlan;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * OrderInfoService实现
 *
 * <AUTHOR>
 * @version OrderInfoService.java v 0.1 2024-06-20 11:43:37
 */
@Slf4j
@Service
public class OrderInfoServiceImpl implements OrderInfoService {

    /** DAO */
    @Autowired
    private OrderInfoDAO orderInfoDAO;

    @Autowired
    private InvoiceInfoService invoiceInfoService;

    @Autowired
    private UserService userService;

    @Lazy
    @Autowired
    private DistributorService distributorService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserPointService userPointService;

    @Autowired
    private OrderNoGenHelper orderNoGenHelper;

    @Autowired
    @Lazy
    private OrderSettlementService orderSettlementService;

    @Autowired
    private PricePlanHelper pricePlanHelper;

    @Override
    public OrderInfoVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        OrderInfoDO data = orderInfoDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return OrderInfoConverter.do2VO(data);
    }

    @Override
    public OrderInfoVO lockById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        OrderInfoDO data = orderInfoDAO.lockById(id);
        if (null == data) {
            return null;
        }

        return OrderInfoConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = orderInfoDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除OrderInfo失败");
    }

    @Override
    public OrderInfoVO insert(OrderInfoVO orderInfo) {
        AssertUtil.assertNotNull(orderInfo, ResultCode.PARAM_INVALID, "orderInfo is null");
        AssertUtil.assertTrue(orderInfo.getId() == null, ResultCode.PARAM_INVALID, "orderInfo.id is present");

        // 创建时间、修改时间兜底
        if (orderInfo.getCreateTime() == null) {
            orderInfo.setCreateTime(new Date());
        }

        if (orderInfo.getModifyTime() == null) {
            orderInfo.setModifyTime(new Date());
        }

        OrderInfoDO data = OrderInfoConverter.vo2DO(orderInfo);
        // 逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = orderInfoDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建OrderInfo失败");
        AssertUtil.assertNotNull(data.getId(), "新建OrderInfo返回id为空");
        orderInfo.setId(data.getId());
        return orderInfo;
    }

    /**
     * 充值到用户（线下转账）
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public OrderInfoVO topup2User(Topup2UserReq req) {

        UserVO merchant = userService.selectById(req.getUserId());
        AssertUtil.assertTrue(merchant != null && merchant.getUserType() == UserTypeEnum.MASTER, ResultCode.PARAM_INVALID, "用户不存在或非主账号");

        OrderInfoVO order = null;

        // 是否微信收款，如果是，这里需要应该是商家已经完成微信收款，这里需要查询订单
        boolean collectMoneyByWxQrCode = StringUtils.equals(req.getCollectMoneyType(), "wxQrCode");
        if (collectMoneyByWxQrCode) {
            AssertUtil.assertNotBlank(req.getCollectedMoneyOrderNo(), ResultCode.PARAM_INVALID, "微信收款订单号不能为空");

            OrderInfoQuery orderInfoQuery = new OrderInfoQuery();
            orderInfoQuery.setOrderNo(req.getCollectedMoneyOrderNo());
            List<OrderInfoVO> existed = this.queryOrderInfoList(orderInfoQuery);
            AssertUtil.assertTrue(CollectionUtils.isNotEmpty(existed) && existed.size() == 1, ResultCode.PARAM_INVALID, "微信收款订单号不存在或有重复");

            order = existed.get(0);

            // 缺省情况下的逻辑
        } else {
            // 生成订单
            order = new OrderInfoVO();
            order.setOrderNo(orderNoGenHelper.generateOrderNo());
            order.setOperatorUserId(merchant.getId());
            order.setOperatorUserNick(merchant.getNickName());
            order.setOperatorUserLoginId(merchant.getLoginId());
            order.setMasterUserId(merchant.getId());
            order.setMasterUserNick(merchant.getMasterNick());
            order.setMasterUserLoginId(merchant.getLoginId());
            order.setOriginalAmount(req.getAmount());
            order.setProductCode(req.getProductCode());
            order.setPayAmount(req.getAmount());

            JSONObject productDetail = CommonUtil.java2JSONObject(req);
            PricePlanCode pricePlanCode = PricePlanCode.getByCode(req.getProductCode());
            if (pricePlanCode != null) {
                PricePlan pricePlan = pricePlanHelper.queryPricePlanByCode(req.getProductCode(), OperationContextHolder.getOperatorUserId());
                productDetail.put("name", pricePlan.getName());
            }
            order.setProductDetail(productDetail.toJSONString());

            JSONObject payDetail = new JSONObject();
            payDetail.put("operatorId", OperationContextHolder.getOperatorUserId());
            payDetail.put("operatorNick", OperationContextHolder.getOperatorNick());
            payDetail.put("memo", req.getMemo());
            order.setPayDetail(payDetail.toJSONString());

            order.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());

            if (StringUtils.isNotBlank(req.getTopupDate())) {
                Date topupDate = DateUtils.parseSimpleDate(req.getTopupDate());

                Date now = new Date();
                if (DateUtils.isSameDay(topupDate, now)) {
                    order.setFinishTime(now);
                } else {
                    order.setFinishTime(topupDate);
                }

            } else {
                order.setFinishTime(new Date());
            }
            order.setCreateTime(order.getFinishTime());

            order = this.insert(order);

            String salesInfo = distributorService.getSalesInfoByMerchantId(order.getMasterUserId());
            DingTalkNoticeHelper.sendMsg2BizGroup(String.format("客户充值\n客户：%s\n金额：%s元\n销售：%s",
                    order.getMasterUserNick(), order.getPayAmount(), salesInfo));
        }

        // 充值muse点
        if (req.getMusePoint() != null && BigDecimalUtils.greaterThanZero(req.getMusePoint())) {
            userPointService.adjustMuse(merchant.getId(), req.getMusePoint(), req.getMemo());
        }

        // 充值赠送图片
        if (req.getCreativeImgCountGave() != null && req.getCreativeImgCountGave() > 0) {
            UserPointVO userPoint = userPointService.selectByUserId(merchant.getId());
            int give = 0;
            if (userPoint != null && userPoint.getGivePoint() != null) {
                give += userPoint.getGivePoint();
            }
            int experience = 0;
            if (userPoint != null && userPoint.getExperiencePoint() != null) {
                experience += userPoint.getExperiencePoint();
            }

            // experience保持原值
            userPointService.give(merchant.getId(), experience, give + req.getCreativeImgCountGave());
        }

        return order;
    }

    @Override
    public void updateByIdSelective(OrderInfoVO orderInfo) {
        AssertUtil.assertNotNull(orderInfo, ResultCode.PARAM_INVALID, "orderInfo is null");
        AssertUtil.assertTrue(orderInfo.getId() != null, ResultCode.PARAM_INVALID, "orderInfo.id is null");

        // 修改时间必须更新
        orderInfo.setModifyTime(new Date());
        OrderInfoDO data = OrderInfoConverter.vo2DO(orderInfo);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = orderInfoDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新OrderInfo失败，影响行数:" + n);
    }

    @Override
    public List<OrderInfoVO> queryOrderInfoList(OrderInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        OrderInfoExample example = OrderInfoConverter.query2Example(query);

        List<OrderInfoDO> list = orderInfoDAO.selectByExample(example);
        return OrderInfoConverter.doList2VOList(list);
    }

    @Override
    public Long queryOrderInfoCount(OrderInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        OrderInfoExample example = OrderInfoConverter.query2Example(query);
        long c = orderInfoDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询订单
     */
    @Override
    public PageInfo<OrderInfoVO> queryOrderInfoByPage(OrderInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<OrderInfoVO> page = new PageInfo<>();

        OrderInfoExample example = OrderInfoConverter.query2Example(query);
        long totalCount = orderInfoDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<OrderInfoDO> list = orderInfoDAO.selectByExample(example);
        page.setList(OrderInfoConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public OrderInfoVO getFirstOrder(Integer userId, BigDecimal notLessThanAmount) {
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
        AssertUtil.assertNotNull(notLessThanAmount, ResultCode.PARAM_INVALID, "notLessThanAmount is null");

        OrderInfoExample exam = new OrderInfoExample();
        exam.createCriteria().andMasterUserIdEqualTo(userId).andOriginalAmountGreaterThanOrEqualTo(notLessThanAmount).andLogicalDeleted(false);
        exam.setOrderByClause("id desc");
        exam.page(1, 1);

        List<OrderInfoDO> list = orderInfoDAO.selectByExample(exam);
        if (CollectionUtils.isNotEmpty(list)) {
            return OrderInfoConverter.do2VO(list.get(0));
        }

        return null;
    }

    @Override
    public List<Integer> findUsersWithMaxRechargeLessThan3999(Boolean greaterThan, List<Integer> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return null;
        }
        return orderInfoDAO.findUsersWithMaxRechargeCompare3999(greaterThan, userIdList, null, null);
    }

    @Override
    public List<Integer> findUsersWithMaxRechargeLessThan3999(Boolean greaterThan, List<Integer> userIdList, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return null;
        }
        return orderInfoDAO.findUsersWithMaxRechargeCompare3999(greaterThan, userIdList, startDate, endDate);
    }

    @Override
    public List<Integer> selectNoOrderOrLessThan3999(String yesterdayDate, List<Integer> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return null;
        }

        // 统计 userIdList中 有订单 的用户id
        List<Integer> haveOrderUserIdList = orderInfoDAO.selectNoOrderOrLessThan3999(yesterdayDate, userIdList, false);

        // 获取没有订单的用户id
        List<Integer> noOrderUserIdList = userIdList.stream().filter(id -> !haveOrderUserIdList.contains(id)).collect(Collectors.toList());

        // 查询出来是为（有订单 且  订单金额小于 3999的用户 id）
        List<Integer> integers = orderInfoDAO.selectNoOrderOrLessThan3999(yesterdayDate, haveOrderUserIdList, true);

        // 拼装没有订单的用户id 和  有订单且订单金额小于3999的用户id
        noOrderUserIdList.addAll(integers);

        // 返回结果
        return noOrderUserIdList;
    }

    /**
     * 查询满足最低充值金额的用户列表
     *
     * @param userIdList   用户ID列表
     * @param minAmount    最低充值金额
     * @param startDate    开始时间（可为null）
     * @param endDate      结束时间（可为null）
     * @return 满足条件的用户ID列表
     */
    @Override
    public List<Integer> findUsersWithMinRechargeAmount(List<Integer> userIdList, BigDecimal minAmount, 
                                                       String startDate, String endDate) {
        if (CollectionUtils.isEmpty(userIdList) || minAmount == null) {
            return new ArrayList<>();
        }
        
        OrderInfoQuery query = new OrderInfoQuery();
        query.setMasterUserIds(userIdList);
        query.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
        
        if (startDate != null) {
            query.setStartDate(startDate);
        }
        
        if (endDate != null) {
            query.setEndDate(endDate);
        }
        
        List<OrderInfoVO> orderInfoList = queryOrderInfoList(query);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return new ArrayList<>();
        }
        
        // 筛选出支付金额大于等于指定金额的订单，并获取用户ID
        return orderInfoList.stream()
                .filter(order -> order.getPayAmount() != null && order.getPayAmount().compareTo(minAmount) >= 0)
                .map(OrderInfoVO::getMasterUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 分页查询商家的充值记录
     *
     * @param query
     * @return
     */
    @Override
    public PageInfo<MerchantTopupVO> queryMerchantTopupByPage(OrderInfoQuery query) {
        PageInfo<MerchantTopupVO> ret = new PageInfo<>();

        PageInfo<OrderInfoVO> page = this.queryOrderInfoByPage(query);

        ret.setHasNextPage(page.isHasNextPage());
        ret.setSize(page.getSize());
        ret.setTotalCount(page.getTotalCount());

        if (CollectionUtils.isNotEmpty(page.getList())) {
            List<MerchantTopupVO> list = new ArrayList<>();
            for (OrderInfoVO vo : page.getList()) {
                list.add(conv2MerchantTopup(vo));
            }

            ret.setList(list);
        }

        return ret;
    }

    private MerchantTopupVO conv2MerchantTopup(OrderInfoVO vo) {
        MerchantTopupVO ret = new MerchantTopupVO();
        ret.setId(vo.getId());
        ret.setOrderNo(vo.getOrderNo());
        ret.setProductCode(vo.getProductCode());
        if (vo.getProductCode() != null && PricePlanCode.getByCode(vo.getProductCode()) != null) {
            ret.setProductName(CommonUtil.getProductShowName(vo));
        }

        ret.setPayAmount(vo.getPayAmount());
        ret.setCreateTime(vo.getCreateTime());
        ret.setOperatorUserLoginId(vo.getOperatorUserLoginId());
        ret.setMasterUserNickName(vo.getMasterUserNick());
        ret.setMasterCorpName(vo.getMasterCorpName());
        ret.setMasterUserId(vo.getMasterUserId());
        ret.setMasterLoginId(vo.getMasterUserLoginId());
        ret.setMasterUser(Objects.equals(vo.getMasterUserId(), vo.getOperatorUserId()));
        ret.setExpireTime(vo.getExpireTime());

        InvoiceInfoVO invoice = invoiceInfoService.queryInvoiceInfoByOrderId(vo.getId());
        if (invoice != null) {
            ret.setInvoiceStatus(invoice.getStatus());
            ret.setInvoiceFileUrl(invoice.getInvoiceDownloadUrl());
            if (invoice.getStatus() != null && InvoiceStatus.getByCode(invoice.getStatus()) != null) {
                ret.setInvoiceStatusName(InvoiceStatus.getByCode(invoice.getStatus()).getDesc());
            }

        } else {
            ret.setInvoiceStatus(InvoiceStatus.NONE.getCode());
            ret.setInvoiceStatusName(InvoiceStatus.NONE.getDesc());
        }

        return ret;
    }
}