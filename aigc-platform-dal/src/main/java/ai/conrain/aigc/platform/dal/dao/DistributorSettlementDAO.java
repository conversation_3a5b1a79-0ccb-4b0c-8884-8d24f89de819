package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO;
import ai.conrain.aigc.platform.dal.example.DistributorSettlementExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DistributorSettlementDAO {
    long countByExample(DistributorSettlementExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DistributorSettlementDO record);

    int insertSelective(DistributorSettlementDO record);

    List<DistributorSettlementDO> selectByExample(DistributorSettlementExample example);

    DistributorSettlementDO selectByPrimaryKey(Integer id);

    DistributorSettlementDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") DistributorSettlementDO record, @Param("example") DistributorSettlementExample example);

    int updateByExample(@Param("record") DistributorSettlementDO record, @Param("example") DistributorSettlementExample example);

    int updateByPrimaryKeySelective(DistributorSettlementDO record);

    int updateByPrimaryKey(DistributorSettlementDO record);

    int logicalDeleteByExample(@Param("example") DistributorSettlementExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    List<DistributorSettlementDO> querySettleStats(String month);

    DistributorSettlementDO lockById(Integer id);

    DistributorSettlementDO statsDistributorSettle(DistributorSettlementExample example);
}