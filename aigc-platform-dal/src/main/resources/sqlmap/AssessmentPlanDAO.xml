<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.AssessmentPlanDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="principal_type" jdbcType="VARCHAR" property="principalType" />
    <result column="principal_id" jdbcType="INTEGER" property="principalId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="plan_from_date" jdbcType="DATE" property="planFromDate" />
    <result column="plan_end_date" jdbcType="DATE" property="planEndDate" />
    <result column="kpi_target" jdbcType="LONGVARCHAR" property="kpiTarget" />
    <result column="kpi_actual" jdbcType="LONGVARCHAR" property="kpiActual" />
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
    <result column="creator_user_id" jdbcType="INTEGER" property="creatorUserId" />
    <result column="modify_user_id" jdbcType="INTEGER" property="modifyUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, principal_type, principal_id, type, status, plan_from_date, plan_end_date, creator_user_id, 
    modify_user_id, create_time, modify_time, deleted,
    kpi_target, kpi_actual, ext_info
  </sql>

  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.AssessmentPlanExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from assessment_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.AssessmentPlanExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from assessment_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from assessment_plan
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from assessment_plan
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from assessment_plan
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into assessment_plan (principal_type, principal_id, type, 
      status, plan_from_date, plan_end_date, 
      creator_user_id, modify_user_id, create_time, 
      modify_time, deleted, kpi_target, 
      kpi_actual, ext_info)
    values (#{principalType,jdbcType=VARCHAR}, #{principalId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{planFromDate,jdbcType=DATE}, #{planEndDate,jdbcType=DATE}, 
      #{creatorUserId,jdbcType=INTEGER}, #{modifyUserId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, #{kpiTarget,jdbcType=LONGVARCHAR}, 
      #{kpiActual,jdbcType=LONGVARCHAR}, #{extInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into assessment_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        principal_type,
      </if>
      <if test="principalId != null">
        principal_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="planFromDate != null">
        plan_from_date,
      </if>
      <if test="planEndDate != null">
        plan_end_date,
      </if>
      <if test="creatorUserId != null">
        creator_user_id,
      </if>
      <if test="modifyUserId != null">
        modify_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="kpiTarget != null">
        kpi_target,
      </if>
      <if test="kpiActual != null">
        kpi_actual,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        #{principalId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="planFromDate != null">
        #{planFromDate,jdbcType=DATE},
      </if>
      <if test="planEndDate != null">
        #{planEndDate,jdbcType=DATE},
      </if>
      <if test="creatorUserId != null">
        #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifyUserId != null">
        #{modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="kpiTarget != null">
        #{kpiTarget,jdbcType=LONGVARCHAR},
      </if>
      <if test="kpiActual != null">
        #{kpiActual,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.AssessmentPlanExample" resultType="java.lang.Long">
    select count(*) from assessment_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update assessment_plan
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.principalType != null">
        principal_type = #{record.principalType,jdbcType=VARCHAR},
      </if>
      <if test="record.principalId != null">
        principal_id = #{record.principalId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.planFromDate != null">
        plan_from_date = #{record.planFromDate,jdbcType=DATE},
      </if>
      <if test="record.planEndDate != null">
        plan_end_date = #{record.planEndDate,jdbcType=DATE},
      </if>
      <if test="record.creatorUserId != null">
        creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="record.modifyUserId != null">
        modify_user_id = #{record.modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.kpiTarget != null">
        kpi_target = #{record.kpiTarget,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.kpiActual != null">
        kpi_actual = #{record.kpiActual,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update assessment_plan
    set id = #{record.id,jdbcType=INTEGER},
      principal_type = #{record.principalType,jdbcType=VARCHAR},
      principal_id = #{record.principalId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      plan_from_date = #{record.planFromDate,jdbcType=DATE},
      plan_end_date = #{record.planEndDate,jdbcType=DATE},
      creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      modify_user_id = #{record.modifyUserId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      kpi_target = #{record.kpiTarget,jdbcType=LONGVARCHAR},
      kpi_actual = #{record.kpiActual,jdbcType=LONGVARCHAR},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update assessment_plan
    set id = #{record.id,jdbcType=INTEGER},
      principal_type = #{record.principalType,jdbcType=VARCHAR},
      principal_id = #{record.principalId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      plan_from_date = #{record.planFromDate,jdbcType=DATE},
      plan_end_date = #{record.planEndDate,jdbcType=DATE},
      creator_user_id = #{record.creatorUserId,jdbcType=INTEGER},
      modify_user_id = #{record.modifyUserId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    update assessment_plan
    <set>
      <if test="principalType != null">
        principal_type = #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        principal_id = #{principalId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="planFromDate != null">
        plan_from_date = #{planFromDate,jdbcType=DATE},
      </if>
      <if test="planEndDate != null">
        plan_end_date = #{planEndDate,jdbcType=DATE},
      </if>
      <if test="creatorUserId != null">
        creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifyUserId != null">
        modify_user_id = #{modifyUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="kpiTarget != null">
        kpi_target = #{kpiTarget,jdbcType=LONGVARCHAR},
      </if>
      <if test="kpiActual != null">
        kpi_actual = #{kpiActual,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    update assessment_plan
    set principal_type = #{principalType,jdbcType=VARCHAR},
      principal_id = #{principalId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      plan_from_date = #{planFromDate,jdbcType=DATE},
      plan_end_date = #{planEndDate,jdbcType=DATE},
      creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      modify_user_id = #{modifyUserId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      kpi_target = #{kpiTarget,jdbcType=LONGVARCHAR},
      kpi_actual = #{kpiActual,jdbcType=LONGVARCHAR},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    update assessment_plan
    set principal_type = #{principalType,jdbcType=VARCHAR},
      principal_id = #{principalId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      plan_from_date = #{planFromDate,jdbcType=DATE},
      plan_end_date = #{planEndDate,jdbcType=DATE},
      creator_user_id = #{creatorUserId,jdbcType=INTEGER},
      modify_user_id = #{modifyUserId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update assessment_plan set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update assessment_plan set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertOrUpdate" parameterType="ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO">
    insert into assessment_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="principalType != null">principal_type,</if>
      <if test="principalId != null">principal_id,</if>
      <if test="type != null">type,</if>
      <if test="status != null">status,</if>
      <if test="planFromDate != null">plan_from_date,</if>
      <if test="planEndDate != null">plan_end_date,</if>
      <if test="creatorUserId != null">creator_user_id,</if>
      <if test="modifyUserId != null">modify_user_id,</if>
      <if test="createTime != null">create_time,</if>
      <if test="modifyTime != null">modify_time,</if>
      <if test="deleted != null">deleted,</if>
      <if test="kpiTarget != null">kpi_target,</if>
      <if test="kpiActual != null">kpi_actual,</if>
      <if test="extInfo != null">ext_info,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="principalType != null">#{principalType,jdbcType=VARCHAR},</if>
      <if test="principalId != null">#{principalId,jdbcType=INTEGER},</if>
      <if test="type != null">#{type,jdbcType=VARCHAR},</if>
      <if test="status != null">#{status,jdbcType=VARCHAR},</if>
      <if test="planFromDate != null">#{planFromDate,jdbcType=DATE},</if>
      <if test="planEndDate != null">#{planEndDate,jdbcType=DATE},</if>
      <if test="creatorUserId != null">#{creatorUserId,jdbcType=INTEGER},</if>
      <if test="modifyUserId != null">#{modifyUserId,jdbcType=INTEGER},</if>
      <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
      <if test="modifyTime != null">#{modifyTime,jdbcType=TIMESTAMP},</if>
      <if test="deleted != null">#{deleted,jdbcType=BIT},</if>
      <if test="kpiTarget != null">#{kpiTarget,jdbcType=LONGVARCHAR},</if>
      <if test="kpiActual != null">#{kpiActual,jdbcType=LONGVARCHAR},</if>
      <if test="extInfo != null">#{extInfo,jdbcType=LONGVARCHAR},</if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="principalType != null">principal_type = #{principalType,jdbcType=VARCHAR},</if>
      <if test="principalId != null">principal_id = #{principalId,jdbcType=INTEGER},</if>
      <if test="type != null">type = #{type,jdbcType=VARCHAR},</if>
      <if test="status != null">status = #{status,jdbcType=VARCHAR},</if>
      <if test="planFromDate != null">plan_from_date = #{planFromDate,jdbcType=DATE},</if>
      <if test="planEndDate != null">plan_end_date = #{planEndDate,jdbcType=DATE},</if>
      <if test="modifyUserId != null">modify_user_id = #{modifyUserId,jdbcType=INTEGER},</if>
      <if test="deleted != null">deleted = #{deleted,jdbcType=BIT},</if>
      <if test="kpiTarget != null">kpi_target = #{kpiTarget,jdbcType=LONGVARCHAR},</if>
      <if test="kpiActual != null">kpi_actual = #{kpiActual,jdbcType=LONGVARCHAR},</if>
      <if test="extInfo != null">ext_info = #{extInfo,jdbcType=LONGVARCHAR},</if>
    </trim>
  </insert>
</mapper>