<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.InvoiceTitleDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="master_user_id" jdbcType="INTEGER" property="masterUserId" />
    <result column="operator_user_id" jdbcType="INTEGER" property="operatorUserId" />
    <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType" />
    <result column="subject_type" jdbcType="VARCHAR" property="subjectType" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="business_address" jdbcType="VARCHAR" property="businessAddress" />
    <result column="business_phone" jdbcType="VARCHAR" property="businessPhone" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, master_user_id, operator_user_id, invoice_type, subject_type, subject_name, credit_code, 
    business_address, business_phone, bank_name, bank_account, ext_info, deleted, create_time, 
    modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.InvoiceTitleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from invoice_title
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_title
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_title
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from invoice_title
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into invoice_title (master_user_id, operator_user_id, invoice_type, 
      subject_type, subject_name, credit_code, 
      business_address, business_phone, bank_name, 
      bank_account, ext_info, deleted, 
      create_time, modify_time)
    values (#{masterUserId,jdbcType=INTEGER}, #{operatorUserId,jdbcType=INTEGER}, #{invoiceType,jdbcType=VARCHAR}, 
      #{subjectType,jdbcType=VARCHAR}, #{subjectName,jdbcType=VARCHAR}, #{creditCode,jdbcType=VARCHAR}, 
      #{businessAddress,jdbcType=VARCHAR}, #{businessPhone,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{bankAccount,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into invoice_title
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="masterUserId != null">
        master_user_id,
      </if>
      <if test="operatorUserId != null">
        operator_user_id,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="subjectType != null">
        subject_type,
      </if>
      <if test="subjectName != null">
        subject_name,
      </if>
      <if test="creditCode != null">
        credit_code,
      </if>
      <if test="businessAddress != null">
        business_address,
      </if>
      <if test="businessPhone != null">
        business_phone,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="masterUserId != null">
        #{masterUserId,jdbcType=INTEGER},
      </if>
      <if test="operatorUserId != null">
        #{operatorUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="subjectType != null">
        #{subjectType,jdbcType=VARCHAR},
      </if>
      <if test="subjectName != null">
        #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="businessAddress != null">
        #{businessAddress,jdbcType=VARCHAR},
      </if>
      <if test="businessPhone != null">
        #{businessPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.InvoiceTitleExample" resultType="java.lang.Long">
    select count(*) from invoice_title
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update invoice_title
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.masterUserId != null">
        master_user_id = #{record.masterUserId,jdbcType=INTEGER},
      </if>
      <if test="record.operatorUserId != null">
        operator_user_id = #{record.operatorUserId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceType != null">
        invoice_type = #{record.invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="record.subjectType != null">
        subject_type = #{record.subjectType,jdbcType=VARCHAR},
      </if>
      <if test="record.subjectName != null">
        subject_name = #{record.subjectName,jdbcType=VARCHAR},
      </if>
      <if test="record.creditCode != null">
        credit_code = #{record.creditCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessAddress != null">
        business_address = #{record.businessAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.businessPhone != null">
        business_phone = #{record.businessPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccount != null">
        bank_account = #{record.bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update invoice_title
    set id = #{record.id,jdbcType=INTEGER},
      master_user_id = #{record.masterUserId,jdbcType=INTEGER},
      operator_user_id = #{record.operatorUserId,jdbcType=INTEGER},
      invoice_type = #{record.invoiceType,jdbcType=VARCHAR},
      subject_type = #{record.subjectType,jdbcType=VARCHAR},
      subject_name = #{record.subjectName,jdbcType=VARCHAR},
      credit_code = #{record.creditCode,jdbcType=VARCHAR},
      business_address = #{record.businessAddress,jdbcType=VARCHAR},
      business_phone = #{record.businessPhone,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_account = #{record.bankAccount,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO">
    update invoice_title
    <set>
      <if test="masterUserId != null">
        master_user_id = #{masterUserId,jdbcType=INTEGER},
      </if>
      <if test="operatorUserId != null">
        operator_user_id = #{operatorUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="subjectType != null">
        subject_type = #{subjectType,jdbcType=VARCHAR},
      </if>
      <if test="subjectName != null">
        subject_name = #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        credit_code = #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="businessAddress != null">
        business_address = #{businessAddress,jdbcType=VARCHAR},
      </if>
      <if test="businessPhone != null">
        business_phone = #{businessPhone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO">
    update invoice_title
    set master_user_id = #{masterUserId,jdbcType=INTEGER},
      operator_user_id = #{operatorUserId,jdbcType=INTEGER},
      invoice_type = #{invoiceType,jdbcType=VARCHAR},
      subject_type = #{subjectType,jdbcType=VARCHAR},
      subject_name = #{subjectName,jdbcType=VARCHAR},
      credit_code = #{creditCode,jdbcType=VARCHAR},
      business_address = #{businessAddress,jdbcType=VARCHAR},
      business_phone = #{businessPhone,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_account = #{bankAccount,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update invoice_title set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update invoice_title set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>