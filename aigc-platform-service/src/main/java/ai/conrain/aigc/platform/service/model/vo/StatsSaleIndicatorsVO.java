package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * StatsSaleIndicatorsVO
 *
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
@Data
public class StatsSaleIndicatorsVO implements Serializable, IExtModel {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    @ApiModelProperty(name = "statsType", value = "统计类型：DAILY/WEEKLY/MONTHLY/TOTAL")
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    @ApiModelProperty(name = "statsDate", value = "统计日期: 格式为yyyy-MM-dd")
    private String statsDate;

    /** 用户 id（渠道/销售/运营） */
    @ApiModelProperty(name = "userId", value = "用户 id（渠道/销售/运营）")
    private Integer userId;

    /** 名称（渠道/销售/运营） */
    @ApiModelProperty(name = "name", value = "名称（渠道/销售/运营）")
    private String name;

    /** 父级 id，默认为 0 */
    @ApiModelProperty(name = "parentId", value = "父级 id，默认为 0")
    private Integer parentId = 0;

    /** 服装体验量 */
    @ApiModelProperty(name = "clothesExpCount", value = "服装体验量")
    private Integer clothesExpCount;

    /** 客户转换量（新签 3999 以上） */
    @ApiModelProperty(name = "customerConversionCount", value = "客户转换量（新签 3999 以上）")
    private Integer customerConversionCount;

    /** 客户消耗点数 */
    @ApiModelProperty(name = "customerConsumptionPoints", value = "客户消耗点数")
    private Integer customerConsumptionPoints;

    /** 活跃客户率 */
    @ApiModelProperty(name = "customerActivityRate", value = "活跃客户率")
    private String customerActivityRate;

    /** 客户复购率 */
    @ApiModelProperty(name = "customerRepurchaseRate", value = "客户复购率")
    private String customerRepurchaseRate;

    /** 定制模特数量 */
    @ApiModelProperty(name = "customModelCustomers", value = "定制模特数量")
    private String customModelCustomers;

    /** 定制场景数量 */
    @ApiModelProperty(name = "customSceneCustomers", value = "定制场景数量")
    private String customSceneCustomers;

    /** 大于 60 天未充值的客户 */
    @ApiModelProperty(name = "customerProtectionMetrics", value = "大于 60 天未充值的客户")
    private Integer customerProtectionMetrics;

    /** 销售出图数量 */
    @ApiModelProperty(name = "createCount", value = "销售出图数量")
    private Integer createCount;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 扩展字段 */
    @ApiModelProperty(name = "extInfo", value = "扩展字段")
    private JSONObject extInfo;

    /** 子数据 */
    private List<StatsSaleIndicatorsVO> children;
}