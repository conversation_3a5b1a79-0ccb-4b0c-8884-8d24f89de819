package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * PromptDictVO
 *
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
@Data
public class PromptDictVO implements Serializable, ModifyTimeClz {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 名词 */
    @ApiModelProperty(name = "word", value = "名词")
    private String word;

    /** 对应的prompt */
    @ApiModelProperty(name = "prompt", value = "对应的prompt")
    private String prompt;

    /** 类型 */
    @ApiModelProperty(name = "type", value = "类型，COMMON、CLOTH_COLLOCATION")
    private DictTypeEnum type;

    /** 展示图url */
    @ApiModelProperty(name = "showImage", value = "展示图url")
    private String showImage;

    /** 标签列表，多个以逗号隔开 */
    @ApiModelProperty(name = "tags", value = "标签列表，多个以逗号隔开")
    private List<String> tags;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 用户id */
    @ApiModelProperty(name = "userId", value = "用户id")
    private Integer userId;

    /** 操作者id */
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

}