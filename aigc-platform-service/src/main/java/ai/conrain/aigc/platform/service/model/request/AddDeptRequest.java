package ai.conrain.aigc.platform.service.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddDeptRequest implements Serializable {

    /** 父级组织ID，根组织为0 */
    @ApiModelProperty(name = "parentId", value = "父级组织ID，根组织为0")
    @NotNull
    private Integer parentId;

    /** 组织名称 */
    @ApiModelProperty(name = "name", value = "组织名称")
    @NotBlank
    private String name;

    /** 销售类型 */
    @ApiModelProperty(name = "salesType", value = "销售类型")
    private String salesType;

    /** 二级渠道管理员昵称 */
    @ApiModelProperty(name = "nickName", value = "二级渠道管理员昵称")
    private String nickName;

    /** 二级渠道管理员手机号 */
    @ApiModelProperty(name = "mobile", value = "二级渠道管理员手机号")
    private String mobile;

    @ApiModelProperty(name = "customRole", value = "角色")
    private String customRole;
}
