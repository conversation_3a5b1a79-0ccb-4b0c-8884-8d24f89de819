package ai.conrain.aigc.platform.service.util;

import java.time.LocalDate;

/**
 * 版本工具类使用示例
 */
public class VersionUtilsExample {

    public static void main(String[] args) {
        System.out.println("=== 版本工具类使用示例 ===\n");

        // 1. 版本格式验证
        System.out.println("1. 版本格式验证:");
        System.out.println("20250619.0 是否有效: " + VersionUtils.isValidVersion("20250619.0"));
        System.out.println("20250619.abc 是否有效: " + VersionUtils.isValidVersion("20250619.abc"));
        System.out.println("20250632.0 是否有效: " + VersionUtils.isValidVersion("20250632.0"));
        System.out.println();

        // 2. 版本解析
        System.out.println("2. 版本解析:");
        VersionUtils.Version version = VersionUtils.parseVersion("20250619.5");
        if (version != null) {
            System.out.println("版本: " + version.getFullVersion());
            System.out.println("日期部分: " + version.getDatePart());
            System.out.println("自增号: " + version.getIncrementPart());
        }
        System.out.println();

        // 3. 版本比较
        System.out.println("3. 版本比较:");
        System.out.println("20250619.5 > 20250619.3: " + VersionUtils.isGreaterThan("20250619.5", "20250619.3"));
        System.out.println("20250619.3 < 20250620.0: " + VersionUtils.isLessThan("20250619.3", "20250620.0"));
        System.out.println("20250619.5 == 20250619.5: " + VersionUtils.isEqual("20250619.5", "20250619.5"));
        System.out.println();

        // 4. 版本生成
        System.out.println("4. 版本生成:");
        System.out.println("当前日期初始版本: " + VersionUtils.getCurrentDateInitialVersion());
        
        LocalDate testDate = LocalDate.of(2025, 6, 19);
        System.out.println("指定日期初始版本: " + VersionUtils.getDateInitialVersion(testDate));
        System.out.println("构建版本: " + VersionUtils.buildVersion(testDate, 3));
        System.out.println();

        // 5. 获取下一个版本
        System.out.println("5. 获取下一个版本:");
        String currentVersion = "20250619.5";
        System.out.println("当前版本: " + currentVersion);
        
        // 模拟同一天的下一个版本
        String nextSameDay = VersionUtils.getNextVersion(currentVersion, LocalDate.of(2025, 6, 19));
        System.out.println("同一天下一个版本: " + nextSameDay);
        
        // 模拟新一天的版本
        String nextNewDay = VersionUtils.getNextVersion(currentVersion, LocalDate.of(2025, 6, 20));
        System.out.println("新一天的版本: " + nextNewDay);
        System.out.println();

        // 6. 版本信息提取
        System.out.println("6. 版本信息提取:");
        String testVersion = "20250619.8";
        System.out.println("版本: " + testVersion);
        System.out.println("日期部分: " + VersionUtils.getDatePart(testVersion));
        System.out.println("自增号: " + VersionUtils.getIncrementPart(testVersion));
        System.out.println("版本日期: " + VersionUtils.getVersionDate(testVersion));
        System.out.println();

        // 7. 版本检查
        System.out.println("7. 版本检查:");
        LocalDate today = LocalDate.now();
        String todayVersion = VersionUtils.buildVersion(today, 0);
        System.out.println("今天的版本: " + todayVersion);
        System.out.println("是否为今天的版本: " + VersionUtils.isTodayVersion(todayVersion));
        System.out.println("是否为2025-06-19的版本: " + VersionUtils.isDateVersion(testVersion, LocalDate.of(2025, 6, 19)));
        System.out.println();

        // 8. 版本格式化
        System.out.println("8. 版本格式化:");
        System.out.println("版本描述: " + VersionUtils.getVersionDescription(testVersion));
        System.out.println("版本显示: " + VersionUtils.formatVersionDisplay(testVersion));
        System.out.println();

        // 9. 实际使用场景示例
        System.out.println("9. 实际使用场景示例:");
        demonstrateRealWorldUsage();
    }

    /**
     * 演示实际使用场景
     */
    private static void demonstrateRealWorldUsage() {
        System.out.println("模拟软件发布版本管理:");
        
        // 模拟今天发布第一个版本
        String firstVersion = VersionUtils.getCurrentDateInitialVersion();
        System.out.println("今天第一个版本: " + firstVersion);
        
        // 模拟发现bug，需要发布修复版本
        String bugFixVersion = VersionUtils.getNextVersion(firstVersion);
        System.out.println("Bug修复版本: " + bugFixVersion);
        
        // 模拟又发现问题，再次发布
        String anotherFixVersion = VersionUtils.getNextVersion(bugFixVersion);
        System.out.println("再次修复版本: " + anotherFixVersion);
        
        // 模拟明天发布新功能版本
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        String tomorrowVersion = VersionUtils.getNextVersion(anotherFixVersion, tomorrow);
        System.out.println("明天新功能版本: " + tomorrowVersion);
        
        // 版本比较
        System.out.println("\n版本比较结果:");
        System.out.println(firstVersion + " < " + bugFixVersion + ": " + 
                         VersionUtils.isLessThan(firstVersion, bugFixVersion));
        System.out.println(anotherFixVersion + " < " + tomorrowVersion + ": " + 
                         VersionUtils.isLessThan(anotherFixVersion, tomorrowVersion));
        
        // 版本信息展示
        System.out.println("\n版本信息展示:");
        System.out.println("第一个版本: " + VersionUtils.getVersionDescription(firstVersion));
        System.out.println("Bug修复版本: " + VersionUtils.getVersionDescription(bugFixVersion));
        System.out.println("明天版本: " + VersionUtils.getVersionDescription(tomorrowVersion));
    }
}
