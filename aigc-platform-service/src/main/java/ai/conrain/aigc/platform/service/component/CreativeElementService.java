package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.request.QueryAutoGenElementRecommendRequest;
import ai.conrain.aigc.platform.service.model.request.ResetOrderRequest;
import ai.conrain.aigc.platform.service.model.vo.ClothTypeScopeItem;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.UserFaceSceneVO;

import java.util.List;
import java.util.Map;

/**
 * 创作元素 Service定义
 *
 * <AUTHOR>
 * @version CreativeElementService.java v 0.1 2024-05-06 04:08:22
 */
public interface CreativeElementService {

    /**
     * 查询创作元素对象
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeElementVO selectById(Integer id);

    /**
     * 查询创作元素对象
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeElementVO selectByIdWithChildren(Integer id);

    /**
     * 查询创作元素对象主要字段
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeElementVO selectPrimaryInfoByIdWithChildren(Integer id);

    /**
     * 删除创作元素对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加创作元素对象
     *
     * @param creativeElement 对象参数
     * @return 返回结果
     */
    CreativeElementVO insert(CreativeElementVO creativeElement);

    /**
     * 修改创作元素对象
     *
     * @param creativeElement 对象参数
     */
    void updateById(CreativeElementVO creativeElement);

    /**
     * 按等级结构查询所有数据
     *
     * @param type    创作类型
     * @param bizType 业务类型
     * @param needAll 是否需要子节点
     * @return 查询结构
     */
    List<CreativeElementVO> queryAllByLevel(CreativeTypeEnum type, CreativeBizTypeEnum bizType, boolean needAll);

    /**
     * 按等级结构查询所有数据
     *
     * @param configKeys 配置类型
     * @param bizType    业务类型
     * @return 查询结构
     */
    List<CreativeElementVO> queryAllByLevel(List<String> configKeys, CreativeBizTypeEnum bizType);

    /**
     * 按等级结构和类型查询专属数据
     *
     * @param creativeType 类型
     * @param bizType      业务类型
     * @param needAll      是否查询所有
     * @return 查询结构
     */
    List<CreativeElementVO> queryAllByLevelOnlyExclusive(CreativeTypeEnum creativeType, CreativeBizTypeEnum bizType,
                                                         boolean needAll);

    /**
     * 按等级结构和类型查询专属数据
     *
     * @param configKeys 配置类型
     * @param bizType    业务类型
     * @return 查询结构
     */
    List<CreativeElementVO> queryAllByLevelOnlyExclusive(List<String> configKeys, CreativeBizTypeEnum bizType);

    /**
     * 按等级结构和类型查询所有数据
     *
     * @param key          类型
     * @param needChildren 是否需要子元素
     * @return 查询结构
     */
    List<CreativeElementVO> queryByLevelAndKey(String key, boolean needChildren);

    /**
     * 按等级结构和类型查询所有数据
     *
     * @param query 查询条件
     * @return 查询结构
     */
    List<CreativeElementVO> queryByLevel(CreativeElementQuery query);

    /**
     * 查询key 的根节点
     *
     * @param key key
     * @return 根节点
     */
    CreativeElementVO queryRootKey(String key);

    /**
     * 重置排序
     *
     * @param request 请求
     */
    void resetOrder(ResetOrderRequest request);

    /**
     * 查询自定义记录条数
     *
     * @param query 查询条件
     * @return 自定义记录条数
     */
    long countCustom(CreativeElementQuery query);

    /**
     * 分页查询系统元素
     *
     * @param query 查询条件
     * @return 元素列表
     */
    PageInfo<CreativeElementVO> queryByPage(CreativeElementQuery query);

    /**
     * 分页查询自定义元素
     *
     * @param query 查询条件
     * @return 元素列表
     */
    PageInfo<CreativeElementVO> queryCustomByPage(CreativeElementQuery query);

    /**
     * 基于id和level查询元素，如果level>2时，则递归查询到父级元素
     *
     * @param id    元素id
     * @param level 层级
     * @return 元素
     */
    CreativeElementVO queryByIdAndLevel(Integer id, int level);

    /**
     * 克隆元素
     *
     * @param id       元素id
     * @param fullCopy 是否完整克隆
     */
    void clone(Integer id, boolean fullCopy);

    /**
     * 重置lora场景元素
     *
     * @param e          当前元素
     * @param labelFiles 打标结果文件
     * @param loraName   lora地址
     */
    void resetStyleScene(CreativeElementVO e, List<FileVO> labelFiles, String loraName);

    /**
     * 重置lora模特元素
     *
     * @param e                   当前元素
     * @param labelFiles          打标结果文件
     * @param loraName            lora地址
     * @param faceImgRelativePath 模特图片相对路径
     */
    void resetLoraFace(CreativeElementVO e, List<FileVO> labelFiles, String loraName, String faceImgRelativePath);

    /**
     * 批量订正类型
     *
     * @param ids id列表
     */
    void batchCorrectType(List<Integer> ids);

    /**
     * 获取场景的服装类型范围（后台GPT离线计算）
     */
    List<ClothTypeScopeItem> autoCalcClothTypeScopeItem4Scene(Integer sceneElementId);

    /**
     * 查询自动生成元素推荐结果
     */
    List<CreativeElementVO> queryRecommendAutoGenElementsByCloth(QueryAutoGenElementRecommendRequest request);

    /**
     * 根据元素id获取所有子元素id, 包括自身
     *
     * @param elementId 元素id
     * @return id列表
     */
    List<Integer> getElementIdsWithChildren(Integer elementId);

    /**
     * 批量初始化训练类型
     *
     * @param ids id列表
     */
    void batchInitTrainType(List<Integer> ids);

    /**
     * 根据id列表批量查询数据
     *
     * @param ids id列表
     * @return 批量数据
     */
    List<CreativeElementVO> batchQueryByIds(List<Integer> ids);

    /**
     * 根据id列表批量查询数据,包括删除数据
     *
     * @param ids id列表
     * @return 批量数据
     */
    List<CreativeElementVO> batchQueryIncludesDelByIds(List<Integer> ids);

    /**
     * 根据id列表批量查询数据，并返回子元素数据
     *
     * @param ids id列表
     * @return 批量数据
     */
    List<CreativeElementVO> batchQueryByIdsWithChildren(List<Integer> ids);

    /**
     * 批量订正风格参数
     *
     * @param ids id列表
     */
    void batchCorrectLora(List<Integer> ids);

    /**
     * 更新实验标记
     *
     * @param id           id
     * @param experimental 实验标记
     */
    void updateExperimental(Integer id, boolean experimental);

    /**
     * 根据key列表，查询对应待处理的数据
     *
     * @param list key列表
     */
    Map<String, Integer> countNeedProcessByKeys(List<String> list);

    /**
     * 查询所有用户元素
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 用户元素列表
     */
    List<UserFaceSceneVO> selectAllUserElementByDate(String startDate, String endDate);

    /**
     * 是否童模
     *
     * @param face 模特元素
     * @return true, 童模
     */
    boolean isChildModel(CreativeElementVO face);

    /**
     * 查询用户专属元素
     *
     * @param configKey 配置key
     * @param maxCount  最大数量
     * @return 用户专属元素列表
     */
    List<CreativeElementVO> queryUserExclusiveElement(String configKey, int maxCount);

    /**
     * 批量设置场景动作
     *
     * @param sceneIdList 场景id列表
     */
    void batchSetScenePose(List<Integer> sceneIdList);
}