package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.PromptDictDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PromptDictDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(PromptDictDO record);

    PromptDictDO selectByPrimaryKey(Integer id);

    List<PromptDictDO> selectAll();

    int updateByPrimaryKey(PromptDictDO record);

    void batchInsert(List<PromptDictDO> list);
}