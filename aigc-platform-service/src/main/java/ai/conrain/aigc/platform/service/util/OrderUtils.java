/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;

import ai.conrain.aigc.platform.dal.entity.IdModel;

/**
 * 排序工具类
 *
 * <AUTHOR>
 * @version : OrderUtils.java, v 0.1 2024/12/13 22:06 renxiao.wu Exp $
 */
public abstract class OrderUtils {
    /**
     * 根据 JSONArray 对list重新排序
     *
     * @param list      待排序列表
     * @param jsonArray 排序规则
     */
    public static <T extends IdModel> void sortListByJsonArray(List<T> list, JSONArray jsonArray) {
        // 将 JSONArray 转为 Map，存储排序优先级
        Map<Integer, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            orderMap.put(jsonArray.getInteger(i), i);
        }

        // 排序
        list.sort((a, b) -> {
            Integer indexA = orderMap.get(a.getId());
            Integer indexB = orderMap.get(b.getId());

            if (indexA != null && indexB != null) {
                // 都存在于 JSONArray 中，按顺序排序
                return indexA - indexB;
            } else if (indexA != null) {
                // 只有 a 存在于 JSONArray 中，优先排序
                return -1;
            } else if (indexB != null) {
                // 只有 b 存在于 JSONArray 中，优先排序
                return 1;
            } else {
                // 都不存在于 JSONArray 中，按原顺序排序
                return 0;
            }
        });
    }
}
