/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

/**
 * 角色类型枚举
 *
 * <AUTHOR>
 * @version : RoleTypeEnum.java, v 0.1 2023/9/1 22:05 renxiao.wu Exp $
 */
@Getter
public enum RoleTypeEnum {
    /** 商户 */
    MERCHANT("MERCHANT", "商户"),

    /** 演示账号 */
    DEMO_ACCOUNT("DEMO_ACCOUNT","演示账号"),

    /** 采购商 */
    PURCHASER("PURCHASER", "采购商"),

    /** 平台运营 */
    OPERATOR("OPERATOR", "平台运营"),

    /** 审核员 */
    REVIEWER("REVIEWER", "审核员"),

    /**渠道商*/
    DISTRIBUTOR("DISTRIBUTOR", "渠道商"),

    /** 管理员 */
    ADMIN("ADMIN", "管理员"),

    /** 财务 */
    FINANCE("FINANCE", "财务"),

    /** 系统调度 */
    SYSTEM("SYSTEM", "系统调度"),

    /** 无权限控制 */
    NONE("NONE", "无权限控制"),

    ;

    private static final List<String> frontRoleList = new ArrayList<>(2);

    private static final List<String> backRoleList = new ArrayList<>(4);

    private static final List<String> backOpRoleList = new ArrayList<>(2);

    static {
        frontRoleList.add(PURCHASER.getCode());
        frontRoleList.add(MERCHANT.getCode());
        frontRoleList.add(DISTRIBUTOR.getCode());

        backRoleList.add(DEMO_ACCOUNT.getCode());
        backRoleList.add(OPERATOR.getCode());
        backRoleList.add(REVIEWER.getCode());
        backRoleList.add(FINANCE.getCode());
        backRoleList.add(ADMIN.getCode());

        backRoleList.add(SYSTEM.getCode());

        backOpRoleList.add(OPERATOR.getCode());
        backOpRoleList.add(FINANCE.getCode());
    }

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    RoleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static RoleTypeEnum getByCode(String code) {
        if (null == code) {
            return null;
        }

        for (RoleTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }

        return null;
    }

    public static List<String> getFrontRoleList() {
        return frontRoleList;
    }

    public static List<String> getBackRoleList() {
        return backRoleList;
    }

    public static List<String> getBackOpRoleList() {
        return backOpRoleList;
    }

    public boolean isBackRole() {
        return backRoleList != null && backRoleList.contains(this.getCode());
    }

    public boolean isFrontRole() {
        return frontRoleList != null && frontRoleList.contains(this.getCode());
    }

    public boolean isFrontRoleOrOperator() {
        return isFrontRole() || OPERATOR == this || DEMO_ACCOUNT == this;
    }
}
