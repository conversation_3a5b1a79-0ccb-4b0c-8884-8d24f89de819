package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_QUEUE_SIZE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE;

/**
 * CreativeTaskVO
 *
 * @version CreativeTaskService.java v 0.1 2024-05-22 12:04:27
 */
@Data
public class CreativeTaskVO implements IExtModel, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** 任务id */
    @ApiModelProperty(name = "id", value = "任务id")
    private Integer id;

    /** 批次id */
    @ApiModelProperty(name = "batchId", value = "批次id")
    private Integer batchId;

    /** 归属主账号id */
    @ApiModelProperty(name = "userId", value = "归属主账号id")
    private Integer userId;

    /** 模型id */
    @ApiModelProperty(name = "modelId", value = "模型id")
    private Integer modelId;

    @ApiModelProperty(name = "type", value = "任务类型")
    private CreativeTypeEnum type;

    /** 图片比例，3:4、1:1等 */
    @ApiModelProperty(name = "imageProportion", value = "图片比例，3:4、1:1等")
    private String imageProportion;

    /** 批次数量 */
    @ApiModelProperty(name = "batchCnt", value = "批次数量")
    private Integer batchCnt;

    /** ComfyUI返回的唯一标识 */
    @ApiModelProperty(name = "promptId", value = "ComfyUI返回的唯一标识")
    private String promptId;

    /** 结果图片路径 */
    @ApiModelProperty(name = "resultPath", value = "结果图片路径")
    private String resultPath;

    /** 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED */
    @ApiModelProperty(name = "status", value = "状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED")
    private CreativeStatusEnum status;

    /** 操作者id */
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** aigc请求参数 */
    @ApiModelProperty(name = "aigcRequest", value = "aigc请求参数")
    private String aigcRequest;

    /** 结果图片url列表 */
    @ApiModelProperty(name = "resultImages", value = "结果图片url列表")
    private List<String> resultImages;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private JSONObject extInfo;

    @Override
    public boolean isProcessing() {
        return status == CreativeStatusEnum.PROCESSING;
    }

    /**
     * 添加结果图片
     *
     * @param imageUrl 图片url
     */
    public void addResultImage(String imageUrl) {
        if (resultImages == null) {
            resultImages = new ArrayList<>();
        }

        resultImages.add(imageUrl);
    }

    public void clearResultImage() {
        if (resultImages != null) {
            resultImages.clear();
        }
    }

    public void clearExtInfo() {
        if (extInfo != null) {
            extInfo.remove(KEY_SCHEDULE);
            extInfo.remove(KEY_QUEUE_SIZE);
        }
    }
}