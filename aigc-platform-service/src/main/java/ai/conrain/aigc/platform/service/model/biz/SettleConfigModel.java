package ai.conrain.aigc.platform.service.model.biz;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SettleConfigModel {
    /** 结算类型 'AGENT_A' | 'AGENT_B' | 'SALE_KA' | 'SALE_IC' | 'SALE_SUB' | 'SALE_DEPT' */
    private String settleType;

    /** 结算日(0-31): 0表示月底结算，其他值表示每月几号结算, 如果当月没有这一天, 则结算日为当月最后一天 */
    private Integer calculationDate;

    /** 新签费率 */
    private BigDecimal newOrderCommRate;

    /** 续签费率 */
    private BigDecimal renewOrderCommRate;

    /** 是否需要被抽成 */
    private Boolean hasBeneficiary;

    /** 抽成对象 id */
    private Integer beneficiaryTargetId;

    /** 签约首年的抽成费率 */
    private BigDecimal initYrRate;

    /** 签约非首年的抽成费率 */
    private BigDecimal subseqYrsRate;

    /** 备注 */
    private String memo;

}
