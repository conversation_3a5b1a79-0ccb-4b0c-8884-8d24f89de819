package ai.conrain.aigc.platform.service.acm;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.AbstractListener;
import com.alibaba.nacos.api.config.listener.Listener;

import ai.conrain.aigc.platform.service.acm.listener.CacheRefreshListener;
import ai.conrain.aigc.platform.service.acm.listener.ResourceConfig;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.NULL_STR;

/**
 * https://help.aliyun.com/document_detail/94562.html?spm=a2c4g.95790.0.0.4d137462RhTAUy
 */
@Slf4j
@DependsOn("envUtil")
@Service
public class AcmConfigServiceImpl implements AcmConfigService, InitializingBean {

    private final Map<AcmResource, String> cache = new ConcurrentHashMap<>();

    private ConfigService configService;

    @Value("${nacos.config.access-key}")
    private String ak;

    @Value("${nacos.config.endpoint}")
    private String endpoint;

    @Value("${nacos.config.namespace}")
    private String namespace;

    @Value("${nacos.config.secret-key}")
    private String sk;

    /** 自定义监听器 */
    private final Map<AcmResource, Listener> customListener = new HashMap<>();

    @Autowired
    private CacheRefreshListener cacheRefreshListener;

    @Override
    public void afterPropertiesSet() {
        if (!enable()) {
            log.info("本地无法连接acm，忽略");
            return;
        }

        //添加自定义监听器
        customListener.put(cacheRefreshListener.getClass().getAnnotation(ResourceConfig.class).value(),
            cacheRefreshListener);

        try {
            doInitClient();
            fetchAllConfig();
        } catch (Throwable t) {
            log.error("init config client failed", t);
        }
    }

    /**
     * 获取分布式配置
     *
     * @param resource
     * @return
     */
    @Override
    public String getConfig(AcmResource resource) {
        if (resource == null || StringUtils.isBlank(resource.getDataId()) || StringUtils.isBlank(resource.getGroup())) {
            throw new IllegalArgumentException();
        }

        try {
            String c = doGetConfig(resource);
            log.info("getAcmConfig,resource:{},cfg:{}", resource.name(), c);
            return c;
        } catch (Throwable t) {
            log.error("getAcmConfig failed:" + resource.name(), t);
            return null;
        }
    }

    @Override
    public <T> T getConfigAsObject(AcmResource resource, Class<T> clz) {
        if (resource == null || clz == null) {
            throw new NullPointerException();
        }

        String s = this.getConfig(resource);
        if (StringUtils.isNotBlank(s) && CommonUtil.isValidJson(s)) {
            return JSONObject.parseObject(s, clz);
        }

        return null;
    }

    @Override
    public <T> List<T> getConfigAsArray(AcmResource resource, Class<T> clz) {
        if (resource == null || clz == null) {
            throw new NullPointerException();
        }

        String s = this.getConfig(resource);
        if (StringUtils.isNotBlank(s) && CommonUtil.isValidJsonArray(s)) {
            return JSONArray.parseArray(s, clz);
        }

        return null;
    }

    /**
     * 初始化acm客户端
     *
     * @throws Exception 异常
     */
    private void doInitClient() throws Exception {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.ENDPOINT, endpoint);
        properties.put(PropertyKeyConst.NAMESPACE, namespace);
        properties.put(PropertyKeyConst.ACCESS_KEY, ak);
        properties.put(PropertyKeyConst.SECRET_KEY, sk);

        configService = NacosFactory.createConfigService(properties);

        //注册监听器
        for (AcmResource r : AcmResource.values()) {
            Listener listener = customListener.containsKey(r) ? customListener.get(r) : buildDefaultListener(r);
            configService.addListener(r.getDataId(), r.getGroup(), listener);
        }
    }

    /**
     * 获取所有配置
     */
    private void fetchAllConfig() {
        if (configService == null) {
            return;
        }

        log.info("ACMConfigClient init success");
        for (AcmResource each : AcmResource.values()) {
            getConfig(each);
        }
        log.info("acm config init success,cache:{}", JSONObject.toJSONString(cache));
    }

    /**
     * 构建默认的监听器
     *
     * @param r 资源名称
     * @return 默认监听器
     */
    @NotNull
    private AbstractListener buildDefaultListener(AcmResource r) {
        return new AbstractListener() {
            @Override
            public void receiveConfigInfo(String s) {
                log.info("receive config,dataId:{}, cfg:{}", r.getDataId(), s);
                cache.put(r, s);
            }
        };
    }

    private String doGetConfig(AcmResource resource) throws Exception {
        if (!enable()) {
            return null;
        }
        if (cache.containsKey(resource)) {
            return StringUtils.equals(NULL_STR, cache.get(resource)) ? null : cache.get(resource);
        } else {
            if (configService == null) {
                doInitClient();
            }
            AssertUtil.assertNotNull(configService, "acm config client init failed");
            String c = configService.getConfig(resource.getDataId(), resource.getGroup(), 3000);
            //解决空指针问题
            if (c == null) {
                cache.put(resource, NULL_STR);
            } else {
                cache.put(resource, c);
            }
            return c;
        }
    }

    /**
     * 判断是否可用
     */
    private boolean enable() {
        return !EnvUtil.isLocalEnv();
    }
}