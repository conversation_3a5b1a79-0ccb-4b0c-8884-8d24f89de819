package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DistributorEnhancedVO extends DistributorSettleConfigVO {
    /** 当前的考核计划 */
    @ApiModelProperty(name = "currentPlan", value = "当前考核计划")
    private AssessmentPlanVO currentPlan;
}
