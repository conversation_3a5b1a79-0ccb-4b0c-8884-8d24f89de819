package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.biz.VideoClipTask;
import ai.conrain.aigc.platform.service.model.common.UserNickClz;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * CreativeBatchVO
 *
 * @version CreativeBatchService.java v 0.1 2024-05-10 10:13:22
 */
@Slf4j
@Data
public class CreativeBatchVO implements IExtModel, UserNickClz, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** 批次id */
    @ApiModelProperty(name = "id", value = "批次id")
    private Integer id;

    /** 模型id */
    @ApiModelProperty(name = "modelId", value = "模型id")
    private Integer modelId;

    @JsonIgnore
    @ApiModelProperty(name = "modelType", value = "模型类型")
    private ModelTypeEnum modelType;

    @ApiModelProperty(name = "type", value = "创作类型")
    private CreativeTypeEnum type;

    /** 归属主账号id */
    @JsonIgnore
    @ApiModelProperty(name = "userId", value = "归属主账号id")
    private Integer userId;

    /** 图片url */
    @ApiModelProperty(name = "showImage", value = "图片url")
    private String showImage;

    /** 图片比例，3:4、1:1等 */
    @ApiModelProperty(name = "imageProportion", value = "图片比例，3:4、1:1等")
    private String imageProportion;

    /** 批次数量 */
    @ApiModelProperty(name = "batchCnt", value = "批次数量")
    private Integer batchCnt;

    /** ComfyUI返回的唯一标识 */
    @JsonIgnore
    @ApiModelProperty(name = "promptId", value = "ComfyUI返回的唯一标识")
    private String promptId;

    /** 结果图片路径 */
    @JsonIgnore
    @ApiModelProperty(name = "resultPath", value = "结果图片路径")
    private String resultPath;

    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private JSONObject extInfo;

    /** 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED */
    @ApiModelProperty(name = "status", value = "状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED")
    private CreativeStatusEnum status;

    /** 操作者id */
    @JsonIgnore
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @JsonIgnore
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

	/** 标题 */
	@ApiModelProperty(name = "title", value = "标题")
	private String title;

	/** 业务类型 */
	@ApiModelProperty(name = "bizType", value = "业务类型, NORMAL, LOOK")
	private CreativeBizTypeEnum bizType;

	/** aigc请求参数 */
	@JsonIgnore
	@ApiModelProperty(name = "aigcRequest", value = "aigc请求参数")
	private String aigcRequest;

    /** 结果图片url列表 */
    @ApiModelProperty(name = "resultImages", value = "结果图片url列表")
    private List<String> resultImages;

    //操作人昵称
    private String operatorNick;

    //主账户昵称
    private String userNick;

    //模型名称
    private String modelName;

    //模型图片
    private String modelShowImg;

    //创作元素：角色人脸名、场景名
    private String faceName;
    private String sceneName;

    //用户对服装标记的颜色 red/green/gray/None（选择后取消）
    private String modelMarkedColor;

    //视频创作切片任务（可灵等）
    private List<VideoClipTask> videoClipGenTasks;

    // 创作任务列表
    private List<CreativeTaskVO> creativeTasksList;

    // 视频创作的单条视频时长
    private Integer timeSecs4Video;

    //是否为运营账号或虚拟商户账号的创作，用于图生视频后台页面是否可以‘换图’
    private boolean canShowChangeImg4VideoBatch;

    // 获取视频创作的单条视频时长
    public Integer getTimeSecs4Video() {
        if (this.timeSecs4Video == null) {
            if (this.extInfo != null && this.extInfo.containsKey(CommonConstants.KEY_TIME_SECS_4_VIDEO)) {
                return this.extInfo.getInteger(CommonConstants.KEY_TIME_SECS_4_VIDEO);
            }
            return 5;
        } else {
            return this.timeSecs4Video;
        }
    }

    public List<VideoClipTask> getUnCompletedVideoClipTasks() {
        List<VideoClipTask> videoClipTasks = this.videoClipGenTasks;
        if (videoClipTasks != null) {
            return videoClipTasks.stream().filter(
                    t -> !CommonTaskEnums.TaskStatus.COMPLETED.name().equals(t.getTaskStatus())).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * 添加结果图片
     *
     * @param imageUrl 图片url
     */
    public void addResultImage(String imageUrl) {
        if (resultImages == null) {
            resultImages = new ArrayList<>();
        }

        if (resultImages.contains(imageUrl)) {
            return;
        }

        resultImages.add(imageUrl);
    }

    /**
     * 添加结果图片
     *
     * @param imageUrls 图片url列表
     */
    public void addResultImage(List<String> imageUrls) {
        if (resultImages == null) {
            resultImages = new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(imageUrls)) {
            return;
        }

        //过滤已存在的图片，避免重复
        imageUrls.forEach(image -> {
            if (resultImages.contains(image)) {
                return;
            }

            //为解决类似：product_35272_0002_DpQNN.png和product_35272_0002_TPhTs.png的问题，需要对product_35272_0002前缀进行判断
            String url = StringUtils.substringBeforeLast(image, "?");
            String fileName = StringUtils.substringBeforeLast(url, "_");

            List<String> find = resultImages.stream().filter(each -> StringUtils.startsWith(each, fileName)).collect(
                Collectors.toList());
            if (CollectionUtils.isNotEmpty(find)) {
                log.warn("发现重复的图片{}和{}，跳过", image, find.get(0));
                return;
            }
            resultImages.add(image);
        });
    }

    @Override
    public boolean isProcessing() {
        return status == CreativeStatusEnum.PROCESSING;
    }

    public String getImageProportionName() {
        ProportionTypeEnum type = ProportionTypeEnum.getByCode(imageProportion);
        return type != null ? type.getWidth() + "x" + type.getHeight() : null;
    }
}