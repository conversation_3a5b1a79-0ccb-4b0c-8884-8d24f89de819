<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.CreativeBatchDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="model_id" jdbcType="INTEGER" property="modelId" />
    <result column="model_type" jdbcType="VARCHAR" property="modelType" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="show_image" jdbcType="VARCHAR" property="showImage" />
    <result column="image_proportion" jdbcType="VARCHAR" property="imageProportion" />
    <result column="batch_cnt" jdbcType="INTEGER" property="batchCnt" />
    <result column="prompt_id" jdbcType="VARCHAR" property="promptId" />
    <result column="result_path" jdbcType="VARCHAR" property="resultPath" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    <result column="aigc_request" jdbcType="LONGVARCHAR" property="aigcRequest" />
    <result column="result_images" jdbcType="LONGVARCHAR" property="resultImages" />
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>

  <!--  view-->
  <resultMap extends="ResultMapWithBLOBs" id="ResultMapView" type="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    <result column="operator_nick" jdbcType="VARCHAR" property="operatorNick" />
    <result column="user_nick" jdbcType="VARCHAR" property="userNick" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_show_img" jdbcType="VARCHAR" property="modelShowImg" />
    <result column="model_ext_info" jdbcType="VARCHAR" property="modelExtInfo" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List">
    id, model_id, model_type, type, user_id, show_image, image_proportion, batch_cnt, 
    prompt_id, result_path, status, operator_id, deleted, create_time, modify_time, title, 
    biz_type
  </sql>
  <sql id="Blob_Column_List">
    result_images, ext_info
  </sql>

  <!--  与视图查询配合查询-->
  <sql id="View_Column_List">
    operator_nick, user_nick, model_name, model_show_img, model_ext_info
  </sql>

  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.CreativeBatchExample" resultMap="ResultMapView">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    ,
    <include refid="View_Column_List" />
    from creative_batch_view

    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>

  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeBatchExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from creative_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from creative_batch
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapView">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    ,
    <include refid="View_Column_List"/>

    from creative_batch_view
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from creative_batch
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeBatchExample">
    delete from creative_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_batch (model_id, model_type, type, 
      user_id, show_image, image_proportion, 
      batch_cnt, prompt_id, result_path, 
      status, operator_id, deleted, 
      create_time, modify_time, title, 
      biz_type, aigc_request, result_images, 
      ext_info)
    values (#{modelId,jdbcType=INTEGER}, #{modelType,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{userId,jdbcType=INTEGER}, #{showImage,jdbcType=VARCHAR}, #{imageProportion,jdbcType=VARCHAR}, 
      #{batchCnt,jdbcType=INTEGER}, #{promptId,jdbcType=VARCHAR}, #{resultPath,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, #{deleted,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{title,jdbcType=VARCHAR}, 
      #{bizType,jdbcType=VARCHAR}, #{aigcRequest,jdbcType=LONGVARCHAR}, #{resultImages,jdbcType=LONGVARCHAR}, 
      #{extInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_batch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        model_id,
      </if>
      <if test="modelType != null">
        model_type,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="showImage != null">
        show_image,
      </if>
      <if test="imageProportion != null">
        image_proportion,
      </if>
      <if test="batchCnt != null">
        batch_cnt,
      </if>
      <if test="promptId != null">
        prompt_id,
      </if>
      <if test="resultPath != null">
        result_path,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="aigcRequest != null">
        aigc_request,
      </if>
      <if test="resultImages != null">
        result_images,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        #{modelId,jdbcType=INTEGER},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="showImage != null">
        #{showImage,jdbcType=VARCHAR},
      </if>
      <if test="imageProportion != null">
        #{imageProportion,jdbcType=VARCHAR},
      </if>
      <if test="batchCnt != null">
        #{batchCnt,jdbcType=INTEGER},
      </if>
      <if test="promptId != null">
        #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="resultPath != null">
        #{resultPath,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="aigcRequest != null">
        #{aigcRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="resultImages != null">
        #{resultImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeBatchExample" resultType="java.lang.Long">
    select count(*) from creative_batch_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update creative_batch
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.modelId != null">
        model_id = #{record.modelId,jdbcType=INTEGER},
      </if>
      <if test="record.modelType != null">
        model_type = #{record.modelType,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.showImage != null">
        show_image = #{record.showImage,jdbcType=VARCHAR},
      </if>
      <if test="record.imageProportion != null">
        image_proportion = #{record.imageProportion,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCnt != null">
        batch_cnt = #{record.batchCnt,jdbcType=INTEGER},
      </if>
      <if test="record.promptId != null">
        prompt_id = #{record.promptId,jdbcType=VARCHAR},
      </if>
      <if test="record.resultPath != null">
        result_path = #{record.resultPath,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.aigcRequest != null">
        aigc_request = #{record.aigcRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.resultImages != null">
        result_images = #{record.resultImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update creative_batch
    set id = #{record.id,jdbcType=INTEGER},
      model_id = #{record.modelId,jdbcType=INTEGER},
      model_type = #{record.modelType,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=INTEGER},
      show_image = #{record.showImage,jdbcType=VARCHAR},
      image_proportion = #{record.imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{record.batchCnt,jdbcType=INTEGER},
      prompt_id = #{record.promptId,jdbcType=VARCHAR},
      result_path = #{record.resultPath,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      title = #{record.title,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      aigc_request = #{record.aigcRequest,jdbcType=LONGVARCHAR},
      result_images = #{record.resultImages,jdbcType=LONGVARCHAR},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update creative_batch
    set id = #{record.id,jdbcType=INTEGER},
      model_id = #{record.modelId,jdbcType=INTEGER},
      model_type = #{record.modelType,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=INTEGER},
      show_image = #{record.showImage,jdbcType=VARCHAR},
      image_proportion = #{record.imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{record.batchCnt,jdbcType=INTEGER},
      prompt_id = #{record.promptId,jdbcType=VARCHAR},
      result_path = #{record.resultPath,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      title = #{record.title,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    update creative_batch
    <set>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=INTEGER},
      </if>
      <if test="modelType != null">
        model_type = #{modelType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="showImage != null">
        show_image = #{showImage,jdbcType=VARCHAR},
      </if>
      <if test="imageProportion != null">
        image_proportion = #{imageProportion,jdbcType=VARCHAR},
      </if>
      <if test="batchCnt != null">
        batch_cnt = #{batchCnt,jdbcType=INTEGER},
      </if>
      <if test="promptId != null">
        prompt_id = #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="resultPath != null">
        result_path = #{resultPath,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="aigcRequest != null">
        aigc_request = #{aigcRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="resultImages != null">
        result_images = #{resultImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    update creative_batch
    set model_id = #{modelId,jdbcType=INTEGER},
      model_type = #{modelType,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      show_image = #{showImage,jdbcType=VARCHAR},
      image_proportion = #{imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{batchCnt,jdbcType=INTEGER},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      result_path = #{resultPath,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      title = #{title,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      aigc_request = #{aigcRequest,jdbcType=LONGVARCHAR},
      result_images = #{resultImages,jdbcType=LONGVARCHAR},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO">
    update creative_batch
    set model_id = #{modelId,jdbcType=INTEGER},
      model_type = #{modelType,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      show_image = #{showImage,jdbcType=VARCHAR},
      image_proportion = #{imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{batchCnt,jdbcType=INTEGER},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      result_path = #{resultPath,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      title = #{title,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update creative_batch set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update creative_batch set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPromptIdWithBLOBs" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    , aigc_request
    from creative_batch
    where prompt_id = #{promptId,jdbcType=INTEGER}
  </select>

  <select id="getCreativeModelIds" resultType="java.lang.Integer">
    select distinct model_id
    from creative_batch
    where operator_id = #{operatorId,jdbcType=INTEGER} and deleted = false and `status` = 'FINISHED'
  </select>

  <select id="selectUnCompletedTopUser" parameterType="java.util.Map" resultMap="BaseResultMap">
    with user_batch as (
      select a.*, b.role_type,
        row_number() over (partition by a.user_id order by a.id) as rn
      from (
        select *, if(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.bizTag')) = 'systemGen',2,user_id) r_user_id
        from creative_batch
        where deleted = 0 and status not in ('FINISHED','FAILED')
          and type not in
          <foreach item="item" index="index" collection="exceptTypes" open="(" separator="," close=")">
            #{item}
          </foreach>
          <if test="includeTypes != null">
            and type in
            <foreach item="item" index="index" collection="includeTypes" open="(" separator="," close=")">
              #{item}
            </foreach>
          </if>
      ) a left join user_pipeline_mapping b on a.r_user_id = b.user_id
      where b.pipeline_id = #{pipelineId,jdbcType=INTEGER}
      order by a.id
    ),
    selected_user_batch as (
      select *
      from user_batch
      where rn &lt;= #{maxPerUser}
        and (
          role_type = 'MERCHANT'
        <if test="vipUserList != null">
          or r_user_id in
          <foreach item="item" index="index" collection="vipUserList" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        )
        and r_user_id not in (1,2)
      order by rn, create_time desc
    )

    <if test="includeProcessing">
    select <include refid="Base_Column_List" />
      , <include refid="Blob_Column_List" />
    from user_batch
    where status in ('PROCESSING')
    union all
    (
    </if>

    select <include refid="Base_Column_List" />
      , <include refid="Blob_Column_List" />
    from (
      select *, 1 rnn
      from selected_user_batch
      where status not in ('PROCESSING')

      union all
      select *, 2 rnn
      from user_batch
      where id not in (
          select id from selected_user_batch
        )
        and status not in ('PROCESSING')
        and r_user_id in (2)
        and rn &lt;= #{maxAutoCreateSize}

      union all
      select *, 3 rnn
      from user_batch
      where id not in (
          select id from selected_user_batch
        )
        and status not in ('PROCESSING')
        and role_type != 'MERCHANT'
        and r_user_id not in (1,2,3)

      union all
      select *, 4 rnn
      from user_batch
      where id not in (
          select id from selected_user_batch
        )
        and status not in ('PROCESSING')
        and r_user_id in (2)
        and rn &gt; #{maxAutoCreateSize}

      union all
      select *, 5 rnn
      from user_batch
      where id not in (
          select id from selected_user_batch
        )
        and status not in ('PROCESSING')
        and r_user_id in (1,3)
    ) t
    order by rnn, rn, id
    limit #{limit,jdbcType=INTEGER}

    <if test="includeProcessing">
    )
    </if>
  </select>

  <select id="selectByElements" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    , aigc_request
    from creative_batch
    where id in (
      select batch_id from creative_batch_elements where element_id in (
        <foreach collection="elementIds" item="item" separator=",">
          #{item,jdbcType=INTEGER}
        </foreach>
    ))
      and `status` = 'FINISHED' and deleted != 1
      <if test="userId != null">
        and user_id = #{userId,jdbcType=INTEGER}
      </if>
      <if test="testFlag != null">
        <choose>
          <when test="testFlag == true">
            and ext_info ->> '$.bizTag' = 'testCase'
          </when>
          <otherwise>
            and (ext_info ->> '$.bizTag' is null or ext_info ->> '$.bizTag' != 'testCase')
          </otherwise>
        </choose>
      </if>
    order by id desc
    limit #{limit,jdbcType=INTEGER}
  </select>

  <select id="queryCreateImageCntByModelId" resultType="java.lang.Long">
    select sum(batch_cnt)
    from creative_batch
    where model_id = #{modelId,jdbcType=INTEGER}
      and user_id = #{userId,jdbcType=INTEGER}
      <if test="sysGenFinished">
        and ext_info ->> '$.bizTag' = 'systemGen'
        and status = 'FINISHED'
      </if>
      and deleted = 0
  </select>

  <select id="queryQueuedUser" parameterType="ai.conrain.aigc.platform.dal.example.CreativeBatchExample" resultType="ai.conrain.aigc.platform.dal.entity.UserCountDO">
    select `user_id`, count(*) cnt
    from creative_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
      and user_id not in (select user_id from `creative_batch` where `status` =  'PROCESSING' and `deleted` != 1 and type not in ( 'CREATE_VIDEO' ))
    group by user_id
  </select>

  <select id="lockByPrimaryKey" resultType="ai.conrain.aigc.platform.dal.entity.CreativeBatchDO" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />,<include refid="Blob_Column_List" />
    from creative_batch
    where id = #{id,jdbcType=INTEGER} and deleted = 0 for update nowait
  </select>

  <select id="statsQueuedCreative" resultType="ai.conrain.aigc.platform.dal.entity.StatsQueuedCreativeDO">
    select sum(if(b.role_type = 'MERCHANT' and (memo is null or memo not like '%虚拟商家%'),1,0)) merchantCnt,
      sum(if(a.user_id in (1,2),1,0)) systemCnt,
      sum(if(b.role_type != 'MERCHANT' and a.user_id not in (1,2),1,0)) backUserCnt
    from `creative_batch` a LEFT JOIN `user` b on a.`user_id` = b.id
    where a.status = 'QUEUE' and a.deleted != 1
  </select>

  <select id="statsCustomerQueue" resultType="ai.conrain.aigc.platform.dal.entity.StatsUserQueuedCreativeDO">
    select user_id userId, nick_name nickName, count(*) total, sum(if(status='queue',1,0)) queueSize,
      sum(if(status='processing',1,0)) processingSize, min(create_time) minCreateTime
    from (
      select a.*,b.nick_name
      from `creative_batch` a
      left join user_vip_view b on a.user_id = b.user_id
      WHERE status in ('queue','processing' )
        and type not in ('CREATE_VIDEO')
        and deleted != 1
        and (JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.bizTag')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.bizTag')) != 'systemGen')
        and b.`user_id` is not null
    ) t
    group by user_id,nick_name
    order by sum(if(status='processing',1,0)),min(create_time)
  </select>

  <select id="selectLatestByPoseIds" resultMap="ResultMapWithBLOBs">
    WITH ranked_records AS (
      SELECT *,
             ROW_NUMBER() OVER (
               PARTITION BY ext_info ->> '$.currentPoseId' 
               ORDER BY create_time DESC
             ) as rn
      FROM creative_batch
      WHERE ext_info ->> '$.currentPoseId' IN 
      <foreach collection="poseIdList" item="poseId" open="(" separator="," close=")">
        #{poseId,jdbcType=VARCHAR}
      </foreach>
        AND type = 'POSE_SAMPLE_DIAGRAM'
        AND deleted != 1
    )
    SELECT 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    FROM ranked_records 
    WHERE rn = 1
  </select>

</mapper>