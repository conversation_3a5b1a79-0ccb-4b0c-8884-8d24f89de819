package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ImageCaseSyncRecordQuery
 *
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
@Data
public class ImageCaseSyncRecordQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键 id */
    @ApiModelProperty(name = "id", value = "主键 id")
    private Integer id;

    /** 图片案例id */
    @ApiModelProperty(name = "caseId", value = "图片案例id")
    private Integer caseId;

    /** 目标服务器 IP */
    @ApiModelProperty(name = "targetServer", value = "目标服务器 IP")
    private String targetServer;

    /** 目标存储目录 */
    @ApiModelProperty(name = "targetStorePath", value = "目标存储目录")
    private String targetStorePath;

    /** 目标图片完整地址 */
    @ApiModelProperty(name = "imageUrl", value = "目标图片完整地址")
    private String imageUrl;

    /** 图片上传时间 */
    @ApiModelProperty(name = "uploadTime", value = "图片上传时间")
    private Date uploadTime;

    /** 是否同步成功 */
    @ApiModelProperty(name = "isSuccess", value = "是否同步成功")
    private Boolean isSuccess;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

    /** 同步类型（badCase、精选图...） */
    @ApiModelProperty(name = "syncType", value = "同步类型（badCase、精选图...）")
    private String syncType;

}