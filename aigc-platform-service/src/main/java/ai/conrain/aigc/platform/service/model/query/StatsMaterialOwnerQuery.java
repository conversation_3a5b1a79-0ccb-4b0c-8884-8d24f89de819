package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * StatsMaterialOwnerQuery
 *
 * @version StatsMaterialOwnerService.java v 0.1 2025-04-30 03:31:59
 */
@Data
public class StatsMaterialOwnerQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    @ApiModelProperty(name = "statsType", value = "统计类型：DAILY/WEEKLY/MONTHLY/TOTAL")
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    @ApiModelProperty(name = "statsDate", value = "统计日期: 格式为yyyy-MM-dd")
    private String statsDate;

    /** 用户 id（为 0 时则是汇总） */
    @ApiModelProperty(name = "userId", value = "用户 id（为 0 时则是汇总）")
    private Integer userId;

    /** 用户名称 */
    @ApiModelProperty(name = "nickname", value = "用户名称")
    private String nickname;

    /** 交付数量 */
    @ApiModelProperty(name = "deliveryCount", value = "交付数量")
    private Integer deliveryCount;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 扩展字段 */
    @ApiModelProperty(name = "extInfo", value = "扩展字段")
    private String extInfo;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}