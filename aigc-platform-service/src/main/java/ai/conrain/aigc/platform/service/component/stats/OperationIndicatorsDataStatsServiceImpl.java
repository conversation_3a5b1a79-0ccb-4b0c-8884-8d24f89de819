package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.query.*;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service(value = "operationIndicatorsDataStatsServiceImpl")
public class OperationIndicatorsDataStatsServiceImpl extends AbstractDataStatsServiceImpl {

    @Autowired
    private StatsOperationIndicatorsService statsOperationIndicatorsService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserProfileService userProfileService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private UserPointLogService userPointLogService;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.OPERATION_INDICATORS;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                       boolean isTotal) {
        // 根据统计周期和是否汇总进行不同处理
        switch (periodEnum) {
            case DAILY:
                return executeDailyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case WEEKLY:
                return executeWeeklyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case MONTHLY:
                return executeMonthlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case QUARTERLY:
                return executeQuarterlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            default:
                return 0;
        }
    }

    /**
     * 执行日度业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计结果
     */
    private int executeDailyBusinessStats(String storageDate, String startDate, String endDate,
                                          StatsPeriodEnum periodEnum) {
        // 1、查询所有运营信息（管理员）
        List<UserVO> adminUserList = queryAdminUsers();
        if (CollectionUtils.isEmpty(adminUserList)) {
            return 0;
        }
        List<Integer> adminUserIdList = adminUserList.stream().map(UserVO::getId).collect(Collectors.toList());

        // 2、查询运营关联的用户列表
        Map<Integer, List<UserVO>> customerUSerList = queryCustomerUsers(adminUserIdList);

        // 3、 填充数据
        List<StatsOperationIndicatorsVO> rseultDataList = buildOperationIndicatorsData(adminUserList,
                customerUSerList, storageDate, startDate, endDate, periodEnum);

        // 4、保存数据
        return saveStatsData(rseultDataList);
    }

    /**
     * 执行周度业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计结果
     */
    private int executeWeeklyBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行月度业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计结果
     */
    private int executeMonthlyBusinessStats(String storageDate, String startDate, String endDate,
                                            StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行季度业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计结果
     */
    private int executeQuarterlyBusinessStats(String storageDate, String startDate, String endDate,
                                              StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行周期业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 返回处理的记录数
     */
    private int executePeriodBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum periodEnum) {
        // 查询某个时间段内的统计数据
        List<StatsOperationIndicatorsVO> sourceStatsList = statsOperationIndicatorsService
                .selectStatsInfoByDateAndPeriod(startDate, endDate, StatsPeriodEnum.DAILY.getCode());

        // 如果统计数据为空，则直接返回0
        if (CollectionUtils.isEmpty(sourceStatsList)) {
            log.info("执行{}周期统计时，时间范围{}至{}内未找到日统计数据", periodEnum.getDesc(), startDate, endDate);
            return 0;
        }

        // 查询所有运营信息（管理员）
        List<UserVO> adminUserList = queryAdminUsers();
        if (CollectionUtils.isEmpty(adminUserList)) {
            return 0;
        }
        List<Integer> adminUserIdList = adminUserList.stream().map(UserVO::getId).collect(Collectors.toList());
        Map<Integer, List<UserVO>> customerUSerList = queryCustomerUsers(adminUserIdList);

        // 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 根据用户ID进行分组
        Map<Integer, List<StatsOperationIndicatorsVO>> userIdStatsMap = sourceStatsList.stream()
                .collect(Collectors.groupingBy(StatsOperationIndicatorsVO::getUserId));

        // 汇总每个用户的统计数据
        List<StatsOperationIndicatorsVO> targetStatsList = new ArrayList<>();

        for (Map.Entry<Integer, List<StatsOperationIndicatorsVO>> entry : userIdStatsMap.entrySet()) {
            Integer userId = entry.getKey();
            List<StatsOperationIndicatorsVO> userStats = entry.getValue();

            // 如果系统调度记录则进行跳过
            if (Objects.equals(userId, CommonUtil.mockSystemContext().getMasterUser())) {
                continue;
            }

            // 提取当前运营的客户列表
            List<UserVO> customerUserList = customerUSerList.get(userId);
            List<Integer> distinctCustomerIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(customerUserList)) {
                distinctCustomerIds = customerUserList.stream().map(UserVO::getId).distinct()
                        .collect(Collectors.toList());
            }

            // 确保有数据
            if (CollectionUtils.isEmpty(userStats)) {
                continue;
            }

            // 获取最新的一条记录，用于提取用户名称等基本信息
            StatsOperationIndicatorsVO firstStat = userStats.stream()
                    .max(Comparator.comparing(StatsOperationIndicatorsVO::getStatsDate))
                    .orElse(userStats.get(0));

            // 进行累加以及比例计算
            StatsOperationIndicatorsVO targetStats = new StatsOperationIndicatorsVO();
            targetStats.setUserId(userId);
            targetStats.setName(firstStat.getName());
            targetStats.setStatsDate(storageDate);
            targetStats.setStatsType(periodEnum.getCode());
            targetStats.setCreateTime(new Date());
            targetStats.setModifyTime(new Date());

            // 初始化各项指标的默认值，避免空值问题
            targetStats.setCustomerConversionCount(0);
            targetStats.setCustomerConsumptionPoints(0);
            targetStats.setCustomerConsumptionPointsAvg("0.00");
            targetStats.setCustomerActivityRate("0.00");
            targetStats.setCustomerRepurchaseRate("0.00");
            targetStats.setCustomModelCustomers("0.00");
            targetStats.setCustomSceneCustomers("0.00");
            targetStats.setCustomerProtectionMetrics(0);
            targetStats.setDeliveryClothingCount(0);
            targetStats.setApproveClothingCount(0);
            targetStats.setGarmentRebateRate("0.00");
            targetStats.setCustomerTotalCount(firstStat.getCustomerTotalCount());

            // 客户转换量 汇总
            int totalCustomerConversionCount = userStats.stream()
                    .filter(data -> data.getCustomerConversionCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getCustomerConversionCount)
                    .sum();

            // 客户消耗点数 汇总
            int totalCustomerConsumptionPoints = userStats.stream()
                    .filter(data -> data.getCustomerConsumptionPoints() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getCustomerConsumptionPoints)
                    .sum();

            // 交付服装量 汇总
            int totalDeliveryClothingCount = userStats.stream()
                    .filter(data -> data.getDeliveryClothingCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getDeliveryClothingCount)
                    .sum();

            // 审核服装量 汇总
            int totalApproveClothingCount = userStats.stream()
                    .filter(data -> data.getApproveClothingCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getApproveClothingCount)
                    .sum();

            // 视频数量 汇总
            int totalVideoCount = userStats.stream()
                    .filter(data -> data.getVideoCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getVideoCount)
                    .sum();

            // 待保护客户 (取 最后一天 的数据)
            targetStats.setCustomerProtectionMetrics(firstStat.getCustomerProtectionMetrics());

            // 设置累加的数值型指标
            targetStats.setCustomerConversionCount(totalCustomerConversionCount);
            targetStats.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
            targetStats.setDeliveryClothingCount(totalDeliveryClothingCount);
            targetStats.setApproveClothingCount(totalApproveClothingCount);
            targetStats.setVideoCount(totalVideoCount);

            // 计算加权平均的比率指标
            calculateWeightedAverageRatios(userStats, targetStats);

            // 重新计算模型比例
            getCustomModelRatio(userId, distinctCustomerIds, userFaceSceneList, targetStats);
            // 重新计算场景比例
            getCustomSceneRatio(userId, distinctCustomerIds, userFaceSceneList, targetStats);
            // 重新计算上传服装数量
            getCustomerUploadMaterialCount(startDate, endDate, userId, distinctCustomerIds, targetStats);

            // 将汇总的统计数据添加到结果列表中
            targetStatsList.add(targetStats);

            log.debug("完成用户ID:{}的{}周期统计，共汇总{}条日统计数据",
                    userId, periodEnum.getDesc(), userStats.size());
        }

        // 提取系统调度的记录
        List<StatsOperationIndicatorsVO> statsOperationIndicatorsVOList = userIdStatsMap
                .get(CommonUtil.mockSystemContext().getMasterUser());

        // 汇总系统调度的数据
        StatsOperationIndicatorsVO statsOperationIndicatorsVO = buildTotalPeriodBusinessStats(storageDate, startDate,
                endDate, periodEnum, statsOperationIndicatorsVOList, userFaceSceneList);

        // 添加至集合中
        targetStatsList.add(statsOperationIndicatorsVO);

        // 保存所有统计数据到数据库
        int insertCount = saveStatsData(targetStatsList);

        log.info("执行{}周期统计完成，时间范围{}至{}，共处理{}条记录",
                periodEnum.getDesc(), startDate, endDate, insertCount);

        return insertCount;
    }

    /**
     * 计算加权平均比率指标
     *
     * @param sourceStatsList 源统计数据列表
     * @param targetStats     目标统计数据
     */
    private void calculateWeightedAverageRatios(List<StatsOperationIndicatorsVO> sourceStatsList,
                                                StatsOperationIndicatorsVO targetStats) {
        // 累计所有指标的分子和分母
        int totalActivityRateMolecular = 0; // 活跃率分子（活跃客户数）
        int totalActivityRateDenominator = 0; // 活跃率分母（总客户数）

        int totalRepurchaseRateMolecular = 0; // 复购率分子（复购客户数）

        int totalGarmentRebateRateMolecular = 0; // 服装返点率分子（退点数量）
        int totalGarmentRebateRateDenominator = 0; // 服装返点率分母（总数量）

        // 从每个统计记录的extInfo中获取分子和分母信息，分别累加
        for (StatsOperationIndicatorsVO data : sourceStatsList) {
            if (data.getExtInfo() == null) {
                continue;
            }

            // 客户活跃率
            Integer activityRateMolecular = data
                    .getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR);
            Integer activityRateDenominator = data
                    .getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);
            if (activityRateMolecular != null && activityRateDenominator != null) {
                totalActivityRateMolecular += activityRateMolecular;
                totalActivityRateDenominator += activityRateDenominator;
            }

            // 客户复购率（复购客户数/所有客户数）

            Integer repurchaseRateMolecular = data
                    .getIntegerFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR);
            if (repurchaseRateMolecular != null) {
                totalRepurchaseRateMolecular += repurchaseRateMolecular;
            }

            // 服装返点率
            Integer garmentRebateRateMolecular = data
                    .getIntegerFromExtInfo(CommonConstants.GARMENT_REBATE_RATE_MOLECULAR);
            Integer garmentRebateRateDenominator = data
                    .getIntegerFromExtInfo(CommonConstants.GARMENT_REBATE_RATE_DENOMINATOR);
            if (garmentRebateRateMolecular != null && garmentRebateRateDenominator != null) {
                totalGarmentRebateRateMolecular += garmentRebateRateMolecular;
                totalGarmentRebateRateDenominator += garmentRebateRateDenominator;
            }
        }

        // 计算各项指标的比率并设置到结果对象中
        // 1. 计算客户活跃率
        calculateRatio(totalActivityRateMolecular, totalActivityRateDenominator,
                "客户活跃率", "活跃客户总数", "客户总数",
                targetStats,
                targetStats::setCustomerActivityRate,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);

        // 2. 计算客户复购率
        calculateRatio(totalRepurchaseRateMolecular, targetStats.getCustomerTotalCount(),
                "客户复购率", "复购客户总数", "有首付客户总数",
                targetStats,
                targetStats::setCustomerRepurchaseRate,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);

        // 5. 计算服装返点率
        calculateRatio(totalGarmentRebateRateMolecular, totalGarmentRebateRateDenominator,
                "服装返点率", "退点数量", "总服装数量",
                targetStats,
                targetStats::setGarmentRebateRate,
                CommonConstants.GARMENT_REBATE_RATE_MOLECULAR,
                CommonConstants.GARMENT_REBATE_RATE_DENOMINATOR);

        // 计算客户消耗点数平均值（总消耗点数/客户总数）
        if (targetStats.getCustomerConsumptionPoints() != null && targetStats.getCustomerConsumptionPoints() > 0) {
            targetStats.setCustomerConsumptionPointsAvg(String.format("%.2f",
                    (double) targetStats.getCustomerConsumptionPoints() / targetStats.getCustomerTotalCount()));
        } else {
            targetStats.setCustomerConsumptionPointsAvg("0.00");
        }

        // 计算视频数量平均值（视频数量/客户总数）
        if (targetStats.getVideoCount() != null && targetStats.getVideoCount() > 0) {
            targetStats.setVideoCountAvg(
                    String.format("%.2f", (double) targetStats.getVideoCount() / targetStats.getCustomerTotalCount()));
        } else {
            targetStats.setVideoCountAvg("0.00");
        }

    }

    /**
     * 计算比率并设置到结果对象中
     *
     * @param molecular       分子值
     * @param denominator     分母值
     * @param indicatorName   指标名称
     * @param molecularDesc   分子描述
     * @param denominatorDesc 分母描述
     * @param target          目标对象
     * @param setter          设置方法
     * @param molecularKey    分子键名
     * @param denominatorKey  分母键名
     */
    private void calculateRatio(int molecular, int denominator,
                                String indicatorName, String molecularDesc, String denominatorDesc,
                                StatsOperationIndicatorsVO target, Consumer<String> setter,
                                String molecularKey, String denominatorKey) {

        // 如果分母大于0，计算比率
        if (denominator > 0) {
            double ratio = (double) molecular / denominator * 100;
            String formattedRatio = String.format("%.2f", ratio);

            // 设置结果
            setter.accept(formattedRatio);

            // 保存分子分母到target的extInfo中
            target.addExtInfo(molecularKey, molecular);
            target.addExtInfo(denominatorKey, denominator);

            log.info("计算{} - {}:{}, {}:{}, 计算结果:{}%",
                    indicatorName, molecularDesc, molecular, denominatorDesc, denominator, formattedRatio);
        } else {
            // 设置默认值
            setter.accept("0.00");

            // 保存默认分子分母
            target.addExtInfo(molecularKey, 0);
            target.addExtInfo(denominatorKey, 0);

            log.info("计算{} - 无有效数据，设置为0.00", indicatorName);
        }
    }

    /**
     * 构建运营指标数据
     *
     * @param adminUserList    运营列表
     * @param customerUSerList 客户用户列表
     * @param storageDate      存储日期
     * @param startDate        开始日期
     * @param endDate          结束日期
     * @param periodEnum       统计周期
     * @return 运营指标数据列表
     */
    private List<StatsOperationIndicatorsVO> buildOperationIndicatorsData(List<UserVO> adminUserList,
                                                                          Map<Integer, List<UserVO>> customerUSerList,
                                                                          String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum) {
        // 构建返回值信息
        List<StatsOperationIndicatorsVO> resultDataList = new ArrayList<>();

        // 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 查询时间范围内的所有服装
        List<MaterialModelVO> materialModelVOList = getMaterModalList(startDate, endDate);

        adminUserList.forEach(adminUser -> {
            // 取出 id 和昵称
            Integer adminUserId = adminUser.getId();
            String adminUserNickName = adminUser.getNickName();

            // 提取当前运营的客户列表
            List<UserVO> customerUserList = customerUSerList.get(adminUserId);
            List<Integer> distinctCustomerIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(customerUserList)) {
                distinctCustomerIds = customerUserList.stream().map(UserVO::getId).distinct()
                        .collect(Collectors.toList());
            }

            // 初始化
            StatsOperationIndicatorsVO statsOperationIndicatorsVO = new StatsOperationIndicatorsVO();
            statsOperationIndicatorsVO.setStatsDate(storageDate);
            statsOperationIndicatorsVO.setStatsType(periodEnum.getCode());
            statsOperationIndicatorsVO.setUserId(adminUserId);
            statsOperationIndicatorsVO.setName(adminUserNickName);
            statsOperationIndicatorsVO.setCreateTime(new Date());
            statsOperationIndicatorsVO.setModifyTime(new Date());

            // 统计客户总数
            statsOperationIndicatorsVO.setCustomerTotalCount(distinctCustomerIds.size());

            // 11、上传服装总数
            getCustomerUploadMaterialCount(startDate, endDate, adminUserId, distinctCustomerIds,
                    statsOperationIndicatorsVO);
            // 1、客户转换量
            statsOperationIndicatorsVO.setCustomerConversionCount(
                    getCustomerConversionCount(startDate, endDate, adminUserId, distinctCustomerIds));
            // 2、客户消耗点数 / 平均消耗点数
            getCustomerConsumptionPoints(startDate, endDate, adminUserId, distinctCustomerIds,
                    statsOperationIndicatorsVO);
            // 3、活跃客户率
            getCustomerActivityRate(startDate, endDate, adminUserId, distinctCustomerIds,
                    statsOperationIndicatorsVO);
            // 4、客户复购率
            getCustomerRepurchaseRate(startDate, endDate, adminUserId, distinctCustomerIds,
                    statsOperationIndicatorsVO);
            // 5、定制模特比例
            getCustomModelRatio(adminUserId, distinctCustomerIds, userFaceSceneList, statsOperationIndicatorsVO);
            // 6、定制场景比例
            getCustomSceneRatio(adminUserId, distinctCustomerIds, userFaceSceneList, statsOperationIndicatorsVO);
            // 7、待保护客户
            getCustomerProtectionMetrics(endDate, adminUserId, distinctCustomerIds, statsOperationIndicatorsVO);
            // 8、交付服装量 和 审核服装量
            getMaterialDeliveryAndAuditCount(materialModelVOList, adminUserId, distinctCustomerIds,
                    statsOperationIndicatorsVO);
            // 9、服装返点率
            getGarmentRebateRate(startDate, endDate, adminUserId, distinctCustomerIds, statsOperationIndicatorsVO);
            // 10、用户视频数量
            getUserVideoCount(startDate, endDate, adminUserId, distinctCustomerIds, statsOperationIndicatorsVO);


            // 统计 Total 数据（今日数据累加昨日数据）
            StatsOperationIndicatorsVO totalOperateIndicatorsVO = buildTotalOperationIndicatorsData(adminUserId,
                    storageDate, statsOperationIndicatorsVO);

            // 添加进入集合
            resultDataList.add(statsOperationIndicatorsVO);
            resultDataList.add(totalOperateIndicatorsVO);

        });

        // vip客户列表
        List<UserVO> vipUserList = userService.queryAll3999VIPOrPaidCustomer();

        // 一、每日汇总数据
        StatsOperationIndicatorsVO dailyAllUserOperateIndicatorsVO = buildDailyAllUserOperationIndicatorsData(startDate,
                endDate, storageDate, periodEnum, userFaceSceneList, vipUserList);
        // 二、累计汇总数据
        StatsOperationIndicatorsVO totalAllUserOperateIndicatorsVO = buildTotalAllUserOperationIndicatorsData(
                storageDate, dailyAllUserOperateIndicatorsVO);

        resultDataList.add(dailyAllUserOperateIndicatorsVO);
        resultDataList.add(totalAllUserOperateIndicatorsVO);

        // 返回封装后的结果
        return resultDataList;
    }

    /**
     * 统计 Total 数据（今日数据累加昨日数据）
     *
     * @param adminUserId                运营id
     * @param storageDate                存储日期
     * @param statsOperationIndicatorsVO 运营指标数据
     * @return 运营指标数据
     */
    private StatsOperationIndicatorsVO buildTotalOperationIndicatorsData(Integer adminUserId, String storageDate,
                                                                         StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        // 构建一个新的TOTAL类型的统计对象
        StatsOperationIndicatorsVO totalIndicatorsVO = new StatsOperationIndicatorsVO();
        totalIndicatorsVO.setUserId(adminUserId);
        totalIndicatorsVO.setName(statsOperationIndicatorsVO.getName());
        totalIndicatorsVO.setStatsDate(storageDate);
        totalIndicatorsVO.setStatsType(StatsPeriodEnum.TOTAL.getCode());
        totalIndicatorsVO.setCreateTime(new Date());
        totalIndicatorsVO.setModifyTime(new Date());

        try {
            // 查询昨日的TOTAL记录
            StatsOperationIndicatorsQuery query = new StatsOperationIndicatorsQuery();
            query.setUserId(adminUserId);
            query.setStatsType(StatsPeriodEnum.TOTAL.getCode());
            // 计算昨日日期
            String yesterdayDate = DateUtils.formatSimpleDate(
                    DateUtils.addDays(Objects.requireNonNull(DateUtils.parseSimpleDate(storageDate)), -1));
            query.setStatsDate(yesterdayDate);

            List<StatsOperationIndicatorsVO> yesterdayTotalList = statsOperationIndicatorsService
                    .queryStatsOperationIndicatorsList(query);
            StatsOperationIndicatorsVO yesterdayTotal = null;

            if (!CollectionUtils.isEmpty(yesterdayTotalList)) {
                yesterdayTotal = yesterdayTotalList.get(0);
                log.info("获取到昨日累计数据 - 用户ID:{}, 日期:{}", adminUserId, yesterdayDate);
            } else {
                log.info("未找到昨日累计数据 - 用户ID:{}, 日期:{}, 将只使用今日数据", adminUserId, yesterdayDate);
            }

            // 统计客户总数
            totalIndicatorsVO.setCustomerTotalCount(statsOperationIndicatorsVO.getCustomerTotalCount());

            // 上传服装总数累计
            int totalCustomerUploadMaterialCount = statsOperationIndicatorsVO.getCustomerUploadMaterialCount() != null
                    ? statsOperationIndicatorsVO.getCustomerUploadMaterialCount()
                    : 0;
            if (yesterdayTotal != null && yesterdayTotal.getCustomerUploadMaterialCount() != null) {
                totalCustomerUploadMaterialCount += yesterdayTotal.getCustomerUploadMaterialCount();
            }
            totalIndicatorsVO.setCustomerUploadMaterialCount(totalCustomerUploadMaterialCount);

            // 1. 客户转换量累计
            int totalCustomerConversionCount = statsOperationIndicatorsVO.getCustomerConversionCount() != null
                    ? statsOperationIndicatorsVO.getCustomerConversionCount()
                    : 0;
            if (yesterdayTotal != null && yesterdayTotal.getCustomerConversionCount() != null) {
                totalCustomerConversionCount += yesterdayTotal.getCustomerConversionCount();
            }
            totalIndicatorsVO.setCustomerConversionCount(totalCustomerConversionCount);

            // 2. 客户消耗点数累计
            int totalCustomerConsumptionPoints = statsOperationIndicatorsVO.getCustomerConsumptionPoints() != null
                    ? statsOperationIndicatorsVO.getCustomerConsumptionPoints()
                    : 0;
            if (yesterdayTotal != null && yesterdayTotal.getCustomerConsumptionPoints() != null) {
                totalCustomerConsumptionPoints += yesterdayTotal.getCustomerConsumptionPoints();
            }
            totalIndicatorsVO.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);

            // 3. 使用昨日数据和今日数据，构建一个包含两天数据的List，用于计算加权平均指标
            List<StatsOperationIndicatorsVO> combinedStatsList = new ArrayList<>();
            combinedStatsList.add(statsOperationIndicatorsVO); // 添加今日数据
            if (yesterdayTotal != null) {
                combinedStatsList.add(yesterdayTotal); // 添加昨日数据
            }

            // 4. 使用calculateWeightedAverageRatios方法计算各项指标的平均值
            calculateWeightedAverageRatios(combinedStatsList, totalIndicatorsVO);

            // 5. 交付服装量累计
            int totalDeliveryClothingCount = statsOperationIndicatorsVO.getDeliveryClothingCount() != null
                    ? statsOperationIndicatorsVO.getDeliveryClothingCount()
                    : 0;
            if (yesterdayTotal != null && yesterdayTotal.getDeliveryClothingCount() != null) {
                totalDeliveryClothingCount += yesterdayTotal.getDeliveryClothingCount();
            }
            totalIndicatorsVO.setDeliveryClothingCount(totalDeliveryClothingCount);

            // 6. 审核服装量累计
            int totalApproveClothingCount = statsOperationIndicatorsVO.getApproveClothingCount() != null
                    ? statsOperationIndicatorsVO.getApproveClothingCount()
                    : 0;
            if (yesterdayTotal != null && yesterdayTotal.getApproveClothingCount() != null) {
                totalApproveClothingCount += yesterdayTotal.getApproveClothingCount();
            }
            totalIndicatorsVO.setApproveClothingCount(totalApproveClothingCount);

            // 7. 待保护客户使用今日数据
            totalIndicatorsVO.setCustomerProtectionMetrics(statsOperationIndicatorsVO.getCustomerProtectionMetrics());

            // 8.模特定制率(以及扩展信息)
            totalIndicatorsVO.setCustomModelCustomers(statsOperationIndicatorsVO.getCustomModelCustomers());
            totalIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR,
                    statsOperationIndicatorsVO.getExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR));
            totalIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR,
                    statsOperationIndicatorsVO.getExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR));

            // 9.场景定制率(以及扩展信息)
            totalIndicatorsVO.setCustomSceneCustomers(statsOperationIndicatorsVO.getCustomSceneCustomers());
            totalIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR,
                    statsOperationIndicatorsVO.getExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR));
            totalIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR,
                    statsOperationIndicatorsVO.getExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR));

            // 10.视频数量需要累加
            int totalVideoCount = statsOperationIndicatorsVO.getVideoCount() != null
                    ? statsOperationIndicatorsVO.getVideoCount()
                    : 0;
            if (yesterdayTotal != null && yesterdayTotal.getVideoCount() != null) {
                totalVideoCount += yesterdayTotal.getVideoCount();
            }
            totalIndicatorsVO.setVideoCount(totalVideoCount);

            // 11.视频客均量
            if (totalIndicatorsVO.getCustomerTotalCount() > 0) {
                totalIndicatorsVO.setVideoCountAvg(
                        String.format("%.2f", (double) totalVideoCount / totalIndicatorsVO.getCustomerTotalCount()));
            } else {
                totalIndicatorsVO.setVideoCountAvg("0.00");
            }

            log.info("构建累计数据完成 - 用户ID:{}, 客户转换量累计:{}, 消耗点数累计:{}, 交付服装累计:{}, 审核服装累计:{}, 上传服装累计:{}",
                    adminUserId, totalCustomerConversionCount, totalCustomerConsumptionPoints,
                    totalDeliveryClothingCount, totalApproveClothingCount, totalCustomerUploadMaterialCount);

        } catch (Exception e) {
            log.error("构建累计数据异常 - 用户ID:{}, 异常信息:{}", adminUserId, e.getMessage(), e);
        }

        return totalIndicatorsVO;
    }

    /**
     * 获取所有用户的服装信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 所有用户的定制元素信息
     */
    private List<MaterialModelVO> getMaterModalList(String startDate, String endDate) {
        MaterialModelQuery materialModelQuery = new MaterialModelQuery();
        materialModelQuery.setStartDate(startDate);
        materialModelQuery.setEndDate(endDate);
        List<MaterialModelVO> materialModelVOList = materialModelService.queryMaterialModelList(materialModelQuery);

        if (CollectionUtils.isEmpty(materialModelVOList)) {
            return null;
        }

        return materialModelVOList;
    }

    /**
     * 获取客户转换量
     *
     * @param startDate           开始日期
     * @param endDate             结束日期
     * @param adminUserId         运营id
     * @param distinctCustomerIds 客户用户列表
     * @return 客户转换量
     */
    private Integer getCustomerConversionCount(String startDate, String endDate, Integer adminUserId,
                                               List<Integer> distinctCustomerIds) {
        try {
            int conversionCount = 0;

            // 在指定日期范围内查询首次大额充值（≥3999）的客户
            List<Integer> newConvertedCustomers = new ArrayList<>();
            for (Integer customerId : distinctCustomerIds) {
                // 查询该客户是否在统计期前已有大额充值
                OrderInfoQuery beforeQuery = new OrderInfoQuery();
                beforeQuery.setMasterUserId(customerId);
                beforeQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                List<OrderInfoVO> beforeOrders = orderInfoService.queryOrderInfoList(beforeQuery);

                boolean hadBigPaymentBefore = false;
                if (!org.springframework.util.CollectionUtils.isEmpty(beforeOrders)) {
                    BigDecimal minAmount = new BigDecimal("3999");
                    // 筛选充值时间在开始日期之前的大额订单
                    for (OrderInfoVO order : beforeOrders) {
                        if (order.getPayAmount() != null &&
                                order.getPayAmount().compareTo(minAmount) >= 0 &&
                                order.getCreateTime() != null &&
                                order.getCreateTime().before(DateUtils.parseSimpleDate(startDate))) {
                            hadBigPaymentBefore = true;
                            break;
                        }
                    }
                }

                // 如果之前没有大额充值，检查统计期内是否有大额充值
                if (!hadBigPaymentBefore) {
                    OrderInfoQuery currentQuery = new OrderInfoQuery();
                    currentQuery.setMasterUserId(customerId);
                    currentQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                    // 仅查询统计期内的订单
                    currentQuery.setStartDate(startDate);
                    currentQuery.setEndDate(endDate);
                    List<OrderInfoVO> currentOrders = orderInfoService.queryOrderInfoList(currentQuery);

                    if (!org.springframework.util.CollectionUtils.isEmpty(currentOrders)) {
                        BigDecimal minAmount = new BigDecimal("3999");
                        // 检查是否有大额订单
                        boolean hasBigPayment = currentOrders.stream()
                                .anyMatch(order -> order.getPayAmount() != null &&
                                        order.getPayAmount().compareTo(minAmount) >= 0);

                        if (hasBigPayment) {
                            newConvertedCustomers.add(customerId);
                        }
                    }
                }
            }

            // 返回新签大额客户数量
            conversionCount = newConvertedCustomers.size();

            log.info("客户转换量统计 - 销售ID:{}, 总客户数:{}, 新签大额客户数:{}",
                    adminUserId, distinctCustomerIds.size(), conversionCount);

            return conversionCount;

        } catch (Exception e) {
            log.error("计算客户转换量异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取客户消耗点数 / 平均消耗点数
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营id
     * @param distinctCustomerIds        客户用户列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomerConsumptionPoints(String startDate, String endDate, Integer adminUserId,
                                              List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            int totalPoints = 0;

            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsOperationIndicatorsVO.setCustomerConsumptionPoints(0);
                statsOperationIndicatorsVO.setCustomerConsumptionPointsAvg("0.00");
                return;
            }

            // 查询客户在指定时间范围内的点数消耗总量
            Integer customerPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate,
                    distinctCustomerIds);

            // 将点数消耗转换为正值（假设消耗是负值）
            totalPoints += (customerPoints != null ? Math.abs(customerPoints) : 0);

            // 如果销售自身也有点数消耗，也应该计入
            List<Integer> salesUserIds = new ArrayList<>();
            salesUserIds.add(adminUserId);
            Integer salesOwnPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate, salesUserIds);
            totalPoints += (salesOwnPoints != null ? Math.abs(salesOwnPoints) : 0);

            log.info("客户消费点数统计 - 销售ID:{}, 客户数:{}, 总消耗点数:{}", adminUserId, distinctCustomerIds.size(), totalPoints);

            // 设置客户消耗点数
            statsOperationIndicatorsVO.setCustomerConsumptionPoints(totalPoints);

            // 设置客户消耗点数平均值(保留两位小数)(总消耗点数/客户总数)
            if (!distinctCustomerIds.isEmpty()) {
                statsOperationIndicatorsVO.setCustomerConsumptionPointsAvg(
                        String.format("%.2f", (double) totalPoints / distinctCustomerIds.size()));
            } else {
                statsOperationIndicatorsVO.setCustomerConsumptionPointsAvg("0.00");
            }

        } catch (Exception e) {
            log.error("计算客户消费点数异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            statsOperationIndicatorsVO.setCustomerConsumptionPoints(0);
            statsOperationIndicatorsVO.setCustomerConsumptionPointsAvg("0.00");
        }
    }

    /**
     * 获取客户活跃率
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营id
     * @param distinctCustomerIds        客户用户列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomerActivityRate(String startDate, String endDate, Integer adminUserId,
                                         List<Integer> distinctCustomerIds,
                                         StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || distinctCustomerIds.isEmpty()) {
                setDefaultCustomerActivityRate(statsOperationIndicatorsVO, distinctCustomerIds);
                return;
            }

            // 统计时间范围内有创作行为的客户数量
            int activeCustomerCount = 0;
            for (Integer customerId : distinctCustomerIds) {
                // 查询客户在时间范围内是否有创作行为（出图、出视频等）
                CreativeBatchQuery query = new CreativeBatchQuery();
                query.setUserId(customerId);
                query.setStartTime(startDate);
                query.setEndTime(endDate);
                query.setStatus(CreativeStatusEnum.FINISHED.getCode()); // 完成状态的创作

                List<CreativeBatchVO> batches = creativeBatchService.queryCreativeBatchList(query);
                if (!CollectionUtils.isEmpty(batches)) {
                    activeCustomerCount++;
                }
            }

            // 计算活跃率并格式化为两位小数的字符串
            double activityRate = (double) activeCustomerCount / distinctCustomerIds.size() * 100;
            String formattedRate = String.format("%.2f", activityRate);

            log.info("客户活跃率统计 - 销售ID:{}, 实际客户总数:{}, 活跃客户数:{}, 活跃率:{}%",
                    adminUserId, distinctCustomerIds.size(), activeCustomerCount, formattedRate);

            // 设置活跃率(活跃客户数/客户总数)
            statsOperationIndicatorsVO.setCustomerActivityRate(
                    String.format("%.2f", (double) activeCustomerCount / distinctCustomerIds.size() * 100));
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR,
                    activeCustomerCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR,
                    distinctCustomerIds.size());

        } catch (Exception e) {
            log.error("计算客户活跃率异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultCustomerActivityRate(statsOperationIndicatorsVO, distinctCustomerIds);
        }
    }

    /**
     * 获取客户复购率
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营id
     * @param distinctCustomerIds        客户用户列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomerRepurchaseRate(String startDate, String endDate, Integer adminUserId,
                                           List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                setDefaultCustomerRepurchaseRate(statsOperationIndicatorsVO, distinctCustomerIds);
                return;
            }

            // 统计复购客户数
            int repurchaseCount = 0; // 复购客户数
            int customerCount = 0; // 客户总数（作为分母）

            // SQL中的计算方式：在指定时间范围内有复购行为（任何金额）的客户数量/付费客户数量
            for (Integer customerId : distinctCustomerIds) {
                // 先确认这个客户是否付费（VIP）用户
                boolean isVipUser = false;

                // 查找客户首次大额充值（>=3999元）的时间
                OrderInfoQuery allOrdersQuery = new OrderInfoQuery();
                allOrdersQuery.setMasterUserId(customerId);
                allOrdersQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                List<OrderInfoVO> allOrders = orderInfoService.queryOrderInfoList(allOrdersQuery);

                BigDecimal minAmount = new BigDecimal("3999");
                if (!CollectionUtils.isEmpty(allOrders)) {
                    // 检查是否有大额充值订单
                    isVipUser = allOrders.stream()
                            .anyMatch(order -> order.getPayAmount() != null &&
                                    order.getPayAmount().compareTo(minAmount) >= 0);
                }

                // 如果不是VIP用户，跳过
                if (!isVipUser) {
                    continue;
                }

                // 这是一个VIP客户，客户总数加1
                customerCount++;

                // 检查这个客户在统计期内是否有多次购买记录
                // 先计算该用户所有订单数量
                long totalOrderCount = allOrders.stream()
                        .filter(order -> order.getCreateTime() != null)
                        .count();

                // 计算统计期内的订单数量
                long orderCountInPeriod = allOrders.stream()
                        .filter(order -> order.getCreateTime() != null &&
                                isWithinPeriod(order.getCreateTime(), startDate, endDate))
                        .count();

                // 如果统计期内有订单且总订单数>1，认为有复购行为
                if (orderCountInPeriod > 0 && totalOrderCount > 1) {
                    repurchaseCount++;
                }
            }

            // 计算复购率 = 复购客户数 / 客户总数
            if (customerCount == 0) {
                setDefaultCustomerRepurchaseRate(statsOperationIndicatorsVO, null);
                return;
            }

            double repurchaseRate = (double) repurchaseCount / customerCount * 100;
            String formattedRate = String.format("%.2f", repurchaseRate);

            log.info("客户复购率统计 - 销售ID:{}, 实际客户总数:{}, VIP客户数:{}, 复购客户数:{}, 复购率:{}%",
                    adminUserId, distinctCustomerIds.size(), customerCount, repurchaseCount, formattedRate);

            // 设置复购率
            statsOperationIndicatorsVO.setCustomerRepurchaseRate(formattedRate);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, repurchaseCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR, customerCount);

        } catch (Exception e) {
            log.error("计算客户复购率异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultCustomerRepurchaseRate(statsOperationIndicatorsVO, null);
        }
    }

    /**
     * 获取定制模特比例
     *
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户用户列表
     * @param userFaceSceneList          用户定制元素列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomModelRatio(Integer adminUserId, List<Integer> distinctCustomerIds,
                                     List<UserFaceSceneVO> userFaceSceneList, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || CollectionUtils.isEmpty(userFaceSceneList)) {
                setDefaultCustomModelRatio(statsOperationIndicatorsVO, distinctCustomerIds);
                return;
            }

            // 统计使用定制模特的客户数
            int customModelCustomerCount = 0;

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制模特
                boolean hasCustomModel = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getFaceCnt() != null && element.getFaceCnt() > 0);

                if (hasCustomModel) {
                    customModelCustomerCount++;
                }
            }

            // 计算定制模特比例 = 使用定制模特的客户数 / 总客户数
            double customModelRatio = (double) customModelCustomerCount / distinctCustomerIds.size() * 100;
            String formattedRatio = String.format("%.2f", customModelRatio);

            log.info("定制模特比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制模特客户数:{}, 定制模特比例:{}%",
                    adminUserId, distinctCustomerIds.size(), customModelCustomerCount, formattedRatio);

            // 设置定制模特比例
            statsOperationIndicatorsVO.setCustomModelCustomers(formattedRatio);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR,
                    customModelCustomerCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR,
                    distinctCustomerIds.size());

        } catch (Exception e) {
            log.error("计算定制模特比例异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultCustomModelRatio(statsOperationIndicatorsVO, distinctCustomerIds);
        }
    }

    /**
     * 获取定制场景比例
     *
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户用户列表
     * @param userFaceSceneList          用户定制元素列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomSceneRatio(Integer adminUserId, List<Integer> distinctCustomerIds,
                                     List<UserFaceSceneVO> userFaceSceneList, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || distinctCustomerIds.isEmpty()
                    || CollectionUtils.isEmpty(userFaceSceneList)) {
                setDefaultCustomSceneRatio(statsOperationIndicatorsVO, distinctCustomerIds);
                return;
            }

            // 统计使用定制场景的客户数
            int customSceneCustomerCount = 0; // 使用了定制场景的客户数

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制场景
                boolean hasCustomScene = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getSceneCnt() != null && element.getSceneCnt() > 0);

                if (hasCustomScene) {
                    customSceneCustomerCount++;
                }
            }

            // 计算定制场景比例 = 使用定制场景的客户数 / 总客户数
            double customSceneRatio = (double) customSceneCustomerCount / distinctCustomerIds.size() * 100;
            String formattedRatio = String.format("%.2f", customSceneRatio);

            log.info("定制场景比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制场景客户数:{}, 定制场景比例:{}%",
                    adminUserId, distinctCustomerIds.size(), customSceneCustomerCount, formattedRatio);

            // 设置定制场景比例
            statsOperationIndicatorsVO.setCustomSceneCustomers(formattedRatio);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR,
                    customSceneCustomerCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR,
                    distinctCustomerIds.size());

        } catch (Exception e) {
            log.error("计算定制场景比例异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultCustomSceneRatio(statsOperationIndicatorsVO, distinctCustomerIds);
        }
    }

    /**
     * 获取待保护客户
     *
     * @param endDate                    结束时间
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户用户列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomerProtectionMetrics(String endDate, Integer adminUserId, List<Integer> distinctCustomerIds,
                                              StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return;
            }

            // 获取准确的客户总数
            long totalCustomerCount = distinctCustomerIds.size();

            // 使用UserService中的queryBefore60Days方法获取所有超过60天创建且非VIP的用户
            List<SalesInfoVO> salesInfoList = userService.queryBefore60DaysByOperate(endDate);
            if (CollectionUtils.isEmpty(salesInfoList)) {
                return;
            }

            List<Integer> userIdList = salesInfoList.stream().map(SalesInfoVO::getUserId).distinct()
                    .collect(Collectors.toList());

            // 取userIdList和distinctCustomerIds的交集
            List<Integer> intersectionUserIds = userIdList.stream()
                    .filter(distinctCustomerIds::contains)
                    .collect(Collectors.toList());

            log.info("客户保护指标统计(超60天非VIP客户) - 运营ID:{}, 实际客户总数:{}, 超60天非VIP客户数:{}",
                    adminUserId, totalCustomerCount, intersectionUserIds.size());

            // 设置待保护客户数
            statsOperationIndicatorsVO.setCustomerProtectionMetrics(intersectionUserIds.size());
        } catch (Exception e) {
            log.error("计算客户保护指标异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            statsOperationIndicatorsVO.setCustomerProtectionMetrics(0);
        }
    }

    /**
     * 获取交付服装量
     *
     * @param materialModelVOList        时间区间 根据服装列表
     * @param adminUserId                运营id
     * @param distinctCustomerIds        客户用户列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getMaterialDeliveryAndAuditCount(List<MaterialModelVO> materialModelVOList, Integer adminUserId,
                                                  List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            // 1、获取交付服装数量
            int totalDeliveryCount = 0;
            // 交付服装 id 集合
            List<Integer> deliveryClothingIds = new ArrayList<>();

            // 2、获取审核服装数量
            int totalAuditCount = 0;
            // 审核服装 id 集合
            List<Integer> auditClothingIds = new ArrayList<>();

            // 获取当前运营相关客户的服装列表
            // 遍历所有服装模型数据
            if (materialModelVOList != null) {
                for (MaterialModelVO materialModel : materialModelVOList) {
                    if (!CollectionUtils.isEmpty(distinctCustomerIds)) {
                        // 检查该服装是否属于当前运营的客户
                        Integer userId = materialModel.getUserId();
                        if (userId != null && distinctCustomerIds.contains(userId)) {
                            deliveryClothingIds.add(materialModel.getId());
                            // 累计交付的服装数量
                            totalDeliveryCount++;
                        }
                    }

                    // 检查该服装的审核员是否为当前运营ID
                    try {
                        Integer reviewerId = materialModel.getExtInfo(CommonConstants.KEY_REVIEWER_ID, Integer.class);
                        if (adminUserId.equals(reviewerId)) {
                            auditClothingIds.add(materialModel.getId());
                            totalAuditCount++;
                        }
                    } catch (Exception e) {
                        log.warn("解析reviewerId异常, materialId: {}, error: {}", materialModel.getId(), e.getMessage());
                    }
                }
            }

            log.info("服装交付与审核统计 - 运营ID: {}, 客户总数: {}, 交付服装数: {}, 审核服装数: {}",
                    adminUserId, distinctCustomerIds.size(), totalDeliveryCount, totalAuditCount);

            // 设置交付服装数量
            statsOperationIndicatorsVO.setDeliveryClothingCount(totalDeliveryCount);
            // 设置审核服装数量
            statsOperationIndicatorsVO.setApproveClothingCount(totalAuditCount);

            // 设置服装 id 集合
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.KEY_MATERIAL_ID_LIST, deliveryClothingIds);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.KEY_AUDIT_MATERIAL_ID_LIST, auditClothingIds);

        } catch (Exception e) {
            log.error("获取服装交付和审核数量异常，用户ID: {}, 异常: {}", adminUserId, e.getMessage(), e);
            statsOperationIndicatorsVO.setDeliveryClothingCount(0);
            statsOperationIndicatorsVO.setApproveClothingCount(0);
        }
    }

    /**
     * 获取服装返点率
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营id
     * @param distinctCustomerIds        客户用户列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getGarmentRebateRate(String startDate, String endDate, Integer adminUserId,
                                      List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                setDefaultGarmentRebateRate(statsOperationIndicatorsVO);
                return;
            }

            // 筛选出累计充值金额大于3999的VIP用户
            List<Integer> vipCustomerIds = new ArrayList<>();
            for (Integer customerId : distinctCustomerIds) {
                // 查找客户充值情况
                OrderInfoQuery orderQuery = new OrderInfoQuery();
                orderQuery.setMasterUserId(customerId);
                orderQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                orderQuery.setBigThanAmount(new BigDecimal("3999"));
                List<OrderInfoVO> orders = orderInfoService.queryOrderInfoList(orderQuery);
                if (!orders.isEmpty()) {
                    vipCustomerIds.add(customerId);
                }
            }

            // 如果没有满足条件的VIP用户，直接返回
            if (CollectionUtils.isEmpty(vipCustomerIds)) {
                setDefaultGarmentRebateRate(statsOperationIndicatorsVO);
                return;
            }

            // 查询指定时间范围内VIP客户所有服装模型(包括已交付和已退点的)
            MaterialModelQuery query = new MaterialModelQuery();
            query.setStartDate(startDate);
            query.setEndDate(endDate);
            query.setUserIds(vipCustomerIds); // 使用筛选后的VIP用户ID列表

            List<MaterialModelVO> materialModelList = materialModelService.queryMaterialModelList(query);
            if (CollectionUtils.isEmpty(materialModelList)) {
                setDefaultGarmentRebateRate(statsOperationIndicatorsVO);
                return;
            }

            // 统计总数、交付数量和退点数量
            int totalCount = materialModelList.size();
            int enabledCount = 0; // 交付数量
            int disabledCount = 0; // 退点数量

            for (MaterialModelVO model : materialModelList) {
                if (MaterialModelStatusEnum.ENABLED.getCode().equals(model.getStatus())) {
                    enabledCount++;
                } else if (MaterialModelStatusEnum.DISABLED.getCode().equals(model.getStatus())) {
                    disabledCount++;
                }
            }

            // 计算返点率 = 退点数量 / 总数量
            double rebateRate = totalCount > 0 ? (double) disabledCount / totalCount * 100 : 0.0;
            String formattedRate = String.format("%.2f", rebateRate);

            log.info("服装返点率统计 - 运营ID:{}, VIP客户数:{}, 总服装数:{}, 交付数量:{}, 退点数量:{}, 返点率:{}%",
                    adminUserId, vipCustomerIds.size(), totalCount, enabledCount, disabledCount, formattedRate);

            // 设置服装返点率
            statsOperationIndicatorsVO.setGarmentRebateRate(formattedRate);

            // 添加扩展信息
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.GARMENT_REBATE_RATE_MOLECULAR, disabledCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.GARMENT_REBATE_RATE_DENOMINATOR, totalCount);

        } catch (Exception e) {
            log.error("计算服装返点率异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultGarmentRebateRate(statsOperationIndicatorsVO);
        }
    }

    /**
     * 获取用户视频数量
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户id 列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getUserVideoCount(String startDate, String endDate, Integer adminUserId,
                                   List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            // 如果客户列表为空，则直接返回
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsOperationIndicatorsVO.setVideoCount(0);
                statsOperationIndicatorsVO.setVideoCountAvg("0.00");
                return;
            }

            CreativeBatchQuery creativeBatchQuery = new CreativeBatchQuery();
            creativeBatchQuery.setStartTime(startDate);
            creativeBatchQuery.setEndTime(endDate);
            creativeBatchQuery.setType(CreativeTypeEnum.CREATE_VIDEO.getCode());
            creativeBatchQuery.setUserIds(distinctCustomerIds);
            List<CreativeBatchVO> creativeBatchVOList = creativeBatchService.queryCreativeBatchList(creativeBatchQuery);

            // 设置用户视频数量
            statsOperationIndicatorsVO.setVideoCount(creativeBatchVOList.size());

            if (!CollectionUtils.isEmpty(distinctCustomerIds) && !distinctCustomerIds.isEmpty()) {
                // 设置用户视频数量平均值
                statsOperationIndicatorsVO
                        .setVideoCountAvg(String.format("%.2f",
                                (double) creativeBatchVOList.size() / distinctCustomerIds.size()));
            } else {
                // 如果客户列表为空，设置平均值为0.00
                statsOperationIndicatorsVO.setVideoCountAvg("0.00");
            }

        } catch (Exception e) {
            log.error("获取用户视频数量异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            statsOperationIndicatorsVO.setVideoCount(0);
            statsOperationIndicatorsVO.setVideoCountAvg("0.00");
        }
    }

    /**
     * 获取上传服装总数
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户id 列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomerUploadMaterialCount(String startDate, String endDate, Integer adminUserId,
                                                List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {

        try {
            // 如果客户列表为空，直接返回
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsOperationIndicatorsVO.setCustomerUploadMaterialCount(0);
                return;
            }

            // 查询指定时间范围内已交付服装模型
            MaterialModelQuery materialModelQuery = new MaterialModelQuery();
            materialModelQuery.setStartDate(startDate);
            materialModelQuery.setEndDate(endDate);
            materialModelQuery.setUserIds(distinctCustomerIds);
            List<MaterialModelVO> materialModelVOList = materialModelService.queryMaterialModelList(materialModelQuery);

            // 设置上传服装总数
            statsOperationIndicatorsVO.setCustomerUploadMaterialCount(materialModelVOList.size());
        } catch (Exception e) {
            log.error("获取上传服装总数异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            statsOperationIndicatorsVO.setCustomerUploadMaterialCount(0);
        }
    }

    /**
     * 设置服装返点率默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void setDefaultGarmentRebateRate(StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        statsOperationIndicatorsVO.setGarmentRebateRate("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.GARMENT_REBATE_RATE_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.GARMENT_REBATE_RATE_DENOMINATOR, 0);
    }

    /**
     * 查询管理员用户
     *
     * @return 管理员用户列表
     */
    private List<UserVO> queryAdminUsers() {
        UserQuery userQuery = new UserQuery();
        userQuery.setRoleType(RoleTypeEnum.ADMIN.getCode());
        return userService.queryUsers(userQuery);
    }

    /**
     * 根据用户 id 查询客户 Id 信息
     *
     * @param adminUserIdList 运营 id 列表
     * @return 客户集合
     */
    private Map<Integer, List<UserVO>> queryCustomerUsers(List<Integer> adminUserIdList) {
        // 首先查询userProfile表
        UserProfileQuery userProfileQuery = new UserProfileQuery();
        userProfileQuery.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
        userProfileQuery.setProfileValList(adminUserIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        List<UserProfileVO> userProfileVOList = userProfileService.queryUserProfileList(userProfileQuery);
        if (CollectionUtils.isEmpty(userProfileVOList)) {
            return null;
        }
        List<Integer> userIdList = userProfileVOList.stream().map(UserProfileVO::getUid).collect(Collectors.toList());

        // 查询用户列表
        UserQuery userQuery = new UserQuery();
        userQuery.setIds(userIdList);
        userQuery.setRefundedMemo(Boolean.TRUE);
        List<UserVO> userVOList = userService.queryUsers(userQuery);
        if (CollectionUtils.isEmpty(userVOList)) {
            return null;
        }

        // 按照profileVal分组
        Map<String, List<UserProfileVO>> profileValMap = userProfileVOList.stream()
                .collect(Collectors.groupingBy(UserProfileVO::getProfileVal));

        // 构建最终结果 adminUserId -> 对应的客户用户列表
        Map<Integer, List<UserVO>> customerUserMap = new HashMap<>();
        profileValMap.forEach((adminUserId, profiles) -> {
            // 获取该运营关联的所有客户ID
            List<Integer> customerIds = profiles.stream().map(UserProfileVO::getUid).collect(Collectors.toList());

            // 从userVOList中筛选出这些客户
            List<UserVO> customers = userVOList.stream().filter(user -> customerIds.contains(user.getId()))
                    .collect(Collectors.toList());

            // 添加到结果Map中
            customerUserMap.put(Integer.parseInt(adminUserId), customers);
        });

        // 返回结果
        return customerUserMap;
    }

    /**
     * 判断日期是否在指定时间范围内
     *
     * @param date      要检查的日期
     * @param startDate 开始日期字符串
     * @param endDate   结束日期字符串
     * @return 是否在范围内
     */
    private boolean isWithinPeriod(Date date, String startDate, String endDate) {
        try {
            Date start = null;
            Date end = null;

            // 处理开始日期
            if (startDate.length() > 10 && startDate.contains(":")) {
                // 包含时间部分，使用完整格式解析
                start = DateUtils.parseSimple(startDate);
            } else {
                // 仅包含日期部分，解析为当天开始时间
                start = DateUtils.parseSimpleDate(startDate);
            }

            // 处理结束日期
            if (endDate.length() > 10 && endDate.contains(":")) {
                // 包含时间部分，使用完整格式解析
                end = DateUtils.parseSimple(endDate);
            } else {
                // 仅包含日期部分，解析为当天结束时间 (23:59:59)
                end = DateUtils.parseSimpleLastTime(endDate);
            }

            if (start == null || end == null) {
                log.error("日期解析失败，startDate: {}, endDate: {}", startDate, endDate);
                return false;
            }

            return date.compareTo(start) >= 0 && date.compareTo(end) <= 0;
        } catch (Exception e) {
            log.error("日期解析出错: {}, startDate={}, endDate={}", e.getMessage(), startDate, endDate, e);
            return false;
        }
    }

    /**
     * 保存统计数据
     *
     * @param statsList 统计数据列表
     * @return 影响行数
     */
    private int saveStatsData(List<StatsOperationIndicatorsVO> statsList) {
        if (!CollectionUtils.isEmpty(statsList)) {
            try {
                return statsOperationIndicatorsService.batchInsertOrUpdate(statsList);
            } catch (Exception e) {
                log.error("保存统计数据异常: {}", e.getMessage(), e);
                return 0;
            }
        }
        return 0;
    }

    /**
     * 设置客户活跃率默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     * @param distinctCustomerIds        去重后的客户ID列表
     */
    private void setDefaultCustomerActivityRate(StatsOperationIndicatorsVO statsOperationIndicatorsVO,
                                                List<Integer> distinctCustomerIds) {
        statsOperationIndicatorsVO.setCustomerActivityRate("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR,
                Objects.isNull(distinctCustomerIds) ? 0 : distinctCustomerIds.size());
    }

    /**
     * 设置客户复购率默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     * @param distinctCustomerIds        去重后的客户ID列表
     */
    private void setDefaultCustomerRepurchaseRate(StatsOperationIndicatorsVO statsOperationIndicatorsVO,
                                                  List<Integer> distinctCustomerIds) {
        statsOperationIndicatorsVO.setCustomerRepurchaseRate("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR,
                Objects.isNull(distinctCustomerIds) ? 0 : distinctCustomerIds.size());
    }

    /**
     * 设置定制模特比例默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     * @param distinctCustomerIds        去重后的客户ID列表
     */
    private void setDefaultCustomModelRatio(StatsOperationIndicatorsVO statsOperationIndicatorsVO,
                                            List<Integer> distinctCustomerIds) {
        statsOperationIndicatorsVO.setCustomModelCustomers("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR,
                Objects.isNull(distinctCustomerIds) ? 0 : distinctCustomerIds.size());
    }

    /**
     * 设置定制场景比例默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     * @param distinctCustomerIds        去重后的客户ID列表
     */
    private void setDefaultCustomSceneRatio(StatsOperationIndicatorsVO statsOperationIndicatorsVO,
                                            List<Integer> distinctCustomerIds) {
        statsOperationIndicatorsVO.setCustomSceneCustomers("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR,
                Objects.isNull(distinctCustomerIds) ? 0 : distinctCustomerIds.size());
    }

    /**
     * 构建每日汇总数据
     *
     * @param startDate         开始日期
     * @param endDate           结束日期
     * @param storageDate       存储日期
     * @param periodEnum        统计周期
     * @param userFaceSceneList 用户面数场景列表
     * @param vipUserList       vip用户列表
     * @return 运营指标数据
     */
    private StatsOperationIndicatorsVO buildDailyAllUserOperationIndicatorsData(String startDate, String endDate,
                                                                                String storageDate,
                                                                                StatsPeriodEnum periodEnum, List<UserFaceSceneVO> userFaceSceneList,
                                                                                List<UserVO> vipUserList) {

        // vip用户 id 列表
        List<Integer> vipUserIdList = vipUserList.stream().map(UserVO::getId).distinct().collect(Collectors.toList());

        // 用户 Id / 昵称
        Integer masterUserId = CommonUtil.mockSystemContext().getMasterUser();
        String masterNickname = CommonUtil.mockSystemContext().getMasterNick();

        // 素材模型列表
        List<MaterialModelVO> materialModelVOList = getMaterModalList(startDate, endDate);

        // 初始化(汇总数据使用 系统操作)
        StatsOperationIndicatorsVO statsOperationIndicatorsVO = new StatsOperationIndicatorsVO();
        statsOperationIndicatorsVO.setStatsDate(storageDate);
        statsOperationIndicatorsVO.setStatsType(periodEnum.getCode());
        statsOperationIndicatorsVO.setUserId(masterUserId);
        statsOperationIndicatorsVO.setName(masterNickname);
        statsOperationIndicatorsVO.setCreateTime(new Date());
        statsOperationIndicatorsVO.setModifyTime(new Date());

        // 统计客户总数
        statsOperationIndicatorsVO.setCustomerTotalCount(vipUserList.size());

        // 1、客户转换量 (无需设置，所有用户均为 vip 客户)
        statsOperationIndicatorsVO.setCustomerConversionCount(0);
        // 2、客户消耗点数 / 平均消耗点数
        getCustomerConsumptionPoints(startDate, endDate, masterUserId, vipUserIdList,
                statsOperationIndicatorsVO);
        // 3、活跃客户率
        getCustomerActivityRate(startDate, endDate, masterUserId, vipUserIdList,
                statsOperationIndicatorsVO);
        // 4、客户复购率
        getCustomerRepurchaseRate(startDate, endDate, masterUserId, vipUserIdList,
                statsOperationIndicatorsVO);
        // 5、定制模特比例
        getCustomModelRatio(masterUserId, vipUserIdList, userFaceSceneList, statsOperationIndicatorsVO);
        // 6、定制场景比例
        getCustomSceneRatio(masterUserId, vipUserIdList, userFaceSceneList, statsOperationIndicatorsVO);
        // 7、待保护客户
        getCustomerProtectionMetrics(endDate, masterUserId, vipUserIdList, statsOperationIndicatorsVO);
        // 8、交付服装量 和 审核服装量
        getMaterialDeliveryAndAuditCount(materialModelVOList, masterUserId, vipUserIdList,
                statsOperationIndicatorsVO);
        // 9、服装返点率
        getGarmentRebateRate(startDate, endDate, masterUserId, vipUserIdList, statsOperationIndicatorsVO);
        // 10、用户视频数量
        getUserVideoCount(startDate, endDate, masterUserId, vipUserIdList, statsOperationIndicatorsVO);
        // 11、上传服装总数
        getCustomerUploadMaterialCount(startDate, endDate, masterUserId, vipUserIdList,
                statsOperationIndicatorsVO);

        return statsOperationIndicatorsVO;
    }

    /**
     * 构建累计汇总数据
     *
     * @param storageDate                     存储日期
     * @param dailyAllUserOperateIndicatorsVO 今日汇总数据
     * @return 运营指标数据
     */
    private StatsOperationIndicatorsVO buildTotalAllUserOperationIndicatorsData(String storageDate, StatsOperationIndicatorsVO dailyAllUserOperateIndicatorsVO) {
        // 用户 Id / 昵称
        Integer masterUserId = CommonUtil.mockSystemContext().getMasterUser();

        // 构建累计汇总数据
        return buildTotalOperationIndicatorsData(masterUserId, storageDate, dailyAllUserOperateIndicatorsVO);
    }

    /**
     * 构建累计汇总数据
     *
     * @param storageDate       存储日期
     * @param startDate         开始日期
     * @param endDate           结束日期
     * @param periodEnum        统计周期
     * @param userStats         运营指标数据列表
     * @param userFaceSceneList 素材模型列表
     * @return 运营指标数据
     */
    private StatsOperationIndicatorsVO buildTotalPeriodBusinessStats(String storageDate, String startDate,
                                                                     String endDate, StatsPeriodEnum periodEnum,
                                                                     List<StatsOperationIndicatorsVO> userStats,
                                                                     List<UserFaceSceneVO> userFaceSceneList) {

        // 查询 vip  用户列表
        List<UserVO> vipUserList = userService.queryAllVIPOrPaidCustomer();

        // vip用户 id 列表
        List<Integer> vipUserIdList = vipUserList.stream().map(UserVO::getId).distinct().collect(Collectors.toList());

        // 用户Id / 昵称
        Integer masterUserId = CommonUtil.mockSystemContext().getMasterUser();
        String masterNickname = CommonUtil.mockSystemContext().getMasterNick();


        // 获取最新的一条记录，用于提取用户名称等基本信息
        StatsOperationIndicatorsVO firstStat = userStats.stream()
                .max(Comparator.comparing(StatsOperationIndicatorsVO::getStatsDate))
                .orElse(userStats.get(0));


        StatsOperationIndicatorsVO targetStats = new StatsOperationIndicatorsVO();
        targetStats.setUserId(masterUserId);
        targetStats.setName(masterNickname);
        targetStats.setStatsDate(storageDate);
        targetStats.setStatsType(periodEnum.getCode());
        targetStats.setCreateTime(new Date());
        targetStats.setModifyTime(new Date());


        // 初始化各项指标的默认值，避免空值问题
        targetStats.setCustomerConversionCount(0);
        targetStats.setCustomerConsumptionPoints(0);
        targetStats.setCustomerConsumptionPointsAvg("0.00");
        targetStats.setCustomerActivityRate("0.00");
        targetStats.setCustomerRepurchaseRate("0.00");
        targetStats.setCustomModelCustomers("0.00");
        targetStats.setCustomSceneCustomers("0.00");
        targetStats.setCustomerProtectionMetrics(0);
        targetStats.setDeliveryClothingCount(0);
        targetStats.setApproveClothingCount(0);
        targetStats.setGarmentRebateRate("0.00");
        targetStats.setCustomerTotalCount(firstStat.getCustomerTotalCount());

        // 客户消耗点数 汇总
        int totalCustomerConsumptionPoints = userStats.stream()
                .filter(data -> data.getCustomerConsumptionPoints() != null)
                .mapToInt(StatsOperationIndicatorsVO::getCustomerConsumptionPoints)
                .sum();

        // 交付服装量 汇总
        int totalDeliveryClothingCount = userStats.stream()
                .filter(data -> data.getDeliveryClothingCount() != null)
                .mapToInt(StatsOperationIndicatorsVO::getDeliveryClothingCount)
                .sum();

        // 审核服装量 汇总
        int totalApproveClothingCount = userStats.stream()
                .filter(data -> data.getApproveClothingCount() != null)
                .mapToInt(StatsOperationIndicatorsVO::getApproveClothingCount)
                .sum();

        // 视频数量 汇总
        int totalVideoCount = userStats.stream()
                .filter(data -> data.getVideoCount() != null)
                .mapToInt(StatsOperationIndicatorsVO::getVideoCount)
                .sum();

        // 待保护客户 (取 最后一天 的数据)
        targetStats.setCustomerProtectionMetrics(firstStat.getCustomerProtectionMetrics());

        // 设置累加的数值型指标
        targetStats.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
        targetStats.setDeliveryClothingCount(totalDeliveryClothingCount);
        targetStats.setApproveClothingCount(totalApproveClothingCount);
        targetStats.setVideoCount(totalVideoCount);

        // 计算加权平均的比率指标
        calculateWeightedAverageRatios(userStats, targetStats);

        // 重新计算模型比例
        getCustomModelRatio(masterUserId, vipUserIdList, userFaceSceneList, targetStats);
        // 重新计算场景比例
        getCustomSceneRatio(masterUserId, vipUserIdList, userFaceSceneList, targetStats);
        // 重新计算上传服装数量
        getCustomerUploadMaterialCount(startDate, endDate, masterUserId, vipUserIdList, targetStats);

        return targetStats;
    }

}
