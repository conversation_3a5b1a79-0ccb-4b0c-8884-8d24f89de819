package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.validation.Mobile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 更新子账户信息
 *
 * <AUTHOR>
 * @version : SubUserUpdate.java, v 0.1 2024/1/20 20:26 renxiao.wu Exp $
 */
@Data
public class SubUserUpdate implements Serializable {
    private static final long serialVersionUID = -1552472398698356181L;
    /** 用户id */
    @NotNull(message = "用户id不能为空")
    @ApiModelProperty(name = "userId", value = "用户id")
    private Integer userId;

    /** 手机号 */
    @Mobile(nullable = true)
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    /** 昵称 */
    @Size(min = 2, max = 50, message = "昵称长度必须在2-50之间")
    @ApiModelProperty(name = "nickName", value = "昵称")
    private String nickName;
}
