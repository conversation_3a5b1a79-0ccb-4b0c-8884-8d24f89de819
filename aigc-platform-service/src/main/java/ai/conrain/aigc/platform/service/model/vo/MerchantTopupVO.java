package ai.conrain.aigc.platform.service.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class MerchantTopupVO implements Serializable {

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 订单号 */
    @ApiModelProperty(name = "orderNo", value = "订单号")
    private String orderNo;

    /** 产品码 */
    @ApiModelProperty(name = "productCode", value = "产品码")
    private String productCode;

    // 产品名称
    private String productName;

    /** 订单支付金额 */
    @ApiModelProperty(name = "payAmount", value = "订单支付金额")
    private BigDecimal payAmount;

    private String payType;

    /** 订单完结时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(name = "finishTime", value = "订单时间")
    private Date createTime;

    /**
     * 过期时间（日期）
     */
    private String expireTime;

    /** 关联的操作员用户登录账号快照 */
    @ApiModelProperty(name = "operatorUserLoginId", value = "关联的操作员用户登录账号快照")
    private String operatorUserLoginId;

    private Integer masterUserId;
    private String masterLoginId;
    private String masterUserNickName;
    private String masterCorpName;

    //是否主账号
    private Boolean masterUser;

    /**
     * 开票状态
     * @see ai.conrain.aigc.platform.service.enums.InvoiceStatus
     */
    private String invoiceStatus;

    /**
     * 开票状态名
     */
    private String invoiceStatusName;

    /**
     * 发票文件url
     */
    private String invoiceFileUrl;
}
