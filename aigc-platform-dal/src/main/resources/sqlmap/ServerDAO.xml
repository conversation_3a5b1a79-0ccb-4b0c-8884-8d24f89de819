<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.ServerDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.ServerDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="config" jdbcType="VARCHAR" property="config" />
    <result column="config_alias" jdbcType="VARCHAR" property="configAlias" />
    <result column="intranet_address" jdbcType="VARCHAR" property="intranetAddress" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="pipeline_id" jdbcType="INTEGER" property="pipelineId" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update server set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.ServerDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into server (name, level, config, config_alias, intranet_address,
      type, status, parent_id, pipeline_id, device_id,
      deleted, create_time, modify_time
      )
    values (#{name,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER}, #{config,jdbcType=VARCHAR},
      #{configAlias,jdbcType=VARCHAR}, #{intranetAddress,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER},
      #{pipelineId,jdbcType=INTEGER}, #{deviceId,jdbcType=VARCHAR},
      #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.ServerDO">
    update server
    set name = #{name,jdbcType=VARCHAR},
      level = #{level,jdbcType=INTEGER},
      config = #{config,jdbcType=VARCHAR},
      config_alias = #{configAlias,jdbcType=VARCHAR},
      intranet_address = #{intranetAddress,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=INTEGER},
      pipeline_id = #{pipelineId,jdbcType=INTEGER},
      device_id = #{deviceId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select id, name, level, config, config_alias, intranet_address, type, status, parent_id,
      pipeline_id, device_id, deleted, create_time, modify_time
    from server
    where id = #{id,jdbcType=INTEGER}
      and deleted = 0
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, name, level, config, config_alias, intranet_address, type, status, parent_id,
      pipeline_id, device_id, deleted, create_time, modify_time
    from server
    where deleted = 0
  </select>
</mapper>