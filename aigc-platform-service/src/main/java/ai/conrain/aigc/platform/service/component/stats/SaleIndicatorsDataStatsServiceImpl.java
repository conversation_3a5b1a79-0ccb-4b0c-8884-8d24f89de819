package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.StatsSaleIndicatorsService;
import ai.conrain.aigc.platform.service.component.UserPointLogService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.OrderStatusEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.enums.StatsTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.SalesInfoVO;
import ai.conrain.aigc.platform.service.model.vo.StatsSaleIndicatorsVO;
import ai.conrain.aigc.platform.service.model.vo.UserFaceSceneVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.DateUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import java.util.Collections;

@Slf4j
@Service("saleIndicatorsDataStatsServiceImpl")
public class SaleIndicatorsDataStatsServiceImpl extends AbstractDataStatsServiceImpl {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private UserPointLogService userPointLogService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    private StatsSaleIndicatorsService statsSaleIndicatorsService;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.SALE_INDICATORS;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                       boolean isTotal) {
        // 1、获取主账户以及子账号用户信息
        List<UserVO> userInfoList = queryUserWithChildren();

        // 2、填充数据
        List<StatsSaleIndicatorsVO> rseultDataList = buildStatsIndicatorsData(storageDate, startDate, endDate,
                periodEnum, userInfoList);

        // 3、保存统计数据
        return saveStatsData(rseultDataList);
    }

    /**
     * 构建统计数据
     *
     * @param storageDate  存储日期
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param periodEnum   统计周期
     * @param userInfoList 用户信息列表
     * @return 统计数据列表
     */
    protected List<StatsSaleIndicatorsVO> buildStatsIndicatorsData(String storageDate, String startDate, String endDate,
                                                                   StatsPeriodEnum periodEnum, List<UserVO> userInfoList) {
        List<StatsSaleIndicatorsVO> resultDataList = new ArrayList<>();

        userInfoList.forEach(masterUser -> {
            // 取出主账号部分信息（用于最终汇总的记录）
            String corpName = masterUser.getCorpName();
            Integer corpOrgId = masterUser.getCorpOrgId();
            // 若corpOrgId为空，则跳过本次循环
            if (corpOrgId == null) {
                return;
            }

            // 2、取出子账号信息
            List<UserVO> subUserVoList = masterUser.getChildren();
            if (CollectionUtils.isEmpty(subUserVoList)) {
                subUserVoList = new ArrayList<>();
            }

            // 3、 合并为一个列表
            subUserVoList.add(masterUser);

            // 4、 构建统计数据
            List<StatsSaleIndicatorsVO> subResultDataList = doBuildStatsIndicatorsData(storageDate, startDate, endDate,
                    periodEnum, subUserVoList, corpOrgId);

            // 5、 构建总统计数据
            doBuildTotalStatsIndicatorsData(storageDate, periodEnum, subResultDataList, corpName, corpOrgId);

            // 6、 添加到结果列表中
            resultDataList.addAll(subResultDataList);
        });

        // 返回处理完成的结果
        return resultDataList;
    }

    /**
     * 构建统计数据
     *
     * @param storageDate   存储日期
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param periodEnum    统计周期
     * @param subUserVoList 子账号列表
     * @param parentId      父级ID
     * @return 统计数据列表
     */
    private List<StatsSaleIndicatorsVO> doBuildStatsIndicatorsData(String storageDate, String startDate, String endDate,
                                                                   StatsPeriodEnum periodEnum, List<UserVO> subUserVoList, Integer parentId) {
        // 初始化构建统计数据
        List<StatsSaleIndicatorsVO> resultDataList = new ArrayList<>();

        // 1、 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(startDate, endDate);

        // 遍历用户列表构建统计数据
        subUserVoList.forEach(userInfo -> {
            // 参数提取
            Integer userId = userInfo.getId();

            // 2、 获取销售关联的所有客户及虚拟账户客户(单个销售的客户数据)
            SalesCustomerData customerData = getSalesCustomerData(userId);
            List<Integer> distinctCustomerIds = customerData.getDistinctCustomerIds();
            List<Integer> virtualUserIds = customerData.getVirtualUserIds();

            // 获取销售的准确客户总数
            DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
            customerQuery.setDistributorSalesUserId(userId);
            Long customerCount = distributorCustomerService.queryDistributorCustomerCount(customerQuery);

            // 如果查询结果为空或为0，则使用传入的客户列表大小作为备选
            if (customerCount == null || customerCount == 0) {
                customerCount = (long) distinctCustomerIds.size();
            }


            // 初始化统计数据
            StatsSaleIndicatorsVO statsSaleIndicatorsVO = new StatsSaleIndicatorsVO();
            statsSaleIndicatorsVO.setParentId(parentId);
            statsSaleIndicatorsVO.setStatsDate(storageDate);
            statsSaleIndicatorsVO.setStatsType(periodEnum.getCode());
            statsSaleIndicatorsVO.setUserId(userInfo.getId());
            statsSaleIndicatorsVO.setName(userInfo.getNickName());
            statsSaleIndicatorsVO.setCreateTime(new Date());
            statsSaleIndicatorsVO.setModifyTime(new Date());
            
            // 初始化各项指标的默认值，避免空值问题
            statsSaleIndicatorsVO.setClothesExpCount(0);
            statsSaleIndicatorsVO.setCustomerConversionCount(0);
            statsSaleIndicatorsVO.setCustomerConsumptionPoints(0);
            statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
            statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
            statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
            statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
            statsSaleIndicatorsVO.setCustomerProtectionMetrics(0);
            statsSaleIndicatorsVO.setCreateCount(0);

            // 1、服装体验量
            statsSaleIndicatorsVO.setClothesExpCount(getClothesExpCount(startDate, endDate, userId));

            // 2、客户转换量（新签3999以上）
            statsSaleIndicatorsVO.setCustomerConversionCount(
                    getCustomerConversionCount(startDate, endDate, userId, distinctCustomerIds));

            // 3、客户消耗点数
            statsSaleIndicatorsVO.setCustomerConsumptionPoints(
                    getCustomerConsumptionPoints(startDate, endDate, userId, distinctCustomerIds,
                            virtualUserIds));

            // 4、客户活跃率
            getCustomerActivityRate(startDate, endDate, userId, distinctCustomerIds, customerCount, statsSaleIndicatorsVO);

            // 5、客户复购率（季度/半年）
            getCustomerRepurchaseRate(startDate, endDate, userId, distinctCustomerIds, customerCount, statsSaleIndicatorsVO);

            // 6、定制模特比例
            getCustomModelRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount,statsSaleIndicatorsVO);

            // 7、定制场景比例
            getCustomSceneRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount,statsSaleIndicatorsVO);

            // 8、客户保护指标
            statsSaleIndicatorsVO.setCustomerProtectionMetrics(
                    getCustomerProtectionMetrics(startDate, endDate, userId, distinctCustomerIds));

            // 9、销售出图数量
            statsSaleIndicatorsVO.setCreateCount(
                    getSalesDrawCount(startDate, endDate, userId, virtualUserIds));

            // 添加至结果列表中
            resultDataList.add(statsSaleIndicatorsVO);
        });

        // 返回构建完成的结果
        return resultDataList;
    }

    /**
     * 构建总统计数据
     *
     * @param storageDate       存储日期
     * @param periodEnum        统计周期
     * @param subResultDataList 子账号统计数据列表
     * @param corpName          公司名称
     * @param corpOrgId         公司ID
     */
    private void doBuildTotalStatsIndicatorsData(String storageDate, StatsPeriodEnum periodEnum, List<StatsSaleIndicatorsVO> subResultDataList,
                                                 String corpName, Integer corpOrgId) {
        // 数据校验，如果没有子账号数据，则无需汇总
        if (CollectionUtils.isEmpty(subResultDataList)) {
            log.warn("构建总统计数据时子账号数据列表为空，无需汇总 corpOrgId:{}", corpOrgId);
            return;
        }

        // 创建一条用于汇总的记录
        StatsSaleIndicatorsVO totalIndicators = new StatsSaleIndicatorsVO();

        // 设置基础信息
        totalIndicators.setStatsDate(storageDate);
        totalIndicators.setStatsType(periodEnum.getCode());
        totalIndicators.setUserId(corpOrgId);
        totalIndicators.setName(corpName);
        // 总记录的父级ID为设置0
        totalIndicators.setParentId(0);
        totalIndicators.setCreateTime(new Date());
        totalIndicators.setModifyTime(new Date());

        // 初始化各项指标值，避免空指针
        totalIndicators.setClothesExpCount(0);
        totalIndicators.setCustomerConversionCount(0);
        totalIndicators.setCustomerConsumptionPoints(0);
        totalIndicators.setCreateCount(0);
        totalIndicators.setCustomerProtectionMetrics(0);
        totalIndicators.setCustomerActivityRate("0.00");
        totalIndicators.setCustomerRepurchaseRate("0.00");
        totalIndicators.setCustomModelCustomers("0.00");
        totalIndicators.setCustomSceneCustomers("0.00");

        // 汇总所有子账号的数据
        int totalClothesExpCount = 0;
        int totalCustomerConversionCount = 0;
        int totalCustomerConsumptionPoints = 0;
        int totalCreateCount = 0;
        int totalCustomerProtectionMetrics = 0;

        // 使用Stream API汇总数值型指标
        totalClothesExpCount = subResultDataList.stream()
                .filter(data -> data.getClothesExpCount() != null)
                .mapToInt(StatsSaleIndicatorsVO::getClothesExpCount)
                .sum();

        totalCustomerConversionCount = subResultDataList.stream()
                .filter(data -> data.getCustomerConversionCount() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCustomerConversionCount)
                .sum();

        totalCustomerConsumptionPoints = subResultDataList.stream()
                .filter(data -> data.getCustomerConsumptionPoints() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCustomerConsumptionPoints)
                .sum();

        totalCreateCount = subResultDataList.stream()
                .filter(data -> data.getCreateCount() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCreateCount)
                .sum();

        totalCustomerProtectionMetrics = subResultDataList.stream()
                .filter(data -> data.getCustomerProtectionMetrics() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCustomerProtectionMetrics)
                .sum();

        // 设置汇总值
        totalIndicators.setClothesExpCount(totalClothesExpCount);
        totalIndicators.setCustomerConversionCount(totalCustomerConversionCount);
        totalIndicators.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
        totalIndicators.setCreateCount(totalCreateCount);
        totalIndicators.setCustomerProtectionMetrics(totalCustomerProtectionMetrics);

        // 计算加权平均比率指标
        calculateWeightedAverageRatios(subResultDataList, totalIndicators);

        log.info("构建总统计数据完成 - 公司名称:{}, 子账号数量:{}, 汇总体验服装数:{}, 汇总客户转换数:{}, 汇总点数消耗:{}",
                corpName, subResultDataList.size(), totalClothesExpCount, totalCustomerConversionCount, totalCustomerConsumptionPoints);

        // 将汇总记录添加到子账号数据列表中
        subResultDataList.add(totalIndicators);
    }

    /**
     * 计算加权平均比率指标
     *
     * @param subResultDataList 子账号统计数据列表
     * @param totalIndicators   汇总统计数据
     */
    private void calculateWeightedAverageRatios(List<StatsSaleIndicatorsVO> subResultDataList, StatsSaleIndicatorsVO totalIndicators) {
        // 处理客户活跃率 - 加权平均，以客户数量为权重
        double totalWeightedActivityRate = 0.0;
        int totalActivityCustomers = 0;

        // 处理客户复购率 - 加权平均
        double totalWeightedRepurchaseRate = 0.0;
        int totalRepurchaseCustomers = 0;

        // 处理定制模特比例 - 加权平均
        double totalWeightedCustomModelRatio = 0.0;
        int totalCustomModelCustomers = 0;

        // 处理定制场景比例 - 加权平均
        double totalWeightedCustomSceneRatio = 0.0;
        int totalCustomSceneCustomers = 0;

        // 获取销售对应的客户数量信息
        Map<Integer, Integer> userCustomerCountMap = new HashMap<>();
        for (StatsSaleIndicatorsVO data : subResultDataList) {
            Integer userId = data.getUserId();
            if (userId == null) continue;

            // 查询该销售关联的客户数据
            SalesCustomerData customerData = getSalesCustomerData(userId);
            List<Integer> distinctCustomerIds = customerData.getDistinctCustomerIds();

            // 存储客户数量信息
            int customerCount = CollectionUtils.isEmpty(distinctCustomerIds) ? 0 : distinctCustomerIds.size();
            userCustomerCountMap.put(userId, customerCount);
        }

        // 累加各子账号的加权数据
        for (StatsSaleIndicatorsVO data : subResultDataList) {
            Integer userId = data.getUserId();
            if (userId == null) continue;

            // 获取该销售对应的客户数量作为权重
            int customerCount = userCustomerCountMap.getOrDefault(userId, 0);

            // 如果这个销售没有客户，则跳过
            if (customerCount == 0) {
                continue;
            }

            // 活跃率处理
            if (data.getCustomerActivityRate() != null && !data.getCustomerActivityRate().isEmpty()) {
                try {
                    // 去掉百分号并解析为数值
                    String rateStr = data.getCustomerActivityRate().replace("%", "").trim();
                    double rate = Double.parseDouble(rateStr);

                    // 加权累加：该销售的活跃率 * 该销售的客户数
                    totalWeightedActivityRate += rate * customerCount;
                    totalActivityCustomers += customerCount;

                    log.debug("累加活跃率数据 - 销售:{}, 活跃率:{}%, 客户数:{}, 当前加权总和:{}",
                            data.getName(), rate, customerCount, totalWeightedActivityRate);
                } catch (NumberFormatException e) {
                    log.warn("无法解析活跃率字符串: {}", data.getCustomerActivityRate());
                }
            }

            // 复购率处理
            if (data.getCustomerRepurchaseRate() != null && !data.getCustomerRepurchaseRate().isEmpty()) {
                try {
                    String rateStr = data.getCustomerRepurchaseRate().replace("%", "").trim();
                    double rate = Double.parseDouble(rateStr);

                    totalWeightedRepurchaseRate += rate * customerCount;
                    totalRepurchaseCustomers += customerCount;

                    log.debug("累加复购率数据 - 销售:{}, 复购率:{}%, 客户数:{}, 当前加权总和:{}",
                            data.getName(), rate, customerCount, totalWeightedRepurchaseRate);
                } catch (NumberFormatException e) {
                    log.warn("无法解析复购率字符串: {}", data.getCustomerRepurchaseRate());
                }
            }

            // 定制模特比例处理
            if (data.getCustomModelCustomers() != null && !data.getCustomModelCustomers().isEmpty()) {
                try {
                    String ratioStr = data.getCustomModelCustomers().replace("%", "").trim();
                    double ratio = Double.parseDouble(ratioStr);

                    totalWeightedCustomModelRatio += ratio * customerCount;
                    totalCustomModelCustomers += customerCount;

                    log.debug("累加模特定制率数据 - 销售:{}, 模特定制率:{}%, 客户数:{}, 当前加权总和:{}",
                            data.getName(), ratio, customerCount, totalWeightedCustomModelRatio);
                } catch (NumberFormatException e) {
                    log.warn("无法解析定制模特比例字符串: {}", data.getCustomModelCustomers());
                }
            }

            // 定制场景比例处理
            if (data.getCustomSceneCustomers() != null && !data.getCustomSceneCustomers().isEmpty()) {
                try {
                    String ratioStr = data.getCustomSceneCustomers().replace("%", "").trim();
                    double ratio = Double.parseDouble(ratioStr);

                    totalWeightedCustomSceneRatio += ratio * customerCount;
                    totalCustomSceneCustomers += customerCount;

                    log.debug("累加场景定制率数据 - 销售:{}, 场景定制率:{}%, 客户数:{}, 当前加权总和:{}",
                            data.getName(), ratio, customerCount, totalWeightedCustomSceneRatio);
                } catch (NumberFormatException e) {
                    log.warn("无法解析定制场景比例字符串: {}", data.getCustomSceneCustomers());
                }
            }
        }

        // 计算最终的加权平均值并设置到总计记录中
        // 客户活跃率
        if (totalActivityCustomers > 0) {
            double avgActivityRate = totalWeightedActivityRate / totalActivityCustomers;
            totalIndicators.setCustomerActivityRate(String.format("%.2f", avgActivityRate));
            log.info("计算总客户活跃率 - 权重总和:{}, 客户总数:{}, 计算结果:{}",
                    totalWeightedActivityRate, totalActivityCustomers, String.format("%.2f", avgActivityRate));
        } else {
            totalIndicators.setCustomerActivityRate("0.00");
            log.info("计算总客户活跃率 - 无有效数据，设置为0.00");
        }

        // 客户复购率
        if (totalRepurchaseCustomers > 0) {
            double avgRepurchaseRate = totalWeightedRepurchaseRate / totalRepurchaseCustomers;
            totalIndicators.setCustomerRepurchaseRate(String.format("%.2f", avgRepurchaseRate));
            log.info("计算总客户复购率 - 权重总和:{}, 客户总数:{}, 计算结果:{}",
                    totalWeightedRepurchaseRate, totalRepurchaseCustomers, String.format("%.2f", avgRepurchaseRate));
        } else {
            totalIndicators.setCustomerRepurchaseRate("0.00");
            log.info("计算总客户复购率 - 无有效数据，设置为0.00");
        }

        // 定制模特比例
        if (totalCustomModelCustomers > 0) {
            double avgCustomModelRatio = totalWeightedCustomModelRatio / totalCustomModelCustomers;
            totalIndicators.setCustomModelCustomers(String.format("%.2f", avgCustomModelRatio));
            log.info("计算总模特定制率 - 权重总和:{}, 客户总数:{}, 计算结果:{}",
                    totalWeightedCustomModelRatio, totalCustomModelCustomers, String.format("%.2f", avgCustomModelRatio));
        } else {
            totalIndicators.setCustomModelCustomers("0.00");
            log.info("计算总模特定制率 - 无有效数据，设置为0.00");
        }

        // 定制场景比例
        if (totalCustomSceneCustomers > 0) {
            double avgCustomSceneRatio = totalWeightedCustomSceneRatio / totalCustomSceneCustomers;
            totalIndicators.setCustomSceneCustomers(String.format("%.2f", avgCustomSceneRatio));
            log.info("计算总场景定制率 - 权重总和:{}, 客户总数:{}, 计算结果:{}",
                    totalWeightedCustomSceneRatio, totalCustomSceneCustomers, String.format("%.2f", avgCustomSceneRatio));
        } else {
            totalIndicators.setCustomSceneCustomers("0.00");
            log.info("计算总场景定制率 - 无有效数据，设置为0.00");
        }
    }

    /**
     * 获取客户消费点数
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @param virtualUserIds      虚拟账户ID列表
     * @return 客户消费点数
     */
    private Integer getCustomerConsumptionPoints(String startDate, String endDate, Integer userId,
                                                 List<Integer> distinctCustomerIds, List<Integer> virtualUserIds) {
        try {
            int totalPoints = 0;

            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 查询客户在指定时间范围内的点数消耗总量
            Integer customerPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate, distinctCustomerIds);

            // 将点数消耗转换为正值（假设消耗是负值）
            totalPoints += (customerPoints != null ? Math.abs(customerPoints) : 0);

            // 如果销售自身也有点数消耗，也应该计入
            List<Integer> salesUserIds = new ArrayList<>();
            salesUserIds.add(userId);
            Integer salesOwnPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate, salesUserIds);
            totalPoints += (salesOwnPoints != null ? Math.abs(salesOwnPoints) : 0);

            // 加上虚拟账户的点数消耗
            if (!CollectionUtils.isEmpty(virtualUserIds)) {
                Integer virtualPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate, virtualUserIds);
                totalPoints += (virtualPoints != null ? Math.abs(virtualPoints) : 0);
            }

            log.info("客户消费点数统计 - 销售ID:{}, 客户数:{}, 虚拟账户数:{}, 总消耗点数:{}",
                    userId, distinctCustomerIds.size(),
                    virtualUserIds != null ? virtualUserIds.size() : 0,
                    totalPoints);

            return totalPoints;

        } catch (Exception e) {
            log.error("计算客户消费点数异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取客户转换量
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @return 客户转换量
     */
    private Integer getCustomerConversionCount(String startDate, String endDate, Integer userId,
                                               List<Integer> distinctCustomerIds) {
        try {
            int conversionCount = 0;

            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 在指定日期范围内查询首次大额充值（≥3999）的客户
            List<Integer> newConvertedCustomers = new ArrayList<>();
            for (Integer customerId : distinctCustomerIds) {
                // 查询该客户是否在统计期前已有大额充值
                OrderInfoQuery beforeQuery = new OrderInfoQuery();
                beforeQuery.setMasterUserId(customerId);
                beforeQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                List<OrderInfoVO> beforeOrders = orderInfoService.queryOrderInfoList(beforeQuery);

                boolean hadBigPaymentBefore = false;
                if (!CollectionUtils.isEmpty(beforeOrders)) {
                    BigDecimal minAmount = new BigDecimal("3999");
                    // 筛选充值时间在开始日期之前的大额订单
                    for (OrderInfoVO order : beforeOrders) {
                        if (order.getPayAmount() != null &&
                                order.getPayAmount().compareTo(minAmount) >= 0 &&
                                order.getCreateTime() != null &&
                                order.getCreateTime().before(DateUtils.parseSimpleDate(startDate))) {
                            hadBigPaymentBefore = true;
                            break;
                        }
                    }
                }

                // 如果之前没有大额充值，检查统计期内是否有大额充值
                if (!hadBigPaymentBefore) {
                    OrderInfoQuery currentQuery = new OrderInfoQuery();
                    currentQuery.setMasterUserId(customerId);
                    currentQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                    // 仅查询统计期内的订单
                    currentQuery.setStartDate(startDate);
                    currentQuery.setEndDate(endDate);
                    List<OrderInfoVO> currentOrders = orderInfoService.queryOrderInfoList(currentQuery);

                    if (!CollectionUtils.isEmpty(currentOrders)) {
                        BigDecimal minAmount = new BigDecimal("3999");
                        // 检查是否有大额订单
                        boolean hasBigPayment = currentOrders.stream()
                                .anyMatch(order -> order.getPayAmount() != null &&
                                        order.getPayAmount().compareTo(minAmount) >= 0);

                        if (hasBigPayment) {
                            newConvertedCustomers.add(customerId);
                        }
                    }
                }
            }

            // 返回新签大额客户数量
            conversionCount = newConvertedCustomers.size();

            log.info("客户转换量统计 - 销售ID:{}, 总客户数:{}, 新签大额客户数:{}",
                    userId, distinctCustomerIds.size(), conversionCount);

            return conversionCount;

        } catch (Exception e) {
            log.error("计算客户转换量异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取服装体验量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userId    用户ID
     * @return 服装体验数量
     */
    private Integer getClothesExpCount(String startDate, String endDate, Integer userId) {
        try {
            // 初始化各服装体验量计数器
            int salesOwnClothesCount = 0;       // 销售自己账号上传的服装数量
            int virtualAccountClothesCount = 0; // 虚拟账户上传的服装数量
            int customerBetaClothesCount = 0;   // 客户充值前上传的服装数量

            // 1. 统计销售自己账号上传的服装
            salesOwnClothesCount = countSalesOwnClothes(startDate, endDate, userId);

            // 2. 统计销售关联的虚拟账户上传的服装
            List<Integer> virtualUserIds = fetchVirtualUserIds(userId);
            if (!CollectionUtils.isEmpty(virtualUserIds)) {
                virtualAccountClothesCount = countVirtualAccountClothes(startDate, endDate, virtualUserIds);
            }

            // 3. 统计销售客户充值前(< 3999元)上传的服装（体验服装）
            customerBetaClothesCount = countCustomerBetaClothes(startDate, endDate, userId);

            // 4. 计算总数并返回
            int totalClothesCount = salesOwnClothesCount + virtualAccountClothesCount + customerBetaClothesCount;

            log.info("服装体验量统计 - 用户ID:{}, 自己账号:{}, 虚拟户:{}, 客户充值前:{}, 总计:{}",
                    userId, salesOwnClothesCount, virtualAccountClothesCount, customerBetaClothesCount, totalClothesCount);

            return totalClothesCount;

        } catch (Exception e) {
            log.error("计算服装体验量异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计销售自己账号上传的服装数量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userId    用户ID
     * @return 服装数量
     */
    private int countSalesOwnClothes(String startDate, String endDate, Integer userId) {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setUserId(userId);
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setStatus(MaterialModelStatusEnum.ENABLED.getCode());

        List<MaterialModelVO> clothesList = materialModelService.queryMaterialModelList(query);
        return CollectionUtils.isEmpty(clothesList) ? 0 : clothesList.size();
    }

    /**
     * 统计虚拟账户上传的服装数量
     *
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @param virtualUserIds 虚拟账户ID列表
     * @return 服装数量
     */
    private int countVirtualAccountClothes(String startDate, String endDate, List<Integer> virtualUserIds) {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setUserIds(virtualUserIds);
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setStatus(MaterialModelStatusEnum.ENABLED.getCode());

        List<MaterialModelVO> clothesList = materialModelService.queryMaterialModelList(query);
        return CollectionUtils.isEmpty(clothesList) ? 0 : clothesList.size();
    }

    /**
     * 统计客户在充值3999元前上传的服装数量(体验服装)
     *
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @param salesUserId 销售用户ID
     * @return 体验服装数量
     */
    private int countCustomerBetaClothes(String startDate, String endDate, Integer salesUserId) {
        int totalBetaClothesCount = 0;

        // 1. 获取该销售关联的所有客户
        DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
        customerQuery.setDistributorSalesUserId(salesUserId);
        List<DistributorCustomerVO> customerList = distributorCustomerService
                .queryDistributorCustomerList(customerQuery);

        if (CollectionUtils.isEmpty(customerList)) {
            return 0;
        }

        // 2. 获取客户ID列表
        List<Integer> customerIds = customerList.stream()
                .map(DistributorCustomerVO::getCustomerMasterUserId)
                .collect(Collectors.toList());

        // 3. 处理每个客户的体验服装
        for (Integer customerId : customerIds) {
            totalBetaClothesCount += countSingleCustomerBetaClothes(customerId, startDate, endDate);
        }

        return totalBetaClothesCount;
    }

    /**
     * 统计单个客户的体验服装数量
     *
     * @param customerId 客户ID
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @return 体验服装数量
     */
    private int countSingleCustomerBetaClothes(Integer customerId, String startDate, String endDate) {
        try {
            // 1. 查询客户的首次充值3999+的订单
            OrderInfoQuery orderQuery = new OrderInfoQuery();
            orderQuery.setMasterUserId(customerId);
            orderQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
            List<OrderInfoVO> orders = orderInfoService.queryOrderInfoList(orderQuery);

            // 2. 过滤出大额订单(>=3999元)并找出最早的一笔
            BigDecimal thresholdAmount = new BigDecimal("3999");
            Optional<OrderInfoVO> earliestBigOrder = orders.stream()
                    .filter(order -> order.getPayAmount() != null
                            && order.getPayAmount().compareTo(thresholdAmount) >= 0)
                    .min(Comparator.comparing(OrderInfoVO::getCreateTime));

            // 3. 根据是否有大额订单和订单时间，查询体验服装
            if (earliestBigOrder.isPresent()) {
                // 客户有大额充值，查询充值前的服装
                Date earliestOrderTime = earliestBigOrder.get().getCreateTime();

                // 查询在首次大额充值前上传的服装
                MaterialModelQuery betaQuery = new MaterialModelQuery();
                betaQuery.setUserId(customerId);
                betaQuery.setStartDate(startDate);
                betaQuery.setEndDate(DateUtils.formatSimpleDate(earliestOrderTime));
                betaQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());

                List<MaterialModelVO> betaClothes = materialModelService.queryMaterialModelList(betaQuery);
                return CollectionUtils.isEmpty(betaClothes) ? 0 : betaClothes.size();
            } else {
                // 客户没有大额充值，所有服装都算体验服装
                MaterialModelQuery betaQuery = new MaterialModelQuery();
                betaQuery.setUserId(customerId);
                betaQuery.setStartDate(startDate);
                betaQuery.setEndDate(endDate);
                betaQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());

                List<MaterialModelVO> betaClothes = materialModelService.queryMaterialModelList(betaQuery);
                return CollectionUtils.isEmpty(betaClothes) ? 0 : betaClothes.size();
            }
        } catch (Exception e) {
            log.error("统计客户体验服装异常，客户ID:{}, 异常:{}", customerId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取销售出图数量
     *
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @param userId         用户ID
     * @param virtualUserIds 虚拟账户ID列表，可为null
     * @return 销售出图数量
     */
    private Integer getSalesDrawCount(String startDate, String endDate, Integer userId, List<Integer> virtualUserIds) {
        // 1. 获取销售自己账号的出图量
        CreativeBatchQuery creativeBatchQuery = new CreativeBatchQuery();
        creativeBatchQuery.setStatus(CreativeStatusEnum.FINISHED.getCode());
        creativeBatchQuery.setUserId(userId);
        creativeBatchQuery.setStartTime(startDate);
        creativeBatchQuery.setEndTime(endDate);
        List<CreativeBatchVO> creativeBatchVOS = creativeBatchService.queryCreativeBatchList(creativeBatchQuery);

        int salesDrawCount = 0;
        if (!CollectionUtils.isEmpty(creativeBatchVOS)) {
            salesDrawCount = creativeBatchVOS.stream().map(CreativeBatchVO::getBatchCnt).reduce(0, Integer::sum);
        }

        // 2. 获取销售关联的虚拟户的出图量
        if (!CollectionUtils.isEmpty(virtualUserIds)) {
            for (Integer virtualUserId : virtualUserIds) {
                CreativeBatchQuery virtualUserQuery = new CreativeBatchQuery();
                virtualUserQuery.setStatus(CreativeStatusEnum.FINISHED.getCode());
                virtualUserQuery.setUserId(virtualUserId);
                virtualUserQuery.setStartTime(startDate);
                virtualUserQuery.setEndTime(endDate);
                List<CreativeBatchVO> virtualUserBatchVOS = creativeBatchService
                        .queryCreativeBatchList(virtualUserQuery);

                if (!CollectionUtils.isEmpty(virtualUserBatchVOS)) {
                    salesDrawCount += virtualUserBatchVOS.stream().map(CreativeBatchVO::getBatchCnt).reduce(0,
                            Integer::sum);
                }
            }
        }

        return salesDrawCount;
    }

    /**
     * 获取销售关联的虚拟户用户ID列表
     *
     * @param salesUserId 销售用户ID
     * @return 虚拟户用户ID列表
     */
    private List<Integer> fetchVirtualUserIds(Integer salesUserId) {
        // 通过memo字段包含"虚拟商家"来查找虚拟户
        UserQuery query = new UserQuery();
        query.setMemoLike("虚拟商家");

        List<UserVO> allVirtualUsers = userService.queryUsers(query);
        if (CollectionUtils.isEmpty(allVirtualUsers)) {
            return new ArrayList<>();
        }

        // 过滤出与指定销售关联的虚拟户
        List<UserVO> relatedVirtualUsers = allVirtualUsers.stream()
                .filter(user -> {
                    return user.getMemo() != null &&
                            user.getMemo().contains("sales_id=" + salesUserId);
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(relatedVirtualUsers)) {
            return new ArrayList<>();
        }

        // 返回虚拟户ID列表
        return relatedVirtualUsers.stream().map(UserVO::getId).collect(Collectors.toList());
    }

    /**
     * 查询所有分销商角色的主账号及其子账号
     *
     * @return 主账号及其对应子账号的用户列表(子账号已经设置到对应主账号的children字段中)
     */
    protected List<UserVO> queryUserWithChildren() {
        // 使用渠道商角色类型
        String roleType = RoleTypeEnum.DISTRIBUTOR.getCode();

        // 1. 查询所有符合条件的主账号
        UserQuery masterQuery = new UserQuery();
        masterQuery.setRoleType(roleType);
        masterQuery.setUserType(UserTypeEnum.MASTER.getCode());
        masterQuery.setStatus(UserStatusEnum.ENABLED.getCode());
        List<UserVO> masterUsers = userService.queryUsers(masterQuery);
        if (CollectionUtils.isEmpty(masterUsers)) {
            return new ArrayList<>();
        }

        // 2. 获取主账号ID列表并查询所有相关子账号
        List<Integer> masterIds = masterUsers.stream()
                .map(UserVO::getId)
                .collect(Collectors.toList());

        // 3. 查询子账号并按masterId分组
        UserQuery subQuery = new UserQuery();
        subQuery.setRoleType(roleType);
        subQuery.setUserType(UserTypeEnum.SUB.getCode());
        subQuery.setStatus(UserStatusEnum.ENABLED.getCode());
        subQuery.setMasterIdList(masterIds);

        // 查询子账号并按masterId分组
        Map<Integer, List<UserVO>> subUsersByMasterId = userService.queryUsers(subQuery).stream()
                .collect(Collectors.groupingBy(UserVO::getMasterId));

        // 4. 将子账号设置到对应的主账号上并返回
        return masterUsers.stream()
                .peek(master -> master.setChildren(
                        subUsersByMasterId.getOrDefault(master.getId(), new ArrayList<>())))
                .collect(Collectors.toList());
    }

    /**
     * 获取销售关联的准确客户总数
     *
     * @param userId              销售用户ID
     * @param distinctCustomerIds 去重后的客户ID列表（作为备选数据源）
     * @return 客户总数
     */
    private long getAccurateCustomerCount(Integer userId, List<Integer> distinctCustomerIds) {
        // 获取销售的准确客户总数
        DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
        customerQuery.setDistributorSalesUserId(userId);
        Long totalCustomerCount = distributorCustomerService.queryDistributorCustomerCount(customerQuery);

        // 如果查询结果为空或为0，则使用传入的客户列表大小作为备选
        if (totalCustomerCount == null || totalCustomerCount == 0) {
            totalCustomerCount = (long) distinctCustomerIds.size();
        }

        return totalCustomerCount;
    }

    /**
     * 获取客户活跃率
     *
     * @param startDate             开始时间
     * @param endDate               结束时间
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomerActivityRate(String startDate, String endDate, Integer userId,
                                         List<Integer> distinctCustomerIds, long totalCustomerCount,
                                         StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
                return;
            }

            // 统计时间范围内有创作行为的客户数量
            int activeCustomerCount = 0;
            for (Integer customerId : distinctCustomerIds) {
                // 查询客户在时间范围内是否有创作行为（出图、出视频等）
                CreativeBatchQuery query = new CreativeBatchQuery();
                query.setUserId(customerId);
                query.setStartTime(startDate);
                query.setEndTime(endDate);
                query.setStatus(CreativeStatusEnum.FINISHED.getCode()); // 完成状态的创作

                List<CreativeBatchVO> batches = creativeBatchService.queryCreativeBatchList(query);
                if (!CollectionUtils.isEmpty(batches)) {
                    activeCustomerCount++;
                }
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
                return;
            }

            // 计算活跃率并格式化为两位小数的字符串
            double activityRate = (double) activeCustomerCount / totalCustomerCount * 100;
            String formattedRate = String.format("%.2f", activityRate);

            log.info("客户活跃率统计 - 销售ID:{}, 实际客户总数:{}, 活跃客户数:{}, 活跃率:{}%",
                    userId, totalCustomerCount, activeCustomerCount, formattedRate);

            // 设置活跃率
            statsSaleIndicatorsVO.setCustomerActivityRate(formattedRate);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, activeCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算客户活跃率异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
        }
    }

    /**
     * 获取客户复购率（季度/半年）
     *
     * @param startDate             开始时间
     * @param endDate               结束时间
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomerRepurchaseRate(String startDate, String endDate, Integer userId,
                                             List<Integer> distinctCustomerIds, long totalCustomerCount,
                                             StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
                return;
            }

            // 统计复购客户数
            int repurchaseCount = 0; // 复购客户数
            int hasFirstPaymentCount = 0;   // 有首次大额付款的客户数

            for (Integer customerId : distinctCustomerIds) {
                // 查找客户首次大额充值（>=3999元）的时间
                OrderInfoQuery allOrdersQuery = new OrderInfoQuery();
                allOrdersQuery.setMasterUserId(customerId);
                allOrdersQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                List<OrderInfoVO> allOrders = orderInfoService.queryOrderInfoList(allOrdersQuery);

                // 筛选出大额订单（>=3999元）
                BigDecimal minAmount = new BigDecimal("3999");
                List<OrderInfoVO> bigAmountOrders = allOrders.stream()
                        .filter(order -> order.getPayAmount() != null &&
                                order.getPayAmount().compareTo(minAmount) >= 0)
                        .sorted(Comparator.comparing(OrderInfoVO::getCreateTime))
                        .collect(Collectors.toList());

                if (CollectionUtils.isEmpty(bigAmountOrders)) {
                    // 没有大额订单，跳过此客户
                    continue;
                }

                // 获取首次大额充值时间
                OrderInfoVO firstBigOrder = bigAmountOrders.get(0);
                Date firstPayTime = firstBigOrder.getCreateTime();
                hasFirstPaymentCount++; // 有首次大额充值的客户数+1

                // 检查在统计期内是否有大额充值，且时间晚于首次充值时间
                boolean hasRepurchase = bigAmountOrders.stream()
                        .filter(order -> !order.getId().equals(firstBigOrder.getId())) // 排除首次充值订单
                        .anyMatch(order -> order.getCreateTime().after(firstPayTime) &&
                                isWithinPeriod(order.getCreateTime(), startDate, endDate));

                if (hasRepurchase) {
                    repurchaseCount++;
                }
            }

            // 计算复购率 = 复购客户数 / 有首次大额付款的客户总数
            if (hasFirstPaymentCount == 0) {
                statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
                return;
            }

            double repurchaseRate = (double) repurchaseCount / hasFirstPaymentCount * 100;
            String formattedRate = String.format("%.2f", repurchaseRate);

            log.info("客户复购率统计 - 销售ID:{}, 实际客户总数:{}, 有首次大额付款客户数:{}, 本期复购客户数:{}, 复购率:{}%",
                    userId, totalCustomerCount, hasFirstPaymentCount, repurchaseCount, formattedRate);

            // 设置复购率
            statsSaleIndicatorsVO.setCustomerRepurchaseRate(formattedRate);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, repurchaseCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR, hasFirstPaymentCount);

        } catch (Exception e) {
            log.error("计算客户复购率异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
        }
    }

    /**
     * 判断日期是否在指定时间范围内
     *
     * @param date      要检查的日期
     * @param startDate 开始日期字符串
     * @param endDate   结束日期字符串
     * @return 是否在范围内
     */
    private boolean isWithinPeriod(Date date, String startDate, String endDate) {
        try {
            Date start = DateUtils.parseSimpleDateWithFormats(startDate);
            Date end = DateUtils.parseSimpleDateWithFormats(endDate);

            return date.compareTo(start) >= 0 && date.compareTo(end) < 0;
        } catch (Exception e) {
            log.error("日期解析出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取定制模特比例
     *
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @param userFaceSceneList   所有用户的定制元素信息
     * @param totalCustomerCount  客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomModelRatio(Integer userId,
                                       List<Integer> distinctCustomerIds, List<UserFaceSceneVO> userFaceSceneList,
                                       long totalCustomerCount,StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || CollectionUtils.isEmpty(userFaceSceneList)) {
                statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
                return;
            }

            // 统计使用定制模特的客户数
            int customModelCustomerCount = 0; // 使用了定制模特的客户数

            // userId 也添加进来
            distinctCustomerIds.add(userId);

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制模特
                boolean hasCustomModel = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getFaceCnt() != null && element.getFaceCnt() > 0);

                if (hasCustomModel) {
                    customModelCustomerCount++;
                }
            }

            // 计算定制模特比例 = 使用定制模特的客户数 / 总客户数
            double customModelRatio = (double) customModelCustomerCount / totalCustomerCount * 100;
            String formattedRatio = String.format("%.2f", customModelRatio);

            log.info("定制模特比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制模特客户数:{}, 定制模特比例:{}%",
                    userId, totalCustomerCount, customModelCustomerCount, formattedRatio);

            // 设置定制模特比例
            statsSaleIndicatorsVO.setCustomModelCustomers(formattedRatio);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, customModelCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算定制模特比例异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
        }
    }

    /**
     * 获取定制场景比例
     *
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @param userFaceSceneList   所有用户的定制元素信息
     * @param totalCustomerCount  客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomSceneRatio(Integer userId,
                                       List<Integer> distinctCustomerIds, List<UserFaceSceneVO> userFaceSceneList,
                                       long totalCustomerCount,StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || CollectionUtils.isEmpty(userFaceSceneList)) {
                statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
                return;
            }

            // 统计使用定制场景的客户数
            int customSceneCustomerCount = 0; // 使用了定制场景的客户数

            // userId 也添加进来
            distinctCustomerIds.add(userId);

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制场景
                boolean hasCustomScene = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getSceneCnt() != null && element.getSceneCnt() > 0);

                if (hasCustomScene) {
                    customSceneCustomerCount++;
                }
            }

            // 计算定制场景比例 = 使用定制场景的客户数 / 总客户数
            double customSceneRatio = (double) customSceneCustomerCount / totalCustomerCount * 100;
            String formattedRatio = String.format("%.2f", customSceneRatio);

            log.info("定制场景比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制场景客户数:{}, 定制场景比例:{}%",
                    userId, totalCustomerCount, customSceneCustomerCount, formattedRatio);

            // 设置定制场景比例
            statsSaleIndicatorsVO.setCustomSceneCustomers(formattedRatio);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, customSceneCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算定制场景比例异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
        }
    }

    /**
     * 获取客户保护指标（大于60天未充值的客户数量）
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @return 大于60天未充值的客户数量
     */
    private Integer getCustomerProtectionMetrics(String startDate, String endDate, Integer userId,
                                                 List<Integer> distinctCustomerIds) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 获取准确的客户总数
            long totalCustomerCount = getAccurateCustomerCount(userId, distinctCustomerIds);

            // 使用UserService中的queryBefore60Days方法获取所有超过60天创建且非VIP的用户
            List<SalesInfoVO> salesInfoList = userService.queryBefore60Days();

            if (CollectionUtils.isEmpty(salesInfoList)) {
                return 0;
            }

            // 筛选当前销售相关的记录并累加sleepCnt
            int sleepCustomerCount = salesInfoList.stream()
                    .filter(info -> userId.equals(info.getSalesId()))
                    .mapToInt(SalesInfoVO::getSleepCnt)
                    .sum();

            log.info("客户保护指标统计(超60天非VIP客户) - 销售ID:{}, 实际客户总数:{}, 超60天非VIP客户数:{}",
                    userId, totalCustomerCount, sleepCustomerCount);

            return sleepCustomerCount;

        } catch (Exception e) {
            log.error("计算客户保护指标异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存统计数据
     *
     * @param statsList 统计数据列表
     * @return 影响行数
     */
    protected int saveStatsData(List<StatsSaleIndicatorsVO> statsList) {
        if (!CollectionUtils.isEmpty(statsList)) {
            try {
                return statsSaleIndicatorsService.batchInsertOrUpdate(statsList);
            } catch (Exception e) {
                log.error("保存统计数据异常: {}", e.getMessage(), e);
                return 0;
            }
        }
        return 0;
    }

    /**
     * 销售客户数据类，用于存储销售关联的客户信息
     */
    @Data
    private static class SalesCustomerData {
        // 直接关联的客户ID列表
        private List<Integer> directCustomerIds = new ArrayList<>();
        // 虚拟账户ID列表
        private List<Integer> virtualUserIds = new ArrayList<>();
        // 虚拟账户关联的客户ID列表
        private List<Integer> virtualCustomerIds = new ArrayList<>();
        // 合并去重后的所有客户ID列表
        private List<Integer> distinctCustomerIds = new ArrayList<>();
    }

    /**
     * 获取销售关联的所有客户数据
     *
     * @param userId 销售用户ID
     * @return 销售客户数据
     */
    private SalesCustomerData getSalesCustomerData(Integer userId) {
        SalesCustomerData result = new SalesCustomerData();

        try {
            // 1. 获取销售关联的所有客户（直接客户）
            DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
            customerQuery.setDistributorSalesUserId(userId);
            List<DistributorCustomerVO> customerList = distributorCustomerService
                    .queryDistributorCustomerList(customerQuery);

            List<Integer> directCustomerIds = CollectionUtils.isEmpty(customerList) ? new ArrayList<>()
                    : customerList.stream()
                    .map(DistributorCustomerVO::getCustomerMasterUserId)
                    .distinct()
                    .collect(Collectors.toList());

            result.setDirectCustomerIds(directCustomerIds);

            // 2. 获取销售关联的虚拟账户
            List<Integer> virtualUserIds = fetchVirtualUserIds(userId);
            result.setVirtualUserIds(virtualUserIds);

            // 3. 获取虚拟账户关联的客户
            List<Integer> virtualCustomerIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(virtualUserIds)) {
                for (Integer virtualUserId : virtualUserIds) {
                    DistributorCustomerQuery virtualCustomerQuery = new DistributorCustomerQuery();
                    virtualCustomerQuery.setDistributorSalesUserId(virtualUserId);
                    List<DistributorCustomerVO> virtualCustomers = distributorCustomerService
                            .queryDistributorCustomerList(virtualCustomerQuery);

                    if (!CollectionUtils.isEmpty(virtualCustomers)) {
                        virtualCustomerIds.addAll(
                                virtualCustomers.stream()
                                        .map(DistributorCustomerVO::getCustomerMasterUserId)
                                        .collect(Collectors.toList()));
                    }
                }
            }
            result.setVirtualCustomerIds(virtualCustomerIds);

            // 4. 合并所有客户ID并去重
            List<Integer> allCustomerIds = new ArrayList<>(directCustomerIds);
            allCustomerIds.addAll(virtualCustomerIds);
            List<Integer> distinctCustomerIds = allCustomerIds.stream().distinct().collect(Collectors.toList());
            result.setDistinctCustomerIds(distinctCustomerIds);

        } catch (Exception e) {
            log.error("获取销售客户数据异常，销售ID:{}, 异常:{}", userId, e.getMessage(), e);
        }

        return result;
    }
}
