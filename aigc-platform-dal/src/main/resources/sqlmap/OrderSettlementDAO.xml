<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.OrderSettlementDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.OrderSettlementDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="distributor_corp_id" jdbcType="INTEGER" property="distributorCorpId" />
    <result column="distributor_corp_name" jdbcType="VARCHAR" property="distributorCorpName" />
    <result column="principal_type" jdbcType="VARCHAR" property="principalType" />
    <result column="principal_id" jdbcType="INTEGER" property="principalId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="settle_id" jdbcType="VARCHAR" property="settleId" />
    <result column="settle_time" jdbcType="TIMESTAMP" property="settleTime" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="channel_rate" jdbcType="DECIMAL" property="channelRate" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, type, distributor_corp_id, distributor_corp_name, principal_type, principal_id, 
    status, settle_id, settle_time, total_amount, channel_rate, settle_amount, ext_info, 
    create_time, modify_time
  </sql>

  <sql id="Order_User_Column_View">
    merchant_id, merchant_name, merchant_corp_id, merchant_corp_name, order_finish_time
  </sql>

  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.OrderSettlementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />, <include refid="Order_User_Column_View"/>
    from order_settlement_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_settlement
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_settlement
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.OrderSettlementDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_settlement (order_no, type, distributor_corp_id, 
      distributor_corp_name, principal_type, principal_id, 
      status, settle_id, settle_time, 
      total_amount, channel_rate, settle_amount, 
      ext_info, create_time, modify_time
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{distributorCorpId,jdbcType=INTEGER}, 
      #{distributorCorpName,jdbcType=VARCHAR}, #{principalType,jdbcType=VARCHAR}, #{principalId,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{settleId,jdbcType=VARCHAR}, #{settleTime,jdbcType=TIMESTAMP}, 
      #{totalAmount,jdbcType=DECIMAL}, #{channelRate,jdbcType=DECIMAL}, #{settleAmount,jdbcType=DECIMAL}, 
      #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.OrderSettlementDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="distributorCorpId != null">
        distributor_corp_id,
      </if>
      <if test="distributorCorpName != null">
        distributor_corp_name,
      </if>
      <if test="principalType != null">
        principal_type,
      </if>
      <if test="principalId != null">
        principal_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="settleId != null">
        settle_id,
      </if>
      <if test="settleTime != null">
        settle_time,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="channelRate != null">
        channel_rate,
      </if>
      <if test="settleAmount != null">
        settle_amount,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="distributorCorpId != null">
        #{distributorCorpId,jdbcType=INTEGER},
      </if>
      <if test="distributorCorpName != null">
        #{distributorCorpName,jdbcType=VARCHAR},
      </if>
      <if test="principalType != null">
        #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        #{principalId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="settleId != null">
        #{settleId,jdbcType=VARCHAR},
      </if>
      <if test="settleTime != null">
        #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="channelRate != null">
        #{channelRate,jdbcType=DECIMAL},
      </if>
      <if test="settleAmount != null">
        #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.OrderSettlementExample" resultType="java.lang.Long">
    select count(*) from order_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorCorpId != null">
        distributor_corp_id = #{record.distributorCorpId,jdbcType=INTEGER},
      </if>
      <if test="record.distributorCorpName != null">
        distributor_corp_name = #{record.distributorCorpName,jdbcType=VARCHAR},
      </if>
      <if test="record.principalType != null">
        principal_type = #{record.principalType,jdbcType=VARCHAR},
      </if>
      <if test="record.principalId != null">
        principal_id = #{record.principalId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.settleId != null">
        settle_id = #{record.settleId,jdbcType=VARCHAR},
      </if>
      <if test="record.settleTime != null">
        settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.channelRate != null">
        channel_rate = #{record.channelRate,jdbcType=DECIMAL},
      </if>
      <if test="record.settleAmount != null">
        settle_amount = #{record.settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_settlement
    set id = #{record.id,jdbcType=INTEGER},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      distributor_corp_id = #{record.distributorCorpId,jdbcType=INTEGER},
      distributor_corp_name = #{record.distributorCorpName,jdbcType=VARCHAR},
      principal_type = #{record.principalType,jdbcType=VARCHAR},
      principal_id = #{record.principalId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      settle_id = #{record.settleId,jdbcType=VARCHAR},
      settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      channel_rate = #{record.channelRate,jdbcType=DECIMAL},
      settle_amount = #{record.settleAmount,jdbcType=DECIMAL},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.OrderSettlementDO">
    update order_settlement
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="distributorCorpId != null">
        distributor_corp_id = #{distributorCorpId,jdbcType=INTEGER},
      </if>
      <if test="distributorCorpName != null">
        distributor_corp_name = #{distributorCorpName,jdbcType=VARCHAR},
      </if>
      <if test="principalType != null">
        principal_type = #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        principal_id = #{principalId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="settleId != null">
        settle_id = #{settleId,jdbcType=VARCHAR},
      </if>
      <if test="settleTime != null">
        settle_time = #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="channelRate != null">
        channel_rate = #{channelRate,jdbcType=DECIMAL},
      </if>
      <if test="settleAmount != null">
        settle_amount = #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.OrderSettlementDO">
    update order_settlement
    set order_no = #{orderNo,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      distributor_corp_id = #{distributorCorpId,jdbcType=INTEGER},
      distributor_corp_name = #{distributorCorpName,jdbcType=VARCHAR},
      principal_type = #{principalType,jdbcType=VARCHAR},
      principal_id = #{principalId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      settle_id = #{settleId,jdbcType=VARCHAR},
      settle_time = #{settleTime,jdbcType=TIMESTAMP},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      channel_rate = #{channelRate,jdbcType=DECIMAL},
      settle_amount = #{settleAmount,jdbcType=DECIMAL},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="queryPendingSettle" parameterType="map" resultMap="BaseResultMap">
    select a.*
    from `order_settlement` a
    left join `order_info` b on a.order_no = b.order_no
    where a.`status` = 2 and a.`distributor_corp_id` = #{distributorCorpId} and b.`finish_time` BETWEEN STR_TO_DATE(CONCAT(#{month}, '01'), '%Y%m%d') AND DATE_ADD(LAST_DAY(STR_TO_DATE(CONCAT(#{month}, '01'), '%Y%m%d')), INTERVAL 1 DAY) - INTERVAL 1 SECOND
  </select>

</mapper>