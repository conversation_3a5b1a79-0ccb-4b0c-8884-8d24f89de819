package ai.conrain.aigc.platform.service.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本工具类
 * <p>
 * 支持日期+自增号格式的版本管理，版本格式为：YYYYMMDD.N
 * 例如：20250619.0, 20250619.1, 20250620.0
 * </p>
 * <p>
 * 主要功能：
 * <ul>
 *   <li>版本格式验证</li>
 *   <li>版本比较（支持 Comparable 接口）</li>
 *   <li>版本解析和构建</li>
 *   <li>获取下一个版本号</li>
 *   <li>获取当前日期的初始版本</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-19
 */
@Slf4j
public class Versions {

    /**
     * 版本格式正则表达式：YYYYMMDD.N
     */
    private static final Pattern VERSION_PATTERN = Pattern.compile("^(\\d{8})\\.(\\d+)$");

    /**
     * 日期格式化器
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 版本信息类
     */
    @Data
    @AllArgsConstructor
    public static class Version implements Comparable<Version> {
        /**
         * 日期部分（格式：YYYYMMDD）
         */
        private String datePart;

        /**
         * 自增号部分
         */
        private Integer incrementPart;

        /**
         * 完整版本字符串
         */
        private String fullVersion;

        /**
         * 构造函数
         */
        public Version(String datePart, Integer incrementPart) {
            this.datePart = datePart;
            this.incrementPart = incrementPart;
            this.fullVersion = datePart + "." + incrementPart;
        }

        @Override
        public int compareTo(Version other) {
            if (other == null) {
                return 1;
            }

            // 首先比较日期部分
            int dateComparison = this.datePart.compareTo(other.datePart);
            if (dateComparison != 0) {
                return dateComparison;
            }

            // 日期相同时比较自增号
            return this.incrementPart.compareTo(other.incrementPart);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            Version version = (Version) obj;
            return Objects.equals(datePart, version.datePart) &&
                   Objects.equals(incrementPart, version.incrementPart);
        }

        @Override
        public int hashCode() {
            return Objects.hash(datePart, incrementPart);
        }

        @Override
        public String toString() {
            return fullVersion;
        }
    }

    /**
     * 验证版本格式是否正确
     *
     * @param version 版本字符串
     * @return true 如果格式正确，false 否则
     */
    public static boolean isValid(String version) {
        if (StringUtils.isBlank(version)) {
            return false;
        }

        Matcher matcher = VERSION_PATTERN.matcher(version.trim());
        if (!matcher.matches()) {
            return false;
        }

        // 验证日期部分是否为有效日期
        String datePart = matcher.group(1);
        try {
            LocalDate.parse(datePart, DATE_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            log.warn("版本中的日期部分无效: {}", datePart);
            return false;
        }
    }

    /**
     * 解析版本字符串为 Version 对象
     *
     * @param version 版本字符串
     * @return Version 对象，如果解析失败返回 null
     */
    public static Version parse(String version) {
        if (!isValid(version)) {
            log.warn("无效的版本格式: {}", version);
            return null;
        }

        Matcher matcher = VERSION_PATTERN.matcher(version.trim());
        if (matcher.matches()) {
            String datePart = matcher.group(1);
            Integer incrementPart = Integer.parseInt(matcher.group(2));
            return new Version(datePart, incrementPart, version.trim());
        }

        return null;
    }

    /**
     * 比较两个版本
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return 负数表示 version1 < version2，0 表示相等，正数表示 version1 > version2
     */
    public static int compare(String version1, String version2) {
        Version v1 = parse(version1);
        Version v2 = parse(version2);

        if (v1 == null && v2 == null) {
            return 0;
        }
        if (v1 == null) {
            return -1;
        }
        if (v2 == null) {
            return 1;
        }

        return v1.compareTo(v2);
    }

    /**
     * 判断版本1是否大于版本2
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return true 如果版本1大于版本2
     */
    public static boolean isGreaterThan(String version1, String version2) {
        return compare(version1, version2) > 0;
    }

    /**
     * 判断版本1是否小于版本2
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return true 如果版本1小于版本2
     */
    public static boolean isLessThan(String version1, String version2) {
        return compare(version1, version2) < 0;
    }

    /**
     * 判断两个版本是否相等
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return true 如果两个版本相等
     */
    public static boolean isEqual(String version1, String version2) {
        return compare(version1, version2) == 0;
    }

    /**
     * 获取当前日期的初始版本（自增号为0）
     *
     * @return 当前日期的初始版本，格式如 "20250619.0"
     */
    public static String getCurrentDateInitial() {
        String currentDate = LocalDate.now().format(DATE_FORMATTER);
        return currentDate + ".0";
    }

    /**
     * 获取指定日期的初始版本（自增号为0）
     *
     * @param date 指定日期
     * @return 指定日期的初始版本，格式如 "20250619.0"
     */
    public static String getDateInitial(LocalDate date) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }
        String dateStr = date.format(DATE_FORMATTER);
        return dateStr + ".0";
    }

    /**
     * 获取下一个版本号
     * 如果是同一天，自增号+1；如果是新的一天，自增号重置为0
     *
     * @param currentVersion 当前版本
     * @return 下一个版本号
     */
    public static String getNext(String currentVersion) {
        Version version = parse(currentVersion);
        if (version == null) {
            throw new IllegalArgumentException("无效的版本格式: " + currentVersion);
        }

        String currentDate = LocalDate.now().format(DATE_FORMATTER);

        // 如果是同一天，自增号+1
        if (version.getDatePart().equals(currentDate)) {
            return currentDate + "." + (version.getIncrementPart() + 1);
        } else {
            // 如果是新的一天，自增号重置为0
            return currentDate + ".0";
        }
    }

    /**
     * 获取指定日期的下一个版本号
     *
     * @param currentVersion 当前版本
     * @param targetDate 目标日期
     * @return 下一个版本号
     */
    public static String getNext(String currentVersion, LocalDate targetDate) {
        if (targetDate == null) {
            throw new IllegalArgumentException("目标日期不能为空");
        }

        Version version = parse(currentVersion);
        if (version == null) {
            throw new IllegalArgumentException("无效的版本格式: " + currentVersion);
        }

        String targetDateStr = targetDate.format(DATE_FORMATTER);

        // 如果是同一天，自增号+1
        if (version.getDatePart().equals(targetDateStr)) {
            return targetDateStr + "." + (version.getIncrementPart() + 1);
        } else {
            // 如果是不同的日期，自增号重置为0
            return targetDateStr + ".0";
        }
    }

    /**
     * 构建版本号
     *
     * @param date 日期
     * @param increment 自增号
     * @return 版本号字符串
     */
    public static String build(LocalDate date, int increment) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }
        if (increment < 0) {
            throw new IllegalArgumentException("自增号不能为负数");
        }

        String dateStr = date.format(DATE_FORMATTER);
        return dateStr + "." + increment;
    }

    /**
     * 构建版本号
     *
     * @param dateStr 日期字符串（格式：YYYYMMDD）
     * @param increment 自增号
     * @return 版本号字符串
     */
    public static String build(String dateStr, int increment) {
        if (StringUtils.isBlank(dateStr)) {
            throw new IllegalArgumentException("日期字符串不能为空");
        }
        if (increment < 0) {
            throw new IllegalArgumentException("自增号不能为负数");
        }

        // 验证日期格式
        try {
            LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("无效的日期格式: " + dateStr);
        }

        return dateStr + "." + increment;
    }

    /**
     * 获取版本的日期部分
     *
     * @param version 版本字符串
     * @return 日期部分（格式：YYYYMMDD），如果版本无效返回 null
     */
    public static String getDatePart(String version) {
        Version v = parse(version);
        return v != null ? v.getDatePart() : null;
    }

    /**
     * 获取版本的自增号部分
     *
     * @param version 版本字符串
     * @return 自增号，如果版本无效返回 null
     */
    public static Integer getIncrementPart(String version) {
        Version v = parse(version);
        return v != null ? v.getIncrementPart() : null;
    }

    /**
     * 获取版本对应的日期对象
     *
     * @param version 版本字符串
     * @return LocalDate 对象，如果版本无效返回 null
     */
    public static LocalDate getDate(String version) {
        String datePart = getDatePart(version);
        if (datePart == null) {
            return null;
        }

        try {
            return LocalDate.parse(datePart, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("解析版本日期失败: {}", datePart);
            return null;
        }
    }

    /**
     * 判断版本是否为今天的版本
     *
     * @param version 版本字符串
     * @return true 如果是今天的版本
     */
    public static boolean isToday(String version) {
        LocalDate versionDate = getDate(version);
        return versionDate != null && versionDate.equals(LocalDate.now());
    }

    /**
     * 判断版本是否为指定日期的版本
     *
     * @param version 版本字符串
     * @param date 指定日期
     * @return true 如果是指定日期的版本
     */
    public static boolean isDate(String version, LocalDate date) {
        if (date == null) {
            return false;
        }
        LocalDate versionDate = getDate(version);
        return versionDate != null && versionDate.equals(date);
    }

    /**
     * 获取版本的可读描述
     *
     * @param version 版本字符串
     * @return 可读描述，如 "2025年06月19日第1个版本"
     */
    public static String getDescription(String version) {
        Version v = parse(version);
        if (v == null) {
            return "无效版本";
        }

        LocalDate date = getDate(version);
        if (date == null) {
            return "无效版本";
        }

        return String.format("%d年%02d月%02d日第%d个版本",
                date.getYear(),
                date.getMonthValue(),
                date.getDayOfMonth(),
                v.getIncrementPart() + 1);
    }

    /**
     * 格式化版本显示
     *
     * @param version 版本字符串
     * @return 格式化后的版本显示，如 "2025-06-19 v0"
     */
    public static String formatDisplay(String version) {
        Version v = parse(version);
        if (v == null) {
            return "无效版本";
        }

        LocalDate date = getDate(version);
        if (date == null) {
            return "无效版本";
        }

        return String.format("%s v%d", date.toString(), v.getIncrementPart());
    }
}
