package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum PrincipalTypeEnum {

    CORP("CORP", "组织"),
    SUB_CORP("SUB_CORP", "子组织"),
    USER("USER", "用户"),
    ;

    private String code;

    private String desc;

    PrincipalTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static PrincipalTypeEnum getByCode(String code) {
        for (PrincipalTypeEnum typeEnum : PrincipalTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
