package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Data
public class MaterialModelExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    protected String startDate;

    protected String endDate;

    protected Integer currentUserId;

    @Getter
    @Setter
    public boolean onlyUnconfirmedLora;

    @Getter
    @Setter
    public String relatedOperatorType;

    @Getter
    @Setter
    public Integer relatedOrOperatorId;

    @Getter
    @Setter
    public String nameOrUserLike;

    public MaterialModelExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public MaterialModelExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public MaterialModelExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public MaterialModelExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria)this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria)this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria)this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria)this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria)this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria)this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria)this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andShowImageIsNull() {
            addCriterion("show_image is null");
            return (Criteria)this;
        }

        public Criteria andShowImageIsNotNull() {
            addCriterion("show_image is not null");
            return (Criteria)this;
        }

        public Criteria andShowImageEqualTo(String value) {
            addCriterion("show_image =", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotEqualTo(String value) {
            addCriterion("show_image <>", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageGreaterThan(String value) {
            addCriterion("show_image >", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageGreaterThanOrEqualTo(String value) {
            addCriterion("show_image >=", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLessThan(String value) {
            addCriterion("show_image <", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLessThanOrEqualTo(String value) {
            addCriterion("show_image <=", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLike(String value) {
            addCriterion("show_image like", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotLike(String value) {
            addCriterion("show_image not like", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageIn(List<String> values) {
            addCriterion("show_image in", values, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotIn(List<String> values) {
            addCriterion("show_image not in", values, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageBetween(String value1, String value2) {
            addCriterion("show_image between", value1, value2, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotBetween(String value1, String value2) {
            addCriterion("show_image not between", value1, value2, "showImage");
            return (Criteria)this;
        }

        public Criteria andLoraNameIsNull() {
            addCriterion("lora_name is null");
            return (Criteria)this;
        }

        public Criteria andLoraNameIsNotNull() {
            addCriterion("lora_name is not null");
            return (Criteria)this;
        }

        public Criteria andLoraNameEqualTo(String value) {
            addCriterion("lora_name =", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameNotEqualTo(String value) {
            addCriterion("lora_name <>", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameGreaterThan(String value) {
            addCriterion("lora_name >", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameGreaterThanOrEqualTo(String value) {
            addCriterion("lora_name >=", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameLessThan(String value) {
            addCriterion("lora_name <", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameLessThanOrEqualTo(String value) {
            addCriterion("lora_name <=", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameLike(String value) {
            addCriterion("lora_name like", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameNotLike(String value) {
            addCriterion("lora_name not like", value, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameIn(List<String> values) {
            addCriterion("lora_name in", values, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameNotIn(List<String> values) {
            addCriterion("lora_name not in", values, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameBetween(String value1, String value2) {
            addCriterion("lora_name between", value1, value2, "loraName");
            return (Criteria)this;
        }

        public Criteria andLoraNameNotBetween(String value1, String value2) {
            addCriterion("lora_name not between", value1, value2, "loraName");
            return (Criteria)this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria)this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria)this;
        }

        public Criteria andStatusEqualToInitialOrAgainReview() {
            addCriterion("((status = 'IN_TRAINING'"
                         + "AND train_detail -> '$.label.status' = 'COMPLETED'"
                         + "AND (train_detail -> '$.loraConfirmed' IS NULL OR train_detail -> '$.loraConfirmed' != 'Y'))"
                         + " OR (status = 'TESTING' AND ext_info -> '$.testImageFinished' = 'Y'))");
            return (Criteria)this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria)this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria)this;
        }

        public Criteria andOperatorIdEqualTo(Integer value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotEqualTo(Integer value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdGreaterThan(Integer value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdLessThan(Integer value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIn(List<Integer> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotIn(List<Integer> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdBetween(Integer value1, Integer value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria)this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria)this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria)this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria)this;
        }

        public Criteria andTrainDetailIsNotNull() {
            addCriterion("train_detail is not null");
            return (Criteria)this;
        }

        public Criteria andExtValueIsBlank(String key) {
            addCriterion(String.format("(ext_info->>'$.%s' is null or ext_info->>'$.%s' ='')", key, key));
            return (Criteria)this;
        }

        public Criteria andLabelCompleted() {
            addCriterion("train_detail -> '$.label.status' = 'COMPLETED'");
            return (Criteria)this;
        }

        public Criteria andLoraUnconfirmed() {
            addCriterion("(train_detail -> '$.loraConfirmed' IS NULL OR train_detail -> '$.loraConfirmed' != 'Y')");
            return (Criteria)this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andIsExperimental() {
            addCriterion("(ext_info -> '$.experimental' = true or ext_info -> '$.experimental' = 'true')");
            return (Criteria)this;
        }

        public Criteria andIsNotExperimental() {
            addCriterion("(ext_info -> '$.experimental' is null or ext_info -> '$.experimental' = false or ext_info -> '$.experimental' = 'false')");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andClothStyleTypeEqualTo(String clothStyleType) {
            addCriterion("JSON_EXTRACT(ext_info, '$.clothStyleType') like", "\"" + clothStyleType + "\"", "extInfo");
            return (Criteria)this;
        }

        public Criteria andClothStyleTypeIsNull() {
            addCriterion("JSON_EXTRACT(ext_info, '$.clothStyleType') is null");
            return (Criteria)this;
        }

        public Criteria andLoraTypeEquals(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.loraType')) =", value, "train_detail");
            return (Criteria)this;
        }

        public Criteria andCustomerCaseIsNotNull() {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.customerCase')) is not null");
            return (Criteria)this;
        }

        public Criteria andGarmentTypeEquals(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.garmentType')) =", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andGarmentTypeIn(List<String> value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.garmentType')) in", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andLabelTypeEquals(String value) {
            addCriterion("LOWER(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.labelType'))) =", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andIsDemoTag() {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.demoTag')) = 'Y'");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsCustomer() {
            addCriterion("user_id in (select user_id from user_vip_view )");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsVIP() {
            addCriterion("user_id in (select user_id from user_vip_view where vip = 1)");
            return (Criteria)this;
        }

        public Criteria andExtValueEquals(String key, String value) {
            addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key
                         + "')) is not null and JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) = '" + value
                         + "')");
            return (Criteria)this;
        }

        public Criteria andExtValueNotEquals(String key, String value) {
            addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key
                         + "')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) != '" + value + "')");
            return (Criteria)this;
        }

        public Criteria andExtValueGreaterThan(String key, Object value) {
            addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key
                         + "')) is not null and JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) > " + value
                         + ")");
            return (Criteria)this;
        }

        public Criteria andExtValueLike(String key, Object value) {
            String valueStr = String.valueOf(value);
            
            // 当value为adult时的特殊处理
            if ("adult".equals(valueStr)) {
                addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key 
                         + "')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) = 'adult')");
            } else {
                // 原有逻辑
                valueStr = "%" + valueStr + "%";
                addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key
                         + "')) is not null and JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) like '" + valueStr
                         + "')");
            }
            
            return (Criteria)this;
        }

        public Criteria andTestImageFinished() {
            addCriterion("ext_info -> '$.testImageFinished' = 'Y'");
            return (Criteria)this;
        }

        public Criteria andReviewerIdEqualTo(Integer value) {
            addCriterion("ext_info -> '$.reviewerId' = ", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andReviewerIdIsNull() {
            addCriterion("ext_info -> '$.reviewerId' is null");
            return (Criteria)this;
        }

        public Criteria andReviewerIdOrUserIdsIn(Integer reviewerId, List<Integer> userIds) {
            addCriterion("(ext_info -> '$.reviewerId' = " + reviewerId + " or user_id in (" + StringUtils.join(userIds, ",") + "))");
            return (Criteria)this;
        }

        public Criteria andMainIdEqualTo(Integer value) {
            addCriterion("main_id =", value, "mainId");
            return (Criteria)this;
        }

        public Criteria andMainTypeEqualTo(String value) {
            addCriterion("main_type =", value, "mainType");
            return (Criteria)this;
        }

        public Criteria andMainTypeIn(List<String> values) {
            addCriterion("main_type in", values, "mainType");
            return (Criteria)this;
        }

        public Criteria andMaterialTypeEqualTo(String value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria)this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(MaterialModelDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(
                MaterialModelDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}