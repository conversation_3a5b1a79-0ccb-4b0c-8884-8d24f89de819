package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.impl.AlipayPayService;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.CreateQrCodeRequest;
import ai.conrain.aigc.platform.service.model.vo.PayQRCode;
import ai.conrain.aigc.platform.service.model.vo.PayQueryRet;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.internal.util.AlipaySignature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/alipay")
public class AlipayController {

    @Autowired
    private AlipayPayService alipayPayService;

    @Value("${alipay.alipayPublicKey}")
    private String alipayPublicKey;

    /**
     * 订单码支付，下单
     */
    @PostMapping("/qrcode")
    public Result<PayQRCode> createPayQRCode(@Valid @RequestBody CreateQrCodeRequest request) {
        try {
            return Result.success(alipayPayService.createPayQRCode(request));
        } catch (Exception e) {
            log.error("创建支付二维码失败", e);
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "创建支付二维码失败");
    }

    @PostMapping("/query")
    public Result<PayQueryRet> queryPayResult(@JsonArg @NotBlank String orderNo) {
        try {
            return Result.success(alipayPayService.queryPayResult(orderNo));
        } catch (Throwable e) {
            log.error("查询支付订单失败", e);
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "查询支付订单失败");
    }

    /**
     * <a href="https://opendocs.alipay.com/open/0c2c19">...</a>
     */
    @PostMapping("/notify")
    public String notify(HttpServletRequest request, HttpServletResponse response) {

        try {
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
                params.put(name, valueStr);
            }

            log.info("接收到支付宝通知:{}", JSON.toJSONString(params));

            if (MapUtils.isEmpty(params)) {
                log.error("解析支付宝通知内容为空，忽略");
                return notifySuccessOrIgnore();
            }

            //调用SDK验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(params, alipayPublicKey, "UTF-8", "RSA2");
            if (!signVerified) {
                log.error("支付宝支付结果通知验签失败");
                DingTalkNoticeHelper.sendMsg2DevGroup("支付宝支付结果通知处理验签失败，返回进行重试\ntraceId:" + MDC.get("traceId"));
                return notifyFailed();
            }

            String tradeStatus = params.get("trade_status");
            if ("TRADE_FINISHED".equals(tradeStatus) || "TRADE_SUCCESS".equals(tradeStatus)) {
                String orderNo = params.get("out_trade_no");
                log.info("支付宝支付成功: orderNo={},result={}", orderNo, params);
                AssertUtil.assertTrue(alipayPayService.onPaySuccess(orderNo), "支付成功但处理失败，需要重试");
            }

            return notifySuccessOrIgnore();

        } catch (Throwable t) {

            log.error("支付宝支付结果通知处理失败，返回进行重试", t);
            DingTalkNoticeHelper.sendMsg2DevGroup("支付宝支付结果通知处理异常，返回进行重试\ntraceId:" + MDC.get("traceId"));
            return notifyFailed();
        }
    }

    // 返回成功
    private String notifySuccessOrIgnore() {
        return "success";
    }

    // 返回失败，会重发
    private String notifyFailed() {
        return "fail";
    }
}
