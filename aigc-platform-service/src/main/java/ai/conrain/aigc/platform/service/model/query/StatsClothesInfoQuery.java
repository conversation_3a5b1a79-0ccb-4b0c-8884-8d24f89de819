package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * StatsClothesInfoQuery
 *
 * @version StatsClothesInfoService.java v 0.1 2025-04-22 05:07:33
 */
@Data
public class StatsClothesInfoQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    @ApiModelProperty(name = "statsType", value = "统计类型：DAILY/WEEKLY/MONTHLY/TOTAL")
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    @ApiModelProperty(name = "statsDate", value = "统计日期: 格式为yyyy-MM-dd")
    private String statsDate;

    /** vip 用户上传服装 套数 */
    @ApiModelProperty(name = "vipClothesCount", value = "vip 用户上传服装 套数")
    private Integer vipClothesCount;

    /** 自动训练服装 套数 */
    @ApiModelProperty(name = "autoTrainCount", value = "自动训练服装 套数")
    private Integer autoTrainCount;

    /** 人工交付服装 套数 */
    @ApiModelProperty(name = "manualDeliveryCount", value = "人工交付服装 套数")
    private Integer manualDeliveryCount;

    /** 自动训练+交付 套数 */
    @ApiModelProperty(name = "autoTrainAndDeliveryCount", value = "自动训练+交付 套数")
    private Integer autoTrainAndDeliveryCount;

    /** 二次抠图（手动上传图片+系统级抠图） 套数 */
    @ApiModelProperty(name = "retryMattingCount", value = "二次抠图（手动上传图片+系统级抠图） 套数")
    private Integer retryMattingCount;

    /** 更新提示词 套数 */
    @ApiModelProperty(name = "updatePromptCount", value = "更新提示词 套数")
    private Integer updatePromptCount;

    /** 克隆服装 套数 */
    @ApiModelProperty(name = "copyCount", value = "克隆服装 套数")
    private Integer copyCount;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 扩展字段 */
    @ApiModelProperty(name = "extInfo", value = "扩展字段")
    private String extInfo;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

    /** 开始时间 */
    private String startDate;

    /** 结束时间 */
    private String endDate;
}