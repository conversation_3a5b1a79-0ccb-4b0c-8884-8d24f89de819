/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import java.util.ArrayList;
import java.util.List;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 创作批次状态
 *
 * <AUTHOR>
 * @version : CreativeStatusEnum.java, v 0.1 2024/5/9 14:36 renxiao.wu Exp $
 */
@Getter
public enum CreativeStatusEnum {
    INIT("INIT", "初始化", 1),
    QUEUE("QUEUE", "排队中", 2),
    PROCESSING("PROCESSING", "处理中", 3),
    FINISHED("FINISHED", "完成", 4),
    FAILED("FAILED", "失败", 4);

    /** 处理中的状态列表 */
    @Getter
    private static final List<String> processingStatusList = new ArrayList<String>();

    /** 未完结的状态列表 */
    @Getter
    private static final List<String> uncompleteStatusList = new ArrayList<String>();

    static {
        processingStatusList.add(CreativeStatusEnum.QUEUE.getCode());
        processingStatusList.add(CreativeStatusEnum.PROCESSING.getCode());
    }

    static {
        uncompleteStatusList.add(CreativeStatusEnum.INIT.getCode());
        uncompleteStatusList.add(CreativeStatusEnum.QUEUE.getCode());
        uncompleteStatusList.add(CreativeStatusEnum.PROCESSING.getCode());
    }

    private String code;
    private String desc;
    private Integer order;

    CreativeStatusEnum(String code, String desc, Integer order) {
        this.code = code;
        this.desc = desc;
        this.order = order;
    }

    public static CreativeStatusEnum getByQueueCode(QueueCodeEnum queueCode) {
        switch (queueCode) {
            case RUNNING:
                return PROCESSING;
            case COMPLETED:
                return FINISHED;
            case FAILED:
                return FAILED;
            default:
                return QUEUE;
        }
    }

    public static CreativeStatusEnum getByCode(String code) {
        for (CreativeStatusEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    public boolean isEnd() {
        return this.equals(FINISHED) || this.equals(FAILED);
    }
}
