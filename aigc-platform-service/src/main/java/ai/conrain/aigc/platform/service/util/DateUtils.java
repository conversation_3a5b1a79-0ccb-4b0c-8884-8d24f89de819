/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

/**
 * 时间工具类
 *
 * <AUTHOR>
 * @version : DateUtils.java, v 0.1 2023/9/10 22:35 renxiao.wu Exp $
 */
@Slf4j
public abstract class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    //bugfix: SimpleDateFormat 是线程不安全的类，包装到ThreadLocal中

    /** yyyyMMdd类型format */
    private static final ThreadLocal<SimpleDateFormat> SHORT_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));

    /** yyyyMMdd HH:mm:ss格式format */
    private static final ThreadLocal<SimpleDateFormat> SHORT_TIME_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd HH:mm:ss"));

    /** yyyy-MM-dd HH:mm:ss格式format */
    private static final ThreadLocal<SimpleDateFormat> SIMPLE_TIME_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /** yyyy-MM-dd HH:mm:ss.SSS格式format */
    private static final ThreadLocal<SimpleDateFormat> FULL_TIME_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));

    /** yyyy-MM-dd格式format */
    public static final ThreadLocal<SimpleDateFormat> ONLY_DATE_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    /**
     * 将yyyyMMdd格式的字符串转化成date类型
     *
     * @param date yyyyMMdd格式字符串
     * @return 目标date类型
     */
    public static Date parseShort(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        date = StringUtils.left(date, 8);

        try {
            return SHORT_FORMAT.get().parse(date);
        } catch (ParseException e) {
            log.error("parseShort error", e);
        }

        return null;
    }

    /**
     * 将yyyyMMdd格式的字符串转化成date类型
     * 转后后时间：yyyyMMdd 23:59:59
     *
     * @param date yyyyMMdd格式字符串
     * @return 目标date类型
     */
    public static Date parseShortLastTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        date = StringUtils.left(date, 8);

        try {
            return SHORT_TIME_FORMAT.get().parse(date + " 23:59:59");
        } catch (ParseException e) {
            log.error("parseShortLastTime error", e);
        }

        return null;
    }

    /**
     * 将yyyyMMdd格式的字符串转化成date类型
     *
     * @param date yyyyMMdd格式字符串
     * @return 目标date类型
     */
    public static Date parseSimple(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        try {
            return SIMPLE_TIME_FORMAT.get().parse(date);
        } catch (ParseException e) {
            log.error("parseSimple error", e);
        }

        return null;
    }

    public static String formatTime(Date d) {
        if (d == null) {
            return null;
        }
        return SIMPLE_TIME_FORMAT.get().format(d);
    }

    public static String formatFullTime(Date d) {
        if (d == null) {
            return null;
        }
        return FULL_TIME_FORMAT.get().format(d);
    }

    public static String formatTime(Date d, String format) {
        if (d == null || StringUtils.isBlank(format)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(d);
    }

    /**
     * 将yyyy-MM-dd格式的字符串转化成date类型
     *
     * @param date yyyy-MM-dd格式字符串
     * @return 目标date类型
     */
    public static Date parseSimpleDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        try {
            return ONLY_DATE_FORMAT.get().parse(date);
        } catch (ParseException e) {
            log.error("parseSimple error", e);
        }

        return null;
    }

    public static String formatSimpleDate(Date date) {
        if (null == date) {
            return null;
        }
        return ONLY_DATE_FORMAT.get().format(date);
    }

    /**
     * 将yyyy-MM-dd格式的字符串转化成date类型
     *
     * @param date yyyy-MM-dd格式字符串
     * @return 目标date类型
     */
    public static Date parseSimpleLastTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        try {
            return SIMPLE_TIME_FORMAT.get().parse(date + " 23:59:59");
        } catch (ParseException e) {
            log.error("parseSimple error", e);
        }

        return null;
    }

    /**
     * 两个时间比较
     * 1表示date1大于date2，0相等，-1表示date2大于date1
     * 异常分支：
     * 1、date1为空返回-1
     * 2、date2为空返回1
     *
     * @param date1 被比较时间
     * @param date2 待比较时间
     * @return 1表示date1大于date2，0相等，-1表示date2大于date1
     */
    public static int compare(Date date1, Date date2) {
        if (null == date1) {
            return -1;
        }

        if (null == date2) {
            return 1;
        }

        return date1.compareTo(date2);
    }

    /**
     * 判断某个时间是否在当前时间之前
     *
     * @param date 目标时间
     * @return true在当前时间之间
     */
    public static boolean beforeNow(Date date) {
        return compare(date, new Date()) <= 0;
    }

    /**
     * 判断date2是否在date1的seconds秒之后
     *
     * @param date1   被比较时间
     * @param date2   比较时间
     * @param seconds 秒数
     * @return true在date1的seconds秒之后
     */
    public static boolean after(Date date1, Date date2, long seconds) {
        return (date2.getTime() - date1.getTime()) / 1000 > seconds;
    }

    /**
     * 只比较日期，date2 是否在 date1 之后
     * 使用Java 8 LocalDate进行比较，自动忽略时间部分
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果date2的日期在date1的日期之后，返回true；否则返回false
     */
    public static boolean afterDate(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        // 转换为LocalDate，只保留日期部分
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 比较两个日期
        return localDate2.isAfter(localDate1);
    }

    /** 
     * 只比较日期, date2 是否在 date1 的 days 天之后
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @param days 天数
     * @return 如果date2的日期在date1的日期之后，返回true；否则返回false
     */
    public static boolean afterDate(Date date1, Date date2, int days) {
        if (date1 == null || date2 == null) {
            return false;
        }

        // 转换为LocalDate，只保留日期部分
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 将第一个日期加上指定天数
        LocalDate localDate1PlusDays = localDate1.plusDays(days);

        // 比较两个日期
        return localDate2.isAfter(localDate1PlusDays);
    }

    /**
     * 格式化当前时间，20230910
     *
     * @return 格式化后时间字符串
     */
    public static String getNowShort() {
        return formatShort(new Date());
    }

    /**
     * 格式化当前时间，20230910
     *
     * @return 格式化后时间字符串
     */
    public static String formatShort(Date date) {
        return SHORT_FORMAT.get().format(date);
    }

    /**
     * 格式化当前时间，20230910 12:22:33
     *
     * @return 格式化后时间字符串
     */
    public static String formatSimple(Date date) {
        return SHORT_TIME_FORMAT.get().format(date);
    }

    /**
     * 格式化当前时间，20230910
     *
     * @return 格式化后时间字符串
     */
    public static String getYesterdayShort() {
        return SHORT_FORMAT.get().format(getYesterday());
    }

    /**
     * 格式化当前时间，20230910
     *
     * @return 格式化后时间字符串
     */
    public static Date getYesterday() {
        // 获取当前日期
        Date currentDate = new Date();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将日期减去一天，得到昨天的日期
        calendar.add(Calendar.DAY_OF_YEAR, -1);

        return calendar.getTime();
    }

    /**
     * 计算两个时间差值，精确到秒
     *
     * @param date1 date1
     * @param date2 date2
     * @return 差值
     */
    public static long diffSecond(Date date1, Date date2) {
        if (null == date1 || null == date2) {
            return 0;
        }
        return (date1.getTime() - date2.getTime()) / 1000;
    }

    /**
     * 计算目标时间与当前事件的差值，精确到秒
     *
     * @param date date
     * @return 差值
     */
    public static long diffNowSecond(Date date) {
        if (null == date) {
            return 0;
        }
        return (System.currentTimeMillis() - date.getTime()) / 1000;
    }

    public static float diffNowHours(Date date) {
        if (null == date) {
            return 0;
        }
        return (float)Math.abs(System.currentTimeMillis() - date.getTime()) / 1000 / 3600.0F;
    }

    /**
     * 判断两个时间是否相等
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean timeEquals(Date date1, Date date2) {
        if (date1 == null && date2 == null) {
            return true;
        }

        if (date1 == null || date2 == null) {
            return false;
        }

        return date1.compareTo(date2) == 0;
    }

    /**
     * 根据给定的createTime计算出一年后的同日期的0点
     *
     * @param createTime
     * @return
     */
    public static Date oneYearLaterAtMidnight(Date createTime) {
        if (createTime == null) {
            throw new IllegalArgumentException("createTime cannot be null");
        }

        // 创建Calendar实例并设置时间为createTime
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createTime);

        // 增加一年
        calendar.add(Calendar.YEAR, 1);

        // 设置小时、分钟、秒和毫秒为0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // 返回Date对象
        return calendar.getTime();
    }

    /**
     * 获取当前时间的字符串
     *
     * @return yyyy-MM-dd HH:mm:ss 格式的当前时间
     */
    public static String getCurrentTimeStr() {
        return new DateTime(DateTimeZone.forOffsetHours(8)).toString("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取当天的开始时间 (00:00:00)
     *
     * @return 当天的00:00:00
     */
    public static Date getFirstOfDay() {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.set(Calendar.HOUR_OF_DAY, 0);
        startCalendar.set(Calendar.MINUTE, 0);
        startCalendar.set(Calendar.SECOND, 0);
        startCalendar.set(Calendar.MILLISECOND, 0);
        return startCalendar.getTime();
    }

    /**
     * 获取当天的结束时间 (23:59:59)
     *
     * @return 当天的23:59:59
     */
    public static Date getLastOfDay() {
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.set(Calendar.HOUR_OF_DAY, 23);
        endCalendar.set(Calendar.MINUTE, 59);
        endCalendar.set(Calendar.SECOND, 59);
        endCalendar.set(Calendar.MILLISECOND, 999);
        return endCalendar.getTime();
    }


    /**
     * 判断两个日期是否是同一天
     */
    public static boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }


    /**
     * 获取日期所在周的标识 (格式: yyyy-MM-dd，表示周一的日期)
     */
    public static String getWeekKey(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        // 调整到本周的周一
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : Calendar.MONDAY - dayOfWeek;
        cal.add(Calendar.DAY_OF_MONTH, offset);

        // 返回周一的日期作为周标识
        return ONLY_DATE_FORMAT.get().format(cal.getTime());
    }

    /**
     * 获取日期所在月的标识 (格式: yyyy-MM-dd，表示月初第一天的日期)
     */
    public static String getMonthKey(Date date) {
        Calendar monthCal = Calendar.getInstance();
        monthCal.setTime(date);
        monthCal.set(Calendar.DAY_OF_MONTH, 1);
        return ONLY_DATE_FORMAT.get().format(monthCal.getTime());
    }

    /**
     * 解析月标识为日期 (月初第一天)
     */
    public static Date parseMonthKey(String monthKey) throws ParseException {
        // 直接解析日期，月标识已经是月初第一天的日期
        return ONLY_DATE_FORMAT.get().parse(monthKey);
    }

    /**
     * 解析周标识为日期（周标识即为周一的日期，格式：yyyy-MM-dd）
     */
    public static Date parseWeekKey(String weekKey) throws ParseException {
        // 直接解析日期，周标识已经是周一的日期
        return ONLY_DATE_FORMAT.get().parse(weekKey);
    }

    /**
     * 获取日期所在季度的标识 (格式: yyyy-MM-dd，表示季度第一天的日期)
     */
    public static String getQuarterKey(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        // 获取当前月份 (0-11)
        int month = cal.get(Calendar.MONTH);

        // 计算当前季度的第一个月 (0, 3, 6, 9)
        int quarterFirstMonth = (month / 3) * 3;

        // 设置为季度的第一个月
        cal.set(Calendar.MONTH, quarterFirstMonth);
        // 设置为该月的第一天
        cal.set(Calendar.DAY_OF_MONTH, 1);

        return ONLY_DATE_FORMAT.get().format(cal.getTime());
    }

    /**
     * 解析季度标识为日期（季度标识表示季度第一天的日期，格式：yyyy-MM-dd）
     */
    public static Date parseQuarterKey(String quarterKey) throws ParseException {
        return ONLY_DATE_FORMAT.get().parse(quarterKey);
    }

    /**
     * 判断日期是否是周一
     *
     * @param date 日期
     * @return 是否是周一
     */
    public static boolean isMonday(Date date) {
        if (date == null) {
            return false;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY;
    }

    /**
     * 判断日期是否是月初第一天
     *
     * @param date 日期
     * @return 是否是月初第一天
     */
    public static boolean isFirstDayOfMonth(Date date) {
        if (date == null) {
            return false;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_MONTH) == 1;
    }

    /**
     * 判断日期是否是季度初第一天（1月1日、4月1日、7月1日或10月1日）
     *
     * @param date 日期
     * @return 是否是季度初第一天
     */
    public static boolean isFirstDayOfQuarter(Date date) {
        if (date == null) {
            return false;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int month = cal.get(Calendar.MONTH); // 0-11
        int day = cal.get(Calendar.DAY_OF_MONTH);

        // 季度初始月份：0(1月)、3(4月)、6(7月)、9(10月)
        return day == 1 && (month == Calendar.JANUARY || month == Calendar.APRIL ||
                month == Calendar.JULY || month == Calendar.OCTOBER);
    }

    /**
     * 判断一个日期是否是今天
     *
     * @param date 要判断的日期
     * @return 如果是今天返回 true，否则返回 false
     */
    public static boolean isToday(Date date) {
        if (date == null) {
            return false;
        }

        // 获取今天的日历实例
        Calendar today = Calendar.getInstance();
        // 获取目标日期的日历实例
        Calendar targetDate = Calendar.getInstance();
        targetDate.setTime(date);

        // 比较年、月、日是否相同
        return today.get(Calendar.YEAR) == targetDate.get(Calendar.YEAR) &&
               today.get(Calendar.MONTH) == targetDate.get(Calendar.MONTH) &&
               today.get(Calendar.DAY_OF_MONTH) == targetDate.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 将多种常见格式的日期字符串转换为Date类型，支持更灵活的格式
     * 支持格式包括:
     * - yyyy-MM-dd (标准格式)
     * - yyyy-MM-dd HH:mm:ss (带时间格式)
     * - yyyy/MM/dd (斜杠分隔)
     * - MM/dd/yyyy (美式格式)
     * - yyyyMMdd (无分隔符)
     * - yyyy.MM.dd (点分隔)
     *
     * @param dateStr 日期字符串parseSimpleDateWithFormats
     * @return 解析后的Date对象，解析失败返回null
     */
    public static Date parseSimpleDateWithFormats(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 首先尝试标准格式 yyyy-MM-dd
        try {
            return ONLY_DATE_FORMAT.get().parse(dateStr);
        } catch (ParseException e) {
            // 标准格式解析失败，继续尝试其他格式
        }

        // 如果包含冒号，可能是带时间的格式
        if (dateStr.length() > 10 && dateStr.contains(":")) {
            try {
                return SIMPLE_TIME_FORMAT.get().parse(dateStr);
            } catch (ParseException e) {
                // 带时间格式解析失败，继续尝试其他格式
            }
        }

        // 尝试其他常见格式
        SimpleDateFormat[] formats = new SimpleDateFormat[] {
            new SimpleDateFormat("yyyy/MM/dd"),
            new SimpleDateFormat("MM/dd/yyyy"),
            new SimpleDateFormat("yyyyMMdd"),
            new SimpleDateFormat("yyyy.MM.dd")
        };

        for (SimpleDateFormat format : formats) {
            try {
                return format.parse(dateStr);
            } catch (ParseException pe) {
                // 继续尝试下一种格式
            }
        }

        // 所有格式都解析失败，记录错误并返回null
        log.error("无法解析日期: {}, 不支持的日期格式", dateStr);
        return null;
    }

    /**
     * 获取 n 个月 后的 第一天
     */
    public static Date getFirstDayOfNMonth(int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, n); // 使用add代替set，正确处理月份跨年
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取 n 个月后的 最后一天
     */
    public static Date getLastDayOfNMonth(int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, n); // 使用add代替set，正确处理月份跨年
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 设置为当天的23:59:59.999，确保包含当天的所有时间
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取n天后的日期
     */
    public static Date getDateAfterNDays(Date date, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, n);
        return calendar.getTime();
    }

    /**
     * 获取 n 个自然月后的日期
     * 
     * @param date 基准日期
     * @param n 月份偏移量
     * @param subtractOneDay 是否减去一天
     *                       - 如果为true，则在结果基础上减1天
     * @return 计算后的日期
     * 
     * 示例：
     * - getDateAfterNMonths(2025-03-15, 1, true) = 2025-04-14
     * - getDateAfterNMonths(2025-03-01, 2, true) = 2025-04-30
     * - getDateAfterNMonths(2025-05-31, 1, false) = 2025-06-30
     */
    public static Date getDateAfterNNatureMonths(Date date, int n, boolean subtractOneDay) {
        if (date == null) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        
        // 保存原始日期的日
        int originalDay = calendar.get(Calendar.DAY_OF_MONTH);
        
        // 添加n个月
        calendar.add(Calendar.MONTH, n);
        
        // 获取目标月份的最大天数
        int maxDayInTargetMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        
        // 如果原始日期的日大于目标月份的最大天数，则设置为目标月份的最后一天
        if (originalDay > maxDayInTargetMonth) {
            calendar.set(Calendar.DAY_OF_MONTH, maxDayInTargetMonth);
        } else {
            calendar.set(Calendar.DAY_OF_MONTH, originalDay);
        }
        
        // 处理减一天的逻辑
        if (subtractOneDay) {
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        
        return calendar.getTime();
    }

    /**
     * 验证日期字符串是否符合指定格式且日期有效
     * 
     * @param date 日期字符串
     * @param format 日期格式，如 "yyyy-MM-dd"
     * @return 如果日期字符串有效且符合格式返回true，否则返回false
     */
    public static boolean isValidDate(String date, String format) {
        if (StringUtils.isBlank(date) || StringUtils.isBlank(format)) {
            return false;
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            // 设置严格模式，不允许解析不存在的日期如2023-02-30
            sdf.setLenient(false);
            sdf.parse(date);
            return true;
        } catch (ParseException | IllegalArgumentException e) {
            log.debug("Invalid date format: date={}, format={}, error={}", date, format, e.getMessage());
            return false;
        }
    }
}
