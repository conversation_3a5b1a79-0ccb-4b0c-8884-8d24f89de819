/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;

import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.validation.EnumValid;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 印花上身请求
 *
 * <AUTHOR>
 * @version : LogoCombineRequest.java, v 0.1 2024/7/18 14:30 renxiao.wu Exp $
 */
@Data
public class LogoCombineRequest implements CreativeRequest {
    private static final long serialVersionUID = -2352017000010389917L;
    /** 相关配置 */
    @NotEmpty
    private Map<Integer, List<Integer>> configs;

    /** 图片数量，默认5张 */
    private int imageNum = 5;

    /** 图片比例 */
    @EnumValid(ProportionTypeEnum.class)
    private String proportion;

    /** 合成后的图片 */
    @JsonIgnore
    @NotEmpty
    private MultipartFile image;
}
