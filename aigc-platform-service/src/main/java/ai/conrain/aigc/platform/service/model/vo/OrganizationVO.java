package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson.JSONObject;
import ai.conrain.aigc.platform.service.enums.SalesTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SALES_TYPE;

/**
 * OrganizationVO
 *
 * @version OrganizationService.java v 0.1 2024-07-12 04:26:40
 */
@Data
public class OrganizationVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 组织ID */
	@ApiModelProperty(name = "id", value = "组织ID")
	private Integer id;

	/** 父级组织IDOrganizationVO，根组织为0 */
	@ApiModelProperty(name = "parentId", value = "父级组织ID，根组织为0")
	private Integer parentId;

	/** 是否根结点组织，0不是，1是 */
	@ApiModelProperty(name = "root", value = "是否根结点组织，0不是，1是")
	private Boolean root;

	/** 根组织ID，根组织ID等于ID,根组织有且只有一个 */
	@ApiModelProperty(name = "rootId", value = "根组织ID，根组织ID等于ID,根组织有且只有一个")
	private Integer rootId;

	/** 组织类型，DISTRIBUTOR_CORP：渠道商企业｜DISTRIBUTOR_DEPT：渠道商部门 */
	@ApiModelProperty(name = "orgType", value = "组织类型，DISTRIBUTOR_CORP：渠道商企业｜DISTRIBUTOR_DEPT：渠道商部门")
	private String orgType;

	/** 组织名称 */
	@ApiModelProperty(name = "name", value = "组织名称")
	private String name;

	/** 组织标签，作为组织类型的补充，预留 */
	@ApiModelProperty(name = "tags", value = "组织标签，作为组织类型的补充，预留")
	private String tags;

	/** 组织层级，根组织为0，根组织下有1级组织，1级组织下有2级组织，以此类推 */
	@ApiModelProperty(name = "orgLevel", value = "组织层级，根组织为0，根组织下有1级组织，1级组织下有2级组织，以此类推")
	private Integer orgLevel;

	/** 创建者主账号id */
	@ApiModelProperty(name = "creatorMasterUserId", value = "创建者主账号id")
	private Integer creatorMasterUserId;

	/** 创建人角色类型，DISTRIBUTOR：渠道商 */
	@ApiModelProperty(name = "creatorUserRoleType", value = "创建人角色类型，DISTRIBUTOR：渠道商")
	private String creatorUserRoleType;

	/** 创建人操作员账号id */
	@ApiModelProperty(name = "creatorOperatorUserId", value = "创建人操作员账号id")
	private Integer creatorOperatorUserId;

	/** 最近修改人的操作员账号id */
	@ApiModelProperty(name = "modifierOperatorUserId", value = "最近修改人的操作员账号id")
	private Integer modifierOperatorUserId;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private JSONObject extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	/**下级部门*/
	private List<OrganizationVO> children;

	// ========= 额外字段, 使用时看看 sql 有没有查========

	/** 组织主账号 id */
	@ApiModelProperty(name = "masterUserId", value = "组织主账号 id")
	private Integer channelAdminId;

	/** 组织主账号昵称 */
	@ApiModelProperty(name = "masterUserNickName", value = "组织主账号昵称")
	private String channelAdminNickName;


	public <T> T getExtInfo(String key, Class<T> clz) {
		if (ObjectUtils.isEmpty(this.extInfo)) {
			return null;
		}
		return this.extInfo.getObject(key, clz);
	}

	public void putExtInfo(String key, Object value) {
		if (ObjectUtils.isEmpty(this.extInfo)) {
			this.extInfo = new JSONObject();
		}
		this.extInfo.put(key, value);
	}

	public SalesTypeEnum getSalesType() {
		return SalesTypeEnum.getByCode(this.getExtInfo(KEY_SALES_TYPE, String.class));
	}
}