package ai.conrain.aigc.platform.service.enums;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import lombok.Getter;

@Getter
public enum MaterialModelTaskStatusEnum {
    init("init", "未开始"),
    queue("queue", "排队中"),
    active("active", "进行中"),
    success("success", "成功"),
    fail("fail", "失败"),
    cancel("cancel", "取消");

    private String code;
    private String desc;

    MaterialModelTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MaterialModelTaskStatusEnum getByQueueCode(QueueResult.QueueCodeEnum code) {
        if (code == null) {
            throw new NullPointerException("QueueCodeEnum is null");
        }
        switch(code){
            case QUEUED: return queue;
            case RUNNING: return active;
            case COMPLETED: return success;
            case FAILED: return fail;
            default: return queue;
        }
    }
}
