package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.CreativeBatchDO;
import ai.conrain.aigc.platform.dal.entity.StatsQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.StatsUserQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.UserCountDO;
import ai.conrain.aigc.platform.dal.example.CreativeBatchExample;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CreativeBatchDAO {
    long countByExample(CreativeBatchExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CreativeBatchDO record);

    int insertSelective(CreativeBatchDO record);

    List<CreativeBatchDO> selectByExampleWithBLOBs(CreativeBatchExample example);

    List<CreativeBatchDO> selectByExample(CreativeBatchExample example);

    CreativeBatchDO selectByPrimaryKey(Integer id);

    CreativeBatchDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id,
                                                        @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") CreativeBatchDO record,
                                 @Param("example") CreativeBatchExample example);

    int updateByExampleWithBLOBs(@Param("record") CreativeBatchDO record,
                                 @Param("example") CreativeBatchExample example);

    int updateByExample(@Param("record") CreativeBatchDO record, @Param("example") CreativeBatchExample example);

    int updateByPrimaryKeySelective(CreativeBatchDO record);

    int updateByPrimaryKeyWithBLOBs(CreativeBatchDO record);

    int updateByPrimaryKey(CreativeBatchDO record);

    int logicalDeleteByExample(@Param("example") CreativeBatchExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    CreativeBatchDO selectByPromptIdWithBLOBs(String promptId);

    List<Integer> getCreativeModelIds(@Param("operatorId") Integer operatorId);

    List<CreativeBatchDO> selectUnCompletedTopUser(Map<String, Object> params);

    List<CreativeBatchDO> selectByElements(List<Integer> elementIds, Integer userId, Boolean testFlag, Integer limit);

    Long queryCreateImageCntByModelId(Integer modelId, Integer userId, boolean sysGenFinished);

    CreativeBatchDO lockByPrimaryKey(Integer id);

    List<UserCountDO> queryQueuedUser(CreativeBatchExample example);

    /**
     * 统计创作等待队列数据
     *
     * @return 创作等待队列数据
     */
    StatsQueuedCreativeDO statsQueuedCreative();

    /**
     * 统计用户创作等待队列数据
     *
     * @return 用户创作等待队列数据
     */
    List<StatsUserQueuedCreativeDO> statsCustomerQueue();

    /**
     * 根据姿势ID列表批量查询每个ID的最新创作记录
     *
     * @param poseIdList 姿势ID列表
     * @return 最新的创作记录列表
     */
    List<CreativeBatchDO> selectLatestByPoseIds(@Param("poseIdList") List<Integer> poseIdList);
}