package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * WorkflowTaskQuery
 *
 * @version WorkflowTaskService.java v 0.1 2025-04-02 04:59:35
 */
@Data
public class WorkflowTaskQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(name = "id", value = "主键ID")
    private Integer id;

    /** 关联的业务id */
    @ApiModelProperty(name = "bizId", value = "关联的业务id")
    private Integer bizId;

    /** 任务类型 */
    @ApiModelProperty(name = "type", value = "任务类型")
    private String type;

    /** 任务类型 */
    @ApiModelProperty(name = "types", value = "任务类型")
    private List<String> types;

    /** 操作人 */
    @ApiModelProperty(name = "operatorId", value = "操作人")
    private Integer operatorId;

    /** 状态 */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /** 是否审核 */
    @ApiModelProperty(name = "reviewed", value = "审核状态")
    private Boolean reviewed;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 拓展字段 */
    @ApiModelProperty(name = "meta", value = "拓展字段")
    private String meta;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

    /** 是否当天的 */
    @ApiModelProperty(name = "onlyToday", value = "是否当天的")
    private boolean onlyToday;

    /** 操作人id集合 */
    @ApiModelProperty(name = "operatorIds", value = "操作人id集合")
    private List<Integer> operatorIds;

    /** 业务id集合 */
    @ApiModelProperty(name = "bizIds", value = "业务id集合")
    private List<Integer> bizIds;
}