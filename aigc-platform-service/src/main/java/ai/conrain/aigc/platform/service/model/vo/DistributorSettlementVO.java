package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.DistributorSettleStatusEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.resolver.DefaultDecimalSerializer;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.Objects;

/**
 * DistributorSettlementVO
 *
 * @version DistributorSettlementService.java v 0.1 2025-05-22 05:39:41
 */
@Data
public class DistributorSettlementVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 结算主体类型 */
	@ApiModelProperty(name = "principalType", value = "主体类型")
	private PrincipalTypeEnum principalType;

	/** 结算主体 id */
	@ApiModelProperty(name = "principalId", value = "结算主体 id")
	private Integer principalId;

	/** 结算id，前8位是YYYYMMDD */
	@ApiModelProperty(name = "settleId", value = "结算id，前8位是YYYYMMDD")
	private String settleId;

	/**
	 * @see DistributorSettleStatusEnum
	 * 状态，0未结算、1已结算
	 */
	@ApiModelProperty(name = "status", value = "状态，0未结算、1已结算")
	private DistributorSettleStatusEnum status;

	/** 结算类型，1系统结算，2手工结算 */
	@ApiModelProperty(name = "settleType", value = "结算类型，1系统结算，2手工结算")
	private Integer settleType;

	/** 总金额 */
	@JsonSerialize(using = DefaultDecimalSerializer.class)
	@ApiModelProperty(name = "totalAmount", value = "总金额")
	private BigDecimal totalAmount;

	/** 结算金额，结算给渠道商的总金额 */
	@JsonSerialize(using = DefaultDecimalSerializer.class)
	@ApiModelProperty(name = "settleAmount", value = "结算金额，结算给渠道商的总金额")
	private BigDecimal settleAmount;

	/** 结算订单笔数 */
	@ApiModelProperty(name = "orderNum", value = "结算订单笔数")
	private Integer orderNum;

	/** 外部业务单号，如银行流水号 */
	@ApiModelProperty(name = "outBizNo", value = "外部业务单号，如银行流水号")
	private String outBizNo;

	/** 渠道商实体id */
	@ApiModelProperty(name = "distributorCorpId", value = "渠道商实体id")
	private Integer distributorCorpId;

	/** 渠道商实体名称 */
	@ApiModelProperty(name = "distributorCorpName", value = "渠道商实体名称")
	private String distributorCorpName;

	/** 结算日期 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "settleTime", value = "结算日期")
	private Date settleTime;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private JSONObject extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	public <T> T getExtInfo(String key, Class<T> clazz) {
		if (ObjectUtils.isEmpty(this.extInfo)) {
			return null;
		}
		return this.extInfo.getObject(key, clazz);
	}

	public void putExtInfo(String key, Object value) {
		if (Objects.isNull(this.extInfo)) {
			this.extInfo = new JSONObject();
		}
		this.extInfo.put(key, value);
	}

	public void addTotalAmount(BigDecimal amount) {
		if (ObjectUtils.isEmpty(this.totalAmount)) {
			this.totalAmount = amount;
		} else {
			this.totalAmount = this.totalAmount.add(amount);
		}
	}

	public void addSettleAmount(BigDecimal amount) {
		if (ObjectUtils.isEmpty(this.settleAmount)) {
			this.settleAmount = amount;
		} else {
			this.settleAmount = this.settleAmount.add(amount);
		}
	}

	public void addOrderNum(int num) {
		if (ObjectUtils.isEmpty(this.orderNum)) {
			this.orderNum = num;
		} else {
			this.orderNum += num;
		}
	}

}