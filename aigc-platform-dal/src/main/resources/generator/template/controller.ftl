package ${controllerUrl};

import java.util.*;

import ${entityUrl}.${entityName}DO;
import ${voPackageUrl}.${entityName}VO;
import ${queryPackageUrl}.${entityName}Query;
import ${serviceUrl}.${entityName}Service;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ${entityName}控制器
 *
 * <AUTHOR>
 * @version ${entityName}Service.java v 0.1 ${createTime}
 */
@Slf4j
@Api("${entityComment}")
@Validated
@RestController
@RequestMapping("/${objectName}")
public class ${entityName}Controller {

	/** ${objectName}Service */
	@Autowired
	private ${entityName}Service ${objectName}Service;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询${entityComment}", notes = "根据id查询${entityComment}[${objectName}]")
	public Result<${entityName}VO> getById(@NotNull @PathVariable("id")${idType} id) {
		return Result.success(${objectName}Service.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建${entityComment}", notes = "新建${entityComment}[${objectName}]")
	public Result<${idType}> create(@Valid @RequestBody ${entityName}VO ${objectName}){
		try {
			${entityName}VO data = ${objectName}Service.insert(${objectName});
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加${entityComment}失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建${entityComment}失败");
	}

<#if enableDeleteByPrimaryKey>
	@PostMapping("/deleteById")
	@ApiOperation(value = "删除${entityComment}", notes = "删除${entityComment}")
	public Result<?> deleteById(@NotNull @JsonArg ${idType} id) {
		${objectName}Service.deleteById(id);
		return Result.success();
	}
</#if>

<#if targetRuntime == "MyBatis3Simple">
	<#if enableUpdateByPrimaryKey>
	@PostMapping("/updateById")
	@ApiOperation(value = "更新${entityComment}", notes = "修改${entityComment}[${objectName}]，id必填，所有字段按请求直接更新")
	public Result<?> updateById(@Valid @RequestBody ${entityName}VO ${objectName}){
		${objectName}Service.updateById(${objectName});
		return Result.success();
	}
	</#if>

	@PostMapping("/findAll")
	@ApiOperation(value = "全量查询${entityComment}", notes = "全量查询${entityComment}")
	public Result<List<${entityName}VO>> findAll(){
		return Result.success(${objectName}Service.findAll());
	}

<#else>
<#if enableUpdateByPrimaryKey>
	@PostMapping("/updateById")
	@ApiOperation(value = "更新${entityComment}", notes = "修改${entityComment}[${objectName}]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody ${entityName}VO ${objectName}){
		${objectName}Service.updateByIdSelective(${objectName});
		return Result.success();
	}
</#if>

	@ApiOperation(value = "批量查询${entityComment}", notes = "批量查询[${objectName}]")
	@PostMapping("/queryList")
	public Result<List<${entityName}VO>> query${entityName}List(@Valid @RequestBody ${entityName}Query query){
		return Result.success(${objectName}Service.query${entityName}List(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<${entityName}DO>]")
	public Result<PageInfo<${entityName}VO>> get${entityName}ByPage(@Valid @RequestBody ${entityName}Query query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(${objectName}Service.query${entityName}ByPage(query));
	}
</#if>
}