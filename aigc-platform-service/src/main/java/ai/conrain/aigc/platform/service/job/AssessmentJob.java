package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.AssessmentPlanService;
import ai.conrain.aigc.platform.service.enums.AssessStatusEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DingTalkBuildUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.shade.org.slf4j.MDC;
import com.alibaba.schedulerx.shade.scala.Int;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class AssessmentJob extends JavaProcessor {

    @Autowired
    private AssessmentPlanService assessmentPlanService;
    @Autowired
    private TairService tairService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        log.info("【考核定时任务】开始执行...");
        long startTime = System.currentTimeMillis();
        try {
            assessmentPlanService.processAssessment();
            sendDingTalkNotice();
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }
        log.info("【考核定时任务】执行完毕，耗时 {} ms", System.currentTimeMillis() - startTime);
        return new ProcessResult(true);
    }

    private void sendDingTalkNotice() {
        Integer initCount = tairService.getObject(AssessStatusEnum.ASSESS_INIT.getCode(), Integer.class);
        Integer passedCount = tairService.getObject(AssessStatusEnum.ASSESS_PASSED.getCode(), Integer.class);
        Integer failedCount = tairService.getObject(AssessStatusEnum.ASSESS_FAILED.getCode(), Integer.class);
        DingTalkNoticeHelper.sendMsg2DevGroup(DingTalkBuildUtils.buildAssessmentPlanTextMessage(initCount, passedCount, failedCount));

        // 使用完后手动清理缓存
        tairService.clear(AssessStatusEnum.ASSESS_INIT.getCode());
        tairService.clear(AssessStatusEnum.ASSESS_PASSED.getCode());
        tairService.clear(AssessStatusEnum.ASSESS_FAILED.getCode());
    }
}
