/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.interceptor;

import ai.conrain.aigc.platform.dal.entity.PermissionDO;
import ai.conrain.aigc.platform.service.component.PermissionService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.web.helper.IpUtils;
import ai.conrain.aigc.platform.web.helper.MessageHelper;
import ai.conrain.aigc.platform.web.helper.WebDigestUtils;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 权限校验拦截器
 *
 * <AUTHOR>
 * @version : PermissionCheckInterceptor.java, v 0.1 2023/9/24 10:14 renxiao.wu Exp $
 */
@Slf4j
@Component
public class PermissionCheckInterceptor implements HandlerInterceptor {
    private static final Pattern PATTERN = Pattern.compile("^[/(a-zA-Z0-9+)]+\\{([a-zA-Z0-9]+)}$");

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private MessageHelper messageHelper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        //请求路径，如/users/myInfo、/users/getUsersById/100005
        String path = request.getRequestURI();

        //校验当前请求是否有权限，有权限则返回true
        if (permissionService.hasPermission(path)) {
            return true;
        }

        //无权限时返回ILLEGAL_PERMISSION
        ResultCode resultCode = ResultCode.ILLEGAL_PERMISSION;
        //未登录时反馈登录过期LOGIN_EXPIRED
        if (!OperationContextHolder.getContext().isLoggedIn()) {
            resultCode = ResultCode.LOGIN_EXPIRED;
        }

        String msg = messageHelper.fetchErrorMsg(resultCode, null);

        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.setStatus(HttpServletResponse.SC_OK);
        response.getWriter().write(JSONObject.toJSONString(Result.failedWithMessage(resultCode, msg)));

        String action = permissionService.parseRequestPath(request.getServletPath());
        WebDigestUtils.digest(action, IpUtils.getIpAddr(request), false, resultCode);

        return false;
    }

    /**
     * 初始化所有权限，如果权限表中缺少路径，则插入到表中
     */
    @PostConstruct
    public void init() {
        long start = System.currentTimeMillis();
        List<PermissionDO> paths = scanRequestMappingPaths();

        log.info("启动加载角色权限数据,size={},耗时{}ms", paths.size(), System.currentTimeMillis() - start);

        //将新增的权限增加到表中
        permissionService.initPermissions(paths);
        log.info("初始化角色权限数据,size={},耗时{}ms", paths.size(), System.currentTimeMillis() - start);
    }

    /**
     * 扫描所有请求配置
     *
     * @return 所有请求配置
     */
    private List<PermissionDO> scanRequestMappingPaths() {
        List<PermissionDO> requestMappingPaths = new ArrayList<>();

        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(Controller.class);
        beans.putAll(applicationContext.getBeansWithAnnotation(RestController.class));

        for (Object controller : beans.values()) {
            Class<?> controllerClass = controller.getClass();
            if (StringUtils.equals(controllerClass.getSimpleName(), "MainController")) {
                continue;
            }

            RequestMapping classRequestMapping = AnnotationUtils.findAnnotation(controllerClass, RequestMapping.class);
            String classPath = (classRequestMapping != null) ? classRequestMapping.value()[0] : "";

            for (Method method : controllerClass.getDeclaredMethods()) {
                RequestMapping methodRequestMapping = AnnotationUtils.findAnnotation(method, RequestMapping.class);
                if (methodRequestMapping != null && ArrayUtils.isNotEmpty(methodRequestMapping.method())) {
                    requestMappingPaths.add(build(method, methodRequestMapping, classPath));
                }
            }
        }

        return requestMappingPaths;
    }

    /**
     * 构建权限配置
     *
     * @param method               当前防范
     * @param methodRequestMapping 配置
     * @param classPath            类路径
     * @return 权限配置
     */
    private PermissionDO build(Method method, RequestMapping methodRequestMapping, String classPath) {
        String methodType = methodRequestMapping.method()[0].name();

        String path = null;
        if (StringUtils.equals("GET", methodType)) {
            GetMapping getMapping = AnnotationUtils.findAnnotation(method, GetMapping.class);
            path = getMapping != null && ArrayUtils.isNotEmpty(getMapping.value()) ? getMapping.value()[0] : "";
        } else if (StringUtils.equals("POST", methodType)) {
            PostMapping postMapping = AnnotationUtils.findAnnotation(method, PostMapping.class);
            path = postMapping != null && ArrayUtils.isNotEmpty(postMapping.value()) ? postMapping.value()[0] : "";
        }
        String fullPath = classPath + path;
        if (PATTERN.matcher(fullPath).matches()) {
            fullPath = StringUtils.substringBeforeLast(fullPath, "/") + "/*";
        }

        ApiOperation apiOperation = AnnotationUtils.findAnnotation(method, ApiOperation.class);
        String memo = apiOperation != null ? apiOperation.value() : "null";

        Roles roles = AnnotationUtils.findAnnotation(method, Roles.class);
        String configRoles = Optional.ofNullable(roles)
            .map(Roles::value)
            .map(Arrays::stream)
            .orElseGet(() -> Stream.of(RoleTypeEnum.ADMIN))
            .distinct()
            .map(RoleTypeEnum::getCode)
            .collect(Collectors.joining(","));

        PermissionDO permission = new PermissionDO();
        permission.setAction(fullPath);
        permission.setName(memo);
        permission.setConfig(configRoles);
        permission.setVersion(roles == null ? 0 : roles.version());
        return permission;
    }

}
