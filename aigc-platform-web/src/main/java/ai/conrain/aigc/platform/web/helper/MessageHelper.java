/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.helper;

import java.util.Locale;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Component;

/**
 * 消息帮助类
 *
 * <AUTHOR>
 * @version : MessageUtils.java, v 0.1 2023/10/18 10:58 renxiao.wu Exp $
 */
@Component
public class MessageHelper {
    private final MessageSource messageSource;

    @Autowired
    public MessageHelper(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    /**
     * 获取错误信息
     *
     * @param code    错误码
     * @param message 默认消息
     * @return 错误信息
     */
    public String fetchErrorMsg(ResultCode code, String message) {
        String defaultMsg = StringUtils.isBlank(message) ? code.getDesc() : message;
        return messageSource.getMessage("error." + code.getCode(), null, defaultMsg, Locale.getDefault());
    }
}

@Configuration
class MessageConfig {
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }
}
