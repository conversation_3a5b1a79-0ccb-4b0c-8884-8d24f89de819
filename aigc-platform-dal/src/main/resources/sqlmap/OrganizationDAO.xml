<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.OrganizationDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.OrganizationDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="root" jdbcType="BIT" property="root" />
    <result column="root_id" jdbcType="INTEGER" property="rootId" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="org_level" jdbcType="INTEGER" property="orgLevel" />
    <result column="channel_admin_id" jdbcType="INTEGER" property="channelAdminId" />
    <result column="channel_admin_nick_name" jdbcType="VARCHAR" property="channelAdminNickName" />
    <result column="creator_master_user_id" jdbcType="INTEGER" property="creatorMasterUserId" />
    <result column="creator_user_role_type" jdbcType="VARCHAR" property="creatorUserRoleType" />
    <result column="creator_operator_user_id" jdbcType="INTEGER" property="creatorOperatorUserId" />
    <result column="modifier_operator_user_id" jdbcType="INTEGER" property="modifierOperatorUserId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, parent_id, root, root_id, org_type, name, tags, org_level, creator_master_user_id,
    creator_user_role_type, creator_operator_user_id, modifier_operator_user_id, ext_info, 
    create_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.OrganizationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from organization
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from organization
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.OrganizationDO" useGeneratedKeys="true" keyProperty="id">
    insert into organization (parent_id, root, root_id,
      org_type, name, tags,
      org_level, creator_master_user_id, creator_user_role_type, 
      creator_operator_user_id, modifier_operator_user_id, 
      ext_info, create_time, modify_time
      )
    values (#{parentId,jdbcType=INTEGER}, #{root,jdbcType=BIT},
        <choose>
          <when test="root">
            #{id,jdbcType=INTEGER},
          </when>
          <otherwise>
            #{rootId,jdbcType=INTEGER},
          </otherwise>
        </choose>
      #{orgType,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR},
      #{orgLevel,jdbcType=INTEGER}, #{creatorMasterUserId,jdbcType=INTEGER}, #{creatorUserRoleType,jdbcType=VARCHAR}, 
      #{creatorOperatorUserId,jdbcType=INTEGER}, #{modifierOperatorUserId,jdbcType=INTEGER}, 
      #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.OrganizationDO" useGeneratedKeys="true" keyProperty="id">
    insert into organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="root != null">
        root,
      </if>
      <if test="rootId != null">
        root_id,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="orgLevel != null">
        org_level,
      </if>
      <if test="creatorMasterUserId != null">
        creator_master_user_id,
      </if>
      <if test="creatorUserRoleType != null">
        creator_user_role_type,
      </if>
      <if test="creatorOperatorUserId != null">
        creator_operator_user_id,
      </if>
      <if test="modifierOperatorUserId != null">
        modifier_operator_user_id,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="root != null">
        #{root,jdbcType=BIT},
      </if>
      <if test="rootId != null">
        #{rootId,jdbcType=INTEGER},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="orgLevel != null">
        #{orgLevel,jdbcType=INTEGER},
      </if>
      <if test="creatorMasterUserId != null">
        #{creatorMasterUserId,jdbcType=INTEGER},
      </if>
      <if test="creatorUserRoleType != null">
        #{creatorUserRoleType,jdbcType=VARCHAR},
      </if>
      <if test="creatorOperatorUserId != null">
        #{creatorOperatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifierOperatorUserId != null">
        #{modifierOperatorUserId,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.OrganizationExample" resultType="java.lang.Long">
    select count(*) from organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update organization
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.root != null">
        root = #{record.root,jdbcType=BIT},
      </if>
      <if test="record.rootId != null">
        root_id = #{record.rootId,jdbcType=INTEGER},
      </if>
      <if test="record.orgType != null">
        org_type = #{record.orgType,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.orgLevel != null">
        org_level = #{record.orgLevel,jdbcType=INTEGER},
      </if>
      <if test="record.creatorMasterUserId != null">
        creator_master_user_id = #{record.creatorMasterUserId,jdbcType=INTEGER},
      </if>
      <if test="record.creatorUserRoleType != null">
        creator_user_role_type = #{record.creatorUserRoleType,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorOperatorUserId != null">
        creator_operator_user_id = #{record.creatorOperatorUserId,jdbcType=INTEGER},
      </if>
      <if test="record.modifierOperatorUserId != null">
        modifier_operator_user_id = #{record.modifierOperatorUserId,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update organization
    set id = #{record.id,jdbcType=INTEGER},
      parent_id = #{record.parentId,jdbcType=INTEGER},
      root = #{record.root,jdbcType=BIT},
      root_id = #{record.rootId,jdbcType=INTEGER},
      org_type = #{record.orgType,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      tags = #{record.tags,jdbcType=VARCHAR},
      org_level = #{record.orgLevel,jdbcType=INTEGER},
      creator_master_user_id = #{record.creatorMasterUserId,jdbcType=INTEGER},
      creator_user_role_type = #{record.creatorUserRoleType,jdbcType=VARCHAR},
      creator_operator_user_id = #{record.creatorOperatorUserId,jdbcType=INTEGER},
      modifier_operator_user_id = #{record.modifierOperatorUserId,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.OrganizationDO">
    update organization
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="root != null">
        root = #{root,jdbcType=BIT},
      </if>
      <if test="rootId != null">
        root_id = #{rootId,jdbcType=INTEGER},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="orgLevel != null">
        org_level = #{orgLevel,jdbcType=INTEGER},
      </if>
      <if test="creatorMasterUserId != null">
        creator_master_user_id = #{creatorMasterUserId,jdbcType=INTEGER},
      </if>
      <if test="creatorUserRoleType != null">
        creator_user_role_type = #{creatorUserRoleType,jdbcType=VARCHAR},
      </if>
      <if test="creatorOperatorUserId != null">
        creator_operator_user_id = #{creatorOperatorUserId,jdbcType=INTEGER},
      </if>
      <if test="modifierOperatorUserId != null">
        modifier_operator_user_id = #{modifierOperatorUserId,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.OrganizationDO">
    update organization
    set parent_id = #{parentId,jdbcType=INTEGER},
      root = #{root,jdbcType=BIT},
      root_id = #{rootId,jdbcType=INTEGER},
      org_type = #{orgType,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      tags = #{tags,jdbcType=VARCHAR},
      org_level = #{orgLevel,jdbcType=INTEGER},
      creator_master_user_id = #{creatorMasterUserId,jdbcType=INTEGER},
      creator_user_role_type = #{creatorUserRoleType,jdbcType=VARCHAR},
      creator_operator_user_id = #{creatorOperatorUserId,jdbcType=INTEGER},
      modifier_operator_user_id = #{modifierOperatorUserId,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectOrganizationByUserId" resultMap="BaseResultMap">
    SELECT o.id, o.parent_id, o.root, o.root_id, o.org_type, o.name, o.tags,
            o.org_level, o.creator_master_user_id,
            o.creator_user_role_type, o.creator_operator_user_id, o.modifier_operator_user_id,
            o.ext_info, o.create_time, o.modify_time
    FROM organization o
    JOIN user_organization uo ON o.id = uo.org_id
    WHERE uo.user_id = #{userId}
    limit 1
  </select>

  <select id="selectChildrenByParentId" resultType="ai.conrain.aigc.platform.dal.entity.OrganizationDO">
    SELECT
      <include refid="Base_Column_List" />
    FROM organization
    WHERE parent_id = #{parentId}
  </select>

  <select id="selectByRootId" resultType="ai.conrain.aigc.platform.dal.entity.OrganizationDO">
    SELECT <include refid="Base_Column_List" />
    FROM organization
    WHERE root_id = #{rootId}
  </select>

  <select id="selectDistributorCorps" resultMap="BaseResultMap">
    SELECT o.*,
           u.id as channel_admin_id,
           u.nick_name as channel_admin_nick_name
    FROM organization o
    LEFT JOIN user u ON o.id = u.corp_org_id
    WHERE o.root = 1 AND o.org_type = 'CORP' AND o.tags = 'DISTRIBUTOR'
    AND u.status = 'ENABLED' AND u.deleted IS NOT TRUE
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

</mapper>