package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.UserDAO;
import ai.conrain.aigc.platform.dal.entity.UserDO;
import ai.conrain.aigc.platform.dal.example.UserExample;
import ai.conrain.aigc.platform.dal.example.UserExample.Criteria;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.wechat.WechatService;
import ai.conrain.aigc.platform.service.acm.AcmConfigService;
import ai.conrain.aigc.platform.service.acm.AcmResource;
import ai.conrain.aigc.platform.service.acm.cfg.UserWhitelistCfg;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.SecurityService;
import ai.conrain.aigc.platform.service.component.SmsService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserOrganizationService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ConfigStatusEnum;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.DingTalkNoticeEnum;
import ai.conrain.aigc.platform.service.enums.OrganizationTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserReviewActionEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserConverter;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.OrganizationQuery;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.request.RegisterRequest;
import ai.conrain.aigc.platform.service.model.request.SmsLoginRequest;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.request.UserUpdate;
import ai.conrain.aigc.platform.service.model.vo.ActiveUserVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.RelatedAccounts;
import ai.conrain.aigc.platform.service.model.vo.SalesInfoVO;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.model.vo.UserReviewInfo;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DingTalkBuildUtils;
import ai.conrain.aigc.platform.service.util.MapConvertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserService实现
 *
 * <AUTHOR>
 * @version UserService.java v 0.1 2024-01-20 01:21:36
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserDAO userDAO;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private WechatService wechatService;
    @Lazy
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private AcmConfigService acmConfigService;
    @Autowired
    private UserPointService userPointService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private UserOrganizationService userOrganizationService;

    @Autowired
    private DistributorService distributorService;

    @Autowired
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private TairService tairService;

    @Override
    public UserVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        UserDO data = userDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        UserVO userVO = UserConverter.do2VO(data);
        fillMasterInfo4SubUser(userVO);

        return userVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        UserVO user = selectById(id);
        if (user != null && user.getRoleType() == RoleTypeEnum.DISTRIBUTOR) {
            distributorCustomerService.onDeleteDistributorStaff(id);
            organizationService.onDeleteUser(user);
        }

        if (user != null && user.getRoleType() == RoleTypeEnum.MERCHANT) {
            distributorService.deleteCustomer(user.getId());
        }

        int n = userDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除User失败");
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public UserVO create(UserRegister req) {
        AssertUtil.assertNotNull(req, ResultCode.PARAM_INVALID, "users is null");

        String mobile = req.getMobile();

        // 1.根据手机号码查询用户是否已存在
        checkMobileExists(mobile);

        RoleTypeEnum roleType = RoleTypeEnum.getByCode(req.getRoleType());
        if (roleType == RoleTypeEnum.DISTRIBUTOR) {
            AssertUtil.assertNotBlank(req.getCorpName(), "渠道商用户的corpName不可为空");
        }

        // 创建企业实体
        OrganizationVO org = null;
        if (StringUtils.isNotBlank(req.getCorpName())) {

            OrganizationQuery organizationQuery = new OrganizationQuery();
            organizationQuery.setName(req.getCorpName());

            List<OrganizationVO> existed = organizationService.queryOrganizationList(organizationQuery);
            AssertUtil.assertTrue(CollectionUtils.isEmpty(existed), ResultCode.CORP_NAME_ALREADY_EXISTS,
                    "该企业已注册，请联系贵司管理员在团队管理中进行账号添加！");

            // 创建新的组织
            org = createOrganization(req.getCorpName(), req.getRoleType());
            org = organizationService.insert(org);

            // 渠道商费率
            if (roleType == RoleTypeEnum.DISTRIBUTOR && req.getSettleRate() != null) {
                SystemConfigVO cfg = new SystemConfigVO();
                cfg.setConfKey(SystemConstants.DISTRIBUTOR_SETTLE_RATE_PREFIX + org.getId());
                cfg.setConfValue(req.getSettleRate().toPlainString());
                cfg.setStatus(ConfigStatusEnum.ACTIVE);
                cfg.setEffectTime(new Date());
                cfg.setMemo("渠道商结算费率");
                cfg.setOperatorId(OperationContextHolder.getOperatorUserId());

                systemConfigService.insert(cfg);
            }
        }

        // 2.初始化db数据
        UserDO target = new UserDO();

        if (org != null) {
            target.setCorpOrgId(org.getId());
            target.setCorpName(req.getCorpName());
        }

        target.setMemo(req.getMemo());
        target.setNickName(req.getNickName());
        target.setLoginId(mobile);
        target.setMobile(mobile);
        target.setRoleType(req.getRoleType());
        Integer operatorId = OperationContextHolder.getOperatorUserId() == null ? 0
                : OperationContextHolder.getOperatorUserId();
        target.setOperatorId(operatorId);
        target.setStatus(req.getUserStatus());

        target.setUserType(UserTypeEnum.MASTER.getCode());

        if (req.getUserReviewInfo() != null) {
            target.setUserReviewInfo(CommonUtil.toJSONString(req.getUserReviewInfo()));
        }

        target.setRegisterFrom(req.getRegisterFrom());

        target.setDeleted(false);
        target.setLoginFailCount(0);

        // 设置初始密码
        if (StringUtils.isNotBlank(req.getPassword())) {
            target.setPswd(securityService.encodePassword(req.getPassword()));
        } else {

            // 2.2.如果是后台操作人员设置密码
            if (RoleTypeEnum.OPERATOR == roleType) {
                // 初始化密码为随机密码，靠找回密码修改后可登录
                target.setPswd(securityService.encodePassword(CommonUtil.uuid()));
            }

            // 2.3.如果是演示账号，设置密码为bb123456
            if (RoleTypeEnum.DEMO_ACCOUNT == roleType) {
                target.setPswd(securityService.encodePassword("bb123456"));
            }
        }

        // 赋予用户角色为渠道管理员
        if (roleType == RoleTypeEnum.DISTRIBUTOR) {
            target.setCustomRole(CustomRoleEnum.CHANNEL_ADMIN.getCode());
        }

        // 3.插入数据库
        int n = userDAO.insertSelective(target);
        AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "新建Users失败");
        AssertUtil.assertNotNull(target.getId(), "新建Users返回id为空");

        // 创建组织-用户关联关系
        if (org != null) {
            // 关联用户组织
            UserOrganizationVO userOrganizationVO = createUserOrganization(target.getId(), org);
            userOrganizationService.insert(userOrganizationVO);
        }

        // 赠送初始化算力点，120muse点
        if (StringUtils.equals(req.getNeedInitMusePoints(), CommonConstants.YES)) {
            userPointService.topupByImage(target.getId(), 120, 0, 0);

            // 标记用户：赠送了初始化算力点，后面不让买体验套餐99
            UserProfileVO profile = new UserProfileVO();
            profile.setUid(target.getId());
            profile.setProfileKey(CommonConstants.PROFILE_SEND_MUSE_POINTS);
            profile.setProfileVal(CommonConstants.YES);
            profile = userProfileService.insert(profile);
        }

        // 演示账号，赠送初始化算力点，10000muse点
        if (RoleTypeEnum.DEMO_ACCOUNT == roleType) {
            userPointService.topupByImage(target.getId(), 10000, 0, 0);
        }

        userPointService.give(target.getId(), 50, 0);

        // 创建渠道商关联账号，初始化渠道商需要的关联账号
        if (roleType == RoleTypeEnum.DISTRIBUTOR && req.isInitDistributorRelatedAccounts()) {
            createRelatedAccounts4Distributor(req.getNickName(), target.getId(), target.getId());
        }

        return UserConverter.do2VO(target);
    }

    @Override
    public void createRelatedAccounts4Distributor(String nick, Integer distributorMasterUserId,
            Integer distributorSalesUserId) {
        AssertUtil.assertNotBlank(nick, "渠道商用户的nickName不可为空");
        AssertUtil.assertNotNull(distributorMasterUserId, "渠道商主账号用户的id不可为空");
        AssertUtil.assertNotNull(distributorSalesUserId, "渠道销售的id不可为空");

        log.info("创建渠道商账号，初始化渠道商需要的关联账号");

        {
            UserRegister salesDemoAccountReg = new UserRegister();
            salesDemoAccountReg.setMobile(genTestMobile());
            salesDemoAccountReg.setNickName(nick + "-演示");
            salesDemoAccountReg.setRoleType(RoleTypeEnum.DEMO_ACCOUNT.getCode());
            salesDemoAccountReg.setPassword("bb123456");
            UserVO relatedDemoAccount = create(salesDemoAccountReg);

            UserProfileVO p1 = new UserProfileVO();
            p1.setUid(distributorSalesUserId);
            p1.setProfileKey(CommonConstants.PROFILE_RELATED_DEMO_ACCOUNT);
            p1.setProfileVal(relatedDemoAccount.getId().toString());
            p1 = userProfileService.insert(p1);
        }

        {
            UserRegister merchantAccountReg = new UserRegister();
            merchantAccountReg.setMobile(genTestMobile());
            merchantAccountReg.setNickName(nick + "-商家");
            merchantAccountReg.setRoleType(RoleTypeEnum.MERCHANT.getCode());
            merchantAccountReg.setMemo("虚拟商家");
            merchantAccountReg.setPassword("bb123456");
            UserVO relatedMerchant = create(merchantAccountReg);
            userPointService.topupByImage(relatedMerchant.getId(), 10000, 0, 0);

            // 关联商家到渠道商账号
            assignCustomer2Distributor(null, relatedMerchant.getId(), distributorMasterUserId, distributorSalesUserId,
                    false);

            UserProfileVO p2 = new UserProfileVO();
            p2.setUid(distributorSalesUserId);
            p2.setProfileKey(CommonConstants.PROFILE_RELATED_MERCHANT_ACCOUNT);
            p2.setProfileVal(relatedMerchant.getId().toString());
            p2 = userProfileService.insert(p2);
        }
    }

    @Override
    public RelatedAccounts queryRelatedAccounts(Integer userId) {
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");

        RelatedAccounts ret = new RelatedAccounts();

        UserProfileVO demoProfile = userProfileService.selectByUidAndProfileKey(userId,
                CommonConstants.PROFILE_RELATED_DEMO_ACCOUNT);
        if (demoProfile != null && StringUtils.isNotBlank(demoProfile.getProfileVal())
                && StringUtils.isNumeric(demoProfile.getProfileVal())) {
            Integer demoAccountId = Integer.valueOf(demoProfile.getProfileVal());
            UserVO relatedDemoAccount = selectById(demoAccountId);

            ret.setRelatedDemoAccount(relatedDemoAccount);
        }

        UserProfileVO merchantProfile = userProfileService.selectByUidAndProfileKey(userId,
                CommonConstants.PROFILE_RELATED_MERCHANT_ACCOUNT);
        if (merchantProfile != null && StringUtils.isNotBlank(merchantProfile.getProfileVal())
                && StringUtils.isNumeric(merchantProfile.getProfileVal())) {
            Integer accountId = Integer.valueOf(merchantProfile.getProfileVal());
            UserVO relatedAccount = selectById(accountId);

            ret.setRelatedMerchantAccount(relatedAccount);
        }

        return ret;
    }

    @Override
    public String genTestMobile() {
        for (int i = 0; i < 10; ++i) {
            String mobile = tairService.getNextTestMobile();
            try {
                checkMobileExists(mobile);
                return mobile;
            } catch (Exception e) {
                // ignore
            }
        }
        throw new RuntimeException("创建随机测试号码失败");
    }

    private static @NotNull UserOrganizationVO createUserOrganization(Integer userId, OrganizationVO org) {
        UserOrganizationVO userOrganizationVO = new UserOrganizationVO();
        userOrganizationVO.setUserId(userId);
        userOrganizationVO.setOrgId(org.getId());
        userOrganizationVO.setCreatorOperatorUserId(OperationContextHolder.getOperatorUserId());
        userOrganizationVO.setModifierOperatorUserId(OperationContextHolder.getOperatorUserId());
        return userOrganizationVO;
    }

    private static @NotNull OrganizationVO createOrganization(String corpName, String roleType) {

        OrganizationVO org = new OrganizationVO();
        org.setParentId(0);
        org.setRoot(true);
        org.setOrgType(OrganizationTypeEnum.CORP.getCode());
        org.setName(corpName);
        org.setOrgLevel(0);
        org.setCreatorMasterUserId(OperationContextHolder.getMasterUserId());
        org.setCreatorOperatorUserId(OperationContextHolder.getOperatorUserId());
        org.setCreatorUserRoleType(
                OperationContextHolder.getRoleType() == null ? null : OperationContextHolder.getRoleType().getCode());
        org.setModifierOperatorUserId(OperationContextHolder.getOperatorUserId());
        org.setTags(StringUtils.defaultIfBlank(roleType, RoleTypeEnum.MERCHANT.getCode()));
        return org;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBack(UserUpdate updateReq) {
        RoleTypeEnum roleType = RoleTypeEnum.getByCode(updateReq.getRoleType());
        OperationContext context = OperationContextHolder.getContext();
        RoleTypeEnum operatorRole = context.getRoleType();
        Integer operator = context.getCurrentUserId();
        Integer userId = updateReq.getUserId();

        // 2.平台运营无法修改其他运营
        if (operatorRole == RoleTypeEnum.OPERATOR && roleType.isBackRole()) {
            log.error("平台运营无法修改其他运营类型,userId={},operator={},roleType={},operatorRole={}", userId,
                    operator, roleType, operatorRole);
            throw new BizException(ResultCode.ILLEGAL_PERMISSION, "权限非法");
        }

        UserDO usersDO = userDAO.selectByPrimaryKey(userId);
        RoleTypeEnum userRole = RoleTypeEnum.getByCode(usersDO.getRoleType());
        if (operatorRole == RoleTypeEnum.OPERATOR && userRole.isBackRole()) {
            log.error("平台运营无法修改其他运营的信息,userId={},operator={},userRole={},operatorRole={}", userId,
                    operator, userRole, operatorRole);
            throw new BizException(ResultCode.ILLEGAL_PERMISSION, "权限非法");
        }

        // 3.如果修改了手机号，则检查手机号码是否已存在
        if (StringUtils.isNotBlank(updateReq.getMobile()) && !StringUtils.equals(updateReq.getMobile(),
                usersDO.getMobile())) {
            checkMobileExists(updateReq.getMobile());
        }

        // 4.如果修改了企业主体名称，则更新企业主体名称
        OrganizationVO org = null;
        if (StringUtils.isNotBlank(updateReq.getCorpName())) {
            org = organizationService.queryOrganizationByUserId(updateReq.getUserId());
            if (org != null && !StringUtils.equals(org.getName(), updateReq.getCorpName())) {
                OrganizationVO orgVO = new OrganizationVO();
                orgVO.setId(org.getId());
                orgVO.setName(updateReq.getCorpName());
                organizationService.updateByIdSelective(orgVO);

                // 编辑用户时，补充新建的公司组织信息时，新建公司实体信息和关联关系
            } else if (org == null) {
                OrganizationQuery organizationQuery = new OrganizationQuery();
                organizationQuery.setName(updateReq.getCorpName());

                List<OrganizationVO> existed = organizationService.queryOrganizationList(organizationQuery);
                AssertUtil.assertTrue(CollectionUtils.isEmpty(existed), ResultCode.CORP_NAME_ALREADY_EXISTS,
                        "该企业已注册，请联系贵司管理员在团队管理中进行账号添加！");

                // 创建新的组织
                org = createOrganization(updateReq.getCorpName(), updateReq.getRoleType());
                org = organizationService.insert(org);

                // 创建组织-用户关联关系
                UserOrganizationVO userOrganizationVO = createUserOrganization(usersDO.getId(), org);
                userOrganizationVO = userOrganizationService.insert(userOrganizationVO);
            }
        }

        // 4.更新
        UserVO target = new UserVO();
        target.setId(userId);
        target.setMobile(updateReq.getMobile());
        target.setLoginId(updateReq.getMobile());
        target.setNickName(updateReq.getNickName());
        target.setRoleType(roleType);
        target.setMemo(updateReq.getMemo());

        if (StringUtils.isNotBlank(updateReq.getStatus())) {
            AssertUtil.assertNotNull(UserStatusEnum.getByCode(updateReq.getStatus()),
                    "用户状态参数非法:" + updateReq.getStatus());
            target.setStatus(UserStatusEnum.getByCode(updateReq.getStatus()));
        }

        if (org != null && StringUtils.equals(usersDO.getUserType(), UserTypeEnum.MASTER.getCode())) {
            target.setCorpOrgId(org.getId());
        }

        // 赋予用户角色为渠道管理员
        if (roleType == RoleTypeEnum.DISTRIBUTOR && StringUtils.equals(usersDO.getUserType(),
                UserTypeEnum.MASTER.getCode()) && StringUtils.isBlank(usersDO.getCustomRole())) {
            target.setCustomRole(CustomRoleEnum.CHANNEL_ADMIN.getCode());
        }

        // 赋予用户角色为渠道销售（只有主账号渠道商设置关联企业，子账号不设置）
        if (roleType == RoleTypeEnum.DISTRIBUTOR && StringUtils.equals(usersDO.getUserType(),
                UserTypeEnum.SUB.getCode()) && StringUtils.isBlank(usersDO.getCustomRole())) {
            target.setCustomRole(CustomRoleEnum.SALES_MEMBER.getCode());
        }

        int n = userDAO.updateByPrimaryKeySelective(UserConverter.vo2DO(target));
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新User失败，影响行数:" + n);

    }

    @Override
    public void updateFront(UserUpdate user) {
        Integer operator = OperationContextHolder.getContext().getCurrentUserId();
        Integer userId = user.getUserId();

        UserDO target = userDAO.selectByPrimaryKey(userId);
        AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "修改用户信息失败");

        // 1.修改的权限控制，要么是自己账号，要么修改的是下属子账户
        if (!operator.equals(userId)) {
            if (!StringUtils.equals(target.getUserType(), UserTypeEnum.SUB.getCode()) || !operator.equals(
                    target.getMasterId())) {
                log.error("目标修改的用户非当前用户或所属子账号，operator={},userId={},masterId={}", operator, userId,
                        target.getMasterId());
                throw new BizException(ResultCode.ILLEGAL_PERMISSION, "权限非法");
            }
        }

        // 2.如果修改了手机号，则检查手机号码是否已存在
        if (StringUtils.isNotBlank(user.getMobile()) && !StringUtils.equals(user.getMobile(), target.getMobile())) {
            checkMobileExists(user.getMobile());
        }

        // 3.更新
        UserDO data = new UserDO();
        data.setId(userId);
        data.setMobile(user.getMobile());
        data.setLoginId(user.getMobile());
        data.setNickName(user.getNickName());

        int cnt = userDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "修改用户信息失败");
    }

    @Override
    public void updateByIdSelective(UserVO userVO) {
        AssertUtil.assertNotNull(userVO, ResultCode.PARAM_INVALID, "userVO is null");
        AssertUtil.assertTrue(userVO.getId() != null, ResultCode.PARAM_INVALID, "userVO.id is null");

        // 修改时间必须更新
        userVO.setModifyTime(new Date());
        UserDO data = UserConverter.vo2DO(userVO);
        int n = userDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新User失败，影响行数:" + n);
    }

    @Override
    public Long queryUserCount(UserQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserExample example = UserConverter.query2Example(query);
        return userDAO.countByExample(example);
    }

    /**
     * 带条件分页查询用户信息
     */
    @Override
    public PageInfo<UserVO> queryUserByPage(UserQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<UserVO> page = new PageInfo<>();

        UserExample example = UserConverter.query2Example(query);
        long totalCount = userDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<UserDO> list = userDAO.selectByExample(example);
        List<UserVO> result = UserConverter.doList2VOList(list);

        // 填充profiles
        List<Integer> userIds = list.stream().map(UserDO::getId).collect(Collectors.toList());
        UserProfileQuery userProfileQuery = new UserProfileQuery();
        userProfileQuery.setProfileKeys(Arrays.asList(CommonConstants.KEY_PROMPT_USER_ID, CommonConstants.KEY_MEMO));
        userProfileQuery.setUids(userIds);
        List<UserProfileVO> userProfiles = userProfileService.queryUserProfileList(userProfileQuery);
        result.forEach(userVO -> {
            JSONObject valueByKey = new JSONObject();
            userProfiles.stream()
                    .filter(profile -> profile.getUid().equals(userVO.getId()))
                    .forEach(profile -> {
                        valueByKey.put(profile.getProfileKey(), profile);
                    });
            userVO.setProfiles(valueByKey);
        });

        // 填充用户积分
        // 填充子账号的master信息
        result.forEach(this::fillMasterInfo4SubUser);

        if (CollectionUtils.isNotEmpty(result) && query.isNeedPoint()) {

            // 用户 id 列表
            List<Integer> userIdList = result.stream().map(UserVO::getId).collect(Collectors.toList());

            // 查询用户积分列表
            UserPointQuery pointQuery = new UserPointQuery();
            pointQuery.setUserIdList(userIdList);
            List<UserPointVO> points = userPointService.queryUserPointList(pointQuery);

            // 查询企业列表
            List<OrganizationVO> organizations = queryByUserId(userIdList);

            if (CollectionUtils.isNotEmpty(points)) {

                // 填充用户积分
                result.forEach(userVO -> {

                    // 填充用户积分
                    points.stream().filter(point -> point.getUserId().equals(userVO.getId())).findFirst().ifPresent(
                            userPointVO -> fillPoint(userVO, userPointVO));

                    // 填充用户关联企业信息
                    organizations.stream()
                            .filter(org -> userVO.getCorpOrgId() != null && org.getId().equals(userVO.getCorpOrgId()))
                            .findFirst()
                            .ifPresent(userVO::setOrganizationVO);
                });
            }
        }

        page.setList(result);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<UserVO> queryUsers(UserQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserExample example = UserConverter.query2Example(query);

        List<UserDO> list = userDAO.selectByExample(example);
        return UserConverter.doList2VOList(list);
    }

    @Override
    public List<UserVO> queryAllVIPOrPaidCustomer() {
        List<UserDO> list = userDAO.queryAllVipOrPaidMasterUsers();
        return UserConverter.doList2VOList(list);
    }

    @Override
    public void resetPassword(String mobile, String newPswd) {
        String encoded = securityService.encodePassword(newPswd);

        UserDO record = new UserDO();
        record.setPswd(encoded);
        UserQuery query = new UserQuery();
        query.setMobile(mobile);
        UserExample example = UserConverter.query2Example(query);
        userDAO.updateByExampleSelective(record, example);

        if (log.isInfoEnabled()) {
            log.info("重置用户密码成功，mobile={}", mobile);
        }
    }

    @Override
    public void resetPassword(Integer userId, String newPswd) {
        String encoded = securityService.encodePassword(newPswd);

        UserDO record = new UserDO();
        record.setPswd(encoded);
        record.setId(userId);
        userDAO.updateByPrimaryKeySelective(record);

        if (log.isInfoEnabled()) {
            log.info("重置用户密码成功，userId={}", userId);
        }
    }

    @Override
    public Result<UserVO> loginByPswd(String loginId, String pswd) {
        UserQuery query = new UserQuery();
        query.setLoginId(loginId);

        // 1.查询数据
        UserExample example = UserConverter.query2Example(query);
        List<UserDO> list = userDAO.selectByExample(example);

        if (CollectionUtils.isEmpty(list)) {
            log.warn("后台用户登录失败，用户名错误，loginId={}", SecurityUtils.maskPhoneNumber(loginId));
            return Result.failedWithMessage(ResultCode.USER_NOT_EXISTS, "登录名错误");
        }

        UserDO user = list.get(0);

        if (UserStatusEnum.getByCode(user.getStatus()) != UserStatusEnum.ENABLED) {
            log.warn("后台用户登录失败，用户状态异常，loginId={}", SecurityUtils.maskPhoneNumber(loginId));
            if (UserStatusEnum.REJECT.getCode().equals(user.getStatus())) {
                return Result.failedWithMessage(ResultCode.USER_IS_REJECT, "用户审核拒绝");
            }
            if (UserStatusEnum.UNDER_REVIEW.getCode().equals(user.getStatus())) {
                return Result.failedWithMessage(ResultCode.USER_IS_REVIEW, "用户审核中");
            }
            return Result.failedWithMessage(ResultCode.USER_IS_DISABLED, "用户已停用");
        }

        // 2.密码验证
        if (!securityService.matchePassword(pswd, user.getPswd())) {
            log.warn("后台用户登录失败，密码错误");
            // 2.3.返回异常
            return Result.failedWithMessage(ResultCode.PASSWORD_ERROR, "密码错误");
        }

        // 刷新用户最后登录时间
        refreshLastLoginTime(user.getId());

        // 3.结果转换
        UserVO userVo = UserConverter.do2VO(user);
        fillMasterInfo4SubUser(userVo);
        fillPoint(userVo);

        return Result.success(userVo);
    }

    @Override
    public Result<UserVO> loginBySms(SmsLoginRequest request) {
        String mobile = request.getMobile();
        boolean valid = smsService.verifyCaptcha(mobile, request.getCode());
        if (!valid) {
            return Result.failedWithMessage(ResultCode.ILLEGAL_CAPTCHA, "无效的手机号或验证码");
        }

        UserQuery query = new UserQuery();
        query.setLoginId(mobile);

        List<UserVO> usersList = queryUsers(query);
        // 用户未注册时直接新建用户
        if (CollectionUtils.isEmpty(usersList)) {
            return createByLogin(mobile);
        }

        UserVO user = usersList.get(0);

        if (user.getStatus() != UserStatusEnum.ENABLED) {
            log.warn("后台用户登录失败，用户状态异常，request={}", request);

            if (user.getStatus() == UserStatusEnum.REJECT) {
                return Result.failedWithMessage(ResultCode.USER_IS_REJECT, "用户审核拒绝");
            }
            if (user.getStatus() == UserStatusEnum.UNDER_REVIEW) {
                return Result.failedWithMessage(ResultCode.USER_IS_REVIEW, "用户审核中");
            }
            return Result.failedWithMessage(ResultCode.USER_IS_DISABLED, "用户已停用");
        }

        fillMasterInfo4SubUser(user);
        refreshLastLoginTime(user.getId());
        fillPoint(user);

        return Result.success(user);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<UserVO> register(RegisterRequest request) {
        String mobile = request.getMobile();
        log.info("开始用户注册流程，mobile={}, nickName={}, corpName={}",
                SecurityUtils.maskPhoneNumber(mobile), request.getNickName(), request.getCorpName());

        boolean valid = smsService.verifyCaptcha(mobile, request.getCode());
        if (!valid) {
            log.warn("验证码验证失败，mobile={}", SecurityUtils.maskPhoneNumber(mobile));
            return Result.failedWithMessage(ResultCode.ILLEGAL_CAPTCHA, "无效的手机号或验证码!");
        }
        log.info("验证码验证成功，mobile={}", SecurityUtils.maskPhoneNumber(mobile));

        // 初始化用户注册信息
        UserRegister userRegister = new UserRegister();

        userRegister.setMobile(request.getMobile());

        // 昵称优先使用前端传过来的昵称，否则使用企业名称
        userRegister.setNickName(
                StringUtils.isBlank(request.getNickName()) ? request.getCorpName() : request.getNickName());

        userRegister.setRoleType(RoleTypeEnum.MERCHANT.getCode());
        userRegister.setCorpName(request.getCorpName());
        userRegister.setNeedInitMusePoints("N");
        userRegister.setUserStatus(UserStatusEnum.ENABLED.getCode());
        userRegister.setRegisterFrom("Natural Registration");
        userRegister.setMemo("【首页】自然注册...");

        // 执行创建用户
        log.info("开始创建用户，mobile={}", SecurityUtils.maskPhoneNumber(mobile));
        UserVO userVO = create(userRegister);
        log.info("用户创建成功，userId={}, mobile={}", userVO.getId(), SecurityUtils.maskPhoneNumber(mobile));

        // 刷新登录时间
        fillMasterInfo4SubUser(userVO);
        refreshLastLoginTime(userVO.getId());
        fillPoint(userVO);

        // 获取注册通知手机号
        List<String> noticePhones = DingTalkNoticeEnum.getRegisterNoticePhones();
        // 发送钉钉通知(构建请求入参)
        String message = DingTalkBuildUtils.buildUserRegisterTextMessage(userVO, noticePhones);
        // 发送钉钉通知
        DingTalkNoticeHelper.sendMsg2UserRegister(message, noticePhones);

        log.info("用户注册流程完成，userId={}, mobile={}", userVO.getId(), SecurityUtils.maskPhoneNumber(mobile));
        return Result.success(userVO);
    }

    /**
     * 判断一个手机号是否在白名单内
     * acm白名单配置为空或enabled=false，不检查直接返回true
     * 手机号或手机号对应主账号的手机号在白名单内：返回true
     * 其余返回false
     *
     * @param mobile
     * @return
     */
    @Override
    public boolean loginMobileCheck(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return false;
        }
        UserWhitelistCfg cfg = acmConfigService.getConfigAsObject(AcmResource.USER_WHILTELIST, UserWhitelistCfg.class);
        log.info("白名单配置：{}", cfg);

        if (cfg == null || !Boolean.TRUE.equals(cfg.getEnabled())) {
            return true;
        }
        if (cfg.getWhitelist() != null && cfg.getWhitelist().contains(mobile)) {
            return true;
        }

        UserExample exam = new UserExample();
        exam.createCriteria().andLogicalDeleted(false).andMobileEqualTo(mobile).andStatusEqualTo(
                UserStatusEnum.ENABLED.getCode());
        List<UserDO> users = userDAO.selectByExample(exam);
        if (CollectionUtils.isEmpty(users)) {
            return false;
        }
        UserDO user = users.get(0);
        if (UserTypeEnum.SUB.getCode().equals(user.getUserType())) {
            UserDO masterUser = userDAO.selectByPrimaryKeyWithLogicalDelete(user.getMasterId(), false);
            return masterUser != null && cfg.getWhitelist().contains(masterUser.getMobile());
        }
        return false;
    }

    @Override
    public Result<UserVO> loginByMobileAuth(String appId, String code) {
        String appSec = systemConfigService.queryValueByKey(appId);
        String mobile = wechatService.getPhoneNumber(appId, appSec, code);
        AssertUtil.assertNotBlank(mobile, ResultCode.BIZ_FAIL, "获取用户授权手机号失败，无效的授权码");

        if (!loginMobileCheck(mobile)) {
            return Result.failedWithMessage(ResultCode.NOT_IN_WHILTE_LIST, "小程序测试中，敬请期待。");
        }

        UserQuery query = new UserQuery();
        query.setLoginId(mobile);
        query.setRoleTypeList(RoleTypeEnum.getFrontRoleList());

        List<UserVO> usersList = queryUsers(query);

        // 用户未注册时直接新建用户
        if (CollectionUtils.isEmpty(usersList)) {
            return createByLogin(mobile);
        }

        UserVO user = usersList.get(0);

        if (user.getStatus() != UserStatusEnum.ENABLED) {
            log.warn("用户登录失败，用户已停用，userId={}", user.getId());
            return Result.failedWithMessage(ResultCode.USER_IS_DISABLED, "用户已停用");
        }

        fillMasterInfo4SubUser(user);
        refreshLastLoginTime(user.getId());
        fillPoint(user);

        return Result.success(user);
    }

    @Override
    public void checkMobileExists(String mobile) {
        UserQuery query = new UserQuery();
        query.setMobile(mobile);

        UserExample usersExample = UserConverter.query2Example(query);
        long count = userDAO.countByExample(usersExample);
        AssertUtil.assertTrue(count <= 0, ResultCode.MOBILE_ALREADY_EXISTS, "手机号已被注册，mobile=" + mobile);
    }

    @Override
    public List<UserVO> batchQueryById(List<Integer> userIdList) {
        AssertUtil.assertNotEmpty(userIdList, ResultCode.PARAM_INVALID, "userIdList不能为空");

        UserExample example = new UserExample();
        Criteria criteria = example.createCriteria();
        criteria.andIdIn(userIdList);
        criteria.andDeletedEqualTo(false);
        return UserConverter.doList2VOList(userDAO.selectByExample(example));
    }

    @Override
    public List<UserVO> queryAllMasterMetaInfo(List<String> roleTypes) {
        List<UserDO> userList = userDAO.queryAllMasterMetaInfo(roleTypes);
        return UserConverter.doList2VOList(userList);
    }

    /**
     * 登录时注册用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    private Result<UserVO> createByLogin(String mobile) {
        List<String> mobiles = acmConfigService.getConfigAsArray(AcmResource.TOPUP_WHILTELIST, String.class);
        if (CollectionUtils.isEmpty(mobiles) || !mobiles.contains(mobile)) {
            log.info("当前用户不在白名单手机号中，不允许注册，mobile={}", mobile);
            throw new BizException(ResultCode.NOT_IN_WHILTE_LIST, "当前用户不在白名单手机号中，不允许注册");
        }

        // 如果没有账户，则通过手机号直接完成注册
        UserRegister userRegister = new UserRegister();
        userRegister.setMobile(mobile);
        userRegister.setRoleType(RoleTypeEnum.MERCHANT.getCode());
        UserVO userVO = create(userRegister);
        log.info("微信授权登录成功，已完成新账户的注册，mobile={}", mobile);

        // 刷新登录时间
        fillMasterInfo4SubUser(userVO);
        refreshLastLoginTime(userVO.getId());
        fillPoint(userVO);

        return Result.success(userVO);
    }

    /**
     * 填充用户积分
     *
     * @param user 用户
     */
    private void fillPoint(UserVO user) {
        UserPointVO point = userPointService.queryImagePoint(user.getId());
        fillPoint(user, point);
    }

    private static void fillPoint(UserVO user, UserPointVO point) {
        if (point == null) {
            return;
        }
        user.setImagePoint(point.getImagePoint());
        user.setGivePoint(point.getGivePoint());
        user.setExperiencePoint(point.getExperiencePoint());
    }

    /**
     * 填充主账号信息
     *
     * @param data 数据
     */
    private void fillMasterInfo4SubUser(UserVO data) {
        if (data.getUserType() == UserTypeEnum.MASTER) {
            data.setMasterId(data.getId());
            data.setMasterNick(data.getNickName());
            data.setMasterLoginId(data.getLoginId());
            data.setMasterStatus(data.getStatus());
            return;
        }

        // 子账号，通过master id查询得到主账号信息
        if (data.getMasterId() != null) {
            UserQuery masterQuery = new UserQuery();
            masterQuery.setId(data.getMasterId());
            List<UserVO> masterList = queryUsers(masterQuery);
            AssertUtil.assertTrue(masterList.size() == 1, ResultCode.BIZ_FAIL, "获取主账号信息失败");
            UserVO master = masterList.get(0);
            data.setMasterNick(master.getNickName());
            data.setMasterLoginId(master.getLoginId());
            data.setMasterStatus(master.getStatus());

            // 目前只有主账号放了corpId/corpName，子账号的user记录并没有放，（为了方便应对公司信息变更）
            // 从主账号中获取它对应的公司实体信息，放到子账号中
            if (data.getCorpOrgId() == null && master.getCorpOrgId() != null) {
                data.setCorpOrgId(master.getCorpOrgId());
                data.setCorpName(master.getCorpName());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void assignCustomer2Distributor(String userCorpName, Integer userId, Integer distributorUserId,
            Integer distributorSalesUserId, boolean notify) {

        // 会员
        UserVO customer = this.selectById(userId);
        AssertUtil.assertNotNull(customer, "无效的会员userId:" + userId);

        // 渠道商
        UserVO distributor = this.selectById(distributorUserId);
        AssertUtil.assertNotNull(distributor, "无效的渠道商userId:" + distributorUserId);

        // 查询会员是否已经有关联渠道商
        DistributorCustomerQuery dcq = new DistributorCustomerQuery();
        dcq.setCustomerMasterUserId(userId);
        dcq.setPageSize(1);
        dcq.setPageNum(1);
        List<DistributorCustomerVO> distributorCustomerVOS = distributorCustomerService.queryDistributorCustomerList(
                dcq);

        // 已经有关联的渠道商
        if (CollectionUtils.isNotEmpty(distributorCustomerVOS)) {
            DistributorCustomerVO dc = distributorCustomerVOS.get(0);
            if (!Objects.equals(dc.getDistributorMasterUserId(), distributorUserId)) {
                log.info("变更客户已经关联渠道商，userId:{},当前渠道商:{},变更后渠道商:{}", userId,
                        dc.getDistributorCorpName(), distributor.getCorpName());

                dc.setDistributorCorpOrgId(distributor.getCorpOrgId());
                dc.setDistributorCorpName(distributor.getCorpName());
                dc.setDistributorMasterUserId(distributorUserId);
                dc.setDistributorSalesUserId(
                        distributorSalesUserId != null ? distributorSalesUserId : distributorUserId);
                dc.setCreatorId(distributorUserId);

                distributorCustomerService.updateByIdSelective(dc);
            }

            // 没有已经关联的渠道
        } else {
            log.info("变更客户首次关联渠道商，userId:{},渠道商:{}", userId, distributor.getCorpName());

            DistributorCustomerVO distributorCustomerVO = new DistributorCustomerVO();
            distributorCustomerVO.setCustomerMasterUserId(userId);
            distributorCustomerVO.setDistributorCorpOrgId(distributor.getCorpOrgId());
            distributorCustomerVO.setDistributorCorpName(distributor.getCorpName());
            distributorCustomerVO.setDistributorMasterUserId(distributorUserId);
            distributorCustomerVO.setDistributorSalesUserId(
                    distributorSalesUserId != null ? distributorSalesUserId : distributorUserId);
            distributorCustomerVO.setCreatorId(distributorUserId);

            distributorCustomerVO = distributorCustomerService.insert(distributorCustomerVO);
        }

        // 更新会员关联的渠道商信息
        UserReviewInfo review = new UserReviewInfo();
        review.setCreatorMasterUserId(distributorUserId);
        review.setCreatorUserId(distributorUserId);
        review.setCreatorUserName(distributor.getNickName());
        review.setCreatorCorpOrgId(distributor.getCorpOrgId());
        review.setCreatorCorpName(distributor.getCorpName());
        review.setCreatorMobile(distributor.getMobile());
        review.setCreateTime(new Date());
        review.setReviewTime(new Date());
        review.setReviewAction(UserReviewActionEnum.PASS.getCode());
        review.setReviewerUserId(100000);
        review.setReviewerUserName("MuseGate");
        review.setReviewMemo("MuseGate");

        customer.setUserReviewInfo(review);

        if (StringUtils.isNotBlank(userCorpName)) {

            // 判断公司名是否已经存在
            if (StringUtil.isNotBlank(customer.getCorpName())) {
                AssertUtil.assertTrue(customer.getCorpName().equals(userCorpName), ResultCode.PARAM_INVALID,
                        "公司名无效");

                // 公司名不存在，首次设置公司名
            } else {
                OrganizationQuery organizationQuery = new OrganizationQuery();
                organizationQuery.setName(userCorpName);

                List<OrganizationVO> existed = organizationService.queryOrganizationList(organizationQuery);
                AssertUtil.assertTrue(CollectionUtils.isEmpty(existed), ResultCode.CORP_NAME_ALREADY_EXISTS,
                        "该企业已注册，请联系贵司管理员在团队管理中进行账号添加！");

                // 创建新的组织
                OrganizationVO org = createOrganization(userCorpName, RoleTypeEnum.MERCHANT.getCode());
                org = organizationService.insert(org);

                // 创建组织-用户关联关系
                UserOrganizationVO userOrganizationVO = createUserOrganization(customer.getId(), org);
                userOrganizationVO = userOrganizationService.insert(userOrganizationVO);

                customer.setCorpName(userCorpName);
                customer.setCorpOrgId(org.getId());
            }
        }

        this.updateByIdSelective(customer);

        // 发送钉钉通知
        if (notify) {
            DingTalkNoticeHelper.sendMsg2UserRegister(
                    String.format("用户已经完成分配：\n用户：%s\n渠道商：%s\n", customer.getNickName(), distributor.getCorpName()));
        }
    }

    @Override
    public boolean isVip(Integer userId) {
        UserVO user = selectById(userId);
        if (user.getRoleType().isBackRole()) {
            return true;
        }
        return userDAO.isVipOrPaidMasterUser(userId);
    }

    @Override
    public boolean isVipWithoutBackUser(Integer userId) {
        return userDAO.isVipOrPaidMasterUser(userId);
    }

    @Override
    public boolean isCustomer(Integer userId) {
        return userDAO.isCustomer(userId);
    }

    @Override
    public boolean isTrialAccount(Integer userId) {
        if (userId == null) {
            return true;
        }
        UserVO user = selectById(userId);
        return user == null || StringUtils.contains(user.getMemo(), "投资体验");
    }

    @Override
    public List<SalesInfoVO> queryBefore60Days() {
        List<Map<String, Object>> list = userDAO.queryBefore60Days();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SalesInfoVO> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            SalesInfoVO vo = new SalesInfoVO();
            vo.setSalesId(MapConvertUtil.getValue(map, "sales_id", Integer.class));
            vo.setChannelName((String) map.get("channel_name"));
            vo.setSleepCnt(MapConvertUtil.getValue(map, "sleep_cnt", Integer.class));
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<ActiveUserVO> queryActiveUser(String startDate, String endDate) {
        List<Map<String, Object>> list = userDAO.queryActiveUser(startDate,endDate);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<ActiveUserVO> result = new ArrayList<>();
        for (Map<String, Object> map : list) {
            ActiveUserVO vo = new ActiveUserVO();
            vo.setUserId(MapConvertUtil.getValue(map, "user_id", Integer.class));
            vo.setIsActive(MapConvertUtil.getValue(map, "is_active", Integer.class));
            vo.setSalesId(MapConvertUtil.getValue(map, "sales_id", Integer.class));
            vo.setChannelName((String) map.get("channel_name"));
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<Integer> queryIdList(UserQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserExample example = UserConverter.query2Example(query);

        return userDAO.selectIdByExample(example);
    }

    /**
     * 刷新最后登录时间
     *
     * @param userId 用户id
     */
    private void refreshLastLoginTime(Integer userId) {
        UserDO data = new UserDO();
        data.setId(userId);
        data.setLastLoginTime(new Date());
        userDAO.updateByPrimaryKeySelective(data);
    }

    /**
     * 查询用户对应角色列表
     *
     * @param userIdList 用户 id 列表
     * @return 企业列表
     */
    private List<OrganizationVO> queryByUserId(List<Integer> userIdList) {
        // 查询关联表获取企业id列表
        List<UserOrganizationVO> userOrganizationList = userOrganizationService.queryUserPointList(userIdList);

        // 提取企业id列表
        List<Integer> orgIdList = userOrganizationList.stream().map(UserOrganizationVO::getOrgId).collect(
                Collectors.toList());

        // 查询企业信息
        OrganizationQuery query = new OrganizationQuery();
        query.setOrgIdList(orgIdList);
        return organizationService.queryOrganizationList(query);
    }

}