package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.OrderStatusEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.helper.PricePlanHelper;
import ai.conrain.aigc.platform.service.model.biz.PricePlanCode;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.request.Topup2UserReq;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.MerchantTopupVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.PricePlan;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * OrderInfo控制器
 *
 * <AUTHOR>
 * @version OrderInfoService.java v 0.1 2024-06-20 11:43:37
 */
@Slf4j
@Api("订单")
@Validated
@RestController
@RequestMapping("/orderInfo")
public class OrderInfoController {

	/** orderInfoService */
	@Autowired
	private OrderInfoService orderInfoService;

	@Autowired
	private PricePlanHelper pricePlanHelper;

	@Autowired
	private UserService userService;

	@Autowired
	private UserProfileService userProfileService;

	@Autowired
	private SystemConfigService systemConfigService;

	@Autowired
	private DistributorCustomerService distributorCustomerService;

	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询订单", notes = "根据id查询订单[orderInfo]")
	public Result<OrderInfoVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(orderInfoService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建订单", notes = "新建订单[orderInfo]")
	public Result<Integer> create(@Valid @RequestBody OrderInfoVO orderInfo){
		try {
			OrderInfoVO data = orderInfoService.insert(orderInfo);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加订单失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建订单失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除订单", notes = "删除订单")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		orderInfoService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新订单", notes = "修改订单[orderInfo]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody OrderInfoVO orderInfo){
		orderInfoService.updateByIdSelective(orderInfo);
		return Result.success();
	}

	@GetMapping("/getPricePlan")
	public Result<List<PricePlan>> getPricePlan(){
		return Result.success(pricePlanHelper.queryPricePlansByUser(OperationContextHolder.getMasterUserId()));
	}

	@ApiOperation(value = "批量查询订单", notes = "批量查询[orderInfo]")
	@PostMapping("/queryList")
	public Result<List<OrderInfoVO>> queryOrderInfoList(@Valid @RequestBody OrderInfoQuery query){
		return Result.success(orderInfoService.queryOrderInfoList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<OrderInfoDO>]")
	public Result<PageInfo<OrderInfoVO>> getOrderInfoByPage(@Valid @RequestBody OrderInfoQuery query){

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(orderInfoService.queryOrderInfoByPage(query));
	}

	/**
	 * 商户充值记录
	 * 主账号可以看所有，子账号只看自己
	 * @param query
	 * @return
	 */
	@PostMapping("/queryMerchantTopupByPage")
	public Result<PageInfo<MerchantTopupVO>> queryMerchantTopupByPage(@Valid @RequestBody OrderInfoQuery query){
		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
				|| query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		// 如果根据指定 masterUserId 查询，则需要管理员权限
		if (Objects.nonNull(query.getMasterUserId())) {
			AssertUtil.assertTrue(OperationContextHolder.isAdmin(), ResultCode.ILLEGAL_PERMISSION, "无权限访问");
		}
		// 管理员可以查看全量数据, 商户只能看到自己的记录
		if (!OperationContextHolder.isAdmin()){
			query.setMasterUserId(OperationContextHolder.getMasterUserId());
		}
		if (OperationContextHolder.isSubUser()) {
			query.setOperatorUserId(OperationContextHolder.getOperatorUserId());
		}

		return Result.success(orderInfoService.queryMerchantTopupByPage(query));
	}

	@GetMapping("/checkIfCanShowNewbiePlan")
	public Result<Boolean> checkIfCanShowNewbiePlan(){
		OrderInfoQuery query = new OrderInfoQuery();
		query.setMasterUserId(OperationContextHolder.getMasterUserId());
		query.setProductCode(PricePlanCode.PLAN_NEWBIE.getCode());
		query.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());

		// 查询用户是否购买过新手包
		List<OrderInfoVO> list = orderInfoService.queryOrderInfoList(query);
		if (CollectionUtils.isNotEmpty(list)) {
			log.info("[checkIfCanShowNewbiePlan]用户{}，已经充值过新手包，不允许再购买新手套餐", OperationContextHolder.getMasterUserId());
			return Result.success(false);
		}

		// 判断用户是否送过muse点
		UserProfileVO userProfileVO = userProfileService.selectByUidAndProfileKey(OperationContextHolder.getMasterUserId(), CommonConstants.PROFILE_SEND_MUSE_POINTS);
		if (userProfileVO != null && StringUtils.equals(userProfileVO.getProfileVal(), CommonConstants.YES)) {
			log.info("[checkIfCanShowNewbiePlan]用户{}，没有充值过新手包，但是已经送过缪斯点了，不允许再购买新手套餐", OperationContextHolder.getMasterUserId());
			return Result.success(false);
		}

		return Result.success(true);
	}

	@GetMapping("/checkIfShowTopup")
	@ApiOperation("当前用户是否展示充值入口")
	public Result<Boolean> checkIfShowTopup(){

		/*
		   {
			  "hideCustomerByDistributor": {
				"distributor": [
				  222
				],
				"exceptCustomer": [
				  333
				],
				"exceptCustomerUnderSalesId":[
				 444
				]
			  }
			}
		 */
		if (RoleTypeEnum.MERCHANT == OperationContextHolder.getRoleType()){
			try {
				JSONObject cfg = systemConfigService.queryJsonValue(SystemConstants.SHOW_TOPUP_CONFIG);
				if (cfg != null && cfg.containsKey("hideCustomerByDistributor")) {
					JSONArray distributorIds = cfg.getJSONObject("hideCustomerByDistributor").getJSONArray("distributor");
					JSONArray exceptCustomerIds = cfg.getJSONObject("hideCustomerByDistributor").getJSONArray("exceptCustomer");
					JSONArray exceptCustomerUnderSalesIds = cfg.getJSONObject("hideCustomerByDistributor").getJSONArray("exceptCustomerUnderSalesId");
					if (CollectionUtils.isNotEmpty(distributorIds)) {

						Integer masterId = OperationContextHolder.getMasterUserId();
						if (CollectionUtils.isNotEmpty(exceptCustomerIds) && exceptCustomerIds.contains(masterId)) {
							return Result.success(true);
						}

						DistributorCustomerQuery dcq = new DistributorCustomerQuery();
						dcq.setCustomerMasterUserId(OperationContextHolder.getMasterUserId());
						dcq.setPageNum(1);
						dcq.setPageSize(1);

						List<DistributorCustomerVO> customerRelation = distributorCustomerService.queryDistributorCustomerList(dcq);
						if (CollectionUtils.isNotEmpty(customerRelation)){
							for (DistributorCustomerVO c : customerRelation) {
								if (distributorIds.contains(c.getDistributorMasterUserId())) {

									//客户关联的销售账号，属于需要展示充值入口的销售，则展示充值入口
									if (CollectionUtils.isNotEmpty(exceptCustomerUnderSalesIds) && exceptCustomerUnderSalesIds.contains(c.getDistributorSalesUserId())) {
										return Result.success(true);
									}

									return Result.success(false);
								}
							}
						}
					}
				}
			} catch (Exception e){
				log.error("[checkIfShowTopup]查询充值开关配置失败", e);
			}
		}
		return Result.success(true);
	}

	@PostMapping("/topup2User")
	public Result<Integer> topup2User(@Valid @RequestBody Topup2UserReq req) {
		if (req.getAmount() == null || BigDecimalUtils.lessThanOneCent(req.getAmount())) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "充值金额必须大于0.01");
		}
		if (req.getMusePoint() != null && BigDecimalUtils.lessThanZero(req.getMusePoint())) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "muse点不可小于0");
		}
		if (req.getCreativeImgCountGave() != null && req.getCreativeImgCountGave() < 0){
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "赠送图不可小于0");
		}

		if (StringUtils.isNotBlank(req.getTopupDate()) && DateUtils.parseSimpleDate(req.getTopupDate()) == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "充值日期格式不正确:" + req.getTopupDate());
		}

		OrderInfoVO order = orderInfoService.topup2User(req);
		if (order == null) {
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "充值失败");
		}
		return Result.success(order.getId());
	}
}