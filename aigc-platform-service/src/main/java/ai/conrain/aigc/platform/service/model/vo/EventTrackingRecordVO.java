package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * EventTrackingRecordVO
 *
 * @version EventTrackingRecordService.java v 0.1 2024-12-07 02:51:15
 */
@Data
public class EventTrackingRecordVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 埋点记录主键 */
	@ApiModelProperty(name = "id", value = "埋点记录主键")
	private Integer id;

	/** 关联用户 Id（为空时则说明用户尚未登录） */
	@ApiModelProperty(name = "userId", value = "关联用户 Id（为空时则说明用户尚未登录）")
	private Integer userId;

	/** 临时uuid（唯一 UUID） */
	@ApiModelProperty(name = "tempUserUuid", value = "临时uuid（唯一 UUID）")
	private String tempUserUuid;

	/** ip 地址 */
	@ApiModelProperty(name = "ipAddress", value = "ip 地址")
	private String ipAddress;

	/** 会话 ID，用于标记一次用户的会话操作 */
	@ApiModelProperty(name = "sessionId", value = "会话 ID，用于标记一次用户的会话操作")
	private String sessionId;

	/** 事件类型 枚举记录 （1.进入页面 2.离开页面 3.按钮点击 4.鼠标悬停 ....） */
	@ApiModelProperty(name = "eventType", value = "事件类型 枚举记录 （1.进入页面 2.离开页面 3.按钮点击 4.鼠标悬停 ....）")
	private String eventType;

	/** 具体事件内容(点击登录按钮、点击发送验证码、鼠标悬停在视频上面...) */
	@ApiModelProperty(name = "eventContent", value = "具体事件内容(点击登录按钮、点击发送验证码、鼠标悬停在视频上面...)")
	private String eventContent;

	/** 网页标题 */
	@ApiModelProperty(name = "pageTitle", value = "网页标题")
	private String pageTitle;

	/** 当前页面的 URL */
	@ApiModelProperty(name = "pageUrl", value = "当前页面的 URL")
	private String pageUrl;

	/** 用户留存时间 （单位：秒） 默认为0 */
	@ApiModelProperty(name = "userRetentionTime", value = "用户留存时间 （单位：秒） 默认为0")
	private Integer userRetentionTime;

	/** 操作系统 */
	@ApiModelProperty(name = "os", value = "操作系统")
	private String os;

	/** 浏览器信息 */
	@ApiModelProperty(name = "browser", value = "浏览器信息")
	private String browser;

	/** 请求来源（1.PC  2.App 3.小程序 4.抖音....） */
	@ApiModelProperty(name = "requestResource", value = "请求来源（1.PC  2.App 3.小程序 4.抖音....）")
	private String requestResource;

	/** 上一个页面的 url */
	@ApiModelProperty(name = "preReferrer", value = "上一个页面的 url")
	private String preReferrer;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	/** 存储其他额外的事件数据（JSON 格式存储） */
	@ApiModelProperty(name = "additionalData", value = "存储其他额外的事件数据（JSON 格式存储）")
	private String additionalData;

}