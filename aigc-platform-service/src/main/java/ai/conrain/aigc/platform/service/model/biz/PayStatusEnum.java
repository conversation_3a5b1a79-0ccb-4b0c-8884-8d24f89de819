package ai.conrain.aigc.platform.service.model.biz;

import lombok.Getter;

/**
 * 支付状态枚举
 * 以微信支付为基础定义
 */
@Getter
public enum PayStatusEnum {
    SUCCESS("SUCCESS", "支付成功"),
    NOTPAY("NOTPAY", "未支付"),
    USERPAYING("USERPAYING", "用户支付中"),
    PAYERROR("PAYERROR", "支付失败"),
    CLOSED("CLOSED", "已关闭");

    private String code;
    private String desc;

    PayStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayStatusEnum getByCode(String code) {
        for (PayStatusEnum value : PayStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static PayStatusEnum getByAlipayTradeStatus(String tradeStatus){
        switch (tradeStatus) {
            case "WAIT_BUYER_PAY":
                return PayStatusEnum.NOTPAY;
            case "TRADE_CLOSED":
                return PayStatusEnum.CLOSED;
            case "TRADE_SUCCESS":
                return PayStatusEnum.SUCCESS;
            case "TRADE_FINISHED":
                return PayStatusEnum.SUCCESS;
            default:
                return PayStatusEnum.PAYERROR;
        }
    }
}
