<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.UserOrganizationDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.UserOrganizationDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="creator_operator_user_id" jdbcType="INTEGER" property="creatorOperatorUserId" />
    <result column="modifier_operator_user_id" jdbcType="INTEGER" property="modifierOperatorUserId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from user_organization
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByUserId" parameterType="java.lang.Integer">
    delete from user_organization
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>

  <delete id="deleteByOrgId" parameterType="java.lang.Integer">
    delete from user_organization
    where org_id = #{orgId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.UserOrganizationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_organization (user_id, org_id, creator_operator_user_id, 
      modifier_operator_user_id, ext_info, create_time, 
      modify_time)
    values (#{userId,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, #{creatorOperatorUserId,jdbcType=INTEGER}, 
      #{modifierOperatorUserId,jdbcType=INTEGER}, #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.UserOrganizationDO">
    update user_organization
    set user_id = #{userId,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=INTEGER},
      creator_operator_user_id = #{creatorOperatorUserId,jdbcType=INTEGER},
      modifier_operator_user_id = #{modifierOperatorUserId,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select id, user_id, org_id, creator_operator_user_id, modifier_operator_user_id, 
    ext_info, create_time, modify_time
    from user_organization
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, user_id, org_id, creator_operator_user_id, modifier_operator_user_id, 
    ext_info, create_time, modify_time
    from user_organization
  </select>
  <select id="selectByOrgIds" resultType="ai.conrain.aigc.platform.dal.entity.UserOrganizationDO">
    select id, user_id, org_id, creator_operator_user_id, modifier_operator_user_id,
    ext_info, create_time, modify_time
    from user_organization
    where org_id in
    <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
      #{orgId}
    </foreach>
  </select>
  <select id="selectByUserIds" resultType="ai.conrain.aigc.platform.dal.entity.UserOrganizationDO">
    select id, user_id, org_id, creator_operator_user_id, modifier_operator_user_id,
    ext_info, create_time, modify_time
    from user_organization
    where user_id in
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
  </select>
</mapper>