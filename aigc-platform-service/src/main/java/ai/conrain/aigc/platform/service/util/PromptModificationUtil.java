package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.component.PromptModificationLogService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.PromptModificationLogVO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 修改记录工具类
 */
@Slf4j
@Component
public class PromptModificationUtil {

    @Autowired
    private PromptModificationLogService promptModificationLogService;

    /**
     * 创建并保存变更记录
     */
    private void createAndSaveLog(String moduleCode, String moduleType, Integer parentElementId,
                                  JSONObject oldFieldValue, JSONObject newFieldValue, JSONObject fixedAttrs,
                                  JSONArray changes) {

        PromptModificationLogVO logVO = new PromptModificationLogVO();
        logVO.setModuleCode(moduleCode);
        logVO.setModuleType(moduleType);
        logVO.setParentElementId(parentElementId);
        logVO.setOperatorId(OperationContextHolder.getOperatorUserId());
        logVO.setOperatorName(OperationContextHolder.getOperatorNick());
        logVO.setOperationTime(new Date());
        logVO.setOldFieldValue(oldFieldValue);
        logVO.setNewFieldValue(newFieldValue);
        logVO.setFixedAttrs(fixedAttrs);
        logVO.addExtInfo("changes", changes);
        logVO.setRemark("Prompt提示词变更记录...");

        promptModificationLogService.insert(logVO);
    }

    /**
     * 记录MaterialModelVO对象的变更
     */
    public void recordMaterialModelChanges(MaterialModelVO oldMaterial, MaterialModelVO newMaterial,
                                           String moduleCode) {
        try {
            log.info("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges开始记录MaterialModelVO变更, moduleCode: {}",
                    moduleCode);
            if (oldMaterial == null || newMaterial == null) {
                log.warn("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges MaterialModelVO对象为空，跳过记录");
                return;
            }

            List<ClothTypeConfig> oldConfigs = extractClothTypeConfigs(oldMaterial);
            List<ClothTypeConfig> newConfigs = newMaterial.getClothTypeConfigs();

            if (!hasConfigChanges(oldConfigs, newConfigs)) {
                log.info("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges 没有配置变更，跳过记录");
                return;
            }

            JSONArray configChanges = extractChanges(oldConfigs, newConfigs);
            if (configChanges.isEmpty()) {
                log.info("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges 变更内容为空，跳过记录");
                return;
            }

            log.info("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges 检测到配置变更，变更数量: {}",
                    configChanges.size());
            JSONObject oldFieldObject = new JSONObject();
            JSONObject newFieldObject = new JSONObject();
            oldFieldObject.put("clothTypeConfigs", oldConfigs);
            newFieldObject.put("clothTypeConfigs", newConfigs);

            String moduleType = buildModuleType(CommonConstants.KEY_GARMENT_TYPE,
                    newMaterial.getExtInfo(CommonConstants.KEY_GARMENT_TYPE));
            JSONObject fixedAttrs = buildFixedAttrs(newMaterial);

            createAndSaveLog(moduleCode, moduleType, newMaterial.getId(), oldFieldObject, newFieldObject, fixedAttrs,
                    configChanges);
            log.info("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges MaterialModelVO变更记录完成");
        } catch (Exception e) {
            log.error("[服装prompt变更记录]PromptModificationUtil::recordMaterialModelChanges 记录MaterialModelVO变更失败: ", e);
        }
    }

    /**
     * 记录CreativeElementVO的children字段变更
     */
    public void recordCreativeElementChanges(CreativeElementVO oldParentElement, CreativeElementVO newParentElement) {
        try {
            String configKey = oldParentElement.getConfigKey();

            log.info("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges开始记录CreativeElementVO变更",
                    configKey.equals("SCENE") ? "场景" : "人脸");
            if (newParentElement == null) {
                log.warn("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges CreativeElementVO对象为空，跳过记录",
                        configKey.equals("SCENE") ? "场景" : "人脸");
                return;
            }

            List<CreativeElementVO> oldElements = oldParentElement.getChildren();
            List<CreativeElementVO> newElements = newParentElement.getChildren();

            if (CollectionUtils.isEmpty(oldElements) && CollectionUtils.isEmpty(newElements)) {
                log.info("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges 没有子元素变更，跳过记录",
                        configKey.equals("SCENE") ? "场景" : "人脸");
                return;
            }

            log.info("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges 检测到子元素变更，旧元素数量: {}, 新元素数量: {}",
                    configKey.equals("SCENE") ? "场景" : "人脸",
                    CollectionUtils.isEmpty(oldElements) ? 0 : oldElements.size(),
                    CollectionUtils.isEmpty(newElements) ? 0 : newElements.size());

            List<Map<String, Object>> simplifiedOldElements = simplifyCreativeElements(oldElements);
            List<Map<String, Object>> simplifiedNewElements = simplifyCreativeElements(newElements);

            JSONArray changesArray = extractCreativeElementChanges(oldElements, newElements);
            if (changesArray.isEmpty()) {
                log.info("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges 变更内容为空，跳过记录",
                        configKey.equals("SCENE") ? "场景" : "人脸");
                return;
            }

            log.info("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges 检测到元素变更，变更数量: {}",
                    configKey.equals("SCENE") ? "场景" : "人脸", changesArray.size());
            JSONObject oldFieldObject = new JSONObject();
            JSONObject newFieldObject = new JSONObject();
            oldFieldObject.put("elements", simplifiedOldElements);
            newFieldObject.put("elements", simplifiedNewElements);

            String moduleType = buildModuleTypeForCreativeElement(newParentElement, configKey);
            JSONObject fixedAttrs = buildFixedAttrsForCreativeElement(newParentElement, configKey);

            createAndSaveLog(configKey, moduleType, newParentElement.getId(),
                    oldFieldObject, newFieldObject, fixedAttrs, changesArray);

            log.info("[{}prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges CreativeElementVO变更记录完成",
                    configKey.equals("SCENE") ? "场景" : "人脸");
        } catch (Exception e) {
            log.error("[prompt变更记录]PromptModificationUtil::recordCreative::ElementChanges 记录CreativeElement变更失败: ", e);
        }
    }

    /**
     * 提取ClothTypeConfigs
     */
    private List<ClothTypeConfig> extractClothTypeConfigs(MaterialModelVO material) {
        log.debug("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 开始提取ClothTypeConfigs, material id: {}",
                material.getId());

        if (material.getExtInfo() == null) {
            log.warn("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs material extInfo为空");
            return new ArrayList<>();
        }

        if (!material.getExtInfo().containsKey(CommonConstants.KEY_CLOTH_TYPE_CONFIGS)) {
            log.warn("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs material extInfo中不包含clothTypeConfigs配置");
            return new ArrayList<>();
        }

        JSONArray configsArray = material.getExtInfo().getJSONArray(CommonConstants.KEY_CLOTH_TYPE_CONFIGS);
        if (configsArray == null) {
            log.warn("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs clothTypeConfigs配置为空");
            return new ArrayList<>();
        }

        log.debug("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 开始转换clothTypeConfigs, 数组大小: {}",
                configsArray.size());
        List<ClothTypeConfig> configs = new ArrayList<>();

        for (int i = 0; i < configsArray.size(); i++) {
            try {
                Object config = configsArray.get(i);
                log.debug("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 配置类型: {}",
                        config.getClass().getName());
                if (config instanceof JSONObject || config instanceof com.alibaba.fastjson2.JSONObject) {
                    // 将对象转换为字符串，然后再解析，避免直接类型转换
                    String jsonStr = JSON.toJSONString(config);
                    ClothTypeConfig clothConfig = JSON.parseObject(jsonStr, ClothTypeConfig.class);
                    if (clothConfig != null) {
                        configs.add(clothConfig);
                        log.debug("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 成功转换第{}个配置", i + 1);
                    }
                } else if (config instanceof ClothTypeConfig) {
                    configs.add((ClothTypeConfig) config);
                    log.debug("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 直接使用第{}个配置", i + 1);
                } else {
                    log.warn("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 无法处理的配置类型: {}, index: {}",
                            config.getClass().getName(), i);
                }
            } catch (Exception e) {
                log.error("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs 转换第{}个配置失败: {}", i + 1, e.getMessage(),
                        e);
            }
        }

        log.info("[服装prompt变更记录]PromptModificationUtil::extractClothTypeConfigs ClothTypeConfigs提取完成, 共转换{}个配置",
                configs.size());
        return configs;
    }

    /**
     * 检查是否有配置变更
     */
    private boolean hasConfigChanges(List<ClothTypeConfig> oldConfigs, List<ClothTypeConfig> newConfigs) {
        return !CollectionUtils.isEmpty(oldConfigs) || !CollectionUtils.isEmpty(newConfigs);
    }

    private String buildModuleType(String key, Object value) {
        return "【" + key + "】" + value;
    }

    /**
     * 构建固定属性
     */
    private JSONObject buildFixedAttrs(MaterialModelVO material) {
        JSONObject fixedAttrs = new JSONObject();
        fixedAttrs.put(CommonConstants.CLOTH_STYLE_TYPE, material.getExtInfo(CommonConstants.CLOTH_STYLE_TYPE));
        fixedAttrs.put(CommonConstants.KEY_INCLUDES_BRA, material.getExtInfo(CommonConstants.KEY_INCLUDES_BRA));
        fixedAttrs.put(CommonConstants.MULTI_COLORS, Objects.nonNull(material.getClothLoraTrainDetail()) ? material.getClothLoraTrainDetail().getMultiColors() : null);
        fixedAttrs.put(CommonConstants.CLOTHE_TYPE, material.getClothType());
        fixedAttrs.put(CommonConstants.KEY_GARMENT_TYPE, material.getExtInfo(CommonConstants.KEY_GARMENT_TYPE));
        return fixedAttrs;
    }

    /**
     * 构建CreativeElement的模块类型
     */
    private String buildModuleTypeForCreativeElement(CreativeElementVO element, String configKey) {
        if ("SCENE".equals(configKey)) {
            return buildModuleType(CommonConstants.KEY_IS_LORA, element.getExtInfo(CommonConstants.KEY_IS_LORA));
        } else if ("FACE".equals(configKey)) {
            return buildModuleType(CommonConstants.KEY_SWAP_TYPE, element.getExtInfo(CommonConstants.KEY_SWAP_TYPE));
        }
        return null;
    }

    /**
     * 构建CreativeElement的固定属性
     */
    private JSONObject buildFixedAttrsForCreativeElement(CreativeElementVO element, String configKey) {
        JSONObject fixedAttrs = new JSONObject();
        if ("SCENE".equals(configKey)) {
            fixedAttrs.put(CommonConstants.KEY_IS_LORA, element.getExtInfo(CommonConstants.KEY_IS_LORA));
        } else if ("FACE".equals(configKey)) {
            fixedAttrs.put(CommonConstants.KEY_SWAP_TYPE, element.getExtInfo(CommonConstants.KEY_SWAP_TYPE));
            fixedAttrs.put(CommonConstants.TYPE, element.getType());
        }
        return fixedAttrs;
    }

    /**
     * 简化CreativeElement对象
     */
    private List<Map<String, Object>> simplifyCreativeElements(List<CreativeElementVO> elements) {
        if (CollectionUtils.isEmpty(elements)) {
            return new ArrayList<>();
        }

        return elements.stream()
                .map(element -> {
                    Map<String, Object> simplified = new HashMap<>(4);
                    simplified.put("id", element.getId());
                    simplified.put("tags", element.getTags());
                    simplified.put("extTags", element.getExtTags());
                    simplified.put("extInfo", element.getExtInfo());
                    return simplified;
                })
                .collect(Collectors.toList());
    }

    /**
     * 提取CreativeElement元素列表之间的变更
     */
    private JSONArray extractCreativeElementChanges(List<CreativeElementVO> oldElements,
                                                    List<CreativeElementVO> newElements) {

        String configKey = newElements.get(0).getConfigKey();

        log.debug("[{}prompt变更记录]PromptModificationUtil::extractCreativeElementChanges 开始提取CreativeElement变更",
                configKey.equals("SCENE") ? "场景" : "人脸");
        JSONArray changesArray = new JSONArray();
        Map<Integer, CreativeElementVO> oldElementMap = buildElementMap(oldElements);

        for (CreativeElementVO newElement : newElements) {
            if (newElement.getId() == null) {
                log.warn("[{}prompt变更记录]PromptModificationUtil::extractCreativeElementChanges 发现ID为空的元素，跳过处理",
                        configKey.equals("SCENE") ? "场景" : "人脸");
                continue;
            }

            JSONObject elementChanges = new JSONObject();
            elementChanges.put("id", newElement.getId());

            CreativeElementVO oldElement = oldElementMap.get(newElement.getId());
            if (oldElement == null) {
                log.debug("[{}prompt变更记录]PromptModificationUtil::extractCreativeElementChanges 发现新增元素, id: {}",
                        configKey.equals("SCENE") ? "场景" : "人脸", newElement.getId());
                addNewElementChange(elementChanges, newElement);
                changesArray.add(elementChanges);
                continue;
            }

            if (compareElementFields(elementChanges, oldElement, newElement)) {
                log.debug("[{}prompt变更记录]PromptModificationUtil::extractCreativeElementChanges 发现元素变更, id: {}",
                        configKey.equals("SCENE") ? "场景" : "人脸", newElement.getId());
                changesArray.add(elementChanges);
            }

            oldElementMap.remove(newElement.getId());
        }

        if (!oldElementMap.isEmpty()) {
            log.debug("[{}prompt变更记录]PromptModificationUtil::extractCreativeElementChanges 发现{}个删除的元素",
                    configKey.equals("SCENE") ? "场景" : "人脸", oldElementMap.size());
            addDeletedElementChanges(changesArray, oldElementMap);
        }

        log.debug("[{}prompt变更记录]PromptModificationUtil::extractCreativeElementChanges  CreativeElement变更提取完成，共{}个变更",
                configKey.equals("SCENE") ? "场景" : "人脸", changesArray.size());
        return changesArray;
    }

    /**
     * 构建元素映射表
     */
    private Map<Integer, CreativeElementVO> buildElementMap(List<CreativeElementVO> elements) {
        return elements.stream()
                .filter(element -> element.getId() != null)
                .collect(Collectors.toMap(CreativeElementVO::getId, element -> element));
    }

    /**
     * 添加新元素变更
     */
    private void addNewElementChange(JSONObject elementChanges, CreativeElementVO newElement) {
        JSONObject change = new JSONObject();
        change.put("oldValue", null);
        change.put("newValue", newElement.getExtInfo());
        elementChanges.put("extInfo", change);
    }

    /**
     * 比较元素字段
     */
    private boolean compareElementFields(JSONObject elementChanges, CreativeElementVO oldElement,
                                         CreativeElementVO newElement) {
        boolean hasChanges = false;

        if (!Objects.equals(oldElement.getTags(), newElement.getTags())) {
            addChangeItem(elementChanges, "tags", oldElement.getTags(), newElement.getTags());
            hasChanges = true;
        }

        if (!Objects.equals(oldElement.getExtTags(), newElement.getExtTags())) {
            addChangeItem(elementChanges, "extTags", oldElement.getExtTags(), newElement.getExtTags());
            hasChanges = true;
        }

        JSONObject extInfoChanges = compareExtInfo(oldElement.getExtInfo(), newElement.getExtInfo());
        if (!extInfoChanges.isEmpty()) {
            elementChanges.put("extInfo", extInfoChanges);
            hasChanges = true;
        }

        return hasChanges;
    }

    /**
     * 比较ExtInfo
     */
    private JSONObject compareExtInfo(JSONObject oldExtInfo, JSONObject newExtInfo) {
        JSONObject extInfoChanges = new JSONObject();
        if (oldExtInfo == null || newExtInfo == null) {
            if (!Objects.equals(oldExtInfo, newExtInfo)) {
                addChangeItem(extInfoChanges, "extInfo", oldExtInfo, newExtInfo);
            }
            return extInfoChanges;
        }

        Set<String> allKeys = new HashSet<>();
        allKeys.addAll(oldExtInfo.keySet());
        allKeys.addAll(newExtInfo.keySet());

        for (String key : allKeys) {
            Object oldValue = oldExtInfo.get(key);
            Object newValue = newExtInfo.get(key);
            if (!Objects.equals(oldValue, newValue)) {
                addChangeItem(extInfoChanges, key, oldValue, newValue);
            }
        }

        return extInfoChanges;
    }

    /**
     * 添加已删除元素变更
     */
    private void addDeletedElementChanges(JSONArray changesArray, Map<Integer, CreativeElementVO> oldElementMap) {
        for (CreativeElementVO oldElement : oldElementMap.values()) {
            JSONObject elementChanges = new JSONObject();
            elementChanges.put("id", oldElement.getId());

            JSONObject change = new JSONObject();
            change.put("oldValue", oldElement.getExtInfo());
            change.put("newValue", null);
            elementChanges.put("extInfo", change);

            changesArray.add(elementChanges);
        }
    }

    /**
     * 提取两个ClothTypeConfig列表之间的变更
     */
    private JSONArray extractChanges(List<ClothTypeConfig> oldConfigs, List<ClothTypeConfig> newConfigs) {
        log.debug("[服装prompt变更记录]PromptModificationUtil::extractChanges 开始提取ClothTypeConfig变更");
        JSONArray changesArray = new JSONArray();
        oldConfigs = oldConfigs != null ? oldConfigs : new ArrayList<>();
        newConfigs = newConfigs != null ? newConfigs : new ArrayList<>();

        int maxSize = Math.max(oldConfigs.size(), newConfigs.size());
        for (int i = 0; i < maxSize; i++) {
            ClothTypeConfig oldConfig = i < oldConfigs.size() ? oldConfigs.get(i) : null;
            ClothTypeConfig newConfig = i < newConfigs.size() ? newConfigs.get(i) : null;

            JSONObject configChanges = new JSONObject();
            configChanges.put("index", i);

            if (oldConfig == null && newConfig != null) {
                log.debug("[服装prompt变更记录]PromptModificationUtil::extractChanges 发现新增配置, index: {}", i);
                addAllFieldsAsChanges(configChanges, null, newConfig);
            } else if (oldConfig != null && newConfig == null) {
                log.debug("[服装prompt变更记录]PromptModificationUtil::extractChanges 发现删除配置, index: {}", i);
                addAllFieldsAsChanges(configChanges, oldConfig, null);
            } else if (oldConfig != null) {
                log.debug("[服装prompt变更记录]PromptModificationUtil::extractChanges 比较配置变更, index: {}", i);
                compareConfigFields(configChanges, oldConfig, newConfig);
            }

            if (!configChanges.isEmpty() && configChanges.size() > 1) {
                changesArray.add(configChanges);
            }
        }

        log.debug("[服装prompt变更记录]PromptModificationUtil::extractChanges ClothTypeConfig变更提取完成，共{}个变更", changesArray.size());
        return changesArray;
    }

    /**
     * 比较配置字段
     */
    private void compareConfigFields(JSONObject changes, ClothTypeConfig oldConfig, ClothTypeConfig newConfig) {
        compareAndAddChanges(changes, "tags", oldConfig.getTags(), newConfig.getTags());
        JSONArray colorListChanges = extractColorListChanges(oldConfig.getColorList(), newConfig.getColorList());
        if (!colorListChanges.isEmpty()) {
            changes.put("colorList", colorListChanges);
        }
    }

    /**
     * 添加所有字段变更
     */
    private void addAllFieldsAsChanges(JSONObject changes, ClothTypeConfig oldConfig, ClothTypeConfig newConfig) {
        if (oldConfig == null && newConfig != null) {
            addChangeItem(changes, "type", null, newConfig.getType());
            addChangeItem(changes, "tags", null, newConfig.getTags());
            addChangeItem(changes, "includesBra", null, newConfig.isIncludesBra());
            addChangeItem(changes, "extInfo", null, newConfig.getExtInfo());
            addChangeItem(changes, "colorList", null, newConfig.getColorList());
        } else if (oldConfig != null && newConfig == null) {
            addChangeItem(changes, "type", oldConfig.getType(), null);
            addChangeItem(changes, "tags", oldConfig.getTags(), null);
            addChangeItem(changes, "includesBra", oldConfig.isIncludesBra(), null);
            addChangeItem(changes, "extInfo", oldConfig.getExtInfo(), null);
            addChangeItem(changes, "colorList", oldConfig.getColorList(), null);
        }
    }

    /**
     * 提取colorList列表之间的变更
     */
    private JSONArray extractColorListChanges(List<ClothColorDetail> oldColorList,
                                              List<ClothColorDetail> newColorList) {
        log.debug("[服装prompt变更记录]PromptModificationUtil::extractColorListChanges 开始提取颜色列表变更");
        JSONArray changesArray = new JSONArray();
        oldColorList = oldColorList != null ? oldColorList : new ArrayList<>();
        newColorList = newColorList != null ? newColorList : new ArrayList<>();

        Map<String, ClothColorDetail> oldColorMap = buildColorMap(oldColorList);

        for (ClothColorDetail newColor : newColorList) {
            if (StringUtils.isBlank(newColor.getName())) {
                log.warn("[服装prompt变更记录]PromptModificationUtil::extractColorListChanges 发现名称为空的颜色，跳过处理");
                continue;
            }

            ClothColorDetail oldColor = oldColorMap.get(newColor.getName());
            if (oldColor == null) {
                log.debug("[服装prompt变更记录]PromptModificationUtil::extractColorListChanges 发现新增颜色: {}", newColor.getName());
                addNewColorChange(changesArray, newColor);
                continue;
            }

            JSONObject detailChanges = compareColorDetails(oldColor, newColor);
            if (!detailChanges.isEmpty()) {
                log.debug("[服装prompt变更记录]PromptModificationUtil::extractColorListChanges 发现颜色变更: {}", newColor.getName());
                changesArray.add(detailChanges);
            }

            oldColorMap.remove(newColor.getName());
        }

        if (!oldColorMap.isEmpty()) {
            log.debug("[服装prompt变更记录]PromptModificationUtil::extractColorListChanges 发现{}个删除的颜色", oldColorMap.size());
            addDeletedColorChanges(changesArray, oldColorMap);
        }

        log.debug("[服装prompt变更记录]PromptModificationUtil::extractColorListChanges 颜色列表变更提取完成，共{}个变更", changesArray.size());
        return changesArray;
    }

    /**
     * 构建颜色映射表
     */
    private Map<String, ClothColorDetail> buildColorMap(List<ClothColorDetail> colorList) {
        return colorList.stream()
                .filter(color -> StringUtils.isNotBlank(color.getName()))
                .collect(Collectors.toMap(ClothColorDetail::getName, color -> color));
    }

    /**
     * 添加新颜色变更
     */
    private void addNewColorChange(JSONArray changesArray, ClothColorDetail newColor) {
        JSONObject change = new JSONObject();
        change.put("oldValue", null);
        change.put("newValue", newColor);
        changesArray.add(change);
    }

    /**
     * 比较颜色详情
     */
    private JSONObject compareColorDetails(ClothColorDetail oldColor, ClothColorDetail newColor) {
        JSONObject detailChanges = new JSONObject();
        compareAndAddDetailChange(detailChanges, "value", oldColor.getValue(), newColor.getValue());
        return detailChanges;
    }

    /**
     * 添加已删除颜色变更
     */
    private void addDeletedColorChanges(JSONArray changesArray, Map<String, ClothColorDetail> oldColorMap) {
        for (ClothColorDetail oldColor : oldColorMap.values()) {
            JSONObject change = new JSONObject();
            change.put("oldValue", oldColor);
            change.put("newValue", null);
            changesArray.add(change);
        }
    }

    /**
     * 比较并添加变更项
     */
    private void compareAndAddChanges(JSONObject changes, String key, Object oldValue, Object newValue) {
        if (oldValue == null && newValue == null) {
            return;
        }

        if (oldValue instanceof String && newValue instanceof String) {
            if (!StringUtils.equals((String) oldValue, (String) newValue)) {
                addChangeItem(changes, key, oldValue, newValue);
            }
            return;
        }

        if (oldValue instanceof Boolean && newValue instanceof Boolean) {
            if (!Objects.equals(oldValue, newValue)) {
                addChangeItem(changes, key, oldValue, newValue);
            }
            return;
        }

        String oldJson = oldValue != null ? JSON.toJSONString(oldValue) : null;
        String newJson = newValue != null ? JSON.toJSONString(newValue) : null;
        if (!StringUtils.equals(oldJson, newJson)) {
            addChangeItem(changes, key, oldValue, newValue);
        }
    }

    /**
     * 添加变更项
     */
    private void addChangeItem(JSONObject changes, String key, Object oldValue, Object newValue) {
        JSONObject change = new JSONObject();
        change.put("oldValue", oldValue);
        change.put("newValue", newValue);
        changes.put(key, change);
    }

    /**
     * 比较并添加详细字段变更
     */
    private void compareAndAddDetailChange(JSONObject detailChanges, String fieldName, Object oldValue,
                                           Object newValue) {
        if (!Objects.equals(oldValue, newValue)) {
            addChangeItem(detailChanges, fieldName, oldValue, newValue);
        }
    }
}