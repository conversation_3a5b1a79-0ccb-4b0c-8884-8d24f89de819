package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 收藏类型枚举
 *
 *
 */
@Getter
public enum FavorTypeEnum {

    MATERIAL("MATERIAL", "创作素材"),

    ELEMENT("ELEMENT", "创作元素"),

    IMAGE("IMAGE", "创作图片"),

    VIDEO("VIDEO", "创作视频"),
    ;
    /** 枚举码 */
    private String code;

    /** 枚举描述 */
    private String desc;


    FavorTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     *  根据枚举码获取枚举
     * @param code 枚举码
     * @return 枚举
     */
    public static FavorTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (FavorTypeEnum e : FavorTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static boolean isCreationType (FavorTypeEnum type) {
        if (type == null) {
            return false;
        }
        return type == IMAGE || type == VIDEO;
    }

    public static boolean isCreationType (String type) {
        if (StringUtils.isBlank(type)) {
            return false;
        }
        return type.equals(IMAGE.code) || type.equals(VIDEO.code);
    }
}
