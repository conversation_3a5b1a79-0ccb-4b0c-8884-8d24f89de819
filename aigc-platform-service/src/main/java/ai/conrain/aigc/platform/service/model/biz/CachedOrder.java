package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CachedOrder implements Serializable {
    private String orderNo;
    private String codeUrl;
    private Integer operatorUserId;
    private Integer masterUserId;
    private Date createTime;
    private String planCode;
    private Integer amountInCents;
    // 订单代理用户id（后台管理员收款）
    private Integer orderAgentUserId;
    private OrderInfoVO orderInfo;
    // 支付类型，如支付宝、微信等,wx/alipay
    private String payType;
    //充值muse点，有值则充值成功后，会自动加点（用于后台自定义转账-实时收款）
    private Integer musePoint;
    //充值创意图片数，有值时，充值成功后会赠送图片（用于后台自定义转账-实时收款）
    private Integer creativeImgCountGave;
}
