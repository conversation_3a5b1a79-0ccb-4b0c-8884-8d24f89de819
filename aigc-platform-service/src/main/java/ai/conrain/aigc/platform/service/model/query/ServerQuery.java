package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ServerQuery
 *
 * @version ServerService.java v 0.1 2024-06-15 05:24:29
 */
@Data
public class ServerQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 服务名称 */
    @ApiModelProperty(name = "name", value = "服务名称")
    private String name;

    /** 等级，一级为服务器，二级为具体端口 */
    @ApiModelProperty(name = "level", value = "等级，一级为服务器，二级为具体端口")
    private Integer level;

    /** 配置值 */
    @ApiModelProperty(name = "config", value = "配置值")
    private String config;

    /** 类型，生图、Lora训练、文件服务 */
    @ApiModelProperty(name = "type", value = "类型，生图、Lora训练、文件服务")
    private String type;

    /** 父节点id */
    @ApiModelProperty(name = "parentId", value = "父节点id")
    private Integer parentId;

    /** 管道id */
    @ApiModelProperty(name = "pipelineId", value = "管道id")
    private Integer pipelineId;


    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}