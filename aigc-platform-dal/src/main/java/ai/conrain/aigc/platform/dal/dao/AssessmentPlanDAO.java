package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO;
import ai.conrain.aigc.platform.dal.example.AssessmentPlanExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AssessmentPlanDAO {
    long countByExample(AssessmentPlanExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(AssessmentPlanDO record);

    int insertSelective(AssessmentPlanDO record);

    List<AssessmentPlanDO> selectByExampleWithBLOBs(AssessmentPlanExample example);

    List<AssessmentPlanDO> selectByExample(AssessmentPlanExample example);

    AssessmentPlanDO selectByPrimaryKey(Integer id);

    AssessmentPlanDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") AssessmentPlanDO record, @Param("example") AssessmentPlanExample example);

    int updateByExampleWithBLOBs(@Param("record") AssessmentPlanDO record, @Param("example") AssessmentPlanExample example);

    int updateByExample(@Param("record") AssessmentPlanDO record, @Param("example") AssessmentPlanExample example);

    int updateByPrimaryKeySelective(AssessmentPlanDO record);

    int updateByPrimaryKeyWithBLOBs(AssessmentPlanDO record);

    int updateByPrimaryKey(AssessmentPlanDO record);

    int logicalDeleteByExample(@Param("example") AssessmentPlanExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    int insertOrUpdate(AssessmentPlanDO record);
}