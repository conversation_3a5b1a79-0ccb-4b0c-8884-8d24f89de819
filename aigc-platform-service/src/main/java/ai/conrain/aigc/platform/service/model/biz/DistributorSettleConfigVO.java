package ai.conrain.aigc.platform.service.model.biz;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DistributorSettleConfigVO extends DistributorPrincipalBasicVO {

    /** 结算策略模型 */
	@ApiModelProperty(name = "settleType", value = "结算策略类型")
    private SettleConfigModel settleConfig;
}
