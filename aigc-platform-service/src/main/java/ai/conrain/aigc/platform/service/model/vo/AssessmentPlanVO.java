package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.AssessStatusEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.enums.SalesAssessTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.KPIModel;
import com.alibaba.fastjson.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.io.Serializable;
import java.util.Objects;

/**
 * AssessmentPlanVO
 *
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
@Data
public class AssessmentPlanVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 结算主体类型 */
	@ApiModelProperty(name = "principalType", value = "结算主体类型")
	private PrincipalTypeEnum principalType;

	/** 结算主体 id */
	@ApiModelProperty(name = "principalId", value = "结算主体 id")
	private Integer principalId;

	/** 考核类型 */
	@ApiModelProperty(name = "type", value = "考核类型")
	private SalesAssessTypeEnum type;

	/** 考核任务状态 */
	@ApiModelProperty(name = "status", value = "考核任务状态")
	private AssessStatusEnum status;

	/** 考核指标 */
	@ApiModelProperty(name = "kpiTarget", value = "考核指标")
	private KPIModel kpiTarget;

	/** 实际完成情况 */
	@ApiModelProperty(name = "kpiActual", value = "实际完成情况")
	private KPIModel kpiActual;

	/** 考核计划开始日期 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
	@ApiModelProperty(name = "planFromDate", value = "考核计划开始日期")
	private Date planFromDate;

	/** 考核计划结束日期 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
	@ApiModelProperty(name = "planEndDate", value = "考核计划结束日期")
	private Date planEndDate;

	/** 扩展字段 */
	@ApiModelProperty(name = "extInfo", value = "扩展字段")
	private JSONObject extInfo;

	/** 创建人用户id */
	@ApiModelProperty(name = "creatorUserId", value = "创建人用户id")
	private Integer creatorUserId;

	/** 修改人用户id */
	@ApiModelProperty(name = "modifyUserId", value = "修改人用户id")
	private Integer modifyUserId;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	public <T> T getExtInfo(String key, Class<T> clazz) {
		if (ObjectUtils.isEmpty(this.extInfo)) {
			return null;
		}
		return this.extInfo.getObject(key, clazz);
	}

	public void putExtInfo(String key, Object value) {
		if (Objects.isNull(this.extInfo)) {
			this.extInfo = new JSONObject();
		}
		this.extInfo.put(key, value);
	}


}