package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.creative.async.BatchToAsyncExecutor;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest.ReferenceInfo;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.ContextUtils;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.FlowUtils;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import ai.conrain.aigc.platform.service.util.PromptUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_COLOR_INDEX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ENABLE_ANTI_BLUR_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ENABLE_NEW_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_EXPRESSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_EXT_TAGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_AFTER_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_CFG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_LORA_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_HAIRSTYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_INCREASE_STEPS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_SWAP_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODEL_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NEGATIVE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_POSTURE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REPAIR_AFTER_SWAP;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REPAIR_FACE_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SAMPLER_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SEED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SUB_LORA_NAMES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_CONTEXT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_PARAMS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NOW_SHOW_FACE_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.REFERENCE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.REFERENCE_INFO_LIST;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.clothStyleType;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.CREATIVE_PURE_BG_FLOW_PARAMS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FACE_NEW_FLOW_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_NEW_FACE_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_NEW_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_NEW_INSTANT_ID_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_STYLE_EXP_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;

@Slf4j
@Service
public class FixedPostureCreationCreativeService extends CreateImageCreativeService {


    @Autowired
    private BatchToAsyncExecutor batchToAsyncExecutor;
    @Autowired
    private ComfyUIHelper comfyUIHelper;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private CreativeElementService creativeElementService;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.FIXED_POSTURE_CREATION;
    }

    @Override
    protected CreativeBatchVO buildData(AddCreativeRequest request, MaterialModelVO modelVO) {
        CreativeBatchVO creativeBatchVO = super.buildData(request, modelVO);

        // 添加固定姿势信息列表
        creativeBatchVO.addExtInfo(REFERENCE_INFO_LIST, request.getReferenceInfoList());
        //  设置任务类型
        creativeBatchVO.setType(getCreativeType());

        // 处理服装搭配
        return creativeBatchVO;
    }

    @Override
    protected List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements, AddCreativeRequest request) {
        log.info(
                "[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据...");

        List<CreativeTaskVO> result = new ArrayList<>();
        List<ReferenceInfo> referenceInfoList = request.getReferenceInfoList();
        for (int i = 0; i < referenceInfoList.size(); i++) {
            ReferenceInfo referenceInfo = referenceInfoList.get(i);
            // 构建任务信息
            CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);


            // 填充场景信息
            CreativeElementVO sceneElement = creativeElementService.selectById(referenceInfo.getLoraId());
            elements.add(sceneElement);

            // 填充扩展信息,i为referenceInfo的索引
            fillTaskExt(target, batch, elements, i);

            // 获取姿势图片地址
            String referenceImageUrl = StringUtils.EMPTY;
            try {

                String originalImageUrl = referenceInfo.getOriginalImageUrl();
                if (StringUtils.isBlank(originalImageUrl) && referenceInfo.getReferenceConfig() != null){
                    originalImageUrl = referenceInfo.getReferenceConfig().getString(CommonConstants.ORIGINAL_IMAGE_URL);
                }

                if (StringUtils.isBlank(originalImageUrl)){
                    log.error("[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::原始参考图为空，终止流程");
                    throw new BizException(ResultCode.BIZ_FAIL);
                }

                referenceImageUrl = comfyUIHelper.upLoadImage(originalImageUrl);
            } catch (IOException e) {
                log.error("[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::图片上传至 ComfyUI 时出现错误::{}",
                        e.getMessage());
            }

            // 设置 loraId
            target.addExtInfo(KEY_MODEL_ID, referenceInfo.getLoraId());
            //  设置任务类型
            target.setType(getCreativeType());
            // 设置任务出图数量
            target.setBatchCnt(3);

            // 添加 参考图图片
            target.addExtInfo(CommonConstants.REFERENCE_IMAGE, referenceImageUrl);

            // 添加参考图原图片
            target.addExtInfo(CommonConstants.REFERENCE_ORIGINAL_IMAGE, referenceInfo.getImageUrl());
            // 添加风格lora配置
            if (Objects.nonNull(referenceInfo.getReferenceConfig())) {
                // 设置 lens
                target.addExtInfo(CommonConstants.KEY_LENS, referenceInfo.getReferenceConfig().get(CommonConstants.KEY_LENS));
                // 设置 posture
                target.addExtInfo(CommonConstants.KEY_POSTURE, referenceInfo.getReferenceConfig().get(CommonConstants.KEY_POSTURE));
                // 设置 style
                target.addExtInfo(CommonConstants.KEY_STYLE, referenceInfo.getReferenceConfig().get(CommonConstants.KEY_STYLE));
            }
            // 设置扩展标签
            target.addExtInfo(CommonConstants.BACK_TAGS, referenceInfo.getBackExtTags());
            // 设置 lora 地址
            target.addExtInfo(CommonConstants.KEY_LORA_PATH, referenceInfo.getLoraPath());

            // 设置图片比例
            target.setImageProportion(batch.getImageProportion());

            // 插入任务信息
            CreativeTaskVO data = creativeTaskService.insert(target);

            // 添加至集合中
            result.add(data);
        }

        log.info(
                "[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据构建完成,共：{}个任务",
                result.size());


        return result;
    }


    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        JSONObject testContext = task.getExtValue(KEY_TEST_CONTEXT, JSONObject.class);
        if (testContext != null) {
            log.info("【固定姿势创作】识别到测试场景,需变更上下文,id={},testContext={}", task.getBatchId(), testContext);
            ContextUtils.updateContext(context, testContext);
        }

        CreativeElementVO sceneElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.SCENE.name());

        if (modelVO.getVersion() == ModelVersionEnum.FLUX) {

            CreativeElementVO face = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());
            if (face != null) {

                if (ElementUtils.isInstantIdFlow(face)) {
                    if (context.get("newInstantId") != null && (Boolean) context.get("newInstantId")) {
                        log.info("【固定姿势创作】识别到flux换脸新instantId流程,id={}", task.getBatchId());
                        return FLUX_FIXED_POSTURE_CREATIVE_NEW_INSTANT_ID_FLOW;
                    }

                    log.info("【固定姿势创作】识别到flux新换脸流程,id={}", task.getBatchId());
                    return FLUX_FIXED_POSTURE_CREATIVE_NEW_FACE_FLOW;
                }

                // 临时逻辑，部分代码写死，236、355和365节点，后续修改可参考FLUX_CREATIVE_STYLE_EXP_FLOW中的配置
                Integer faceId = face.getLevel().equals(2) ? face.getId() : face.getParentId();
                if (!isNoshowFace(context, sceneElement) && (systemConfigService.isInJsonArray(FACE_NEW_FLOW_SWITCH,
                        faceId) || StringUtils.equals(YES, face.getExtInfo(KEY_REPAIR_AFTER_SWAP, String.class)))) {
                    log.info("【固定姿势创作】识别到flux先换脸后修脸场景,id={}", task.getBatchId());
                    return FLUX_FIXED_POSTURE_CREATIVE_NEW_FLOW;
                }
            }

            log.info("【固定姿势创作】识别到flux场景,id={}", task.getBatchId());
            return FLUX_FIXED_POSTURE_CREATIVE_STYLE_EXP_FLOW;
        }

        if (getPureBgScene(elements) != null) {
            log.info("【固定姿势创作】识别到纯色背景场景");
            return CREATIVE_PURE_BG_FLOW_PARAMS;
        }

        return super.getFlowKey(elements, modelVO, task, context);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context) {
        // 初始化各种seed
        String faceSeed = RandomStringUtils.randomNumeric(15);
        long promptSeed = Long.parseLong(RandomStringUtils.randomNumeric(15));// 1-2048的随机数字,无法发挥随机数seed，调整成15位
        CreateTestParams testParams = task.getExtValue(KEY_TEST_PARAMS, CreateTestParams.class);
        if (testParams != null) {
            context.put(KEY_SEED, testParams.getSeed());
            faceSeed = testParams.getFaceSeed();
            promptSeed = Long.parseLong(testParams.getPromptSeed());
        }

        // 1.异步执行的参数解析
        batchToAsyncExecutor.asyncExecAndStore(task, elements);

        // 2.进行数据修正
        dataCorrect(elements, modelVO, context, task, promptSeed);

        CreativeElementVO sceneElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.SCENE.name());
        CreativeElementVO faceElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());

        // 默认写死30步，兼容逻辑，修改流程后可优化 TODO
        modelVO.addExtInfo(KEY_INCREASE_STEPS, 30);

        // 3.补齐数据
        context.put("faceSeed", faceSeed);

        String faceCfg = faceElement.getExtInfo(KEY_FACE_CFG) != null ? faceElement.getExtInfo(KEY_FACE_CFG).toString()
                : String.valueOf(1.5); // 默认3.0
        context.put("faceCfg", faceCfg);

        context.put("promptSeed", promptSeed);
        context.put("loraStrength", 1);

        // 4.基于个性化需求，调整部分参数，如角度、性别、搭配、表情、发型、场景等

        // 设置服装款式个性化配置（正、背、全身、下半身）
        // 注意：要放在resetClothCollocation之前，服装搭配依赖于补充激活词，extTags
        resetCameraAngle(task, modelVO, promptSeed, faceElement, sceneElement);

        // 设置服装搭配
        resetClothCollocation(task, modelVO, sceneElement, faceElement, context, promptSeed);

        // 重置表情
        resetExpression(faceElement, promptSeed, modelVO, task);

        // 重置发型
        resetHairstyle(faceElement, sceneElement, modelVO);

        // 重置场景lora信息
        resetSceneLora(sceneElement, context);

        // 背面切换，1是需要换脸 todo 待删除，先兼容
        context.put("switchFace", isBackViewFlow(task) ? 0 : 1);
        context.put("repairFaceDenoise", "0.55");

        // 加速开关，默认不开启
        boolean speedUpSwitch = false;
        JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
        float flag = switchJson.getFloatValue("speedUp");
        if (GrayscaleTestUtils.isHit(flag, "出图-推理加速灰度")) {
            log.info("出图-推理加速灰度命中,taskId={},batchId={}", task.getId(), task.getBatchId());
            speedUpSwitch = true;
            task.addExtInfo("speedUp", "Y");
        }
        context.put("speedUpSwitch", speedUpSwitch);
        context.put("faceDetailerMaxSize", 1785);
        context.put("faceDetailerCropFactor", 1.5);
        // instantId换脸模型
        context.put("instantIdModel", "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors");
    }

    /**
     * 数据修正
     *
     * @param elements   元素列表
     * @param modelVO    模型数据
     * @param context    配置map上下文
     * @param task       任务
     * @param promptSeed prompt的种子
     */
    private void dataCorrect(List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context,
                             CreativeTaskVO task, long promptSeed) {
        CreativeElementVO originFaceElement = elements.stream().filter(
                element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())).findFirst().orElse(null);
        String needReplaceModelKeys = systemConfigService.queryValueByKey(SystemConstants.NEED_REPLACE_MODEL_KEYS);

        Integer loraId = task.getIntegerFromExtInfo(KEY_MODEL_ID);
        CreativeElementVO sceneElement = creativeElementService.selectById(loraId);
        if (sceneElement == null) {
            // 初始化创建场景参数
            sceneElement = new CreativeElementVO();
            sceneElement.setConfigKey(ElementConfigKeyEnum.SCENE.name());

            sceneElement.addExtInfo(KEY_LORA_PATH, task.getStringFromExtInfo(CommonConstants.KEY_LORA_PATH));
            sceneElement.addExtInfo(KEY_LENS, task.getStringFromExtInfo(CommonConstants.KEY_LENS));
            sceneElement.setTags(task.getStringFromExtInfo(CommonConstants.BACK_TAGS));
            sceneElement.addExtInfo(KEY_POSTURE, task.getStringFromExtInfo(CommonConstants.KEY_POSTURE));
            sceneElement.addExtInfo(KEY_STYLE, task.getStringFromExtInfo(CommonConstants.KEY_STYLE));
            sceneElement.addExtInfo(KEY_NEGATIVE, StringUtils.EMPTY);
        }
        // 添加场景元素
        elements.add(sceneElement);

        CreativeElementVO faceElement = CommonUtil.deepCopy(originFaceElement);

        // 添加参考图片
        context.put(REFERENCE_IMAGE, task.getStringFromExtInfo(REFERENCE_IMAGE));


        // lora模特，默认修脸强度1
        if (ElementUtils.isLoraFace(faceElement) && null == faceElement.getExtInfo(KEY_FACE_AFTER_STRENGTH,
                Double.class)) {
            faceElement.addExtInfo(KEY_FACE_AFTER_STRENGTH, 1);
        }

        // 如果不需要替换模型，直接返回原有的设置
        if (StringUtils.isBlank(needReplaceModelKeys) || !needReplaceFaceFeatures(faceElement, sceneElement, context)) {
            context.put("lora", modelVO);
            context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
            context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
            return;
        }


        String faceFeatures = StringUtils.EMPTY;
        if (Objects.nonNull(faceElement)) {
            faceFeatures = getFaceFeatures(faceElement);
        }

        String[] split = StringUtils.split(needReplaceModelKeys, ",");

        for (String key : split) {
            replaceFaceFeatures(modelVO, key, faceFeatures);
            replaceFaceFeatures(faceElement, key, faceFeatures);
            replaceFaceFeatures(sceneElement, key, faceFeatures);
        }

        context.put("lora", modelVO);
        context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
        context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
    }


    /**
     * 重置相机角度
     *
     * @param task         创作任务
     * @param modelVO      模型
     * @param promptSeed   prompt随机数
     * @param faceElement  模特元素
     * @param sceneElement 背景元素
     */
    static void resetCameraAngle(CreativeTaskVO task, MaterialModelVO modelVO, Long promptSeed,
                                 CreativeElementVO faceElement, CreativeElementVO sceneElement) {
        List<ClothTypeConfig> clothTypeConfigs = MaterialModelConverter.convert2ClothTypeConfig(modelVO);
        if (CollectionUtils.isEmpty(clothTypeConfigs)) {
            log.error("未找到服装类型配置，无法重置相机角度，modelId={},batchId={}", modelVO.getId(), task.getBatchId());
            return;
        }

        List<String> cameraAngles = getCameraAngles(task);

        if (CollectionUtils.isEmpty(cameraAngles)) {
            return;
        }

        ClothTypeConfig clothTypeConfig = clothTypeConfigs.stream().filter(
                config -> new HashSet<>(config.getType()).containsAll(cameraAngles)).findFirst().orElse(null);
        if (clothTypeConfig == null) {

            CameraAngleEnum orientation = CameraAngleEnum.getOrientationByStr(cameraAngles);
            clothTypeConfig = clothTypeConfigs.stream().filter(
                    config -> new HashSet<>(config.getType()).contains(orientation.getCode())).findFirst().orElse(null);

            if (clothTypeConfig == null) {
                log.warn("未找到服装类型配置，使用默认配置，cameraAngles={},batchId={}", cameraAngles, task.getBatchId());
                clothTypeConfig = clothTypeConfigs.get(0);
            } else {
                log.warn("未找到服装类型配置，但找到同样朝向的配置，使用该配置，cameraAngles={},hit={},batchId={}",
                        cameraAngles, clothTypeConfig.getType(), task.getBatchId());
            }
        }

        // 激活词先进行随机
        String modelTags = PromptUtils.random(ComfyUIUtils.parseJsonStr(clothTypeConfig.getTags()), promptSeed);
        if (ElementUtils.isStyleScene(sceneElement)) {

            String activateKey = StringUtils.contains(modelTags, "(linrun2111:1.3)") ? "(linrun2111:1.3), "
                    : (StringUtils.contains(modelTags, "linrun2111") ? "linrun2111, " : "");
            modelTags = activateKey + sceneElement.getExtInfo(KEY_LENS, String.class) + (StringUtils.contains(modelTags,
                    "mid shot") ? "mid shot," : "") + (StringUtils.contains(modelTags, "close shot") ? "close shot," : "");
            sceneElement.getExtInfo().remove(KEY_LENS);
        }
        modelVO.setTags(modelTags);

        JSONObject extInfo = MapUtils.isEmpty(clothTypeConfig.getExtInfo()) ? new JSONObject()
                : clothTypeConfig.getExtInfo();

        extInfo.forEach((k, v) -> {
            v = ComfyUIUtils.parseJsonStr(k, v != null ? v.toString() : null);
            extInfo.put(k, v);
        });

        // 颜色选择
        if (CollectionUtils.isNotEmpty(clothTypeConfig.getColorList())) {
            Integer colorIndex = task.getExtValue(KEY_COLOR_INDEX, Integer.class);
            if (colorIndex == null) {
                colorIndex = 0;
            }

            if (colorIndex > clothTypeConfig.getColorList().size()) {
                colorIndex = 0;
            }

            ClothColorDetail colorDetail = getColorDetail(clothTypeConfig.getColorList(), colorIndex, promptSeed);
            String extTags = colorDetail.getValue();
            if (StringUtils.isNotBlank(extTags)) {
                String randomExtTags = PromptUtils.random(ComfyUIUtils.parseJsonStr(extTags), promptSeed);
                extInfo.put(KEY_EXT_TAGS, randomExtTags);
            }

            if (modelVO.getMainType() == MainTypeEnum.MAIN) {
                String subLoraNamesStr = modelVO.getExtInfo(KEY_SUB_LORA_NAMES, String.class);
                JSONArray subLoraNames = JSONArray.parseArray(subLoraNamesStr);
                modelVO.setLoraName(subLoraNames.getString(colorDetail.getIndex() - 1));

                log.info("识别到主模型，使用对应子模型的lora，batchId={}，taskId={}，modelId={}，index={}，替换后loraName={}",
                        task.getBatchId(), task.getId(), modelVO.getId(), colorDetail.getIndex(), modelVO.getLoraName());
            }
        }

        modelVO.getExtInfo().putAll(extInfo);

        // 如果是下半身情况的话，清除模型元素中的表情等信息
        if (faceElement != null && (isLowerBodyFlow(task) || ElementUtils.isNoshowFace(sceneElement))) {
            log.info("识别到{}，删除模特特征/表情/发型/修复信息,batchId={}",
                    isLowerBodyFlow(task) ? "下半身场景" : "风格场景不展示人脸", task.getBatchId());

            if (!ElementUtils.isNoshowFace(sceneElement)) {
                String clothType = task.getExtValue(clothStyleType, String.class);
                // 模特区分男女
                String tags = "a female model. ";
                if (StringUtils.equalsIgnoreCase("male", clothType)) {
                    tags = "a male model. ";
                }

                faceElement.setTags(tags);
            }
            faceElement.getExtInfo().remove(KEY_EXPRESSION);
            faceElement.getExtInfo().remove(KEY_HAIRSTYLE);
            faceElement.getExtInfo().remove(KEY_EXT_TAGS);
        }
    }


    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        CreativeElementVO sceneElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.SCENE.name());
        CreativeElementVO faceElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());

        // 1.正/背
        boolean isBackView = isBackViewFlow(task);
        // 2.lora/换脸
        boolean isLoraFace = ElementUtils.isLoraFace(faceElement);
        // 3.纯色/非纯色
        boolean isPureBg = isPureBgScene(sceneElement);


        String repairFaceType = faceElement.getExtInfo(KEY_REPAIR_FACE_TYPE, String.class);
        boolean isNeedRepair = !StringUtils.equals(NO, repairFaceType);

        boolean isFluxRepair = isNeedRepair && !StringUtils.equals(ModelVersionEnum.SDXL.getCode(), repairFaceType);

        boolean isLoraSwapFace = isLoraFace && StringUtils.equals(YES,
                faceElement.getExtInfo(KEY_LORA_SWAP_FACE, String.class));

        Boolean enableAntiBlueLora = task.getExtValue(KEY_ENABLE_ANTI_BLUR_LORA, Boolean.class);
        boolean isAntiBlueLora = ElementUtils.isStyleScene(sceneElement) || (enableAntiBlueLora != null
                ? enableAntiBlueLora : true);

        boolean isForcePW = context.get("isForcePW") != null && (Boolean) context.get("isForcePW");

        boolean isPWModel = task.getExtInfo(KEY_ENABLE_NEW_MODEL, Boolean.class) != null && task.getExtInfo(
                KEY_ENABLE_NEW_MODEL, Boolean.class);
        isPWModel = (isLoraFace && (isPWModel || StringUtils.equals("PW", repairFaceType))) || isForcePW;

        boolean isFaceAfter = isFaceAfter(context) && isLoraFace && isFluxRepair && isNeedRepair;

        boolean reactorAndInstantId = context.get("reactorAndInstantId") != null && (Boolean) context.get(
                "reactorAndInstantId");

        CreateTestParams testParams = task.getExtValue(KEY_TEST_PARAMS, CreateTestParams.class);
        if (testParams == null) { // 非ab实验的reactorAndInstantId默认设置为true
            reactorAndInstantId = true;
        }

        boolean pulidAndInstantId = context.get("pulidAndInstantId") != null && (Boolean) context.get(
                "pulidAndInstantId");
        boolean enablePromptCorrect = context.get("enablePromptCorrect") != null && (Boolean)context.get(
            "enablePromptCorrect");

        if (pulidAndInstantId) {
            isLoraFace = false;
        }
        boolean isChildModel = ElementUtils.isChildModel(faceElement);

        // 风格场景不展示人脸时，将模特lora、换脸、修脸等都进行关闭，同时清除模特信息
        if (isNoshowFace(context, sceneElement)) {
            isLoraSwapFace = false;
            isNeedRepair = false;
            isFluxRepair = false;
            isPWModel = false;
            isFaceAfter = false;
            // 清除模特特征信息
            faceElement.setExtTags(null);
            faceElement.setTags(null);
            faceElement.addExtInfo(KEY_FACE_LORA_STRENGTH, 0);
            faceElement.getExtInfo().remove(KEY_EXPRESSION);
            faceElement.getExtInfo().remove(KEY_HAIRSTYLE);
        } else if (sceneElement != null) {
            // 兼容处理，展示人脸时将关键词下掉
            sceneElement.setTags(StringUtils.replaceIgnoreCase(sceneElement.getTags(), NOW_SHOW_FACE_PROMPT + ",", ""));
        }

        // 冗余加一份
        context.put("isBackView", isBackView);
        context.put("isLoraFace", isLoraFace);
        context.put("isPureBg", isPureBg);
        context.put("isNeedRepair", isNeedRepair);
        context.put("isFluxRepair", isFluxRepair);
        context.put("isLoraSwapFace", isLoraSwapFace);
        context.put("isAntiBlueLora", isAntiBlueLora);
        context.put("isPWModel", isPWModel);
        context.put("isFaceAfter", isFaceAfter);
        context.put("reactorAndInstantId", reactorAndInstantId);
        context.put("isPulidAndInstantId", pulidAndInstantId);
        context.put("isPromptCorrect", enablePromptCorrect);

        // 采样器实验专用 TODO 后续明确类型后废弃

        String samplerName = (String) context.get(KEY_SAMPLER_NAME);
        String scheduleName = StringUtils.equals("dpmpp_2m", samplerName) ? "sgm_uniform" : "beta";
        if (StringUtils.isBlank(samplerName)) {
            samplerName = "dpmpp_2m"; // 默认统一改为deis
            scheduleName = "beta"; // 默认统一改成beta
        } else if (StringUtils.contains(samplerName, "|")) {
            String[] split = StringUtils.split(samplerName, "|");
            AssertUtil.assertTrue(split.length == 2, "samplerName|scheduleName格式错误");
            samplerName = split[0];
            scheduleName = split[1];
        }

        context.put(KEY_SAMPLER_NAME, samplerName);
        context.put(KEY_SCHEDULE_NAME, scheduleName);

        // 4.获取对应的流程配置
        return FlowUtils.correctFlow(flow, flowKey, isBackView, isLoraFace, isPureBg, isFluxRepair, isLoraSwapFace,
                isAntiBlueLora, isPWModel, isFaceAfter, isNeedRepair, isForcePW, reactorAndInstantId, pulidAndInstantId,
            enablePromptCorrect, isChildModel);
    }


}
