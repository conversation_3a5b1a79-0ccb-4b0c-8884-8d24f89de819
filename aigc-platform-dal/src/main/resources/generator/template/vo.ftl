package ${voPackageUrl};

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
<#list entityJavaImports as im>
import ${im};
</#list>
import java.io.Serializable;

/**
 * ${entityName}VO
 *
 * @version ${entityName}Service.java v 0.1 ${createTime}
 */
@Data
public class ${entityName}VO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

<#list columns as ci>
<#if ci.column != "deleted" && ci.column != "tenant" && ci.column != "env">
	/** ${ci.comment} */
<#if ci.javaType=="Date">
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
</#if>
	@ApiModelProperty(name = "${ci.property}", value = "${ci.comment}")
	private ${ci.javaType} ${ci.property};

</#if>
</#list>
}