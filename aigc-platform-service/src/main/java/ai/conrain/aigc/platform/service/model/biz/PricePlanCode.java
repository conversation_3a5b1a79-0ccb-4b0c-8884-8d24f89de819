package ai.conrain.aigc.platform.service.model.biz;

import lombok.Getter;

@Getter
public enum PricePlanCode {

    PLAN_FLAGSHIP("PLAN_FLAGSHIP", "黄金包"),
    PLAN_PREMIUM("PLAN_PREMIUM", "白银包"),
    PLAN_BASIC("PLAN_BASIC", "青铜包"),
    PLAN_CUSTOM("PLAN_CUSTOM", "定制包"),
    PLAN_NEWBIE("PLAN_NEWBIE", "体验包");

    private String code;
    private String desc;

    PricePlanCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PricePlanCode getByCode(String code){
        for (PricePlanCode c : values()){
            if (c.getCode().equals(code)) {
                return c;
            }
        }

        return null;
    }
}