package ai.conrain.aigc.platform.service.component.creative;

import javax.imageio.ImageIO;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.EraseBrushRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MASK_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MASK_IMAGE_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE_PATH;

@Slf4j
@Service
public class EraseBrushCreativeService extends AbstractCreativeService<EraseBrushRequest> {
    @Autowired
    private OssHelper ossHelper;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private BatchFillHelper batchFillHelper;
    @Autowired
    private TairService tairService;

    @Override
    protected CreativeBatchVO buildData(EraseBrushRequest request, MaterialModelVO modelVO) throws IOException {
        CreativeBatchVO batch = new CreativeBatchVO();
        batch.setType(CreativeTypeEnum.ERASE_BRUSH);
        batch.setUserId(OperationContextHolder.getMasterUserId());
        batch.setShowImage(request.getOriginImage());
        batch.setBatchCnt(request.getImageNum());
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        batch.setExtInfo(CommonUtil.java2JSONObject(request));
        batch.setImageProportion("NONE");
        batch.setStatus(CreativeStatusEnum.QUEUE);
        batch.addExtInfo(KEY_ORIGIN_IMAGE, request.getOriginImage());
        batch.addExtInfo(KEY_MASK_IMAGE, request.getMaskImage());
        batchFillHelper.fillOriginBatchInfo(request.getOriginImage(), null, batch);

        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {

        String originImage = batch.getExtValue(KEY_ORIGIN_IMAGE, String.class);
        String maskImage = batch.getExtValue(KEY_MASK_IMAGE, String.class);
        String serverUrl = serverHelper.getFileServerUrlByUser(OperationContextHolder.getMasterUserId());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
        String path = sdf.format(new Date()) + batch.getUserId() + "/" + batch.getId();

        boolean success = ossHelper.downloadAndUploadToPath(originImage, path, OperationContextHolder.getMasterUserId(),
            serverUrl, false);

        AssertUtil.assertTrue(success, ResultCode.SYS_ERROR, "图片上传失败");

        // 处理mask，需要设置mask的alpha通道
        BufferedImage mask = null;
        try {
            mask = ossHelper.downloadToBufferedImage(maskImage);
            //设置alpha通道
            BufferedImage maskBuf = FileUtils.addAlphaToMaskImageToMask(mask);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(maskBuf, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            //上传到 comfyui
            String md5 = CommonUtil.calculateMD5(imageBytes);
            fileDispatch.uploadFile(path + "/" + FileUtils.ossImageUrlToFullFileName(maskImage),
                new ByteArrayInputStream(imageBytes), null, batch.getUserId(), false, null, md5);

        } catch (IOException e) {
            log.error("下载oss文件异常,url=" + maskImage, e);
            throw new BizException(ResultCode.SYS_ERROR, "图片上传失败");
        }

        AssertUtil.assertTrue(success, ResultCode.SYS_ERROR, "图片上传失败");

        target.addExtInfo(KEY_ORIGIN_IMAGE_PATH, path + "/" + FileUtils.ossImageUrlToFullFileName(originImage));
        target.addExtInfo(KEY_MASK_IMAGE_PATH, path + "/" + FileUtils.ossImageUrlToFullFileName(maskImage));
    }

    @Override
    protected boolean otherWithoutDeduction(EraseBrushRequest request) {
        String image = request.getOriginImage();
        return Objects.nonNull(batchFillHelper.getTaskByUrl(image));
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {

        context.put(KEY_ORIGIN_IMAGE_PATH, task.getStringFromExtInfo(KEY_ORIGIN_IMAGE_PATH));
        context.put(KEY_MASK_IMAGE_PATH, task.getStringFromExtInfo(KEY_MASK_IMAGE_PATH));
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.ERASE_BRUSH;
    }
}
