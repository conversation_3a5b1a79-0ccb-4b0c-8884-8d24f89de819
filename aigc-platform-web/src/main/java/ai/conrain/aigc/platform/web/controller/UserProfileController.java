package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * UserProfile控制器
 *
 * <AUTHOR>
 * @version UserProfileService.java v 0.1 2024-06-07 07:47:21
 */
@Slf4j
@Api("用户属性")
@Validated
@RestController
@RequestMapping("/userProfile")
public class UserProfileController {

	/** userProfileService */
	@Autowired
	private UserProfileService userProfileService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询用户属性", notes = "根据id查询用户属性[userProfile]")
	public Result<UserProfileVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(userProfileService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建用户属性", notes = "新建用户属性[userProfile]")
	public Result<Boolean> create(@Valid @RequestBody UserProfileVO userProfile){
		try {
			UserProfileVO data = userProfileService.insertOrUpdate(userProfile);
			if (data != null) {
				return Result.success(true);
			}
		} catch (Exception e) {
			log.error("添加用户属性失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户属性失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除用户属性", notes = "删除用户属性")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		userProfileService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新用户属性", notes = "修改用户属性[userProfile]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody UserProfileVO userProfile){
		userProfileService.updateByIdSelective(userProfile);
		return Result.success();
	}

	@ApiOperation(value = "批量查询用户属性", notes = "批量查询[userProfile]")
	@PostMapping("/queryList")
	public Result<List<UserProfileVO>> queryUserProfileList(@Valid @RequestBody UserProfileQuery query){
		return Result.success(userProfileService.queryUserProfileList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<UserProfileDO>]")
	public Result<PageInfo<UserProfileVO>> getUserProfileByPage(@Valid @RequestBody UserProfileQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(userProfileService.queryUserProfileByPage(query));
	}

	@PostMapping("/queryUserProfileByKey")
	@ApiOperation(value = "根据用户id和属性key查询用户属性", notes = "根据用户id和属性key查询用户属性[userProfile]")
	public Result<UserProfileVO> queryUserProfileByKey(@JsonArg @NotBlank String key) {
		return Result.success(userProfileService.selectByUidAndProfileKey(OperationContextHolder.getOperatorUserId(), key));
	}

	@PostMapping("/setUserProfileByKey")
	@ApiOperation(value = "根据用户id和key设置用户属性", notes = "根据用户id和key设置用户属性[userProfile]")
	public Result<?> setUserProfileByKey(@JsonArg @NotBlank String key, @JsonArg @NotBlank String value) {

		UserProfileVO exist = userProfileService.selectByUidAndProfileKey(OperationContextHolder.getOperatorUserId(), key);

		if (exist == null) {
			UserProfileVO userProfile = new UserProfileVO();
			userProfile.setUid(OperationContextHolder.getOperatorUserId());
			userProfile.setProfileKey(key);
			userProfile.setProfileVal(value);
			userProfileService.insert(userProfile);

		} else {
			UserProfileVO target = new UserProfileVO();
			target.setId(exist.getId());
			target.setProfileVal(value);

			userProfileService.updateByIdSelective(target);
		}

		return Result.success();
	}
}