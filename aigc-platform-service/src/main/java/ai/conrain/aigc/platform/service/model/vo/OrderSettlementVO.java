package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.OrderSettleStatusEnum;
import ai.conrain.aigc.platform.service.enums.OrderSettlementTypeEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.resolver.DefaultDecimalSerializer;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.Objects;

/**
 * OrderSettlementVO
 *
 * @version OrderSettlementService.java v 0.1 2025-05-22 03:41:06
 */
@Data
public class OrderSettlementVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 订单号 */
	@ApiModelProperty(name = "orderNo", value = "订单号")
	private String orderNo;

	/** 订单结算类型 */
	@ApiModelProperty(name = "type", value = "订单结算类型")
	private OrderSettlementTypeEnum type;

	/** 渠道商实体id */
	@ApiModelProperty(name = "distributorCorpId", value = "渠道商实体id")
	private Integer distributorCorpId;

	/** 渠道商实体名称 */
	@ApiModelProperty(name = "distributorCorpName", value = "渠道商实体名称")
	private String distributorCorpName;

	/** 结算主体类型 */
	@ApiModelProperty(name = "principalType", value = "结算主体类型")
	private PrincipalTypeEnum principalType;

	/** 结算主体 id */
	@ApiModelProperty(name = "principalId", value = "结算主体 id")
	private Integer principalId;

	/** <span style="text-decoration: line-through">状态，0初始化、1订单关闭、2待结算、</span>3结算中、4结算成功 */
	@ApiModelProperty(name = "status", value = "状态，0初始化、1订单关闭、2待结算、3结算中、4结算成功")
	private OrderSettleStatusEnum status;

	/** 结算id */
	@ApiModelProperty(name = "settleId", value = "结算id")
	private String settleId;

	/** 结算完成时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "settleTime", value = "结算完成时间")
	private Date settleTime;

	/** 总金额 */
	@ApiModelProperty(name = "totalAmount", value = "总金额")
	@JsonSerialize(using = DefaultDecimalSerializer.class)
	private BigDecimal totalAmount;

	/** <span style="text-decoration: line-through">渠道费率</span> 该结算单的费率 */
	@ApiModelProperty(name = "channelRate", value = "渠道费率")
	private BigDecimal channelRate;

	/** 结算金额 */
	@JsonSerialize(using = DefaultDecimalSerializer.class)
	@ApiModelProperty(name = "settleAmount", value = "结算金额")
	private BigDecimal settleAmount;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private JSONObject extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	// ========= view fields =========
	/** 订单商家的master user id */
	private Integer merchantId;

	/** 商家名称 */
	private String merchantName;
	
	/** 订单商家的corp id */
	private Integer merchantCorpId;

	/** 商家公司的名称 */
	private String merchantCorpName;
	
	/** 订单完成时间 */
	private Date orderFinishTime;

	public <T> T getExtInfo(String key, Class<T> clazz) {
		if (ObjectUtils.isEmpty(this.extInfo)) {
			return null;
		}
		return this.extInfo.getObject(key, clazz);
	}

	public void putExtInfo(String key, Object value) {
		if (Objects.isNull(this.extInfo)) {
			this.extInfo = new JSONObject();
		}
		this.extInfo.put(key, value);
	}
}