package ai.conrain.aigc.platform.web.controller;

import java.math.BigDecimal;
import java.util.*;

import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorEnhancedVO;
import ai.conrain.aigc.platform.service.model.biz.DistributorPrincipalBasicVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.model.query.DistributorSettlementQuery;
import ai.conrain.aigc.platform.service.component.DistributorSettlementService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import ai.conrain.aigc.platform.service.validation.SimpleDate;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DistributorSettlement控制器
 *
 * <AUTHOR>
 * @version DistributorSettlementService.java v 0.1 2025-05-22 05:39:41
 */
@Slf4j
@Api("渠道商结算明细")
@Validated
@RestController
@RequestMapping("/distributorSettlement")
public class DistributorSettlementController {

	/** distributorSettlementService */
	@Autowired
	private DistributorSettlementService distributorSettlementService;

	@Autowired
	private SystemConfigService systemConfigService;

	@Autowired
	private UserService userService;

	@GetMapping("/queryAllDistributorSettleRateConfigs")
	@ApiOperation(value = "查询服务费率", notes = "查询服务费率")
	public Result<DistributorSettleRateModel> queryAllDistributorSettleRateConfigs() {
		DistributorSettleRateModel ret = new DistributorSettleRateModel();

		//查询所有渠道商列表
		UserQuery userQuery = new UserQuery();
		userQuery.setRoleType(RoleTypeEnum.DISTRIBUTOR.getCode());
		userQuery.setUserType(UserTypeEnum.MASTER.getCode());

		List<UserVO> distributors = userService.queryUsers(userQuery);

		List<DistributorSettleRateItem> list = new ArrayList<>();

		if (CollectionUtils.isNotEmpty(distributors)) {
			for (UserVO distributor : distributors) {
				DistributorSettleRateItem item = new DistributorSettleRateItem();
				item.setCorpId(distributor.getCorpOrgId());
				item.setCorpName(distributor.getCorpName());
				item.setMasterUserId(distributor.getId());
				item.setMasterUserName(distributor.getNickName());
				item.setSettleRate(systemConfigService.querySettleRateCfgByCorpId(distributor.getCorpOrgId()));

				list.add(item);
			}
		}

		ret.setDistributorSettleRateList(list);

		return Result.success(ret);
	}

	/**
	 * @param serviceRate 配置值
	 * @param effectTime  生效时间,yyyyMMdd格式
	 * @return 成功/失败
	 * @explain 变更服务费率
	 */
	@PostMapping("/updateServiceRate")
	@ApiOperation(value = "变更服务费率", notes = "变更服务费率")
	public Result<?> updateServiceRate(@JsonArg @NotNull Integer distributorCorpId,
			@NotNull @DecimalMax(value = "1", inclusive = false) @JsonArg BigDecimal serviceRate,
			@SimpleDate @JsonArg String effectTime) {

		distributorSettlementService.setServiceRate(distributorCorpId, serviceRate, effectTime);
		return Result.success();
	}

	/**
	 * 手动结算
	 *
	 * @param outBizNo 银行流水号
	 * @return Result 结果
	 */
	@PostMapping("/manualSettle")
	@ApiOperation(value = "手动结算", notes = "手动结算")
	public Result<?> manualSettle(@JsonArg @NotNull Integer id,
								  @JsonArg @NotBlank @Length(min = 6, max = 32) String outBizNo) {
		return distributorSettlementService.manualSettle(id, outBizNo);
	}

	/**
	 * 分页条件查询商户结算明细
	 *
	 * @return PageInfo<DistributorSettlementDO>
	 * <AUTHOR>
	 * @time 2023-09-21 10:14:46
	 */
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<DistributorSettlementVO>]")
	public Result<PageInfo<DistributorSettlementVO>> queryByPage(@Valid @RequestBody DistributorSettlementQuery query) {
		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
				|| query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		if (StringUtils.isBlank(query.getOrderBy())) {
			query.setOrderBy("create_time desc");
		}
		return distributorSettlementService.queryByPage(query);
	}

	/**
	 * 修改渠道商结算配置
	 * @param request 请求
	 * @return 结果
 	 */
	@PostMapping("/modifyDistributorSettlementConfig")
	@ApiOperation(value = "修改渠道商结算配置", notes = "修改渠道商结算配置")
	public Result<?> modifyDistributorSettlementConfig(@RequestBody JSONObject request) {
		// 参数校验
		if (request == null ||
				!request.containsKey("principalId") ||
				!request.containsKey("principalType") ||
				!request.containsKey("settleConfig")) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数不完整");
		}
		
		// 提取 参数
		Integer principalId = request.getInteger("principalId");
		PrincipalTypeEnum principalType = PrincipalTypeEnum.getByCode(request.getString("principalType"));
		SettleConfigModel settleConfig = request.getObject("settleConfig", SettleConfigModel.class);
		if (ObjectUtils.isEmpty(settleConfig)) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数不完整");
		}
		if ( settleConfig.getCalculationDate() < 0 || settleConfig.getCalculationDate() > 27) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "结算日参数错误");
		}
		if (settleConfig.getHasBeneficiary()) {
			if (ObjectUtils.isEmpty(settleConfig.getBeneficiaryTargetId()) ||
					ObjectUtils.isEmpty(settleConfig.getInitYrRate()) ||
					ObjectUtils.isEmpty(settleConfig.getSubseqYrsRate())
			) {
				return Result.failedWithMessage(ResultCode.PARAM_INVALID, "抽成参数不完整");
			}
		}
		distributorSettlementService.modifyDistributorSettlementConfig(principalType, principalId, settleConfig);
		
		return Result.success();
	}

	@GetMapping("/queryAllDistributorPrincipalBasicInfo")
	@ApiOperation(value = "查询渠道商基础信息", notes = "查询渠道商基础信息")
	public Result<List<DistributorPrincipalBasicVO>> queryAllDistributorPrincipalBasicInfo() {
		return Result.success(distributorSettlementService.queryAllDistributorPrincipalBasicInfo());
	}
}