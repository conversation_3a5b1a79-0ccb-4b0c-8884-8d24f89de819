{"client_id": "${clientId}", "prompt": {"52": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "images": ["243", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "53": {"inputs": {"mask": ["52", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "54": {"inputs": {"mask": ["322", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "56": {"inputs": {"channel": "red", "image": ["324", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "60": {"inputs": {"image": ["243", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "61": {"inputs": {"image": ["764", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "159": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存tryon结果"}}, "168": {"inputs": {"padding_left": 0, "padding_right": 0, "padding_top": 0, "padding_bottom": 0, "image": ["397", 0], "mask": ["315", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "176": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]", "any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}}, "194": {"inputs": {"mask": ["315", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "211": {"inputs": {"channel": "red", "image": ["767", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "242": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["346", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "243": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["245", 0], "source": ["254", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "245": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "246": {"inputs": {"seed": ["774", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认宽"}}, "247": {"inputs": {"seed": ["775", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认高"}}, "249": {"inputs": {"value": "b/2-a/2", "a": ["320", 1], "b": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "250": {"inputs": {"value": "b/2-a/2", "a": ["320", 0], "b": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "253": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]", "any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}}, "254": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["242", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "256": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]", "any_a": ["264", 0], "any_b": ["265", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "concat的图片大小"}}, "258": {"inputs": {"a": ["314", 0], "b": ["246", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum宽"}}, "259": {"inputs": {"a": ["314", 1], "b": ["247", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum高"}}, "264": {"inputs": {"a": ["316", 0], "b": ["258", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum宽"}}, "265": {"inputs": {"a": ["316", 1], "b": ["259", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum高"}}, "275": {"inputs": {"seed": 661045452417164, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["283", 0], "positive": ["282", 0], "negative": ["282", 1], "latent_image": ["436", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "276": {"inputs": {"samples": ["275", 0], "vae": ["280", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "277": {"inputs": {"text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. ", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "278": {"inputs": {"guidance": 30, "conditioning": ["277", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"unet_name": "flux-fill-tryon-20250308.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "280": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "281": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "282": {"inputs": {"noise_mask": true, "positive": ["286", 0], "negative": ["290", 0], "vae": ["280", 0], "pixels": ["289", 1], "mask": ["289", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "283": {"inputs": {"model": ["293", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "284": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "285": {"inputs": {"crop": "center", "clip_vision": ["284", 0], "image": ["243", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "286": {"inputs": {"strength": 1, "strength_type": "multiply", "conditioning": ["278", 0], "style_model": ["287", 0], "clip_vision_output": ["285", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "287": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "288": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["792", 0], "inpainted_image": ["793", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "✂️ Inpaint <PERSON>itch"}}, "289": {"inputs": {"context_expand_pixels": 10, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 0, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 1536, "max_height": 1785, "padding": 32, "image": ["321", 0], "mask": ["56", 0], "optional_context_mask": ["297", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "✂️ Inpaint Crop"}}, "290": {"inputs": {"conditioning": ["277", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "291": {"inputs": {"width": ["61", 0], "height": ["61", 1], "x": ["60", 0], "y": 0, "image": ["288", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "293": {"inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "strength_model": 1, "model": ["279", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "297": {"inputs": {"channel": "red", "image": ["323", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "300": {"inputs": {"mask": ["351", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "306": {"inputs": {"blend_factor": 1, "feathering": 5, "target": ["397", 0], "target_bounds": ["176", 0], "source": ["291", 0]}, "class_type": "Bounded_Image_Blend_LR", "_meta": {"title": "Bounded Image Blend LR"}}, "308": {"inputs": {"output_path": ["159", 0], "filename_prefix": ["159", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["306", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "314": {"inputs": {"image": ["397", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "315": {"inputs": {"expand": 5, "tapered_corners": false, "mask": ["847", 0]}, "class_type": "GrowMask", "_meta": {"title": "替换区域扩张大小"}}, "316": {"inputs": {"image": ["168", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "319": {"inputs": {"image": ["242", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "320": {"inputs": {"image": ["254", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "321": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["243", 0], "image2": ["764", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "322": {"inputs": {"expand": 0, "tapered_corners": true, "mask": ["211", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "323": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["385", 0], "image2": ["53", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "324": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["53", 0], "image2": ["54", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "346": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["797", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "347": {"inputs": {"value": "a/2+b", "a": ["264", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "348": {"inputs": {"value": "a/2+b", "a": ["265", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "349": {"inputs": {"mask": ["461", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "350": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["349", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "351": {"inputs": {"channel": "red", "image": ["350", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "362": {"inputs": {"value": "a*2*2", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "384": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["392", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "385": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["386", 0], "source": ["384", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "386": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "392": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["300", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "397": {"inputs": {"upscale_method": "area", "scale_by": ["398", 0], "image": ["761", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "398": {"inputs": {"target_size": ["399", 0], "image": ["761", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "399": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "400": {"inputs": {"image": ["761", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "401": {"inputs": {"seed": 1785}, "class_type": "CR Seed", "_meta": {"title": "最大支持尺寸"}}, "404": {"inputs": {"image": ["462", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "405": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "406": {"inputs": {"target_size": ["405", 0], "image": ["462", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "408": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["462", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "433": {"inputs": {"text": "${imageNum}"}, "class_type": "CR Text", "_meta": {"title": "tryon生成图片张数"}}, "434": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]", "any_a": ["433", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "435": {"inputs": {"text": ["434", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "436": {"inputs": {"multiply_by": ["435", 0], "latents": ["282", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}}, "451": {"inputs": {"images": ["306", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "456": {"inputs": {"image": "${maskImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "459": {"inputs": {"width": ["404", 0], "height": ["404", 1], "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["456", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "460": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["459", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "461": {"inputs": {"channel": "red", "image": ["460", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "462": {"inputs": {"image": "${clotheImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "模特图"}}, "463": {"inputs": {"image": "${referenceImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "674": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "675": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "676": {"inputs": {"text": "", "clip": ["675", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "677": {"inputs": {"noise_mask": false, "positive": ["682", 0], "negative": ["676", 0], "vae": ["674", 0], "pixels": ["697", 0], "mask": ["693", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "678": {"inputs": {"text": "Replace the face in [IMAGE2] with the face in [IMAGE1]", "clip": ["675", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "679": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "680": {"inputs": {"lora_name": "ACE_Plus/comfyui_portrait_lora64.safetensors", "strength_model": 1, "model": ["679", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "681": {"inputs": {"model": ["680", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "682": {"inputs": {"guidance": 50, "conditioning": ["678", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "683": {"inputs": {"samples": ["685", 0], "vae": ["674", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "684": {"inputs": {"image": ["683", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "685": {"inputs": {"seed": 201421469478745, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["681", 0], "positive": ["677", 0], "negative": ["677", 1], "latent_image": ["702", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "686": {"inputs": {"width": ["698", 0], "height": ["699", 0], "x": 0, "y": 0, "image": ["683", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "687": {"inputs": {"width": ["716", 1], "height": ["716", 2], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "688": {"inputs": {"mask": ["704", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "689": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["687", 0], "source": ["709", 0], "mask": ["711", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "690": {"inputs": {"width": ["709", 1], "height": ["709", 2], "red": 0, "green": 0, "blue": 0}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "693": {"inputs": {"channel": "red", "image": ["696", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "694": {"inputs": {"image": ["697", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "695": {"inputs": {"image": ["723", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "696": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["688", 0], "image2": ["${(isNeedReplaceFace&&isUseFacePic)?then('690','688')}", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "697": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["723", 0], "image2": ["${(isNeedReplaceFace&&isUseFacePic)?then('689','723')}", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "698": {"inputs": {"value": "a*b/c", "a": ["684", 0], "b": ["695", 0], "c": ["694", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "699": {"inputs": {"value": "a*b/c", "a": ["684", 1], "b": ["695", 1], "c": ["694", 1]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "700": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "701": {"inputs": {"seg_select": "3.<PERSON>", "add_seg_index": "2,23,24,25,26", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["700", 0], "image": ["463", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "702": {"inputs": {"multiply_by": 1, "latents": ["677", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "703": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["463", 0], "mask": ["701", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "704": {"inputs": {"expand": 15, "tapered_corners": true, "mask": ["721", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "705": {"inputs": {"blend_factor": 1, "feathering": 16, "target": ["463", 0], "target_bounds": ["703", 1], "source": ["686", 0]}, "class_type": "Bounded Image Blend", "_meta": {"title": "Bounded Image Blend"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "708": {"inputs": {"seg_select": "3.<PERSON>", "add_seg_index": "2,23,24,25,26", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["700", 0], "image": ["729", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "709": {"inputs": {"width": 768, "height": 768, "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["713", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "711": {"inputs": {"expand": 10, "tapered_corners": true, "mask": ["717", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "712": {"inputs": {"seed": 128}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "713": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["729", 0], "mask": ["708", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "714": {"inputs": {"mask": ["708", 4]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "715": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["714", 0], "mask": ["708", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "716": {"inputs": {"width": 768, "height": 768, "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["715", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "717": {"inputs": {"channel": "red", "image": ["716", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "718": {"inputs": {"mask": ["701", 4]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "719": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["718", 0], "mask": ["701", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "720": {"inputs": {"width": ["722", 0], "height": ["722", 0], "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["719", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "721": {"inputs": {"channel": "red", "image": ["720", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "722": {"inputs": {"seed": 768}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "723": {"inputs": {"width": ["722", 0], "height": ["722", 0], "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["703", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "724": {"inputs": {"swap_model": "inswapper_128.onnx", "facedetection_model": "retinaface_resnet50", "face_restore_model": "GFPGANv1.4.pth", "parse_model": "parsenet"}, "class_type": "LoadConrainReactorModels", "_meta": {"title": "Load ConrainReactor Models"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "725": {"inputs": {"enabled": true, "face_restore_visibility": 0.7000000000000001, "codeformer_weight": 0.7000000000000001, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "keep_largest": "no", "input_image": ["735", 0], "swap_model": ["724", 0], "facedetection": ["724", 1], "face_restore_model": ["724", 2], "faceparse_model": ["724", 3], "source_image": ["713", 0]}, "class_type": "ConrainReActorFaceSwap", "_meta": {"title": "ConrainReactor Fast Face Swap"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "726": {"inputs": {"blend_factor": 1, "feathering": 16, "target": ["463", 0], "target_bounds": ["703", 1], "source": ["${(isUseLoraFace)?then(742,725)}", 0]}, "class_type": "Bounded Image Blend", "_meta": {"title": "Bounded Image Blend"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "729": {"inputs": {"image": "${FACE.extInfo.faceImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}, "disable": "${(isNeedReplaceFace&&isUseFacePic)?then('false','true')}"}, "731": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "732": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "Prefer GPU"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "733": {"inputs": {"text": ["741", 0], "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["736", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "734": {"inputs": {"text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["736", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "735": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 512, "seed": **************, "steps": 8, "cfg": "3", "sampler_name": "euler", "scheduler": "normal", "denoise": 0.4, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 500, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "wildcard": "", "cycle": 1, "inpaint_model": 1, "noise_mask_feather": 0, "image": ["${(isUseLoraFace)?then(742,686)}", 0], "model": ["736", 0], "clip": ["736", 1], "vae": ["736", 2], "positive": ["733", 0], "negative": ["734", 0], "bbox_detector": ["731", 0], "sam_model_opt": ["732", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "736": {"inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}, "disable": "${(isNeedReplaceFace&&!isUseLoraFace)?then('false','true')}"}, "741": {"inputs": {"prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1842}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "conrain random prompts"}}, "742": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 626530233019237, "steps": 10, "cfg": 2, "sampler_name": "euler", "scheduler": "beta", "denoise": 0.6, "feather": 3, "noise_mask": true, "force_inpaint": false, "bbox_threshold": 0.5, "bbox_dilation": 2, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": 0, "noise_mask_feather": false, "image": ["686", 0], "detailer_pipe": ["743", 0]}, "class_type": "FaceDetailerPipe", "_meta": {"title": "FaceDetailer (pipe)"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "743": {"inputs": {"wildcard": "", "Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["744", 0], "bbox_detector": ["746", 0]}, "class_type": "BasicPipeToDetailerPipe", "_meta": {"title": "BasicPipe -> DetailerPipe"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "744": {"inputs": {"model": ["750", 0], "clip": ["750", 1], "vae": ["754", 0], "positive": ["745", 0], "negative": ["747", 0]}, "class_type": "ToBasicPipe", "_meta": {"title": "ToBasicPipe"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "745": {"inputs": {"text": ["741", 0], "clip": ["750", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "修脸prompt"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "746": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "747": {"inputs": {}, "class_type": "ImpactNegativeConditioningPlaceholder", "_meta": {"title": "Negative Cond Placeholder"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "748": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "749": {"inputs": {"unet_name": "flux1-dev-fp8-e5m2.safetensors", "weight_dtype": "fp8_e5m2"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "750": {"inputs": {"lora_name": "${FACE.extInfo.faceLora}", "strength_model": "1", "strength_clip": "1", "model": ["749", 0], "clip": ["748", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "754": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}, "disable": "${(isNeedReplaceFace&&isUseLoraFace)?then('false','true')}"}, "758": {"inputs": {"text": "${isNeedReplaceFace?then('1','0')}"}, "class_type": "CR Text", "_meta": {"title": "输入图是否要换头"}}, "759": {"inputs": {"comparison": "a == b", "a": ["758", 0], "b": ["760", 0]}, "class_type": "easy compare", "_meta": {"title": "Compare"}}, "760": {"inputs": {"text": "0"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "761": {"inputs": {"boolean": ["759", 0], "on_true": ["463", 0], "on_false": ["763", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "763": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n", "any_a": ["${isNeedReplaceFace?then('726','463')}", 0], "any_b": ["463", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "764": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["397", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "765": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "766": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "767": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["194", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "774": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "775": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "779": {"inputs": {"seed": 0}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}}, "792": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b", "any_a": ["289", 0], "any_b": ["435", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "793": {"inputs": {"image": ["276", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "797": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["799", 0], "source": ["408", 0], "mask": ["461", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "799": {"inputs": {"width": ["800", 0], "height": ["800", 1], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "800": {"inputs": {"image": ["408", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "834": {"inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "Conrain_SAMModelLoader", "_meta": {"title": "Conrain SAMModelLoader"}}, "835": {"inputs": {"prompt": ["840", 0], "background": "white", "threshold": 0.3, "sam_model": ["834", 0], "grounding_dino_model": ["836", 0], "image": ["397", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "836": {"inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader", "_meta": {"title": "Conrain GroundingDinoModelLoader"}}, "837": {"inputs": {"text": "${clotheType}"}, "class_type": "CR Text", "_meta": {"title": "抠图词"}}, "838": {"inputs": {"prompt": ["853", 0], "background": "white", "threshold": 0.3, "sam_model": ["834", 0], "grounding_dino_model": ["836", 0], "image": ["397", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "839": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要替换的区域"}}, "840": {"inputs": {"text": ["839", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "842": {"inputs": {"model": "densepose_r50_fpn_dl.torchscript", "cmap": "<PERSON><PERSON><PERSON> (MagicAnimate)", "resolution": 512, "image": ["397", 0]}, "class_type": "DensePosePreprocessor", "_meta": {"title": "DensePose Estimator"}}, "843": {"inputs": {"color_list": ["844", 0], "threshold": 2, "image": ["842", 0]}, "class_type": "ConrainMaskFromColors", "_meta": {"title": "Conrain Mask From Colors"}}, "844": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "densepose提取mask区域"}}, "845": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的mask"}}, "846": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["843", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "847": {"inputs": {"masks_a": ["849", 0], "masks_b": ["848", 0]}, "class_type": "Masks Add", "_meta": {"title": "Masks Add"}}, "848": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["835", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "849": {"inputs": {"height": ["850", 1], "width": ["850", 0], "interpolation_mode": "bilinear", "mask": ["846", 0]}, "class_type": "JWMaskResize", "_meta": {"title": "Mask Resize"}}, "850": {"inputs": {"image": ["835", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "851": {"inputs": {"width": ["850", 0], "height": ["850", 1], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "852": {"inputs": {"channel": "red", "image": ["851", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "853": {"inputs": {"text": ["845", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "854": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的区域"}}, "856": {"inputs": {"boolean": ["854", 0], "on_true": ["838", 1], "on_false": ["852", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "862": {"inputs": {"mask": ["847", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "863": {"inputs": {"images": ["862", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "864": {"inputs": {"text": "${outputPath}", "text_b": "mask_${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存参看图的mask"}}, "865": {"inputs": {"output_path": ["864", 0], "filename_prefix": ["864", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["862", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "866": {"inputs": {"text": " ${outputPath}", "text_b": "face_${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存换头结果图"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "867": {"inputs": {"output_path": ["866", 0], "filename_prefix": ["866", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["726", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "868": {"inputs": {"images": ["726", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 869, "last_link_id": 1606, "nodes": [{"id": 52, "type": "APersonMaskGenerator", "pos": [-3706.53466796875, 1812.228759765625], "size": [261.10693359375, 178], "flags": {}, "order": 212, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 820}], "outputs": [{"name": "masks", "type": "MASK", "links": [109], "slot_index": 0}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "type": "MaskToImage", "pos": [-2848.5341796875, 1879.2283935546875], "size": [264.5999755859375, 26], "flags": {}, "order": 216, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [594, 596], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 54, "type": "MaskToImage", "pos": [-2852.5341796875, 2061.2265625], "size": [264.5999755859375, 26], "flags": {}, "order": 203, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [597], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 56, "type": "ImageToMask", "pos": [-1986.5281982421875, 1933.196533203125], "size": [210, 59.905555725097656], "flags": {}, "order": 223, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 598}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1434], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 60, "type": "Get Image Size", "pos": [-3917.53466796875, 2266.224853515625], "size": [298.42425537109375, 46], "flags": {}, "order": 213, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 821}], "outputs": [{"name": "width", "type": "INT", "links": [522], "slot_index": 0}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 61, "type": "Get Image Size", "pos": [-3203.9482421875, 3095.456787109375], "size": [210, 46], "flags": {}, "order": 202, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 186}], "outputs": [{"name": "width", "type": "INT", "links": [520], "slot_index": 0}, {"name": "height", "type": "INT", "links": [521], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 113, "type": "Reroute", "pos": [-4042.759765625, 1668.781982421875], "size": [75, 26], "flags": {}, "order": 199, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 819}], "outputs": [{"name": "", "type": "IMAGE", "links": [186, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 159, "type": "Text String", "pos": [-2210, 3360], "size": [228.5572052001953, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [873], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [874], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存tryon结果", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 167, "type": "Reroute", "pos": [-4409.53564453125, 1786.22900390625], "size": [75, 26], "flags": {}, "order": 211, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 441}], "outputs": [{"name": "", "type": "IMAGE", "links": [820, 821, 822, 823], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 168, "type": "Bounded Image Crop with Mask", "pos": [-7087.40234375, 1276.54248046875], "size": [248.14456176757812, 150], "flags": {}, "order": 164, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 606}, {"name": "mask", "type": "MASK", "link": 569}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [571], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [292, 1448], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 176, "type": "ConrainPythonExecutor", "pos": [-5796.40234375, 1658.54248046875], "size": [423.4119567871094, 402.7642517089844], "flags": {"collapsed": true}, "order": 182, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 566, "shape": 7}, {"name": "any_b", "type": "*", "link": 567, "shape": 7}, {"name": "any_c", "type": "*", "link": 292, "shape": 7}, {"name": "any_d", "type": "*", "link": 451, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [544, 1423, 1424], "slot_index": 0}], "title": "以涂抹区域的为中心点画一个矩形框", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "type": "MaskToImage", "pos": [-5828.779296875, 1989.6298828125], "size": [264.5999755859375, 26], "flags": {}, "order": 165, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 570}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1429], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 208, "type": "Reroute", "pos": [-4313.53564453125, 1468.2301025390625], "size": [75, 26], "flags": {}, "order": 195, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1428}], "outputs": [{"name": "", "type": "IMAGE", "links": [819], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 211, "type": "ImageToMask", "pos": [-5005.779296875, 2112.63134765625], "size": [210, 84.4366302490234], "flags": {}, "order": 196, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1432}], "outputs": [{"name": "MASK", "type": "MASK", "links": [591], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 242, "type": "Bounded Image Crop with Mask", "pos": [-6855.95703125, 2875.************], "size": [243.56057739257812, 150], "flags": {}, "order": 193, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 655}, {"name": "mask", "type": "MASK", "link": 1465}, {"name": "padding_left", "type": "INT", "link": 1456, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1457, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1458, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1459, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439, 580], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 243, "type": "ImageCompositeMasked", "pos": [-5033.61572265625, 2818.682373046875], "size": [210, 138], "flags": {}, "order": 209, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 418, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 440, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 420, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 421, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 245, "type": "EmptyImage", "pos": [-5743.5244140625, 2618.************], "size": [243.3533935546875, 102], "flags": {}, "order": 185, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 757, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 759, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [418], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215], "color": "#222", "bgcolor": "#000"}, {"id": 246, "type": "CR Seed", "pos": [-7381.40234375, 1781.54248046875], "size": [281.7162780761719, 102], "flags": {}, "order": 170, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1446, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [453], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认宽", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 247, "type": "CR Seed", "pos": [-7359.40234375, 2077.543701171875], "size": [278.3121032714844, 102], "flags": {}, "order": 171, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1451, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [455], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认高", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 249, "type": "SimpleMath+", "pos": [-5383.61572265625, 2823.682373046875], "size": [220.35072326660156, 98], "flags": {}, "order": 208, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 585, "shape": 7}, {"name": "b", "type": "*", "link": 760, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [421, 770], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 250, "type": "SimpleMath+", "pos": [-5383.61572265625, 2624.68310546875], "size": [210, 112.43743896484375], "flags": {}, "order": 207, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 584, "shape": 7}, {"name": "b", "type": "*", "link": 758, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [420, 771], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 253, "type": "ConrainPythonExecutor", "pos": [-6444.5244140625, 2623.************], "size": [282.12066650390625, 195.71939086914062], "flags": {}, "order": 201, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 481, "shape": 7}, {"name": "any_b", "type": "*", "link": 478, "shape": 7}, {"name": "any_c", "type": "*", "link": 581, "shape": 7}, {"name": "any_d", "type": "*", "link": 582, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [486, 748], "slot_index": 0}], "title": "原logo比目标尺寸大时缩小到目标尺寸", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "type": "ImageScaleBy", "pos": [-6100.5244140625, 2879.************], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 204, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}, {"name": "scale_by", "type": "FLOAT", "link": 486, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [440, 583], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 256, "type": "ConrainPythonExecutor", "pos": [-6189.40234375, 1598.54248046875], "size": [270.53582763671875, 200.77845764160156], "flags": {}, "order": 179, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 474, "shape": 7}, {"name": "any_b", "type": "*", "link": 477, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [451], "slot_index": 0}], "title": "concat的图片大小", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "type": "JWIntegerMin", "pos": [-6928.779296875, 1696.63037109375], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 172, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 564, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 453, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [473], "slot_index": 0}], "title": "Minimum宽", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 259, "type": "JWIntegerMin", "pos": [-6934.779296875, 2046.6298828125], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 173, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 565, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 455, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [476], "slot_index": 0}], "title": "Minimum高", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 264, "type": "JWIntegerMax", "pos": [-6575.779296875, 1672.630126953125], "size": [210, 71.68185424804688], "flags": {}, "order": 174, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 572, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 473, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [474, 481, 648, 675, 755, 1426, 1430], "slot_index": 0}], "title": "Maximum宽", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 265, "type": "JWIntegerMax", "pos": [-6617.779296875, 1963.6302490234375], "size": [210, 71.68185424804688], "flags": {}, "order": 175, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 573, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 476, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [477, 478, 652, 756, 1427, 1431], "slot_index": 0}], "title": "Maximum高", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 268, "type": "Note", "pos": [-6926.40234375, 1832.54248046875], "size": [210, 67.93143463134766], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模特图较小时，以模特图的大小为准"], "color": "#432", "bgcolor": "#653"}, {"id": 269, "type": "Note", "pos": [-6562.779296875, 1830.6304931640625], "size": [210, 67.93143463134766], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"], "color": "#432", "bgcolor": "#653"}, {"id": 275, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2872.509765625, 2777.515869140625], "size": [234.29580688476562, 262], "flags": {}, "order": 229, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 493, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 494, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 495, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 850, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [497], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [661045452417164, "fixed", 20, 1, "euler", "simple", 1]}, {"id": 276, "type": "VAEDecode", "pos": [-2649.509765625, 2683.51611328125], "size": [210, 46], "flags": {}, "order": 230, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 497, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 498, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1501], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 277, "type": "CLIPTextEncode", "pos": [-4101.509765625, 2744.51611328125], "size": [269.3892822265625, 89.79380798339844], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 499, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [500, 513], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "], "color": "#222", "bgcolor": "#000"}, {"id": 278, "type": "FluxGuidance", "pos": [-3845.345703125, 2850.************], "size": [210, 58], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 500, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 279, "type": "UNETLoader", "pos": [-4014.509765625, 2607.************], "size": [326.5174865722656, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [516], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux-fill-tryon-20250308.safetensors", "default"]}, {"id": 280, "type": "VAELoader", "pos": [-4432.3720703125, 2883.************], "size": [300, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [498, 503], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 281, "type": "DualCLIPLoader", "pos": [-4459.341796875, 2688.78076171875], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [499], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux"]}, {"id": 282, "type": "InpaintModelConditioning", "pos": [-3237.509765625, 2790.515869140625], "size": [210, 138], "flags": {}, "order": 227, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 501, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 502, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 503, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 1437, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1438, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [494], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [495], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [848], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 283, "type": "DifferentialDiffusion", "pos": [-3235.97265625, 2670.276123046875], "size": [184.8000030517578, 26], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [493], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 284, "type": "CLIPVisionLoader", "pos": [-4452.9453125, 3088.456787109375], "size": [370, 60], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [507], "slot_index": 0, "label": "CLIP视觉"}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "type": "CLIPVisionEncode", "pos": [-3917.685302734375, 1980.57470703125], "size": [210, 78], "flags": {}, "order": 207, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 507, "label": "CLIP视觉"}, {"name": "image", "type": "IMAGE", "link": 822, "label": "图像"}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [510], "slot_index": 0, "label": "CLIP视觉输出"}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 286, "type": "StyleModelApply", "pos": [-3552.980712890625, 1847.6009521484375], "size": [210, 122], "flags": {}, "order": 210, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 508, "label": "条件"}, {"name": "style_model", "type": "STYLE_MODEL", "link": 509, "label": "风格模型"}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 510, "shape": 7, "label": "CLIP视觉输出"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [501], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 287, "type": "StyleModelLoader", "pos": [-4441.5869140625, 3005.603271484375], "size": [340, 60], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [509], "label": "风格模型"}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "type": "InpaintStitch", "pos": [-2357.509765625, 2665.************], "size": [256.60272216796875, 78], "flags": {}, "order": 232, "mode": 0, "inputs": [{"name": "stitch", "type": "STITCH", "link": 1500, "label": "接缝"}, {"name": "inpainted_image", "type": "IMAGE", "link": 1502, "label": "图像"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1440], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "InpaintStitch"}, "widgets_values": ["bislerp"]}, {"id": 289, "type": "InpaintCrop", "pos": [-3653.509765625, 3144.515869140625], "size": [245.64613342285156, 386], "flags": {}, "order": 224, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1435, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1436, "label": "遮罩"}, {"name": "optional_context_mask", "type": "MASK", "link": 530, "shape": 7, "label": "上下文遮罩(可选)"}], "outputs": [{"name": "stitch", "type": "STITCH", "links": [1498], "slot_index": 0, "label": "接缝"}, {"name": "cropped_image", "type": "IMAGE", "links": [1437], "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [1438], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintCrop"}, "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1785, 32]}, {"id": 290, "type": "ConditioningZeroOut", "pos": [-3801.5908203125, 2779.913330078125], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 68, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 513, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [502], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 291, "type": "ImageCrop", "pos": [-2462.509765625, 2949.51611328125], "size": [210, 118], "flags": {}, "order": 233, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1440}, {"name": "width", "type": "INT", "link": 520, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 521, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 522, "widget": {"name": "x"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [551], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 293, "type": "LoraLoaderModelOnly", "pos": [-3561.509765625, 2635.************], "size": [271.6474304199219, 86.10514068603516], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 516}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 297, "type": "ImageToMask", "pos": [-2040.5343017578125, 1606.2298583984375], "size": [210, 59.905555725097656], "flags": {}, "order": 222, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 595}], "outputs": [{"name": "MASK", "type": "MASK", "links": [530], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 300, "type": "MaskToImage", "pos": [-6903.95703125, 3363.************], "size": [176.39999389648438, 26], "flags": {}, "order": 194, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1466}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [765], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 304, "type": "Reroute", "pos": [-2941.52734375, 3169.814697265625], "size": [75, 26], "flags": {}, "order": 145, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 610}], "outputs": [{"name": "", "type": "IMAGE", "links": [547], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 305, "type": "Reroute", "pos": [-2594.52734375, 3165.814697265625], "size": [75, 26], "flags": {}, "order": 187, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 544}], "outputs": [{"name": "", "type": "*", "links": [550], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 306, "type": "Bounded_Image_Blend_LR", "pos": [-2224.509765625, 3169.515869140625], "size": [239.650634765625, 122], "flags": {}, "order": 234, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 547}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 550}, {"name": "source", "type": "IMAGE", "link": 551}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555, 879], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "widgets_values": [1, 5]}, {"id": 308, "type": "ConrainImageSave", "pos": [-1860, 3280], "size": [231.75296020507812, 266], "flags": {}, "order": 235, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 555, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 873, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 874, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 314, "type": "Get Image Size", "pos": [-7429.40234375, 1354.54248046875], "size": [210, 46], "flags": {}, "order": 144, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 608}], "outputs": [{"name": "width", "type": "INT", "links": [564, 566], "slot_index": 0}, {"name": "height", "type": "INT", "links": [565, 567], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 315, "type": "GrowMask", "pos": [-7412, 1491], "size": [232.74205017089844, 84.21175384521484], "flags": {}, "order": 161, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1599}], "outputs": [{"name": "MASK", "type": "MASK", "links": [569, 570], "slot_index": 0}], "title": "替换区域扩张大小", "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [5, false], "color": "#232", "bgcolor": "#353"}, {"id": 316, "type": "Get Image Size", "pos": [-6768.779296875, 1300.628173828125], "size": [210, 46], "flags": {}, "order": 166, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"name": "width", "type": "INT", "links": [572], "slot_index": 0}, {"name": "height", "type": "INT", "links": [573], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 319, "type": "GetImageSize+", "pos": [-6613.5244140625, 2695.************], "size": [144.6750030517578, 71.8825912475586], "flags": {}, "order": 197, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 580}], "outputs": [{"name": "width", "type": "INT", "links": [581], "slot_index": 0}, {"name": "height", "type": "INT", "links": [582], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 320, "type": "GetImageSize+", "pos": [-5700.61572265625, 2958.350830078125], "size": [214.20001220703125, 66], "flags": {}, "order": 206, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 583}], "outputs": [{"name": "width", "type": "INT", "links": [584], "slot_index": 0}, {"name": "height", "type": "INT", "links": [585], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 321, "type": "easy imageConcat", "pos": [-3773.3818359375, 1401.21484375], "size": [315, 102], "flags": {}, "order": 215, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 823}, {"name": "image2", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [589, 1433, 1477], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 322, "type": "GrowMask", "pos": [-3303.79052734375, 1979.870361328125], "size": [315, 82], "flags": {}, "order": 200, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 591}], "outputs": [{"name": "MASK", "type": "MASK", "links": [592], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, true]}, {"id": 323, "type": "easy imageConcat", "pos": [-2367.5341796875, 1607.2298583984375], "size": [315, 102], "flags": {}, "order": 220, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 773}, {"name": "image2", "type": "IMAGE", "link": 594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 324, "type": "easy imageConcat", "pos": [-2452.4833984375, 1927.94970703125], "size": [315, 102], "flags": {"collapsed": false}, "order": 221, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 596}, {"name": "image2", "type": "IMAGE", "link": 597}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [598], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 328, "type": "Reroute", "pos": [-8927.5751953125, 1416.828857421875], "size": [75, 26], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 807}], "outputs": [{"name": "", "type": "IMAGE", "links": [606, 608, 610, 1425, 1598], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "Reroute", "pos": [-7856.5244140625, 2596.************], "size": [75, 26], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1512}], "outputs": [{"name": "", "type": "MASK", "links": [647, 657, 670], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Bounded Image Crop with Mask", "pos": [-7427.95703125, 2875.************], "size": [243.56057739257812, 150], "flags": {}, "order": 183, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1444}, {"name": "mask", "type": "MASK", "link": 647}, {"name": "padding_left", "type": "INT", "link": 651, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 650, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 653, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 654, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [655], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 347, "type": "SimpleMath+", "pos": [-7790.95703125, 2903.************], "size": [210, 98], "flags": {"collapsed": false}, "order": 176, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 648, "shape": 7}, {"name": "b", "type": "*", "link": 1468, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [650, 651, 659, 660], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 348, "type": "SimpleMath+", "pos": [-7779.5244140625, 3126.************], "size": [210, 98], "flags": {"collapsed": false}, "order": 180, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 652, "shape": 7}, {"name": "b", "type": "*", "link": 1469, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [653, 654, 661, 662], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 349, "type": "MaskToImage", "pos": [-7767.95703125, 3305.************], "size": [176.39999389648438, 26], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 670}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [658], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 350, "type": "Bounded Image Crop with Mask", "pos": [-7416.95703125, 3183.************], "size": [243.56057739257812, 150], "flags": {}, "order": 184, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 658}, {"name": "mask", "type": "MASK", "link": 657}, {"name": "padding_left", "type": "INT", "link": 659, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 660, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 661, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 662, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 351, "type": "ImageToMask", "pos": [-7165.5244140625, 3250.************], "size": [210, 83.63514709472656], "flags": {}, "order": 190, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 663}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1465, 1466, 1467], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 362, "type": "SimpleMath+", "pos": [-4387.9453125, 3188.456787109375], "size": [210, 98], "flags": {"collapsed": false}, "order": 177, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 675, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2*2"]}, {"id": 384, "type": "ImageScaleBy", "pos": [-6074.5244140625, 3323.************], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 205, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 766}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 385, "type": "ImageCompositeMasked", "pos": [-5057.61572265625, 3241.682373046875], "size": [210, 138], "flags": {}, "order": 210, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 749, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 768, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 771, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 770, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [773], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 386, "type": "EmptyImage", "pos": [-5703.61572265625, 3151.682373046875], "size": [243.3533935546875, 102], "flags": {}, "order": 186, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 761, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 762, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [749], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 0], "color": "#222", "bgcolor": "#000"}, {"id": 390, "type": "Reroute", "pos": [-6052.61572265625, 2591.683349609375], "size": [75, 26], "flags": {}, "order": 178, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 755, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [757, 758, 761], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 391, "type": "Reroute", "pos": [-6035.61572265625, 2681.682861328125], "size": [75, 26], "flags": {}, "order": 181, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 756, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [759, 760, 762], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 392, "type": "Bounded Image Crop with Mask", "pos": [-6666.95703125, 3247.************], "size": [243.56057739257812, 150], "flags": {}, "order": 198, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 765}, {"name": "mask", "type": "MASK", "link": 1467}, {"name": "padding_left", "type": "INT", "link": 1461, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1462, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1463, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1464, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [766], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 397, "type": "ImageScaleBy", "pos": [-9361, 1422], "size": [217.8218994140625, 125.52959442138672], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 806}, {"name": "scale_by", "type": "FLOAT", "link": 798, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 398, "type": "UpscaleSizeCalculator", "pos": [-9718, 1570], "size": [220, 118], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 805}, {"name": "target_size", "type": "INT", "link": 797, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [798], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 399, "type": "ConrainPythonExecutor", "pos": [-10318, 1679], "size": [365.79345703125, 195.28152465820312], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 800, "shape": 7}, {"name": "any_b", "type": "*", "link": 801, "shape": 7}, {"name": "any_c", "type": "*", "link": 802, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [797], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "type": "Get Image Size", "pos": [-10659, 1689], "size": [210, 46], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 804}], "outputs": [{"name": "width", "type": "INT", "links": [800], "slot_index": 0}, {"name": "height", "type": "INT", "links": [801], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 401, "type": "CR Seed", "pos": [-11164, 1650], "size": [270.7088317871094, 109.29169464111328], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [802, 811], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "最大支持尺寸", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 402, "type": "Reroute", "pos": [-11040, 1492], "size": [75, 26], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1497}], "outputs": [{"name": "", "type": "*", "links": [804, 805, 806], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 404, "type": "Get Image Size", "pos": [-11103, 2232], "size": [210, 46], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 827}], "outputs": [{"name": "width", "type": "INT", "links": [809, 895], "slot_index": 0}, {"name": "height", "type": "INT", "links": [810, 896], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 405, "type": "ConrainPythonExecutor", "pos": [-10762, 2181], "size": [353.50982666015625, 168.5362548828125], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 809, "shape": 7}, {"name": "any_b", "type": "*", "link": 810, "shape": 7}, {"name": "any_c", "type": "*", "link": 811, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "type": "UpscaleSizeCalculator", "pos": [-10343, 2431], "size": [220, 102.22442626953125], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 828}, {"name": "target_size", "type": "INT", "link": 812, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [817, 897], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 407, "type": "Reroute", "pos": [-11179, 2613], "size": [75, 26], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1257}], "outputs": [{"name": "", "type": "IMAGE", "links": [827, 828, 829], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageScaleBy", "pos": [-9932, 2298], "size": [210, 78], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 829}, {"name": "scale_by", "type": "FLOAT", "link": 817, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1507, 1515], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 433, "type": "CR Text", "pos": [-4473.509765625, 3366.515869140625], "size": [211.76846313476562, 168.80604553222656], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [846], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "tryon生成图片张数", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["${imageNum}"], "color": "#232", "bgcolor": "#353"}, {"id": 434, "type": "ConrainPythonExecutor", "pos": [-4152.9453125, 3298.456298828125], "size": [255.5079803466797, 218.5600128173828], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 846, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [847], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 435, "type": "JWStringToInteger", "pos": [-3942.9482421875, 3091.456787109375], "size": [210, 56.551239013671875], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 847, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [849, 1499], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 436, "type": "VHS_DuplicateLatents", "pos": [-3283.509765625, 2981.51611328125], "size": [260.3999938964844, 58.512535095214844], "flags": {}, "order": 228, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 848}, {"name": "multiply_by", "type": "INT", "link": 849, "widget": {"name": "multiply_by"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [850], "slot_index": 0}, {"name": "count", "type": "INT", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 451, "type": "PreviewImage", "pos": [-1945.509765625, 2759.515869140625], "size": [210, 246], "flags": {}, "order": 236, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 879}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 456, "type": "LoadImage", "pos": [-12934, 1702], "size": [315, 314], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [880], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${maskImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 457, "type": "Reroute", "pos": [-11131.5751953125, 2025.311767578125], "size": [75, 26], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 880}], "outputs": [{"name": "", "type": "IMAGE", "links": [885], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 459, "type": "ImageResize+", "pos": [-10330, 2011], "size": [315, 218], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 885}, {"name": "width", "type": "INT", "link": 895, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 896, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [889], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "type": "ImageScaleBy", "pos": [-9906, 2038], "size": [214.26881408691406, 94.9839630126953], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 889}, {"name": "scale_by", "type": "FLOAT", "link": 897, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 461, "type": "ImageToMask", "pos": [-9131, 2034], "size": [226.7721405029297, 83.94685363769531], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 890}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1506, 1512], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 462, "type": "LoadImage", "pos": [-12920, 2096], "size": [315, 314], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1257], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "title": "模特图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${clotheImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 463, "type": "LoadImage", "pos": [-12939, 1306], "size": [315, 314], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1402, 1407, 1417], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${referenceImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 674, "type": "VAELoader", "pos": [-7838, -2974], "size": [315, 58], "flags": {}, "order": 13, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1269, 1277], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 675, "type": "DualCLIPLoader", "pos": [-7850.68994140625, -3129.468017578125], "size": [315, 106], "flags": {}, "order": 14, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1266, 1272], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux"]}, {"id": 676, "type": "CLIPTextEncode", "pos": [-7407.68994140625, -2916.469970703125], "size": [425.27801513671875, 180.6060791015625], "flags": {"collapsed": true}, "order": 52, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 1266, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1268], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 677, "type": "InpaintModelConditioning", "pos": [-7035.68701171875, -2981.469482421875], "size": [302.4000244140625, 138], "flags": {}, "order": 119, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1267, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 1268, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 1269, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 1270, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1271, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1280], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [1281], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [1311], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 678, "type": "CLIPTextEncode", "pos": [-7455.80078125, -3112.712890625], "size": [379.641845703125, 94.1251449584961], "flags": {}, "order": 53, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 1272, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1275], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Replace the face in [IMAGE2] with the face in [IMAGE1]"], "color": "#222", "bgcolor": "#000"}, {"id": 679, "type": "UNETLoader", "pos": [-7814.68994140625, -3309.46728515625], "size": [315, 82], "flags": {}, "order": 15, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1273], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "fp8_e4m3fn_fast"]}, {"id": 680, "type": "LoraLoaderModelOnly", "pos": [-7469.68994140625, -3314.46728515625], "size": [315, 82], "flags": {}, "order": 54, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL", "link": 1273, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1274], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["ACE_Plus/comfyui_portrait_lora64.safetensors", 1]}, {"id": 681, "type": "DifferentialDiffusion", "pos": [-7042.68701171875, -3306.46728515625], "size": [210, 26], "flags": {}, "order": 74, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL", "link": 1274, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1279], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 682, "type": "FluxGuidance", "pos": [-7034.68701171875, -3115.46826171875], "size": [317.4000244140625, 58], "flags": {}, "order": 73, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1275, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1267], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [50]}, {"id": 683, "type": "VAEDecode", "pos": [-6247.689453125, -3218.46728515625], "size": [210, 46], "flags": {}, "order": 122, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "samples", "type": "LATENT", "link": 1276, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 1277, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1278, 1283], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 684, "type": "GetImageSize+", "pos": [-6225.689453125, -3009.************], "size": [210, 66], "flags": {}, "order": 123, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1278, "label": "图像"}], "outputs": [{"name": "width", "type": "INT", "links": [1303], "slot_index": 0, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1306], "slot_index": 1, "label": "高度"}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 685, "type": "K<PERSON><PERSON><PERSON>", "pos": [-6636.80078125, -3404.712890625], "size": [315, 474], "flags": {}, "order": 121, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL", "link": 1279, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 1280, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 1281, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 1282, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1276], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [201421469478745, "fixed", 20, 1, "euler", "normal", 1]}, {"id": 686, "type": "ImageCrop", "pos": [-5483.615234375, -3005.7666015625], "size": [315, 130], "flags": {}, "order": 126, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1283, "label": "图像"}, {"name": "width", "type": "INT", "link": 1284, "widget": {"name": "width"}, "label": "宽度"}, {"name": "height", "type": "INT", "link": 1285, "widget": {"name": "height"}, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1321, 1380, 1383], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [712, 1280, 0, 0]}, {"id": 687, "type": "Image Blank", "pos": [-8916.8369140625, -1799.7630615234375], "size": [315, 154], "flags": {}, "order": 105, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "width", "type": "INT", "link": 1286, "widget": {"name": "width"}, "label": "宽度"}, {"name": "height", "type": "INT", "link": 1287, "widget": {"name": "height"}, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1289], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 688, "type": "MaskToImage", "pos": [-8427.486328125, -2799.724853515625], "size": [176.39999389648438, 33.2907600402832], "flags": {}, "order": 112, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "mask", "type": "MASK", "link": 1288, "label": "遮罩"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1299], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 689, "type": "ImageCompositeMasked", "pos": [-8411.837890625, -1739.7625732421875], "size": [212.18775939941406, 146], "flags": {}, "order": 113, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "destination", "type": "IMAGE", "link": 1289, "label": "目标图像"}, {"name": "source", "type": "IMAGE", "link": 1290, "label": "源图像"}, {"name": "mask", "type": "MASK", "link": 1291, "shape": 7, "label": "遮罩"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1302], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 690, "type": "Image Blank", "pos": [-8947.8369140625, -2074.762939453125], "size": [315, 154], "flags": {}, "order": 98, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "width", "type": "INT", "link": 1292, "widget": {"name": "width"}, "label": "宽度"}, {"name": "height", "type": "INT", "link": 1293, "widget": {"name": "height"}, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1300], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 0, 0, 0]}, {"id": 693, "type": "ImageToMask", "pos": [-7433, -2781], "size": [315, 58], "flags": {}, "order": 117, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1296, "label": "图像"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1271], "slot_index": 0, "label": "遮罩"}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 694, "type": "GetImageSize+", "pos": [-6195.689453125, -2689.470703125], "size": [210, 66], "flags": {}, "order": 118, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1297, "label": "图像"}], "outputs": [{"name": "width", "type": "INT", "links": [1305], "slot_index": 0, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1308], "slot_index": 1, "label": "高度"}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 695, "type": "GetImageSize+", "pos": [-6227.689453125, -2863.469970703125], "size": [210, 66], "flags": {}, "order": 102, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1298, "label": "图像"}], "outputs": [{"name": "width", "type": "INT", "links": [1304], "slot_index": 0, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1307], "slot_index": 1, "label": "高度"}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 696, "type": "ImageConcanate", "pos": [-7829, -2770], "size": [315, 102], "flags": {}, "order": 115, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image1", "type": "IMAGE", "link": 1299, "label": "图像_1"}, {"name": "image2", "type": "IMAGE", "link": 1300, "label": "图像_2"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1296], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageConcanate"}, "widgets_values": ["right", true]}, {"id": 697, "type": "ImageConcanate", "pos": [-7824, -2578], "size": [315, 102], "flags": {}, "order": 116, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image1", "type": "IMAGE", "link": 1301, "label": "图像_1"}, {"name": "image2", "type": "IMAGE", "link": 1302, "label": "图像_2"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1270, 1297], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageConcanate"}, "widgets_values": ["right", true]}, {"id": 698, "type": "SimpleMath+", "pos": [-5849.689453125, -3041.************], "size": [315, 98], "flags": {}, "order": 124, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "a", "type": "*", "link": 1303, "shape": 7}, {"name": "b", "type": "*", "link": 1304, "shape": 7}, {"name": "c", "type": "*", "link": 1305, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1284], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*b/c"]}, {"id": 699, "type": "SimpleMath+", "pos": [-5875.689453125, -2847.469970703125], "size": [315, 98], "flags": {}, "order": 125, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "a", "type": "*", "link": 1306, "shape": 7}, {"name": "b", "type": "*", "link": 1307, "shape": 7}, {"name": "c", "type": "*", "link": 1308, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1285], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*b/c"]}, {"id": 700, "type": "Sapiens<PERSON><PERSON>der", "pos": [-10686.486328125, -2922.724853515625], "size": [315, 298], "flags": {}, "order": 16, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [1309, 1324], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32", 0.5, true, false, false, true, false]}, {"id": 701, "type": "SapiensSampler", "pos": [-10221, -3333], "size": [315, 258], "flags": {}, "order": 71, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1309}, {"name": "image", "type": "IMAGE", "link": 1310}], "outputs": [{"name": "seg_img", "type": "IMAGE"}, {"name": "depth_img", "type": "IMAGE"}, {"name": "normal_img", "type": "IMAGE"}, {"name": "pose_img", "type": "IMAGE"}, {"name": "mask", "type": "MASK", "links": [1313, 1344, 1346], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["3.<PERSON>", "2,23,24,25,26", false, 255, 255, 255]}, {"id": 702, "type": "VHS_DuplicateLatents", "pos": [-6857.689453125, -2743.470703125], "size": [260.3999938964844, 78], "flags": {}, "order": 120, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "latents", "type": "LATENT", "link": 1311}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1282], "slot_index": 0}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 703, "type": "Bounded Image Crop with Mask", "pos": [-9386.486328125, -3287.72412109375], "size": [279.7300720214844, 150], "flags": {}, "order": 82, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1312}, {"name": "mask", "type": "MASK", "link": 1313}, {"name": "padding_left", "type": "INT", "link": 1314, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1315, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1316, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1317, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1355], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1368], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 704, "type": "GrowMask", "pos": [-8480.486328125, -3143.72412109375], "size": [210, 96.5464859008789], "flags": {}, "order": 108, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "mask", "type": "MASK", "link": 1318, "label": "遮罩"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1288], "slot_index": 0, "label": "遮罩"}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [15, true]}, {"id": 705, "type": "Bounded Image Blend", "pos": [-5504.80078125, -3392.712890625], "size": [261.95465087890625, 156.17076110839844], "flags": {}, "order": 127, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "target", "type": "IMAGE", "link": 1319, "label": "target"}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1320, "label": "target_bounds"}, {"name": "source", "type": "IMAGE", "link": 1321, "label": "source"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "Bounded Image Blend"}, "widgets_values": [1, 16]}, {"id": 706, "type": "Reroute", "pos": [-8406.486328125, -3325.724365234375], "size": [75, 26], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1322}], "outputs": [{"name": "", "type": "IMAGE", "links": [1298, 1301]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 707, "type": "Reroute", "pos": [-10547.51171875, -3300.489501953125], "size": [75, 26], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1402}], "outputs": [{"name": "", "type": "IMAGE", "links": [1310, 1312, 1367]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 708, "type": "SapiensSampler", "pos": [-10231.8359375, -1872.7633056640625], "size": [315, 258], "flags": {}, "order": 75, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1324}, {"name": "image", "type": "IMAGE", "link": 1325}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE"}, {"name": "normal_img", "type": "IMAGE"}, {"name": "pose_img", "type": "IMAGE"}, {"name": "mask", "type": "MASK", "links": [1330, 1335, 1337], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["3.<PERSON>", "2,23,24,25,26", false, 255, 255, 255]}, {"id": 709, "type": "ImageResizeKJ", "pos": [-9403.9013671875, -2013.4605712890625], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 91, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1326, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1290], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [1292], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1293], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 710, "type": "Reroute", "pos": [-10762.5478515625, -1865.1002197265625], "size": [75, 26], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1327}], "outputs": [{"name": "", "type": "IMAGE", "links": [1325, 1329]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 711, "type": "GrowMask", "pos": [-8798.8369140625, -1524.7608642578125], "size": [210, 82], "flags": {}, "order": 109, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "mask", "type": "MASK", "link": 1328}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1291], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [10, true]}, {"id": 712, "type": "CR Seed", "pos": [-10137.486328125, -2837.724853515625], "size": [244.4950714111328, 102], "flags": {}, "order": 17, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1314, 1315, 1316, 1317, 1331, 1332, 1333, 1334, 1338, 1339, 1340, 1341, 1347, 1348, 1349, 1350], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [128, "fixed"]}, {"id": 713, "type": "Bounded Image Crop with Mask", "pos": [-9747.8359375, -2007.7633056640625], "size": [279.7300720214844, 150], "flags": {}, "order": 84, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1329}, {"name": "mask", "type": "MASK", "link": 1330}, {"name": "padding_left", "type": "INT", "link": 1331, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1332, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1333, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1334, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1326, 1363], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 714, "type": "MaskToImage", "pos": [-9872.8359375, -1754.7628173828125], "size": [176.39999389648438, 55.10683822631836], "flags": {}, "order": 85, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "mask", "type": "MASK", "link": 1335}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1336], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 715, "type": "Bounded Image Crop with Mask", "pos": [-9742.8359375, -1584.7613525390625], "size": [279.7300720214844, 150], "flags": {}, "order": 92, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1336}, {"name": "mask", "type": "MASK", "link": 1337}, {"name": "padding_left", "type": "INT", "link": 1338, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1339, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1340, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1341, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1342], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 716, "type": "ImageResizeKJ", "pos": [-9410.9013671875, -1787.4603271484375], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 99, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1342, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1343], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [1286], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1287], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 717, "type": "ImageToMask", "pos": [-9058.8369140625, -1518.760986328125], "size": [210, 81.39774322509766], "flags": {}, "order": 104, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1343}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1328], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 718, "type": "MaskToImage", "pos": [-9695.486328125, -2972.724853515625], "size": [176.39999389648438, 55.10683822631836], "flags": {}, "order": 83, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "mask", "type": "MASK", "link": 1344}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1345], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 719, "type": "Bounded Image Crop with Mask", "pos": [-9359, -2809], "size": [279.7300720214844, 150], "flags": {}, "order": 90, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1345}, {"name": "mask", "type": "MASK", "link": 1346}, {"name": "padding_left", "type": "INT", "link": 1347, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1348, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1349, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1350, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1351], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 720, "type": "ImageResizeKJ", "pos": [-8953.486328125, -2820.724853515625], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 97, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1351, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}, {"name": "width", "type": "INT", "link": 1352, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1353, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1354], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 721, "type": "ImageToMask", "pos": [-8736.486328125, -2965.724853515625], "size": [220.76467895507812, 68.50335693359375], "flags": {}, "order": 103, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1354}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1318], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 722, "type": "CR Seed", "pos": [-9352.486328125, -2996.724853515625], "size": [244.4950714111328, 102], "flags": {}, "order": 18, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1352, 1353, 1356, 1357], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [768, "fixed"]}, {"id": 723, "type": "ImageResizeKJ", "pos": [-8893.486328125, -3301.724365234375], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 88, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1355, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}, {"name": "width", "type": "INT", "link": 1356, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1357, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1322], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 724, "type": "LoadConrainReactorModels", "pos": [-3287.40966796875, -2052.783447265625], "size": [327.5999755859375, 190], "flags": {}, "order": 19, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [1359], "slot_index": 0}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [1360], "slot_index": 1}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [1361], "slot_index": 2}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [1362], "slot_index": 3}], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 725, "type": "ConrainReActorFaceSwap", "pos": [-2895.40966796875, -2074.783447265625], "size": [367.79998779296875, 370], "flags": {}, "order": 131, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [{"name": "input_image", "type": "IMAGE", "link": 1358}, {"name": "swap_model", "type": "FACE_MODEL", "link": 1359}, {"name": "facedetection", "type": "FACE_MODEL", "link": 1360}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 1361}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 1362}, {"name": "source_image", "type": "IMAGE", "link": 1363, "shape": 7}, {"name": "face_model", "type": "FACE_MODEL", "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1413], "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL"}], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, 0.7000000000000001, 0.7000000000000001, "no", "no", "0", "0", 1, "no"]}, {"id": 726, "type": "Bounded Image Blend", "pos": [-1900, -2230], "size": [210, 152.77088928222656], "flags": {}, "order": 132, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "target", "type": "IMAGE", "link": 1414}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1365}, {"name": "source", "type": "IMAGE", "link": 1413}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1404, 1605, 1606], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Blend"}, "widgets_values": [1, 16]}, {"id": 727, "type": "Reroute", "pos": [-5924.435546875, -3293.959716796875], "size": [75, 26], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1367}], "outputs": [{"name": "", "type": "IMAGE", "links": [1319, 1414], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 728, "type": "Reroute", "pos": [-5913.615234375, -3139.7666015625], "size": [75, 26], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1368}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1320, 1365]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 729, "type": "LoadImage", "pos": [-11121.80078125, -1871.8433837890625], "size": [315, 314], "flags": {}, "order": 20, "mode": "${(isNeedReplaceFace&&isUseFacePic)?then(0,4)}", "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1327], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${FACE.extInfo.faceImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 731, "type": "UltralyticsDetectorProvider", "pos": [-4559.599609375, -1403.2276611328125], "size": [315, 78], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1378], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 732, "type": "SAMLoader", "pos": [-4565.**********, -1439.**********], "size": [315, 82], "flags": {}, "order": 22, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1379], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 733, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-4224.412109375, -2063.783447265625], "size": [389.95330810546875, 157.71157836914062], "flags": {}, "order": 57, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 1369, "label": "clip"}, {"name": "text", "type": "STRING", "link": 1370, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1376], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#222", "bgcolor": "#000"}, {"id": 734, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-4251.412109375, -1793.783447265625], "size": [400, 200], "flags": {}, "order": 56, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 1371, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1377], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 735, "type": "FaceDetailer", "pos": [-3724.466796875, -2112.832763671875], "size": [350.5302734375, 902.3991088867188], "flags": {}, "order": 130, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1372, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1373, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 1374, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 1375, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 1376, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1377, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1378, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 1379, "slot_index": 7, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "slot_index": 8, "shape": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7, "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1358], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, **************, "randomize", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 736, "type": "CheckpointLoaderSimple", "pos": [-4664.412109375, -1937.783447265625], "size": [315, 98], "flags": {}, "order": 23, "mode": "${(isNeedReplaceFace&&!isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1373], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [1369, 1371, 1374], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [1375], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 737, "type": "Note", "pos": [-4093.90380859375, -1338.7359619140625], "size": [210, 91.33761596679688], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"], "color": "#322", "bgcolor": "#533"}, {"id": 738, "type": "PreviewImage", "pos": [-5534.80078125, -2724.712890625], "size": [385.619384765625, 246], "flags": {}, "order": 128, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1380}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 741, "type": "ConrainRandomPrompts", "pos": [-4642.78857421875, -2887.185546875], "size": [400, 200], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1370, 1393], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1842, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 742, "type": "FaceDetailerPipe", "pos": [-2537.783203125, -3368.916015625], "size": [346, 782], "flags": {}, "order": 129, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 1383}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 1384}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1372], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 626530233019237, "randomize", 10, 2, "euler", "beta", 0.6, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 743, "type": "BasicPipeToDetailerPipe", "pos": [-2875.78271484375, -3353.185546875], "size": [262, 204.4281768798828], "flags": {}, "order": 93, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 1385}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1386}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [1384], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 744, "type": "ToBasicPipe", "pos": [-3178.78271484375, -3375.185546875], "size": [241.79998779296875, 106], "flags": {}, "order": 86, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL", "link": 1387}, {"name": "clip", "type": "CLIP", "link": 1388}, {"name": "vae", "type": "VAE", "link": 1389}, {"name": "positive", "type": "CONDITIONING", "link": 1390}, {"name": "negative", "type": "CONDITIONING", "link": 1391}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [1385], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 745, "type": "CLIPTextEncode", "pos": [-3551.94189453125, -3229.742431640625], "size": [210, 116.85224914550781], "flags": {}, "order": 77, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 1392}, {"name": "text", "type": "STRING", "link": 1393, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1390], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A French model,clear face, blue eyes,happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression,blue eyes,"], "color": "#222", "bgcolor": "#000"}, {"id": 746, "type": "UltralyticsDetectorProvider", "pos": [-3279.78271484375, -3170.185546875], "size": [334.555114746094, 78], "flags": {}, "order": 26, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1386], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 747, "type": "ImpactNegativeConditioningPlaceholder", "pos": [-3562.94189453125, -3012.74462890625], "size": [210, 26], "flags": {}, "order": 27, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1391], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 748, "type": "DualCLIPLoader", "pos": [-4632.15869140625, -3184.414794921875], "size": [315, 106], "flags": {}, "order": 28, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1395], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 749, "type": "UNETLoader", "pos": [-4627.15869140625, -3356.4130859375], "size": [315, 82], "flags": {}, "order": 29, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1394], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8-e5m2.safetensors", "fp8_e5m2"], "color": "#494949", "bgcolor": "#353535"}, {"id": 750, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-4191.78857421875, -3367.916015625], "size": [325.4342041015625, 126.41029357910156], "flags": {}, "order": 58, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [{"name": "model", "type": "MODEL", "link": 1394}, {"name": "clip", "type": "CLIP", "link": 1395}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1396], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1388, 1392], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["${FACE.extInfo.faceLora}", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 751, "type": "UNETLoader", "pos": [-4184, -3079], "size": [380.9853515625, 82], "flags": {}, "order": 30, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1397], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 752, "type": "ModelMergeFlux1", "pos": [-3814, -3255], "size": [315, 1566], "flags": {"collapsed": true}, "order": 76, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [{"name": "model1", "type": "MODEL", "link": 1396}, {"name": "model2", "type": "MODEL", "link": 1397}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1387], "slot_index": 0}], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 754, "type": "VAELoader", "pos": [-3583.94189453125, -3379.740966796875], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 31, "mode": "${(isNeedReplaceFace&&isUseLoraFace)?then(0,4)}", "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1389], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 757, "type": "Reroute", "pos": [-12237, 1497], "size": [75, 26], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1404}], "outputs": [{"name": "", "type": "IMAGE", "links": [1416], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 758, "type": "CR Text", "pos": [-12326, 1677], "size": [210, 106.75990295410156], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1405], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "输入图是否要换头", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["${isNeedReplaceFace?then('1','0')}"], "color": "#232", "bgcolor": "#353"}, {"id": 759, "type": "easy compare", "pos": [-11988, 1537], "size": [216.3546905517578, 95.56697845458984], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1405}, {"name": "b", "type": "*", "link": 1406}], "outputs": [{"name": "boolean", "type": "BOOLEAN", "links": [1409], "slot_index": 0}], "properties": {"Node name for S&R": "easy compare"}, "widgets_values": ["a == b"]}, {"id": 760, "type": "CR Text", "pos": [-12330, 1888], "size": [210, 110.8138198852539], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1406], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["0"]}, {"id": 761, "type": "easy ifElse", "pos": [-11695, 1370], "size": [210, 80.45691680908203], "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1407}, {"name": "on_false", "type": "*", "link": 1418}, {"name": "boolean", "type": "BOOLEAN", "link": 1409, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1497], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 763, "type": "ConrainPythonExecutor", "pos": [-12126, 1445], "size": [327.3860778808594, 248.97320556640625], "flags": {"collapsed": true}, "order": 136, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1416, "shape": 7}, {"name": "any_b", "type": "*", "link": 1417, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1418], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"]}, {"id": 764, "type": "ImageCrop", "pos": [-5033.40234375, 1428.54248046875], "size": [210, 114], "flags": {}, "order": 191, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1425}, {"name": "width", "type": "INT", "link": 1426, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1427, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1419, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1420, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1428], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 765, "type": "ConrainPythonExecutor", "pos": [-5407.40234375, 1728.54248046875], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 189, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1424, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1419, 1421], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 766, "type": "ConrainPythonExecutor", "pos": [-5399.40234375, 1615.54248046875], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 188, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1423, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1420, 1422], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 767, "type": "ImageCrop", "pos": [-5071.40234375, 1869.54248046875], "size": [210, 114], "flags": {}, "order": 192, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1429}, {"name": "width", "type": "INT", "link": 1430, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1431, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1421, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1422, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1432], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 768, "type": "Reroute", "pos": [-3559.637939453125, 2989.5966796875], "size": [75, 26], "flags": {}, "order": 218, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1433}], "outputs": [{"name": "", "type": "IMAGE", "links": [1435], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 769, "type": "Reroute", "pos": [-3627.509765625, 3008.51611328125], "size": [75, 26], "flags": {}, "order": 225, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1434}], "outputs": [{"name": "", "type": "MASK", "links": [1436], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 773, "type": "Reroute", "pos": [-8257, 2543], "size": [75, 26], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1513}], "outputs": [{"name": "", "type": "IMAGE", "links": [1444], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 774, "type": "ConrainPythonExecutor", "pos": [-7831.40234375, 1775.54248046875], "size": [400, 200], "flags": {}, "order": 168, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1449, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1446], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 775, "type": "ConrainPythonExecutor", "pos": [-7833.40234375, 2109.544677734375], "size": [400, 200], "flags": {}, "order": 169, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1450, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1451], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 776, "type": "Reroute", "pos": [-7788.40234375, 1303.54248046875], "size": [75, 26], "flags": {}, "order": 167, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1448}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1449, 1450], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 779, "type": "CR Seed", "pos": [-7515.95703125, 2642.************], "size": [315, 102], "flags": {}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1455, 1460, 1468, 1469], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [0, "fixed"]}, {"id": 780, "type": "Reroute", "pos": [-7038.95703125, 2964.************], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1455, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1456, 1457, 1458, 1459], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 781, "type": "Reroute", "pos": [-7004.5244140625, 3164.************], "size": [75, 26], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1460, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1461, 1462, 1463, 1464], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 784, "type": "Note", "pos": [-7668.40234375, 1522.54248046875], "size": [210, 60], "flags": {}, "order": 35, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["扩散区域的大小，\n输入5,10,20,30"], "color": "#432", "bgcolor": "#653"}, {"id": 788, "type": "PreviewImage", "pos": [-3258.529296875, 1360.197509765625], "size": [210, 246], "flags": {}, "order": 219, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1477}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 789, "type": "Note", "pos": [-13243, 1368], "size": [210, 60], "flags": {}, "order": 36, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["参考图"], "color": "#432", "bgcolor": "#653"}, {"id": 790, "type": "Note", "pos": [-13237, 1698], "size": [210, 60], "flags": {}, "order": 37, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask"], "color": "#432", "bgcolor": "#653"}, {"id": 791, "type": "Note", "pos": [-13241, 2198], "size": [210, 60], "flags": {}, "order": 38, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装原图"], "color": "#432", "bgcolor": "#653"}, {"id": 792, "type": "ConrainPythonExecutor", "pos": [-3010.509765625, 3271.515869140625], "size": [368.42327880859375, 199.4148864746094], "flags": {}, "order": 226, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1498, "shape": 7}, {"name": "any_b", "type": "*", "link": 1499, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1500], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"]}, {"id": 793, "type": "ImpactImageBatchToImageList", "pos": [-2592.509765625, 2813.51611328125], "size": [223.97938537597656, 66.6196060180664], "flags": {}, "order": 231, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1501}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1502], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": []}, {"id": 797, "type": "ImageCompositeMasked", "pos": [-8701, 2258], "size": [315, 146], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 1510}, {"name": "source", "type": "IMAGE", "link": 1515}, {"name": "mask", "type": "MASK", "link": 1506, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1513], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 799, "type": "Image Blank", "pos": [-9106, 2315], "size": [210, 140.9718322753906], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1508, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1509, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1510], "slot_index": 0}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 800, "type": "Get Image Size", "pos": [-9441, 2317], "size": [210, 46], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1507}], "outputs": [{"name": "width", "type": "INT", "links": [1508], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1509], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 833, "type": "Reroute", "pos": [-8678, 808], "size": [75, 26], "flags": {}, "order": 146, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1598}], "outputs": [{"name": "", "type": "IMAGE", "links": [1559, 1563, 1567]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 834, "type": "Conrain_SAMModelLoader", "pos": [-8942.216796875, 373.8633117675781], "size": [269.19927978515625, 58], "flags": {}, "order": 39, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1557, 1561], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 835, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-8347.2177734375, 91.8626708984375], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 147, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 1557, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 1558, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1559, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 1560, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1579], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1575], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 836, "type": "Conrain_GroundingDinoModelLoader", "pos": [-8953.478515625, 234.54669189453125], "size": [285.80181884765625, 58], "flags": {}, "order": 40, "mode": 0, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [1558, 1562], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 837, "type": "CR Text", "pos": [-9939.216796875, 141.86248779296875], "size": [217.36741638183594, 128.27645874023438], "flags": {}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1565, 1570, 1571, 1584], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "抠图词", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["${clotheType}"], "color": "#232", "bgcolor": "#353"}, {"id": 838, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-7288.212890625, 398.86322021484375], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 148, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 1561, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 1562, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1563, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 1564, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1587], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 839, "type": "ConrainPythonExecutor", "pos": [-9543.216796875, 284.8630065917969], "size": [296.9554443359375, 286.73455810546875], "flags": {"collapsed": true}, "order": 62, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1565, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1566], "slot_index": 0}], "title": "需要替换的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 840, "type": "ShowText|pysssss", "pos": [-9191.216796875, 185.86276245117188], "size": [210, 276], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1566, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1560], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["coat. shirt. jacket"], "coat. shirt. jacket", "coat. shirt. jacket", "coat. shirt. jacket"]}, {"id": 841, "type": "Note", "pos": [-9516.216796875, 327.86328125], "size": [210, 111.4533386230469], "flags": {}, "order": 42, "mode": 0, "inputs": [], "outputs": [], "title": "抠图词", "properties": {}, "widgets_values": ["上装传入：coat. shirt. jacket\n下装：trousers\n套装：clothing\n"], "color": "#432", "bgcolor": "#653"}, {"id": 842, "type": "DensePosePreprocessor", "pos": [-8818.3984375, -115.23675537109375], "size": [315, 106], "flags": {}, "order": 149, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1567}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1568], "slot_index": 0}], "properties": {"Node name for S&R": "DensePosePreprocessor"}, "widgets_values": ["densepose_r50_fpn_dl.torchscript", "<PERSON><PERSON><PERSON> (MagicAnimate)", 512]}, {"id": 843, "type": "ConrainMaskFromColors", "pos": [-8327.2724609375, -128.33203125], "size": [228.88619995117188, 124.55016326904297], "flags": {}, "order": 152, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1568}, {"name": "color_list", "type": "STRING", "link": 1569, "widget": {"name": "color_list"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1572], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainMaskFromColors"}, "widgets_values": ["128,128,128\n255,128,0", 2]}, {"id": 844, "type": "ConrainPythonExecutor", "pos": [-9241, -15], "size": [372.4948425292969, 392.6221008300781], "flags": {"collapsed": true}, "order": 63, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1570, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1569], "slot_index": 0}], "title": "densepose提取mask区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"]}, {"id": 845, "type": "ConrainPythonExecutor", "pos": [-9536.216796875, 522.8630981445312], "size": [300.1857604980469, 284.6385192871094], "flags": {"collapsed": true}, "order": 64, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1571, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1583], "slot_index": 0}], "title": "需要排除的mask", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 846, "type": "GrowMask", "pos": [-7953.271484375, -121.3314208984375], "size": [210, 95.39423370361328], "flags": {}, "order": 154, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1572}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1576], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 847, "type": "Masks Add", "pos": [-6925.212890625, 57.86260986328125], "size": [210, 46], "flags": {}, "order": 158, "mode": 0, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1573}, {"name": "masks_b", "type": "MASK", "link": 1574}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1585], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Add"}, "widgets_values": []}, {"id": 848, "type": "GrowMask", "pos": [-7963.2177734375, 204.86279296875], "size": [210, 93.73921966552734], "flags": {}, "order": 151, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1575}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1574], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 849, "type": "JWMaskResize", "pos": [-7657.2138671875, -105.1370849609375], "size": [259.6524353027344, 135.95858764648438], "flags": {}, "order": 156, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1576}, {"name": "height", "type": "INT", "link": 1577, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 1578, "widget": {"name": "width"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1573], "slot_index": 0}], "properties": {"Node name for S&R": "JWMaskResize"}, "widgets_values": [512, 512, "bilinear"]}, {"id": 850, "type": "GetImageSize+", "pos": [-7975.2724609375, 56.96636962890625], "size": [214.20001220703125, 66], "flags": {}, "order": 150, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1579}], "outputs": [{"name": "width", "type": "INT", "links": [1578, 1580], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1577, 1581], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 851, "type": "Image Blank", "pos": [-7309.212890625, -120.13702392578125], "size": [315, 154], "flags": {}, "order": 153, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1580, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1581, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1582], "slot_index": 0}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 852, "type": "ImageToMask", "pos": [-6934.212890625, -114.1370849609375], "size": [210, 68.49308776855469], "flags": {}, "order": 155, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1582}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1588], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 853, "type": "ShowText|pysssss", "pos": [-9190.216796875, 523.8631591796875], "size": [210, 276], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1583, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1564], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["trousers"], "trousers", "trousers", "trousers"]}, {"id": 854, "type": "ConrainPythonExecutor", "pos": [-7307.212890625, 95.86260986328125], "size": [304.83380126953125, 204.48739624023438], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1584, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1589], "slot_index": 0}], "title": "需要排除的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"]}, {"id": 855, "type": "Masks Subtract", "pos": [-6638, 69], "size": [210, 46], "flags": {}, "order": 159, "mode": 4, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1585}, {"name": "masks_b", "type": "MASK", "link": 1586}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1596, 1599], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Subtract"}, "widgets_values": []}, {"id": 856, "type": "easy ifElse", "pos": [-6627.212890625, -119.13702392578125], "size": [210, 82.75738525390625], "flags": {}, "order": 157, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1587}, {"name": "on_false", "type": "*", "link": 1588}, {"name": "boolean", "type": "BOOLEAN", "link": 1589, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1586], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 862, "type": "MaskToImage", "pos": [-6478.82373046875, 165.40460205078125], "size": [188.457763671875, 48.499874114990234], "flags": {}, "order": 160, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1596}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1597, 1602], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 863, "type": "PreviewImage", "pos": [-6255.82373046875, -54.59527587890625], "size": [210, 246], "flags": {}, "order": 162, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1597}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 864, "type": "Text String", "pos": [-6870, 460], "size": [300.3023681640625, 216.65859985351562], "flags": {}, "order": 43, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1600], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1601], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存参看图的mask", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "mask_${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 865, "type": "ConrainImageSave", "pos": [-6290, 420], "size": [231.75296020507812, 266], "flags": {}, "order": 163, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1602, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1600, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1601, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 866, "type": "Text String", "pos": [-1967, -1850], "size": [300.3023681640625, 216.65859985351562], "flags": {}, "order": 44, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1603], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1604], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存换头结果图", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "face_${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 867, "type": "ConrainImageSave", "pos": [-1536, -1875], "size": [231.75296020507812, 266], "flags": {}, "order": 134, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "images", "type": "IMAGE", "link": 1605, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1603, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1604, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 868, "type": "PreviewImage", "pos": [-1542.19775390625, -2343.46875], "size": [210, 246], "flags": {}, "order": 135, "mode": "${isNeedReplaceFace?then(0,4)}", "inputs": [{"name": "images", "type": "IMAGE", "link": 1606}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 869, "type": "Note", "pos": [-9396, -66], "size": [210, 60], "flags": {}, "order": 45, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["躯干\n73,31,112\n\n手臂\n38,173,129\n70,192,110\n170,220,50\n114,208,86\n53,183,121\n92,200,99\n197,224,33\n142,214,68\n\n腿\n44,113,142\n40,124,142\n31,154,138\n32,163,133"], "color": "#432", "bgcolor": "#653"}], "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [513, 277, 0, 290, 0, "CONDITIONING"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [1257, 462, 0, 407, 0, "*"], [1266, 675, 0, 676, 0, "CLIP"], [1267, 682, 0, 677, 0, "CONDITIONING"], [1268, 676, 0, 677, 1, "CONDITIONING"], [1269, 674, 0, 677, 2, "VAE"], [1270, 697, 0, 677, 3, "IMAGE"], [1271, 693, 0, 677, 4, "MASK"], [1272, 675, 0, 678, 0, "CLIP"], [1273, 679, 0, 680, 0, "MODEL"], [1274, 680, 0, 681, 0, "MODEL"], [1275, 678, 0, 682, 0, "CONDITIONING"], [1276, 685, 0, 683, 0, "LATENT"], [1277, 674, 0, 683, 1, "VAE"], [1278, 683, 0, 684, 0, "IMAGE"], [1279, 681, 0, 685, 0, "MODEL"], [1280, 677, 0, 685, 1, "CONDITIONING"], [1281, 677, 1, 685, 2, "CONDITIONING"], [1282, 702, 0, 685, 3, "LATENT"], [1283, 683, 0, 686, 0, "IMAGE"], [1284, 698, 0, 686, 1, "INT"], [1285, 699, 0, 686, 2, "INT"], [1286, 716, 1, 687, 0, "INT"], [1287, 716, 2, 687, 1, "INT"], [1288, 704, 0, 688, 0, "MASK"], [1289, 687, 0, 689, 0, "IMAGE"], [1290, 709, 0, 689, 1, "IMAGE"], [1291, 711, 0, 689, 2, "MASK"], [1292, 709, 1, 690, 0, "INT"], [1293, 709, 2, 690, 1, "INT"], [1296, 696, 0, 693, 0, "IMAGE"], [1297, 697, 0, 694, 0, "IMAGE"], [1298, 706, 0, 695, 0, "IMAGE"], [1299, 688, 0, 696, 0, "IMAGE"], [1300, 690, 0, 696, 1, "IMAGE"], [1301, 706, 0, 697, 0, "IMAGE"], [1302, 689, 0, 697, 1, "IMAGE"], [1303, 684, 0, 698, 0, "*"], [1304, 695, 0, 698, 1, "*"], [1305, 694, 0, 698, 2, "*"], [1306, 684, 1, 699, 0, "*"], [1307, 695, 1, 699, 1, "*"], [1308, 694, 1, 699, 2, "*"], [1309, 700, 0, 701, 0, "MODEL_SAPIEN"], [1310, 707, 0, 701, 1, "IMAGE"], [1311, 677, 2, 702, 0, "LATENT"], [1312, 707, 0, 703, 0, "IMAGE"], [1313, 701, 4, 703, 1, "MASK"], [1314, 712, 0, 703, 2, "INT"], [1315, 712, 0, 703, 3, "INT"], [1316, 712, 0, 703, 4, "INT"], [1317, 712, 0, 703, 5, "INT"], [1318, 721, 0, 704, 0, "MASK"], [1319, 727, 0, 705, 0, "IMAGE"], [1320, 728, 0, 705, 1, "IMAGE_BOUNDS"], [1321, 686, 0, 705, 2, "IMAGE"], [1322, 723, 0, 706, 0, "*"], [1324, 700, 0, 708, 0, "MODEL_SAPIEN"], [1325, 710, 0, 708, 1, "IMAGE"], [1326, 713, 0, 709, 0, "IMAGE"], [1327, 729, 0, 710, 0, "*"], [1328, 717, 0, 711, 0, "MASK"], [1329, 710, 0, 713, 0, "IMAGE"], [1330, 708, 4, 713, 1, "MASK"], [1331, 712, 0, 713, 2, "INT"], [1332, 712, 0, 713, 3, "INT"], [1333, 712, 0, 713, 4, "INT"], [1334, 712, 0, 713, 5, "INT"], [1335, 708, 4, 714, 0, "MASK"], [1336, 714, 0, 715, 0, "IMAGE"], [1337, 708, 4, 715, 1, "MASK"], [1338, 712, 0, 715, 2, "INT"], [1339, 712, 0, 715, 3, "INT"], [1340, 712, 0, 715, 4, "INT"], [1341, 712, 0, 715, 5, "INT"], [1342, 715, 0, 716, 0, "IMAGE"], [1343, 716, 0, 717, 0, "IMAGE"], [1344, 701, 4, 718, 0, "MASK"], [1345, 718, 0, 719, 0, "IMAGE"], [1346, 701, 4, 719, 1, "MASK"], [1347, 712, 0, 719, 2, "INT"], [1348, 712, 0, 719, 3, "INT"], [1349, 712, 0, 719, 4, "INT"], [1350, 712, 0, 719, 5, "INT"], [1351, 719, 0, 720, 0, "IMAGE"], [1352, 722, 0, 720, 4, "INT"], [1353, 722, 0, 720, 5, "INT"], [1354, 720, 0, 721, 0, "IMAGE"], [1355, 703, 0, 723, 0, "IMAGE"], [1356, 722, 0, 723, 4, "INT"], [1357, 722, 0, 723, 5, "INT"], [1358, 735, 0, 725, 0, "IMAGE"], [1359, 724, 0, 725, 1, "FACE_MODEL"], [1360, 724, 1, 725, 2, "FACE_MODEL"], [1361, 724, 2, 725, 3, "FACE_MODEL"], [1362, 724, 3, 725, 4, "FACE_MODEL"], [1363, 713, 0, 725, 5, "IMAGE"], [1365, 728, 0, 726, 1, "IMAGE_BOUNDS"], [1367, 707, 0, 727, 0, "*"], [1368, 703, 1, 728, 0, "*"], [1369, 736, 1, 733, 0, "CLIP"], [1370, 741, 0, 733, 1, "STRING"], [1371, 736, 1, 734, 0, "CLIP"], [1372, 742, 0, 735, 0, "IMAGE"], [1373, 736, 0, 735, 1, "MODEL"], [1374, 736, 1, 735, 2, "CLIP"], [1375, 736, 2, 735, 3, "VAE"], [1376, 733, 0, 735, 4, "CONDITIONING"], [1377, 734, 0, 735, 5, "CONDITIONING"], [1378, 731, 0, 735, 6, "BBOX_DETECTOR"], [1379, 732, 0, 735, 7, "SAM_MODEL"], [1380, 686, 0, 738, 0, "IMAGE"], [1383, 686, 0, 742, 0, "IMAGE"], [1384, 743, 0, 742, 1, "DETAILER_PIPE"], [1385, 744, 0, 743, 0, "BASIC_PIPE"], [1386, 746, 0, 743, 1, "BBOX_DETECTOR"], [1387, 752, 0, 744, 0, "MODEL"], [1388, 750, 1, 744, 1, "CLIP"], [1389, 754, 0, 744, 2, "VAE"], [1390, 745, 0, 744, 3, "CONDITIONING"], [1391, 747, 0, 744, 4, "CONDITIONING"], [1392, 750, 1, 745, 0, "CLIP"], [1393, 741, 0, 745, 1, "STRING"], [1394, 749, 0, 750, 0, "MODEL"], [1395, 748, 0, 750, 1, "CLIP"], [1396, 750, 0, 752, 0, "MODEL"], [1397, 751, 0, 752, 1, "MODEL"], [1402, 463, 0, 707, 0, "*"], [1404, 726, 0, 757, 0, "*"], [1405, 758, 0, 759, 0, "*"], [1406, 760, 0, 759, 1, "*"], [1407, 463, 0, 761, 0, "*"], [1409, 759, 0, 761, 2, "BOOLEAN"], [1413, 725, 0, 726, 2, "IMAGE"], [1414, 727, 0, 726, 0, "IMAGE"], [1416, 757, 0, 763, 0, "*"], [1417, 463, 0, 763, 1, "*"], [1418, 763, 0, 761, 1, "*"], [1419, 765, 0, 764, 3, "INT"], [1420, 766, 0, 764, 4, "INT"], [1421, 765, 0, 767, 3, "INT"], [1422, 766, 0, 767, 4, "INT"], [1423, 176, 0, 766, 0, "*"], [1424, 176, 0, 765, 0, "*"], [1425, 328, 0, 764, 0, "IMAGE"], [1426, 264, 0, 764, 1, "INT"], [1427, 265, 0, 764, 2, "INT"], [1428, 764, 0, 208, 0, "*"], [1429, 194, 0, 767, 0, "IMAGE"], [1430, 264, 0, 767, 1, "INT"], [1431, 265, 0, 767, 2, "INT"], [1432, 767, 0, 211, 0, "IMAGE"], [1433, 321, 0, 768, 0, "*"], [1434, 56, 0, 769, 0, "*"], [1435, 768, 0, 289, 0, "IMAGE"], [1436, 769, 0, 289, 1, "MASK"], [1437, 289, 1, 282, 3, "IMAGE"], [1438, 289, 2, 282, 4, "MASK"], [1440, 288, 0, 291, 0, "IMAGE"], [1444, 773, 0, 346, 0, "IMAGE"], [1446, 774, 0, 246, 0, "INT"], [1448, 168, 1, 776, 0, "*"], [1449, 776, 0, 774, 0, "*"], [1450, 776, 0, 775, 0, "*"], [1451, 775, 0, 247, 0, "INT"], [1455, 779, 0, 780, 0, "*"], [1456, 780, 0, 242, 2, "INT"], [1457, 780, 0, 242, 3, "INT"], [1458, 780, 0, 242, 4, "INT"], [1459, 780, 0, 242, 5, "INT"], [1460, 779, 0, 781, 0, "*"], [1461, 781, 0, 392, 2, "INT"], [1462, 781, 0, 392, 3, "INT"], [1463, 781, 0, 392, 4, "INT"], [1464, 781, 0, 392, 5, "INT"], [1465, 351, 0, 242, 1, "MASK"], [1466, 351, 0, 300, 0, "MASK"], [1467, 351, 0, 392, 1, "MASK"], [1468, 779, 0, 347, 1, "*"], [1469, 779, 0, 348, 1, "*"], [1477, 321, 0, 788, 0, "IMAGE"], [1497, 761, 0, 402, 0, "*"], [1498, 289, 0, 792, 0, "*"], [1499, 435, 0, 792, 1, "*"], [1500, 792, 0, 288, 0, "STITCH"], [1501, 276, 0, 793, 0, "IMAGE"], [1502, 793, 0, 288, 1, "IMAGE"], [1506, 461, 0, 797, 2, "MASK"], [1507, 408, 0, 800, 0, "IMAGE"], [1508, 800, 0, 799, 0, "INT"], [1509, 800, 1, 799, 1, "INT"], [1510, 799, 0, 797, 0, "IMAGE"], [1512, 461, 0, 331, 0, "*"], [1513, 797, 0, 773, 0, "*"], [1515, 408, 0, 797, 1, "IMAGE"], [1557, 834, 0, 835, 0, "SAM_MODEL"], [1558, 836, 0, 835, 1, "GROUNDING_DINO_MODEL"], [1559, 833, 0, 835, 2, "IMAGE"], [1560, 840, 0, 835, 3, "STRING"], [1561, 834, 0, 838, 0, "SAM_MODEL"], [1562, 836, 0, 838, 1, "GROUNDING_DINO_MODEL"], [1563, 833, 0, 838, 2, "IMAGE"], [1564, 853, 0, 838, 3, "STRING"], [1565, 837, 0, 839, 0, "*"], [1566, 839, 0, 840, 0, "STRING"], [1567, 833, 0, 842, 0, "IMAGE"], [1568, 842, 0, 843, 0, "IMAGE"], [1569, 844, 0, 843, 1, "STRING"], [1570, 837, 0, 844, 0, "*"], [1571, 837, 0, 845, 0, "*"], [1572, 843, 0, 846, 0, "MASK"], [1573, 849, 0, 847, 0, "MASK"], [1574, 848, 0, 847, 1, "MASK"], [1575, 835, 1, 848, 0, "MASK"], [1576, 846, 0, 849, 0, "MASK"], [1577, 850, 1, 849, 1, "INT"], [1578, 850, 0, 849, 2, "INT"], [1579, 835, 0, 850, 0, "IMAGE"], [1580, 850, 0, 851, 0, "INT"], [1581, 850, 1, 851, 1, "INT"], [1582, 851, 0, 852, 0, "IMAGE"], [1583, 845, 0, 853, 0, "STRING"], [1584, 837, 0, 854, 0, "*"], [1585, 847, 0, 855, 0, "MASK"], [1586, 856, 0, 855, 1, "MASK"], [1587, 838, 1, 856, 0, "*"], [1588, 852, 0, 856, 1, "*"], [1589, 854, 0, 856, 2, "BOOLEAN"], [1596, 855, 0, 862, 0, "MASK"], [1597, 862, 0, 863, 0, "IMAGE"], [1598, 328, 0, 833, 0, "*"], [1599, 855, 0, 315, 0, "MASK"], [1600, 864, 0, 865, 1, "STRING"], [1601, 864, 1, 865, 2, "STRING"], [1602, 862, 0, 865, 0, "IMAGE"], [1603, 866, 0, 867, 1, "STRING"], [1604, 866, 1, 867, 2, "STRING"], [1605, 726, 0, 867, 0, "IMAGE"], [1606, 726, 0, 868, 0, "IMAGE"]], "groups": [{"id": 2, "title": "cat图片准备", "bounding": [-4528.39404296875, 1173.5872802734375, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "处理服装图mask", "bounding": [-7910.73583984375, 2524.262939453125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "处理参考图mask", "bounding": [-7866.99169921875, 1201.8798828125, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "重绘", "bounding": [-4531.9833984375, 2540.28515625, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "图片尺寸最大2048", "bounding": [-11264.9794921875, 1178.438232421875, 3091.599609375, 1692.7294921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 20, "title": "Group", "bounding": [-11123.78125, -3475.556640625, 2901.240966796875, 1043.7391357421875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 21, "title": "Group", "bounding": [-11158.2841796875, -2228.6494140625, 3006.705078125, 836.988525390625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 23, "title": "Group", "bounding": [-7929.5029296875, -3474.36767578125, 2831.52978515625, 1073.604736328125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 24, "title": "Group", "bounding": [-4693.18310546875, -2350.241455078125, 2645.1259765625, 1180.4041748046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 25, "title": "Group", "bounding": [-4746.91357421875, -3526.671875, 2616.3447265625, 992.4853515625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 26, "title": "Group", "bounding": [-13011.990234375, 1207.5235595703125, 1616.3167724609375, 1283.9755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 27, "title": "处理参考图的mask", "bounding": [-10034.3876953125, -235.75079345703125, 4049.959228515625, 1155.1162109375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.37974983358324127, "offset": [12001.30657798046, 3512.4097820870147]}}, "version": 0.4, "widget_idx_map": {"275": {"seed": 0, "sampler_name": 4, "scheduler": 5}, "401": {"seed": 0}, "685": {"seed": 0, "sampler_name": 4, "scheduler": 5}, "712": {"seed": 0}, "722": {"seed": 0}, "735": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "741": {"seed": 1}, "742": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "779": {"seed": 0}}, "seed_widgets": {"275": 0, "401": 0, "685": 0, "712": 0, "722": 0, "735": 3, "741": 1, "742": 3, "779": 0}}}}}