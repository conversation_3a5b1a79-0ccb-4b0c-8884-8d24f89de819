package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.enums.SalesTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;

@Slf4j
@Component
public class DistributorServiceImpl implements DistributorService {

    @Autowired
    private UserService userService;

    @Autowired
    private DistributorCustomerService distributorCustomerService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private UserOrganizationService userOrganizationService;

    @Autowired
    private DistributorSettlementService distributorSettlementService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteCustomer(Integer userId) {

        DistributorCustomerQuery dcq = new DistributorCustomerQuery();
        if (OperationContextHolder.isDistributorRole()) {
            dcq.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());
        }
        dcq.setCustomerMasterUserId(userId);
        List<DistributorCustomerVO> dcList = distributorCustomerService.queryDistributorCustomerList(dcq);
        if (CollectionUtils.isNotEmpty(dcList)) {
            for (DistributorCustomerVO dc : dcList) {
                distributorCustomerService.deleteById(dc.getId());
            }
        }
    }

    @Override
    public DistributorCustomerVO queryDistributorInfoByMerchantId(Integer merchantId) {
        AssertUtil.assertNotNull(merchantId, "merchantId is null");

        DistributorCustomerQuery dcq = new DistributorCustomerQuery();
        dcq.setCustomerMasterUserId(merchantId);

        List<DistributorCustomerVO> list = distributorCustomerService.queryDistributorCustomerList(dcq);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        DistributorCustomerVO dc = list.get(0);

        if (dc.getDistributorSalesUserId() != null) {
            UserVO sales = userService.selectById(dc.getDistributorSalesUserId());
            dc.setRelatedDistributorSalesName(sales.getNickName());
        }

        return dc;
    }

    @Override
    public String getSalesInfoByMerchantId(Integer userId){

        if (userId != null) {
            try {
                DistributorCustomerVO salesInfo = this.queryDistributorInfoByMerchantId(userId);
                if (salesInfo != null) {
                    String distCorpName = salesInfo.getDistributorCorpName();
                    String salesName = salesInfo.getRelatedDistributorSalesName();
                    if (StringUtils.isNotBlank(distCorpName) && StringUtils.isNotBlank(salesName)) {
                        return String.format("%s@%s", salesName, distCorpName);
                    }

                    if (StringUtils.isNotBlank(distCorpName)) {
                        return distCorpName;
                    }
                }
            } catch (Exception e){
                log.error("getSalesInfoByMerchantId异常，忽略，关联销售将显示为'无'", e);
            }
        }

        return "无";
    }

    @Override
    public boolean isOwnDistributorCustomer(Integer merchantId) {
        if (merchantId == null) {
            return false;
        }

        List<DistributorCustomerVO> userList = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(
            false);
        DistributorCustomerVO customerVO = (DistributorCustomerVO)userList;

        if (CollectionUtils.isEmpty(userList)) {
            return false;
        }

        return userList.stream().anyMatch(dc -> dc.getCustomerMasterUserId().equals(merchantId));
    }

    @Override
    public List<UserVO> queryAllStaffListByCurrentUser() {
        return this.queryAllStaffListByCurrentUserAndRoles(CustomRoleEnum.values());
    }

    @Override
    public List<UserVO> queryAllStaffListByCurrentUserAndRoles(CustomRoleEnum... customRoleEnums) {

        AssertUtil.assertTrue(customRoleEnums != null && customRoleEnums.length > 0, "customRoleEnums is empty");

        CustomRoleEnum customRole = OperationContextHolder.getDistributorCustomRole();
        Integer orgId = OperationContextHolder.getCorpOrgId();

        List<Integer> userIds = organizationService.getUserIdsOfSubOrgIdsByOrgId(orgId);

        if (CollectionUtils.isNotEmpty(userIds)) {
            UserQuery userQuery = new UserQuery();
            userQuery.setRoleType(DISTRIBUTOR.getCode());
            userQuery.setCustomRoleList(Arrays.stream(customRoleEnums).map(CustomRoleEnum::getCode)
                .collect(Collectors.toList()));
            userQuery.setIds(userIds);
            return userService.queryUsers(userQuery);
        }

        return new ArrayList<>();
    }

    @Override
    public List<UserVO> queryAllCustomersByDistributorMasterId(Integer distributorMasterId) {
        AssertUtil.assertNotNull(distributorMasterId, "distributorMasterId is null");

        DistributorCustomerQuery dcq = new DistributorCustomerQuery();
        dcq.setDistributorMasterUserId(distributorMasterId);

        List<DistributorCustomerVO> list = distributorCustomerService.queryDistributorCustomerList(dcq);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Integer> userIds = list.stream().map(DistributorCustomerVO::getCustomerMasterUserId).distinct().collect(
            Collectors.toList());

        return userService.batchQueryById(userIds);
    }

    @Override
    public void querySettlePrincipalExclusive(List<PrincipalModel> results, OrganizationVO org, CustomRoleEnum... exceptCustomRoles) {
        SalesTypeEnum salesType = org.getSalesType();
        // 如果不是直营, 会直接结算给当前渠道, 结算主体是组织
        if (!SalesTypeEnum.isDirect(salesType)) {
            PrincipalTypeEnum principalType = org.getRoot() ? PrincipalTypeEnum.CORP : PrincipalTypeEnum.SUB_CORP;
            results.add(PrincipalModel.of(principalType, org.getId(), org.getName()));
            return;
        }
        // 如果是直营, 结算主体是用户
        // 根据 org 查询直接关联的账号, 这一步查询出渠道下的主账号和一级运营 和 一级销售 (排除指定角色)
        List<Integer> users = new ArrayList<>();
        users.addAll(CommonUtil.listConverter(userOrganizationService.selectByOrgId(org.getId()), UserOrganizationVO::getUserId));
        if (CollectionUtils.isNotEmpty(users)) {
            UserQuery query = new UserQuery();
            query.setIds(users);
            query.setExceptCustomRoleList(CommonUtil.listConverter(Arrays.asList(exceptCustomRoles), CustomRoleEnum::getCode));
            query.setStatus(UserStatusEnum.ENABLED.getCode());
            List<UserVO> userVOs = userService.queryUsers(query);
            for (UserVO user : userVOs) {
                results.add(PrincipalModel.of(PrincipalTypeEnum.USER, user.getId(), user.getNickName()));
            }
        }
        // 递归处理子渠道
        List<OrganizationVO> subOrgs = organizationService.querySubOrg(org.getId());
        for (OrganizationVO subOrg : subOrgs) {
            querySettlePrincipalExclusive(results, subOrg, exceptCustomRoles);
        }
    }

    @Override
    public List<Integer> getPrincipalRelatedMerchants(PrincipalModel principal) {
        DistributorSettleConfigVO settleConfig = distributorSettlementService.queryDistributorSettleConfig(principal);
        if (ObjectUtils.isEmpty(settleConfig)) {
            log.error("当前销售渠道 结算配置 为空");
            return new ArrayList<>();
        }
        DistributorCustomerQuery query = new DistributorCustomerQuery();
        query.setDistributorCorpOrgId(settleConfig.getMasterCorpId());
        // 如果是直营渠道, 根据关联的 distributor_sales_user_id 查询
        if (SalesTypeEnum.isDirect(settleConfig.getSalesType())) {
            if (PrincipalTypeEnum.CORP.equals(principal.getType())) {
                query.setDistributorSalesUserId(settleConfig.getChannelAdminId());
            } else if (PrincipalTypeEnum.SUB_CORP.equals(principal.getType())) {
                // 查询子渠道下的所有商家
                List<UserOrganizationVO> subCorpStuff = userOrganizationService.findAllByOrgIds(Collections.singletonList(principal.getId()));
                if (CollectionUtils.isNotEmpty(subCorpStuff)) {
                    query.setDistributorSalesUserIds(CommonUtil.listConverter(subCorpStuff, UserOrganizationVO::getUserId));
                }
            } else {
                query.setDistributorSalesUserId(principal.getId());
            }
        }
        // 如果是外部渠道, 直接查询该渠道下的所有商家
        List<DistributorCustomerVO> distributorCustomerList = distributorCustomerService.queryDistributorCustomerList(query);
        return CommonUtil.listConverter(distributorCustomerList, DistributorCustomerVO::getCustomerMasterUserId);
    }
}
