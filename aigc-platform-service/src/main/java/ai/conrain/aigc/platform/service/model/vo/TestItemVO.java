package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

/**
 * TestItemVO
 *
 * @version TestItemService.java v 0.1 2024-12-19 01:24:06
 */
@Data
public class TestItemVO implements Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 测试计划id */
    @ApiModelProperty(name = "planId", value = "测试计划id")
    private Integer planId;

    /** 类型，TRAIN、CREATIVE */
    @ApiModelProperty(name = "type", value = "类型，TRAIN、CREATIVE")
    private String type;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    @ApiModelProperty(name = "status", value = "状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED")
    private TestStatusEnum status;

    /** 前置项目id */
    @ApiModelProperty(name = "preId", value = "前置项目id")
    private Integer preId;

    /** 前置项目状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    @ApiModelProperty(name = "preStatus", value = "前置项目状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED")
    private TestStatusEnum preStatus;

    /** 轮数 */
    @ApiModelProperty(name = "roundsNum", value = "轮数")
    private Integer roundsNum;

    /** 操作者id */
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    @ApiModelProperty(name = "conclusion", value = "结论")
    private String conclusion;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 共用的参数信息 */
    @ApiModelProperty(name = "sharedParams", value = "共用的参数信息")
    private JSONObject sharedParams;

    @ApiModelProperty(name = "comparisonParams", value = "比对参数列表")
    private List<JSONObject> comparisonParams;

    @ApiModelProperty(name = "groups", value = "测试分组列表")
    private List<TestItemGroupVO> groups;

    /**
     * 实验项名称
     */
    @ApiModelProperty(name = "name", value = "实验项名称")
    private String name;

    public String getStatusName() {
        return status != null ? status.getDesc() : null;
    }

}