"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[628],{38081:function(K,j,n){n.r(j),n.d(j,{default:function(){return F}});var P=n(48305),I=n.n(P),b=n(26068),h=n.n(b),O=n(39953),C=n(52649),A=n(34495),B=n(43073),D=n(38434),U=n(75209),k=n(75271),e=n(52676),f={modalTitle:{borderBottom:"1px solid #f0f0f0",margin:"0 -24px",padding:"0 24px 16px"},titleText:{fontSize:16,fontWeight:500},footer:{display:"flex",justifyContent:"flex-end",alignItems:"center",gap:8},selectAllButton:{display:"flex",alignItems:"center",gap:4,backgroundColor:"#52c41a",borderColor:"#52c41a"},contentWrapper:{maxHeight:"70vh",overflow:"auto",margin:"0 -8px",padding:"0 8px"}},L=function(_){var o=_.file,l=_.imgFile,i=_.onToggleSelect,v=_.handlePreviewUrl,p="".concat(o.fileDir,"/").concat(o.fileName),x=function(m){if(m.target instanceof HTMLImageElement){v((l==null?void 0:l.url)||o.imgUrl);return}i(p,!(l!=null&&l.isTrainingMaterial))},M=function(m){i(p,m.target.checked),m.stopPropagation()};return(0,e.jsx)(O.Z,{span:4,children:(0,e.jsxs)("div",{className:"label-ret-item",style:{position:"relative",border:"1px solid ".concat(l!=null&&l.isTrainingMaterial?"#b7eb8f":"#f0f0f0"),borderRadius:"6px",padding:"4px",backgroundColor:l!=null&&l.isTrainingMaterial?"#f6ffed":"white",transition:"all 0.3s",cursor:"pointer"},onClick:x,children:[(0,e.jsx)(C.Z,{checked:l==null?void 0:l.isTrainingMaterial,style:{position:"absolute",top:8,right:8,zIndex:1},onChange:M}),(0,e.jsx)("div",{style:{width:"100%",height:160,overflow:"hidden",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#fafafa",borderRadius:"4px",marginBottom:4},children:(0,e.jsx)("img",{src:(l==null?void 0:l.url)||o.imgUrl,alt:o.fileName,style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain"}})}),(0,e.jsx)(A.Z,{title:o.fileName,children:(0,e.jsx)("div",{style:{fontSize:12,color:"#666",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",padding:"0 4px"},children:o.fileName})})]})})};function F(a){var _=a.open,o=a.onCancel,l=a.onConfirm,i=a.labelImgFiles,v=a.setLabelImgFiles,p=a.selectedTrainModelDetail,x=a.groupedLabelFiles,M=a.handlePreviewUrl,T=a.getLabelRetDirShowName,m=function(u,s){var t=new Map(i),d=t.get(u);d&&(t.set(u,h()(h()({},d),{},{isTrainingMaterial:s})),v(t))},S=function(){var u=Array.from(i.values()).filter(function(t){return!t.deleted}).every(function(t){return t.isTrainingMaterial===!0}),s=new Map(i);s.forEach(function(t,d){t.deleted||s.set(d,h()(h()({},t),{},{isTrainingMaterial:!u}))}),v(s)},y=Array.from(i.values()).filter(function(r){return!r.deleted}).every(function(r){return r.isTrainingMaterial===!0}),W=Array.from(i.values()).filter(function(r){return!r.deleted}).some(function(r){return r.isTrainingMaterial===!0})&&!y;return(0,e.jsx)(B.Z,{title:(0,e.jsx)("div",{style:f.modalTitle,children:(0,e.jsx)("div",{style:f.titleText,children:"\u8BF7\u9009\u62E9\u9700\u8981\u4FDD\u7559mask\u4FE1\u606F\u5E76\u540C\u6B65\u8BB0\u5F55\u7684\u56FE\u7247"})}),open:_,onCancel:o,width:1200,centered:!0,footer:(0,e.jsxs)("div",{style:f.footer,children:[(0,e.jsx)(D.ZP,{onClick:o,children:"\u53D6\u6D88"}),(0,e.jsx)(D.ZP,{type:"primary",style:f.selectAllButton,onClick:S,children:(0,e.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:4,color:"#fff",cursor:"pointer"},children:[(0,e.jsx)(C.Z,{checked:y,indeterminate:W,style:{color:"#fff"}}),(0,e.jsx)("span",{children:"\u5168\u9009"})]})}),(0,e.jsx)(D.ZP,{type:"primary",onClick:l,children:"\u786E\u5B9A"})]}),children:(0,e.jsx)("div",{style:f.contentWrapper,children:p&&i.size>0&&x&&Array.from(x.entries()).map(function(r){var u=I()(r,2),s=u[0],t=u[1],d=t.filter(function(c){if(c.type==="img"){var g,E=c.fileDir+"/"+c.fileName;return i.has(E)&&!((g=i.get(E))!==null&&g!==void 0&&g.deleted)}return!1});return d.length===0?null:(0,e.jsxs)("div",{style:{marginBottom:24},children:[(0,e.jsxs)("div",{style:{fontSize:14,fontWeight:500,color:"#1f1f1f",marginBottom:16,display:"flex",alignItems:"center",gap:8},children:[(0,e.jsx)("span",{children:T(s)}),(0,e.jsxs)("span",{style:{color:"#8c8c8c",fontSize:12,fontWeight:"normal"},children:["(",d.length,"\u5F20\u56FE\u7247)"]})]}),(0,e.jsx)(U.Z,{gutter:[12,12],children:d.map(function(c,g){var E=c.fileDir+"/"+c.fileName,R=i.get(E);return(0,e.jsx)(L,{file:c,imgFile:R,onToggleSelect:m,handlePreviewUrl:M},g)})})]},s)})})})}}}]);
