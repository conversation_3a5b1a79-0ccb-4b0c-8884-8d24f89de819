package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * PrincipalInfoQuery
 *
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
@Data
public class PrincipalInfoQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 主体类型 */
    @ApiModelProperty(name = "principalType", value = "主体类型")
    private String principalType;

    /** 关联的用户id */
    @ApiModelProperty(name = "principalId", value = "关联的用户id")
    private Integer principalId;

    /** key */
    @ApiModelProperty(name = "infoKey", value = "key")
    private String infoKey;

    /** 创建人用户id */
    @ApiModelProperty(name = "creatorUserId", value = "创建人用户id")
    private Integer creatorUserId;

    /** 修改人用户id */
    @ApiModelProperty(name = "modifyUserId", value = "修改人用户id")
    private Integer modifyUserId;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;


    /** 信息 */
    @ApiModelProperty(name = "infoValue", value = "信息")
    private String infoValue;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private String extInfo;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}