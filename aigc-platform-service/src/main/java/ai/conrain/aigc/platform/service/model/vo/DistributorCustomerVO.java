package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * DistributorCustomerVO
 *
 * @version DistributorCustomerService.java v 0.1 2024-07-15 04:11:37
 */
@Data
public class DistributorCustomerVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 客户主账号id */
	@ApiModelProperty(name = "customerMasterUserId", value = "客户主账号id")
	private Integer customerMasterUserId;

	/**
	 * 渠道商主体id
	 */
	private Integer distributorCorpOrgId;

	/**
	 * 渠道商主体名称（快照）
	 */
	private String distributorCorpName;

	/** 渠道商主账号id */
	@ApiModelProperty(name = "distributorMasterUserId", value = "渠道商主账号id")
	private Integer distributorMasterUserId;

	/** 渠道商运营人员id */
	@ApiModelProperty(name = "distributorOperatorUserId", value = "渠道商运营人员id")
	@Deprecated
	private Integer distributorOperatorUserId;

	/** 渠道商销售人员id */
	@ApiModelProperty(name = "distributorSalesUserId", value = "渠道商销售人员id")
	private Integer distributorSalesUserId;
	private String relatedDistributorSalesName;

	/** 创建人id */
	@ApiModelProperty(name = "creatorId", value = "创建人id")
	private Integer creatorId;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private String extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;


	//view columns
	private String customerMasterNick;
	private String customerMasterCorpName;
	private BigDecimal customerMusePoint;
}