package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.entity.EventTrackingRecordDO;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.EventTrackingRecordService;
import ai.conrain.aigc.platform.service.constants.TrackEventCachePrefix;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

/**
 * 埋点缓存数据入库 定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TrackEventStorageJob extends JavaProcessor {

    @Autowired
    private TairService tairService;

    @Autowired
    private EventTrackingRecordService eventTrackingRecordService;

    /**
     * 埋点记录入库操作定时任务
     */
    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 设置traceId 和 运行环境
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));

        log.info("[埋点信息入库定时任务]TrackEventStorageJob::process::缓存埋点数据开始入库....");

        // 获取缓存中的埋点数据
        List<EventTrackingRecordDO> eventTrackList = tairService.getAllFromList(
            TrackEventCachePrefix.WAIT_CACHE_TRACK_EVENT_LIST, EventTrackingRecordDO.class);

        // 缓存中没有数据，直接返回
        if (CollectionUtils.isEmpty(eventTrackList)) {
            log.info("[埋点信息入库定时任务]TrackEventStorageJob::process::缓存中没有数据，入库结束...");
            return new ProcessResult(true);
        }

        // 批量插入埋点数据
        try {
            eventTrackingRecordService.batchInsert(eventTrackList);
        } catch (Exception e) {
            log.error(
                "[埋点信息入库定时任务][ERROR]TrackEventStorageJob::process::缓存埋点数据入库过程中出现错误，具体错误：{}",
                e.getMessage());
            return new ProcessResult(true);
        }

        // 删除缓存中指定数量的埋点数据
        tairService.deleteFirstNElements(TrackEventCachePrefix.WAIT_CACHE_TRACK_EVENT_LIST, eventTrackList.size());

        // 打印日志
        log.info("[埋点信息入库定时任务]TrackEventStorageJob::process::缓存埋点数据入库完成,共计{}条记录....",
            eventTrackList.size());

        return new ProcessResult(true);
    }

}
