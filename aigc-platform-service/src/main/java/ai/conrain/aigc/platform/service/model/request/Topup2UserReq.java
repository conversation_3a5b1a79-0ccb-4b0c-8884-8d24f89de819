package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class Topup2UserReq {

    @NotNull
    private Integer userId;

    @NotNull
    private BigDecimal amount;

    //产品码，@see ai.conrain.aigc.platform.service.model.biz.PricePlanCode
    private String productCode;

    private String memo;

    private BigDecimal musePoint;

    private Integer creativeImgCountGave;

    //充值日期 2023-07-01
    private String topupDate;
}
