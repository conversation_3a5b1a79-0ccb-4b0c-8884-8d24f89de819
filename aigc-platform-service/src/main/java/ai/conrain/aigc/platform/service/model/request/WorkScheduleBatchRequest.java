package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * WorkSchedule批量创建
 *
 * <AUTHOR>
 * @version BatchCreateWorkScheduleRequest.java v 0.1 2025-04-01 16:11:45
 */
@Data
@ApiModel("工作排期批量创建请求")
public class WorkScheduleBatchRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<WorkScheduleVO> list;
}
