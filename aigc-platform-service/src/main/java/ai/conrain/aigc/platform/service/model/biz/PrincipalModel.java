package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorSettlementVO;
import ai.conrain.aigc.platform.service.model.vo.OrderSettlementVO;
import ai.conrain.aigc.platform.service.model.vo.PrincipalInfoVO;
import lombok.Data;

@Data
public class PrincipalModel {

    /** 主体类型 */
    private PrincipalTypeEnum type;
    /** 主体ID */
    private Integer id;
    /** 主体名称 */
    private String name;

    public static PrincipalModel of(PrincipalTypeEnum principalType, Integer principalId, String name) {
        PrincipalModel model = new PrincipalModel();
        model.setType(principalType);
        model.setId(principalId);
        model.setName(name);
        return model;
    }

    public static PrincipalModel of(PrincipalTypeEnum principalType, Integer principalId) {
        return of(principalType, principalId, null);
    }

    public static PrincipalModel of(PrincipalInfoVO principalInfoVO) {
        return of(principalInfoVO.getPrincipalType(), principalInfoVO.getPrincipalId());
    }

    public static PrincipalModel of(AssessmentPlanVO assessmentPlanVO) {
        return of(assessmentPlanVO.getPrincipalType(), assessmentPlanVO.getPrincipalId());
    }

    public static PrincipalModel of(DistributorSettlementVO settlementVO) {
        return of(settlementVO.getPrincipalType(), settlementVO.getPrincipalId());
    }

    public static PrincipalModel of(OrderSettlementVO settlementVO) {
        return of(settlementVO.getPrincipalType(), settlementVO.getPrincipalId());
    }
}
