package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.enums.AssessStatusEnum;
import ai.conrain.aigc.platform.service.model.dingding.DingTalkNotifyMarkdownMessage;
import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;
import ai.conrain.aigc.platform.service.model.vo.CorpAuthVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import com.alibaba.schedulerx.shade.scala.Int;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 钉钉消息构建工具类
 */
public class DingTalkBuildUtils {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 处理ISO格式的日期字符串，只返回年月日
     * @param dateStr ISO格式的日期字符串
     * @return 年月日格式的日期字符串
     */
    private static String formatISODate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return "-";
        }
        return dateStr.substring(0, 10);
    }

    /**
     * 构建用户注册消息
     *
     * @param userVO 用户信息
     * @return Markdown消息对象
     */
    public static DingTalkNotifyMarkdownMessage buildUserRegisterMessage(UserVO userVO) {
        if (userVO == null) {
            return null;
        }

        DingTalkNotifyMarkdownMessage message = new DingTalkNotifyMarkdownMessage();
        message.setMsgtype("markdown");

        DingTalkNotifyMarkdownMessage.Markdown markdown = new DingTalkNotifyMarkdownMessage.Markdown();
        markdown.setTitle("新用户注册通知");

        StringBuilder content = new StringBuilder();
        // 标题
        content.append("# <font color='#1890FF'>新用户注册通知</font>\n\n");
        
        // 用户基本信息
        content.append("### <font color='#1890FF'>用户信息</font>\n\n");
        content.append("- **用户 ID：** `").append(userVO.getId()).append("`\n");
        content.append("- **用户名称：** ").append(userVO.getNickName()).append("\n");
        content.append("- **注册手机：** <font color='#52C41A'>").append(userVO.getMobile()).append("</font>\n");
        content.append("- **所属企业：** ").append(userVO.getCorpName()).append("\n");
        content.append("- **用户角色：** <font color='#722ED1'>").append(userVO.getRoleType() != null ? userVO.getRoleType().getDesc() : "-").append("</font>\n\n");

        // 状态信息
        content.append("### <font color='#1890FF'>状态信息</font>\n\n");
        content.append("- **⏰ 注册时间：** <font color='#1890FF'>").append(DATE_FORMAT.format(new Date())).append("</font>\n");
        content.append("- **✅ 当前状态：** <font color='#52C41A'><b>新用户已成功注册</b></font>\n\n");

        // 底部提示
        content.append("---\n\n");
        content.append("<font color='#FFA500'>💡 请注意跟进新用户引导工作</font>");

        markdown.setText(content.toString());
        message.setMarkdown(markdown);

        return message;
    }

    /**
     * 构建企业认证消息
     * @param userVO 用户信息
     * @param corpAuthVO 企业认证信息
     * @return Markdown消息对象
     */
    public static DingTalkNotifyMarkdownMessage buildCorpAuthMessage(UserVO userVO, CorpAuthVO corpAuthVO) {
        if (userVO == null || corpAuthVO == null) {
            return null;
        }

        DingTalkNotifyMarkdownMessage message = new DingTalkNotifyMarkdownMessage();
        message.setMsgtype("markdown");

        DingTalkNotifyMarkdownMessage.Markdown markdown = new DingTalkNotifyMarkdownMessage.Markdown();
        markdown.setTitle("企业认证通知");

        StringBuilder content = new StringBuilder();
        // 标题
        content.append("# <font color='#1890FF'>企业认证通知</font>\n\n");

        // 申请人信息
        content.append("### <font color='#1890FF'>申请人信息</font>\n\n");
        content.append("- **用户 ID：** `").append(userVO.getId()).append("`\n");
        content.append("- **用户昵称：** ").append(userVO.getNickName()).append("\n");
        content.append("- **联系电话：** <font color='#52C41A'>").append(userVO.getMobile()).append("</font>\n\n");

        // 企业认证信息
        content.append("### <font color='#1890FF'>企业认证信息</font>\n\n");
        content.append("- **企业名称：** <font color='#1890FF'><b>").append(corpAuthVO.getStringFromExtInfo("name")).append("</b></font>\n");
        content.append("- **统一社会信用代码：** `").append(corpAuthVO.getStringFromExtInfo("creditCode")).append("`\n");
        content.append("- **注册号：** <font color='#1890FF'>").append(corpAuthVO.getStringFromExtInfo("no")).append("</font>\n");
        content.append("- **法定代表人：** ").append(corpAuthVO.getStringFromExtInfo("operName")).append("\n");
        content.append("- **企业状态：** <font color='#52C41A'><b>").append(corpAuthVO.getStringFromExtInfo("status")).append("</b></font>\n");
        content.append("- **注册地址：** ").append(corpAuthVO.getStringFromExtInfo("address")).append("\n");
        content.append("- **成立日期：** <font color='#1890FF'>").append(formatISODate(corpAuthVO.getStringFromExtInfo("startDate"))).append("</font>\n\n");

        // 底部提示
        content.append("---\n\n");
        content.append("<font color='#FFA500'>💡 请相关人员及时审核企业认证信息</font>");

        markdown.setText(content.toString());
        message.setMarkdown(markdown);

        return message;
    }

    /**
     * 构建用户注册文本消息
     * @param userVO 用户信息
     * @param noticePhones 需要@的手机号列表
     * @return 文本格式的消息内容
     */
    public static String buildUserRegisterTextMessage(UserVO userVO, String registerCodeInfo, List<String> noticePhones) {
        if (userVO == null) {
            return null;
        }

        StringBuilder message = new StringBuilder();
        
        message.append(String.format(
            "新用户注册通知\n\n" +
            "用户信息：\n" +
            "用户ID：%s\n" +
            "用户名称：%s\n" +
            "注册手机：%s\n" +
            "所属企业：%s\n" +
            "用户角色：%s\n\n" +
            "状态信息：\n" +
            "注册时间：%s\n" +
            "邀请码注册：%s\n" +
            "当前状态：新用户已成功注册\n\n" +
            "请注意跟进新用户引导工作",
            userVO.getId(),
            userVO.getNickName(),
            userVO.getMobile(),
            userVO.getCorpName(),
            userVO.getRoleType() != null ? userVO.getRoleType().getDesc() : "-",
            DATE_FORMAT.format(new Date()),
            StringUtils.defaultIfBlank(registerCodeInfo, "-")
        ));

        // 在消息最后添加@手机号
        if (noticePhones != null && !noticePhones.isEmpty()) {
            message.append("\n\n");
            for (String phone : noticePhones) {
                message.append("@").append(phone).append(" ");
            }
        }

        return message.toString();
    }

    /**
     * 构建企业认证文本消息
     * @param userVO 用户信息
     * @param corpAuthVO 企业认证信息
     * @param noticePhones 需要@的手机号列表
     * @return 文本格式的消息内容
     */
    public static String buildCorpAuthTextMessage(UserVO userVO, CorpAuthVO corpAuthVO, List<String> noticePhones) {
        if (userVO == null || corpAuthVO == null) {
            return null;
        }

        StringBuilder message = new StringBuilder();
        
        message.append(String.format(
            "企业认证通知\n\n" +
            "申请人信息：\n" +
            "用户ID：%s\n" +
            "用户昵称：%s\n" +
            "联系电话：%s\n\n" +
            "企业认证信息：\n" +
            "企业名称：%s\n" +
            "统一社会信用代码：%s\n" +
            "注册号：%s\n" +
            "法定代表人：%s\n" +
            "企业状态：%s\n" +
            "注册地址：%s\n" +
            "成立日期：%s\n\n" +
            "请相关人员及时审核企业认证信息",
            userVO.getId(),
            userVO.getNickName(),
            userVO.getMobile(),
            corpAuthVO.getStringFromExtInfo("name"),
            corpAuthVO.getStringFromExtInfo("creditCode"),
            corpAuthVO.getStringFromExtInfo("no"),
            corpAuthVO.getStringFromExtInfo("operName"),
            corpAuthVO.getStringFromExtInfo("status"),
            corpAuthVO.getStringFromExtInfo("address"),
            formatISODate(corpAuthVO.getStringFromExtInfo("startDate"))
        ));

        // 在消息最后添加@手机号
        if (noticePhones != null && !noticePhones.isEmpty()) {
            message.append("\n\n");
            for (String phone : noticePhones) {
                message.append("@").append(phone).append(" ");
            }
        }

        return message.toString();
    }

    // TODO 发布时, 去掉测试
    /**
     * 构建考核计划文本消息
     * 
     */
    public static String buildAssessmentPlanTextMessage(Integer initCount, Integer passedCount, Integer failedCount) {
        if (initCount == null) {
            initCount = 0;
        }
        if (passedCount == null) {
            passedCount = 0;
        }
        if (failedCount == null) {
            failedCount = 0;
        }
        if (initCount + passedCount + failedCount == 0) {
            return null;
        }
        StringBuilder message = new StringBuilder();
        message.append("考核计划通知#测试\n\n");

        if (initCount > 0) {
            message.append(String.format("考核计划待确认：%s 人\n", initCount));
        }
        if (passedCount + failedCount > 0) {
            message.append(String.format("考核计划已结束：%s 人\n", passedCount + failedCount));
            if (passedCount > 0) {
                message.append(String.format("考核通过：%s 人\n", passedCount));
            }
            if (failedCount > 0) {
                message.append(String.format("考核不通过：%s 人\n", failedCount));
            }
            message.append("\n");
        }
        message.append("请及时处理");
        return message.toString();
    }
} 