package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;

/**
 * InvoiceInfoQuery
 *
 * @version InvoiceInfoService.java v 0.1 2024-06-27 01:42:09
 */
@Data
public class InvoiceInfoQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 关联的主用户id */
    @ApiModelProperty(name = "masterUserId", value = "关联的主用户id")
    private Integer masterUserId;

    /** 关联的主用户昵称快照 */
    @ApiModelProperty(name = "masterUserNick", value = "关联的主用户昵称快照")
    private String masterUserNick;

    /** 关联的主用户登录账号快照 */
    @ApiModelProperty(name = "masterUserLoginId", value = "关联的主用户登录账号快照")
    private String masterUserLoginId;

    /** 关联的操作员用户id */
    @ApiModelProperty(name = "operatorUserId", value = "关联的操作员用户id")
    private Integer operatorUserId;

    /** 关联的操作员用户昵称快照 */
    @ApiModelProperty(name = "operatorUserNick", value = "关联的操作员用户昵称快照")
    private String operatorUserNick;

    /** 关联的操作员用户登录账号快照 */
    @ApiModelProperty(name = "operatorUserLoginId", value = "关联的操作员用户登录账号快照")
    private String operatorUserLoginId;

    /** 发票类型，普票｜专票 */
    @ApiModelProperty(name = "invoiceType", value = "发票类型，普票｜专票")
    private String invoiceType;

    /** 发票抬头类型，个人｜企业 */
    @ApiModelProperty(name = "subjectType", value = "发票抬头类型，个人｜企业")
    private String subjectType;

    /** 发票抬头 */
    @ApiModelProperty(name = "subjectName", value = "发票抬头")
    private String subjectName;

    /** 统一社会信用代码 */
    @ApiModelProperty(name = "creditCode", value = "统一社会信用代码")
    private String creditCode;

    /** 办公地址 */
    @ApiModelProperty(name = "businessAddress", value = "办公地址")
    private String businessAddress;

    /** 办公电话 */
    @ApiModelProperty(name = "businessPhone", value = "办公电话")
    private String businessPhone;

    /** 开户银行 */
    @ApiModelProperty(name = "bankName", value = "开户银行")
    private String bankName;

    /** 银行账号 */
    @ApiModelProperty(name = "bankAccount", value = "银行账号")
    private String bankAccount;

    /** 发票状态，未开票｜开票中｜已开票 */
    @ApiModelProperty(name = "status", value = "发票状态，未开票｜开票中｜已开票")
    private String status;

    /** 申请时间 */
    @ApiModelProperty(name = "applyTime", value = "申请时间")
    private Date applyTime;

    /** 完成时间 */
    @ApiModelProperty(name = "finishTime", value = "完成时间")
    private Date finishTime;

    /** 发票号 */
    @ApiModelProperty(name = "invoiceNo", value = "发票号")
    private String invoiceNo;

    /** 不含税发票金额 */
    @ApiModelProperty(name = "amountNoTax", value = "不含税发票金额")
    private BigDecimal amountNoTax;

    /** 税率，小数形式 */
    @ApiModelProperty(name = "taxRate", value = "税率，小数形式")
    private BigDecimal taxRate;

    /** 税额 */
    @ApiModelProperty(name = "taxAmount", value = "税额")
    private BigDecimal taxAmount;

    /** 含税发票金额 */
    @ApiModelProperty(name = "amountWithTax", value = "含税发票金额")
    private BigDecimal amountWithTax;

    /** 外部发票平台名称 */
    @ApiModelProperty(name = "invoiceTaskThirdPlatform", value = "外部发票平台名称")
    private String invoiceTaskThirdPlatform;

    /** 外部发票任务id */
    @ApiModelProperty(name = "invoiceTaskThirdReqId", value = "外部发票任务id")
    private String invoiceTaskThirdReqId;

    /** 发票任务详情 */
    @ApiModelProperty(name = "invoiceTaskDetail", value = "发票任务详情")
    private String invoiceTaskDetail;

    /** 发票文件下载地址 */
    @ApiModelProperty(name = "invoiceDownloadUrl", value = "发票文件下载地址")
    private String invoiceDownloadUrl;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private String extInfo;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    //内部发票号码
    private String innerInvoiceNo;

    //税务局红冲票号码
    private String negativeInvoiceNo;

    //红冲发票详情
    private String negativeInvoiceDetail;


    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}