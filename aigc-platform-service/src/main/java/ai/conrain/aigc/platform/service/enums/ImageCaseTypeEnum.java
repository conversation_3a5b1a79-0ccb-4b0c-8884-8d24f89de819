package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 图片案例类型枚举
 */
@Getter
public enum ImageCaseTypeEnum {
    /**
     * 不良案例
     */
    BAD_CASE("badCase", "问题图"),
    /**
     * 精选图
     */
    GOOD_CASE("goodCase", "精选图"),
    /**
     * 人工服饰图
     */
    MANUAL_REPLACEMENT("manualReplacement", "人工服饰图"),
    /**
     * 低质量图
     */
    LOW_QUALITY("lowQuality", "低质量图"),
    /**
     * 同步到服务器
     */
    IS_SYNC_TO_SERVER("isSyncToServer", "是否需要同步至服务器");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    ImageCaseTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 Code 获取枚举信息
     *
     * @param code 类型编码
     * @return 枚举信息
     */
    public static ImageCaseTypeEnum getByCode(String code) {
        for (ImageCaseTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}
