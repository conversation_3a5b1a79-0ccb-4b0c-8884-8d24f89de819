package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum SalesTypeEnum {
    AGENT_NORMAL("AGENT_NORMAL", "普通渠道"),
    AGENT_EXCLUSIVE("AGENT_EXCLUSIVE", "独家渠道"),
    DIRECT("DIRECT", "直营渠道"),
    ;

    private String code;

    private String desc;

    SalesTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SalesTypeEnum getByCode(String code) {
        for (SalesTypeEnum type : SalesTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isExclusive(String code) {
        return AGENT_EXCLUSIVE.name().equals(code);
    }

    public static boolean isExclusive(SalesTypeEnum type) {
        return AGENT_EXCLUSIVE.equals(type);
    }

    public static boolean isDirect(String code) {
        return DIRECT.name().equals(code);
    }

    public static boolean isDirect(SalesTypeEnum type) {
        return DIRECT.equals(type);
    }
}
