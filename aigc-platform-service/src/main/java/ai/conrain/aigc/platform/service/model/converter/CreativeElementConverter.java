package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.CreativeElementDO;
import ai.conrain.aigc.platform.dal.example.CreativeElementExample;
import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ElementStatusEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.StyleScene;
import ai.conrain.aigc.platform.service.model.biz.StyleScene.SceneDetail;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_RESTORE_VISIBILITY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_HAIRSTYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NOSHOW_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_POSTURE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCENE_CLOTH_TYPE_SCOPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_OUTFIT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SWAP_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.util.ElementUtils.BACKGROUND_SCENE_IDS;

/**
 * CreativeElementConverter
 *
 * @version CreativeElementService.java v 0.1 2024-05-09 06:10:02
 */
@Slf4j
public class CreativeElementConverter {

    /**
     * DO -> VO
     */
    public static CreativeElementVO do2VO(CreativeElementDO from) {
        CreativeElementVO to = new CreativeElementVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setStatus(ElementStatusEnum.getByCode(from.getStatus()));
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        if (StringUtils.isNotBlank(from.getType())) {
            to.setType(Arrays.asList(from.getType().split(",")));
        }
        to.setBelong(ModelTypeEnum.getByCode(from.getBelong()));
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setTags(ComfyUIUtils.parseJsonStr(from.getTags()));
        to.setExtTags(ComfyUIUtils.parseJsonStr(from.getExtTags()));
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));

        ModelVersionEnum version = StringUtils.equalsIgnoreCase("LORA", to.getExtInfo(KEY_SWAP_TYPE, String.class))
            ? ModelVersionEnum.FLUX : ModelVersionEnum.SDXL;
        to.setVersion(version.getCode());
        to.setStyleScene(ElementUtils.isStyleScene(to));

        to.setLoraModelId(from.getLoraModelId());
        to.setIsNew(from.getIsNew());

        if (to.getExtInfo() != null) {
            to.getExtInfo().forEach((k, v) -> {
                v = ComfyUIUtils.parseJsonObj(k, v);
                to.addExtInfo(k, v);
            });
        }

        //以下兼容处理
        if (StringUtils.equals(to.getConfigKey(), "FACE") && to.getExtInfo(KEY_FACE_RESTORE_VISIBILITY) == null) {
            to.addExtInfo(KEY_FACE_RESTORE_VISIBILITY, "0.7");
        }

        if (to.getExtInfo() != null && to.getExtInfo().containsKey(CommonConstants.KEY_OPEN_SCOPE)
            && !StringUtils.equals(to.getExtInfo(CommonConstants.KEY_OPEN_SCOPE, String.class), CommonConstants.ALL)) {
            to.setExclusive(true);
        }

        to.setPrivatelyOpen2UserId(from.getPrivatelyOpen2UserId());
        to.setPrivatelyOpen2UserNick(from.getPrivatelyOpen2UserNick());

        if (StringUtils.isNotBlank(from.getPrivatelyOpen2UserRoleType())) {
            to.setPrivatelyOpen2UserRoleType(RoleTypeEnum.getByCode(from.getPrivatelyOpen2UserRoleType()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static CreativeElementDO vo2DO(CreativeElementVO from) {
        CreativeElementDO to = new CreativeElementDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        if (CollectionUtils.isNotEmpty(from.getType())) {
            to.setType(String.join(",", from.getType()));
        }
        to.setBelong(from.getBelong() != null ? from.getBelong().getCode() : null);
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(ComfyUIUtils.revertJsonStr(from.getTags()));
        to.setExtTags(ComfyUIUtils.revertJsonStr(from.getExtTags()));
        to.setIsNew(from.getIsNew());

        to.setLoraModelId(from.getLoraModelId());

        if (from.getExtInfo() != null) {
            from.getExtInfo().forEach((k, v) -> {
                v = v == null ? null : ComfyUIUtils.revertJsonObj(k, v);
                from.addExtInfo(k, v);
            });
        }

        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toJSONString() : null);

        return to;
    }

    /**
     * DO -> Query
     */
    public static CreativeElementQuery do2Query(CreativeElementDO from) {
        CreativeElementQuery to = new CreativeElementQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        to.setType(from.getType());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(from.getTags());
        to.setExtTags(from.getExtTags());
        to.setExtInfo(from.getExtInfo());
        to.setLoraModelId(from.getLoraModelId());
        to.setIsNew(from.getIsNew());

        return to;
    }

    /**
     * Query -> DO
     */
    public static CreativeElementDO query2DO(CreativeElementQuery from) {
        CreativeElementDO to = new CreativeElementDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        to.setType(from.getType());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(from.getTags());
        to.setExtTags(from.getExtTags());
        to.setExtInfo(from.getExtInfo());
        to.setLoraModelId(from.getLoraModelId());
        to.setIsNew(from.getIsNew());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CreativeElementExample query2Example(CreativeElementQuery from) {
        CreativeElementExample to = new CreativeElementExample();
        CreativeElementExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getIdsOrParents())) {
            c.andIdOrParentIn(from.getIdsOrParents());
        }
        if (!ObjectUtils.isEmpty(from.getBelong())) {
            c.andBelongEqualTo(from.getBelong());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getConfigKey())) {
            c.andConfigKeyEqualTo(from.getConfigKey());
        }
        if (!ObjectUtils.isEmpty(from.getIsNew())) {
            c.andIsNewEqualTo(from.getIsNew());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeIncludes(from.getType());
        }

        if (!ObjectUtils.isEmpty(from.getTypeNotEqual())) {
            c.andTypeExcludes(from.getTypeNotEqual());
        }

        if (!ObjectUtils.isEmpty(from.getLoraModelId())) {
            c.andLoraModelIdEqualTo(from.getLoraModelId());
        }

        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }

        if (!ObjectUtils.isEmpty(from.getSwapType())) {
            c.andSwapTypeEqualTo(from.getSwapType());
        }

        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameLike(from.getName());
        }

        if (!ObjectUtils.isEmpty(from.getLevel())) {
            c.andLevelEqualTo(from.getLevel());
        }

        if (!ObjectUtils.isEmpty(from.getLevels())) {
            c.andLevelIn(from.getLevels());
        }

        if (from.isTrainFinished()) {
            c.andTrainFinished(BACKGROUND_SCENE_IDS);
        }

        if (from.isFaceOnlyLora()) {
            c.andIsFaceOnlyLora();
        }

        if (!ObjectUtils.isEmpty(from.getTrainType())) {
            c.andTrainTypeEqualTo(from.getTrainType());
        }

        if (!ObjectUtils.isEmpty(from.getIsLora())) {
            if (from.getIsLora()) {
                c.andIsLora();
            } else {
                c.andIsNotLora();
            }
        }

        if (!ObjectUtils.isEmpty(from.getGenderType())) {
            //male-model female-model
            c.andTypeIncludes(from.getGenderType());
        }

        if (!ObjectUtils.isEmpty(from.getGenderTypes())) {
            //male-model female-model
            c.andTypeOrIncludes(from.getGenderTypes());
        }

        // ageRange
        if (!ObjectUtils.isEmpty(from.getAgeRange())) {
            // 如果包含child则正常筛选
            if (from.getAgeRange().contains("child")) {
                c.andTypeOrIncludes(Collections.singletonList(from.getAgeRange()));
            } else {
                // 不包含child则说明是成人'big-child', 'medium-child', 'small-child'
                c.andTypeExcludes(Arrays.asList("big-child", "medium-child", "small-child"));
            }
        }

        //  filterAgeRange 过滤条件（用于后台模糊查询，与上面的ageRange不会同时出现）
        if (!ObjectUtils.isEmpty(from.getFilterAgeRange())) {
            if (from.getFilterAgeRange().contains("adult")) {
                c.andTypeIncludes(from.getFilterAgeRange());
            } else {
                // "big-child", "medium-child", "small-child" 为三种儿童类型，
                // child-model 为童模标识（兼容旧数据）
                c.andTypeOrIncludes(Arrays.asList("big-child", "medium-child", "small-child", "child-model"));
            }
        }

        if (!ObjectUtils.isEmpty(from.getAgeRanges())) {
            c.andTypeOrIncludes(from.getAgeRanges());
        }

        if (!ObjectUtils.isEmpty(from.getOpenScope())) {
            if (StringUtils.equalsIgnoreCase("public", from.getOpenScope())) {
                c.andOpenScopePublic();
            } else if (StringUtils.equalsIgnoreCase("private", from.getOpenScope())) {
                c.andOpenScopePrivate();
            } else {
                c.andOpenScopeEqualTo(from.getOpenScope());
            }
        }

        if (from.isNeedInitClothTypeScope()) {
            c.andExtValueIsBlank(KEY_SCENE_CLOTH_TYPE_SCOPE);
        }

        if (from.isHasClothTypeScope()) {
            c.andExtValueIsNotBlank(KEY_SCENE_CLOTH_TYPE_SCOPE);
            c.andExtValueNotEquals(KEY_SCENE_CLOTH_TYPE_SCOPE, "[]");
        }

        if (from.isStyleScene()) {
            c.andExtValueEquals(KEY_IS_LORA, YES);
            c.andIdNotIn(BACKGROUND_SCENE_IDS);
        }

        if (!ObjectUtils.isEmpty(from.getScopeUserId())) {
            c.andOpenScopePublicOrEquals(from.getScopeUserId());
        }

        if (StringUtils.isBlank(from.getCreativeType())) {
            CreativeTypeEnum creativeType = CreativeTypeEnum.getByCode(from.getCreativeType());
            if (creativeType != null && creativeType.isExternal()) {
                c.andConfigKeyIn(Arrays.stream(creativeType.getConfigKeys()).map(ElementConfigKeyEnum::name)
                    .collect(Collectors.toList()));
            }
        }

        if (!ObjectUtils.isEmpty(from.getTypes())) {
            c.andTypeIncludes(from.getTypes());
        }

        if (!ObjectUtils.isEmpty(from.getTypesOr())) {
            c.andTypeOrIncludes(from.getTypesOr());
        }

        if (!ObjectUtils.isEmpty(from.getBodyTypes())) {
            c.andTypeOrIncludes(from.getBodyTypes());
        }

        if (!ObjectUtils.isEmpty(from.getPositions())) {
            c.andTypeOrIncludes(from.getPositions());
        }
        if (!ObjectUtils.isEmpty(from.getExcludesTypes())) {
            c.andTypeExcludes(from.getExcludesTypes());
        }

        if (from.isOnlyNearingDelivery()) {
            c.andStatusEqualTo(ElementStatusEnum.TEST.getCode());
            c.andUserTypeIsCustomer();

            //创建时间在 20 小时前，并且创建时间在 1 周内
            c.andCreateTimeLessThan(DateUtils.addHours(new Date(), -20));
            c.andCreateTimeGreaterThan(DateUtils.addWeeks(new Date(), -1));
        }

        //专属开放的账号类型
        if (StringUtils.isNotBlank(from.getPrivatelyOpen2UserRoleType())) {
            c.andPrivatelyOpen2UserRoleTypeEqualTo(from.getPrivatelyOpen2UserRoleType());
        }

        //专属开放的账号
        if (from.getPrivatelyOpen2UserId() != null) {
            c.andPrivatelyOpen2UserIdEqualTo(from.getPrivatelyOpen2UserId());
        }

        if (!ObjectUtils.isEmpty(from.getConfigKeys())) {
            c.andConfigKeyIn(from.getConfigKeys());
        }

        // 仅查看实验标签
        if (from.getOnlyExperimental() != null) {
            if (from.getOnlyExperimental()) {
                c.andIsExperimental();
            } else {
                c.andIsNotExperimental();
            }
        }

        if (from.getOnlyNoshowFace() != null && from.getOnlyNoshowFace()) {
            c.andExtValueEquals(KEY_NOSHOW_FACE, YES);
        }

        if (!ObjectUtils.isEmpty(from.getExcludesParentOrIds())) {
            c.andIdNotIn(from.getExcludesParentOrIds());
            c.andParentIdNotIn(from.getExcludesParentOrIds());
        }

        c.andDeletedEqualTo(false);

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<CreativeElementVO> doList2VOList(List<CreativeElementDO> list) {
        return CommonUtil.listConverter(list, CreativeElementConverter::do2VO);
    }

    /**
     * vo list to level list
     */
    public static List<CreativeElementVO> toListByLevel(List<CreativeElementVO> list) {
        return toListByLevel(list, 1, null);
    }

    /**
     * 递归方式遍历生成树
     *
     * @param list     列表
     * @param level    登记
     * @param parentId 父节点id
     * @return 结果
     */
    public static List<CreativeElementVO> toListByLevel(List<CreativeElementVO> list, Integer level, Integer parentId) {
        List<CreativeElementVO> current = list.stream().filter(
            vo -> vo.getLevel() != null && vo.getLevel().equals(level) && (parentId == null || parentId.equals(
                vo.getParentId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(current)) {
            return new ArrayList<>();
        }

        current = current.stream().sorted(Comparator.comparing(CreativeElementVO::getOrder)).collect(
            Collectors.toList());

        current.forEach(vo -> {
            vo.setChildren(toListByLevel(list, level + 1, vo.getId()));
        });

        return current;
    }

    /**
     * 从文件加载场景
     *
     * @param file   文件
     * @param parent 父节点
     * @return 节点
     */
    public static CreativeElementVO loadFromFile(FileVO file, CreativeElementVO parent) {
        if (!StringUtils.equals(file.getType(), CommonConstants.KEY_JSON)) {
            return null;
        }
        JSONObject content = JSONObject.parseObject(file.getTextContent());
        if (content == null || !content.containsKey("desc")) {
            log.error("resetScene error,file={},id={}", file, parent.getId());
            return null;
        }

        StyleScene styleScene = content.getJSONObject("desc").toJavaObject(StyleScene.class);
        log.info("resetScene,id={},file={},content={}", parent.getId(), file.getFileName(),
            content.getJSONObject("desc"));

        SceneDetail scene = styleScene.getScene();

        CreativeElementVO child = new CreativeElementVO();
        List<String> types = new ArrayList<>(parent.getType());
        String characterOrientation = scene.getCharacterOrientation();
        //兼容side view
        CameraAngleEnum orientation = StringUtils.equals(characterOrientation, "side view") ? CameraAngleEnum.FRONT_VIEW
            : CameraAngleEnum.getByCode(characterOrientation, CameraAngleEnum.FRONT_VIEW);
        CameraAngleEnum view = CameraAngleEnum.getByCode(scene.getCharacterView(), CameraAngleEnum.WHOLE_BODY);
        if (orientation != null) {
            types.add(orientation.getCode());
        }
        if (view != null) {
            types.add(view.getCode());
        }

        child.setType(types);
        //构图背景
        child.setTags(styleScene.getActivateKey() + CommonUtil.formatTextWithComma(scene.getComposition()));
        //人物姿势 + 人物动作
        child.addExtInfo(KEY_POSTURE,
            CommonUtil.formatTextWithComma(scene.getAction()) + CommonUtil.formatTextWithComma(scene.getPose()));
        child.addExtInfo(KEY_LENS,
            CommonUtil.formatTextWithComma(characterOrientation) + CommonUtil.formatTextWithComma(
                scene.getCharacterView()));
        //人物搭配
        child.addExtInfo(KEY_STYLE_OUTFIT, JSONObject.toJSONString(styleScene.getOutfit()));
        //模特信息
        child.addExtInfo(KEY_STYLE_MODEL, JSONObject.toJSONString(styleScene.getModel()));

        return child;
    }

    /**
     * 从文件中加载一个主图信息转换成child
     *
     * @param labelFiles 打标文件
     * @param parent     父节点
     * @return 子节点
     */
    public static CreativeElementVO loadMainFromFile(List<FileVO> labelFiles, CreativeElementVO parent) {
        if (CollectionUtils.isEmpty(labelFiles)) {
            log.warn("获取主图信息失败，打标文件为空，labelFiles={},id={}", labelFiles, parent.getId());
            return null;
        }

        if (LabelTypeEnum.MINI != LabelTypeEnum.getByCode(parent.getExtInfo(KEY_LABEL_TYPE, String.class))) {
            log.warn("获取主图信息失败，当前训练方式非极简打标，labelType={},id={}",
                parent.getExtInfo(KEY_LABEL_TYPE, String.class), parent.getId());
            return null;
        }

        for (FileVO file : labelFiles) {
            if (!StringUtils.equals(file.getType(), CommonConstants.KEY_TEXT)) {
                continue;
            }

            String content = file.getTextContent();

            CreativeElementVO child = new CreativeElementVO();

            if (CollectionUtils.isNotEmpty(parent.getType())) {
                Set<String> typeSet = new HashSet<>(parent.getType());
                CameraAngleEnum.removeAllAngle(typeSet);
                child.setType(new ArrayList<>(typeSet));
            } else {
                child.setType(new ArrayList<>());
            }
            child.getType().add(CameraAngleEnum.FRONT_VIEW.getCode());

            child.setLevel(3);
            child.setParentId(parent.getId());
            child.setConfigKey(parent.getConfigKey());
            child.setTags(StringUtils.substringBefore(content, ","));
            if (StringUtils.split(content, ",").length > 1) {
                child.addExtInfo(KEY_HAIRSTYLE, StringUtils.substringAfter(content, ","));
            }

            return child;
        }

        log.warn("获取主图信息失败，加载不到类型为text的文件，labelFiles={},id={}", labelFiles, parent.getId());
        return null;
    }
}