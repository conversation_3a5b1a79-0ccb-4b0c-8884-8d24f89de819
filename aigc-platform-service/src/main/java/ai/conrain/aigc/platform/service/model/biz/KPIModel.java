package ai.conrain.aigc.platform.service.model.biz;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class KPIModel {
    /**
     * 总销售额
     */
    private BigDecimal totalSalesAmount;
    /**
     * 总销售单量
     */
    private Integer totalSalesCount;

    public KPIModel() {
        this.totalSalesAmount = new BigDecimal(0);
        this.totalSalesCount = 0;
    }

    public Boolean upTo(KPIModel target) {
        return totalSalesAmount.compareTo(target.getTotalSalesAmount()) >= 0 ||
                totalSalesCount.compareTo(target.getTotalSalesCount()) >= 0;
    }
}
