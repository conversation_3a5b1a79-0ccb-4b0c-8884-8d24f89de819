{"client_id": "${clientId}", "prompt": {"1102": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "1222": {"inputs": {"samples": ["1224", 0], "vae": ["1102", 0]}, "class_type": "VAEDecode"}, "1224": {"inputs": {"noise": ["1484", 0], "guider": ["1227", 0], "sampler": ["1225", 0], "sigmas": ["1226", 0], "latent_image": ["1491", 2]}, "class_type": "SamplerCustomAdvanced"}, "1225": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "1226": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": "${denoised?number}", "model": ["1592", 0]}, "class_type": "BasicScheduler"}, "1227": {"inputs": {"model": ["1488", 0], "conditioning": ["1531", 0]}, "class_type": "BasicGuider"}, "1484": {"inputs": {"noise_seed": "${seed?number}"}, "class_type": "RandomNoise"}, "1488": {"inputs": {"model": ["1592", 0]}, "class_type": "DifferentialDiffusion"}, "1489": {"inputs": {"text": ["1568", 0], "clip": ["1592", 1]}, "class_type": "CLIPTextEncode"}, "1490": {"inputs": {"guidance": 3.5, "conditioning": ["1489", 0]}, "class_type": "FluxGuidance"}, "1491": {"inputs": {"positive": ["1490", 0], "negative": ["1492", 0], "vae": ["1102", 0], "pixels": ["1611", 0], "mask": ["1603", 0]}, "class_type": "InpaintModelConditioning"}, "1492": {"inputs": {"text": "", "clip": ["1592", 1]}, "class_type": "CLIPTextEncode"}, "1530": {"inputs": {"switch_1": "On", "controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_1": 0.6, "start_percent_1": 0, "end_percent_1": 0.2, "switch_2": "On", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_2": 0.1, "start_percent_2": 0.2, "end_percent_2": 0.5, "switch_3": "Off", "controlnet_3": "None", "controlnet_strength_3": 1, "start_percent_3": 0, "end_percent_3": 1, "image_1": ["1532", 0], "image_2": ["1532", 0]}, "class_type": "CR Multi-ControlNet Stack"}, "1531": {"inputs": {"switch": "On", "base_positive": ["1491", 0], "base_negative": ["1492", 0], "controlnet_stack": ["1530", 0]}, "class_type": "CR Apply Multi-ControlNet"}, "1532": {"inputs": {"resolution": 1024, "image": ["1611", 0]}, "class_type": "Zoe-DepthMapPreprocessor"}, "1565": {"inputs": {"image": "${targetImage}", "upload": "image"}, "class_type": "LoadImage"}, "1568": {"inputs": {"prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 991}, "class_type": "ConrainRandomPrompts"}, "1570": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": true, "body_mask": false, "clothes_mask": false, "confidence": 0.5, "images": ["1611", 0]}, "class_type": "APersonMaskGenerator"}, "1571": {"inputs": {"model_name": "sam_vit_h_4b8939.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader"}, "1572": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider"}, "1573": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 30, "bbox_detector": ["1572", 0], "image": ["1611", 0]}, "class_type": "ImpactSimpleDetectorSEGS"}, "1574": {"inputs": {"detection_hint": "center-1", "dilation": 0, "threshold": 0.93, "bbox_expansion": 0, "mask_hint_threshold": 0.7, "mask_hint_use_negative": "False", "sam_model": ["1571", 0], "segs": ["1573", 0], "image": ["1611", 0]}, "class_type": "SAMDetectorCombined"}, "1575": {"inputs": {"masks_a": ["1574", 0], "masks_b": ["1570", 0]}, "class_type": "Masks Add"}, "1590": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "1591": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader"}, "1592": {"inputs": {"lora_name": "${FACE.extInfo.faceLora}", "strength_model": "1", "strength_clip": "1", "model": ["1591", 0], "clip": ["1590", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "1601": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "1602": {"inputs": {"output_path": ["1601", 0], "filename_prefix": ["1601", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["1222", 0]}, "class_type": "ConrainImageSave"}, "1603": {"inputs": {"expand": 2, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": true, "mask": ["1575", 0]}, "class_type": "ConrainGrowMaskWithBlur"}, "1611": {"inputs": {"width": ["1622", 0], "height": ["1628", 0], "position": "top-left", "x_offset": 0, "y_offset": 0, "image": ["1644", 0]}, "class_type": "ImageCrop+"}, "1616": {"inputs": {"image": ["1644", 0]}, "class_type": "GetImageSize+"}, "1622": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["1616", 0]}, "class_type": "ConrainPythonExecutor"}, "1628": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["1616", 1]}, "class_type": "ConrainPythonExecutor"}, "1644": {"inputs": {"width": 1536, "height": 1536, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 0, "image": ["1565", 0]}, "class_type": "ImageResize+"}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 1645, "last_link_id": 2164, "nodes": [{"id": 1102, "type": "VAELoader", "pos": {"0": -8946.**********, "1": 2337.**********}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1999], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 1490, "type": "FluxGuidance", "pos": {"0": -8027.03125, "1": 3369.019775390625}, "size": {"0": 211.60000610351562, "1": 58}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1897}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1900], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 1531, "type": "CR Apply Multi-ControlNet", "pos": {"0": -6867, "1": 3179}, "size": {"0": 274.56842041015625, "1": 98}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "base_positive", "type": "CONDITIONING", "link": 1962}, {"name": "base_negative", "type": "CONDITIONING", "link": 1963}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": 1954}], "outputs": [{"name": "base_pos", "type": "CONDITIONING", "links": [1964], "slot_index": 0, "shape": 3}, {"name": "base_neg", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "widgets_values": ["On"]}, {"id": 1551, "type": "Reroute", "pos": {"0": -8519.**********, "1": 2329.**********}, "size": [75, 26], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1999}], "outputs": [{"name": "", "type": "VAE", "links": [2004, 2005], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#322", "bgcolor": "#533"}, {"id": 1572, "type": "UltralyticsDetectorProvider", "pos": {"0": -7660.**********, "1": 2056.601806640625}, "size": {"0": 315, "1": 78}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [2038], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 1574, "type": "SAMDetectorCombined", "pos": {"0": -6830.**********, "1": 1976.6014404296875}, "size": {"0": 315, "1": 218}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 2039}, {"name": "segs", "type": "SEGS", "link": 2040}, {"name": "image", "type": "IMAGE", "link": 2059}], "outputs": [{"name": "MASK", "type": "MASK", "links": [2041], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SAMDetectorCombined"}, "widgets_values": ["center-1", 0, 0.93, 0, 0.7, "False"]}, {"id": 1575, "type": "Masks Add", "pos": {"0": -6438.86572265625, "1": 2045.028076171875}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "masks_a", "type": "MASK", "link": 2041}, {"name": "masks_b", "type": "MASK", "link": 2042}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [2092], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Masks Add"}, "widgets_values": []}, {"id": 1587, "type": "Reroute", "pos": {"0": -7624.51220703125, "1": 1965.293701171875}, "size": [75, 26], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2056}], "outputs": [{"name": "", "type": "IMAGE", "links": [2057, 2058, 2059], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1590, "type": "DualCLIPLoader", "pos": {"0": -8947.**********, "1": 2143.**********}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [2070], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 1591, "type": "UNETLoader", "pos": {"0": -8950.**********, "1": 1970.2938232421875}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2069], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 1592, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -8507.**********, "1": 1979.2938232421875}, "size": {"0": 339.9307556152344, "1": 126}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2069}, {"name": "clip", "type": "CLIP", "link": 2070}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2085], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [2086], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["${FACE.extInfo.faceLora}", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 1492, "type": "CLIPTextEncode", "pos": {"0": -8017, "1": 3469}, "size": {"0": 210, "1": 125.07953643798828}, "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2081}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1904, 1963], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1489, "type": "CLIPTextEncode", "pos": {"0": -8333, "1": 3422}, "size": {"0": 210, "1": 125.07953643798828}, "flags": {"collapsed": false}, "order": 19, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2082}, {"name": "text", "type": "STRING", "link": 2067, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1897], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1488, "type": "DifferentialDiffusion", "pos": {"0": -8321, "1": 3349}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2083}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1895], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 1593, "type": "Reroute", "pos": {"0": -8756, "1": 3211}, "size": [75, 26], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2085}], "outputs": [{"name": "", "type": "MODEL", "links": [2083, 2084], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#223", "bgcolor": "#335"}, {"id": 1594, "type": "Reroute", "pos": {"0": -8752, "1": 3338}, "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2086}], "outputs": [{"name": "", "type": "CLIP", "links": [2081, 2082], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 1225, "type": "KSamplerSelect", "pos": {"0": -6244, "1": 3448}, "size": {"0": 314.0994873046875, "1": 58}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1503], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 1227, "type": "BasicGuider", "pos": {"0": -6245, "1": 3322}, "size": {"0": 263.1893615722656, "1": 46}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1895, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1964, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1502], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 1530, "type": "CR Multi-ControlNet Stack", "pos": {"0": -7114, "1": 3629}, "size": {"0": 563.9595947265625, "1": 454}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "image_1", "type": "IMAGE", "link": 1951, "shape": 7}, {"name": "image_2", "type": "IMAGE", "link": 2076, "shape": 7}, {"name": "image_3", "type": "IMAGE", "link": null, "shape": 7}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": null, "shape": 7}], "outputs": [{"name": "CONTROLNET_STACK", "type": "CONTROL_NET_STACK", "links": [1954], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.1, 0.2, 0.5, "Off", "None", 1, 0, 1]}, {"id": 1226, "type": "BasicScheduler", "pos": {"0": -6252, "1": 3587}, "size": {"0": 309.76611328125, "1": 106}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2084, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [2024], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, "${denoised?number}"], "color": "#232", "bgcolor": "#353"}, {"id": 1491, "type": "InpaintModelConditioning", "pos": {"0": -7426, "1": 3182}, "size": {"0": 216.59999084472656, "1": 138}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1900}, {"name": "negative", "type": "CONDITIONING", "link": 1904}, {"name": "vae", "type": "VAE", "link": 2004}, {"name": "pixels", "type": "IMAGE", "link": 1920}, {"name": "mask", "type": "MASK", "link": 2094}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1962], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1908], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": []}, {"id": 1224, "type": "SamplerCustomAdvanced", "pos": {"0": -5836, "1": 3193}, "size": {"0": 314.0994873046875, "1": 106}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1881, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1502, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1503, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 2024, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1908, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1497], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1570, "type": "APersonMaskGenerator", "pos": {"0": -7642.51220703125, "1": 2269.************}, "size": {"0": 315, "1": 178}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2058}], "outputs": [{"name": "masks", "type": "MASK", "links": [2042], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, true, false, false, 0.5]}, {"id": 1510, "type": "Reroute", "pos": {"0": -7989, "1": 3556}, "size": [75, 26], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2029}], "outputs": [{"name": "", "type": "IMAGE", "links": [1920, 2012], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1532, "type": "Zoe-DepthMapPreprocessor", "pos": {"0": -7770, "1": 3658}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2012}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1951, 2076], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "widgets_values": [1024]}, {"id": 1222, "type": "VAEDecode", "pos": {"0": -5430, "1": 3233}, "size": {"0": 190.54541015625, "1": 46}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1497}, {"name": "vae", "type": "VAE", "link": 2005}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2091], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1628, "type": "ConrainPythonExecutor", "pos": {"0": -8330.**********, "1": 2750.**********}, "size": {"0": 354.08526611328125, "1": 193.51210021972656}, "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 2130, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2131], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1566, "type": "Reroute", "pos": {"0": -7915.**********, "1": 2967.**********}, "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2111}], "outputs": [{"name": "", "type": "IMAGE", "links": [2029, 2056], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1622, "type": "ConrainPythonExecutor", "pos": {"0": -8330.**********, "1": 2670.**********}, "size": {"0": 354.08526611328125, "1": 193.51210021972656}, "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 2125, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2128], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1642, "type": "Reroute", "pos": {"0": -8528.**********, "1": 2465.**********}, "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2163}], "outputs": [{"name": "", "type": "IMAGE", "links": [2151, 2152], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1601, "type": "Text String", "pos": {"0": -6240, "1": 3864}, "size": {"0": 315, "1": 190}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [2089], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [2090], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 1573, "type": "ImpactSimpleDetectorSEGS", "pos": {"0": -7210.44873046875, "1": 2136.500732421875}, "size": {"0": 315, "1": 310}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 2038, "label": "bbox_detector"}, {"name": "image", "type": "IMAGE", "link": 2057, "label": "image"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7, "label": "segm_detector_opt"}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [2040], "slot_index": 0, "shape": 3, "label": "SEGS"}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 30]}, {"id": 1568, "type": "ConrainRandomPrompts", "pos": {"0": -8985, "1": 3539}, "size": {"0": 602.1065673828125, "1": 245.0613250732422}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [2067], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 991, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 1484, "type": "RandomNoise", "pos": {"0": -6249, "1": 3175}, "size": {"0": 255.33419799804688, "1": 82}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1881], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed?number}", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 1603, "type": "ConrainGrowMaskWithBlur", "pos": {"0": -7580.44873046875, "1": 2647.500732421875}, "size": {"0": 340.20001220703125, "1": 246}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2092}], "outputs": [{"name": "mask", "type": "MASK", "links": [2094], "slot_index": 0}, {"name": "mask_inverted", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [2, 0, true, false, 10, 1, 1, true]}, {"id": 1571, "type": "SAMLoader", "pos": {"0": -7210.44873046875, "1": 1977.500732421875}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [2039], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_h_4b8939.pth", "AUTO"]}, {"id": 1565, "type": "LoadImage", "pos": {"0": -8948.**********, "1": 2488.**********}, "size": {"0": 315, "1": 314}, "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2162], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${targetImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 1611, "type": "ImageCrop+", "pos": {"0": -8079.**********, "1": 2479.**********}, "size": {"0": 316.08935546875, "1": 239.64944458007812}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2152}, {"name": "width", "type": "INT", "link": 2128, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 2131, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2111], "slot_index": 0}, {"name": "x", "type": "INT", "links": null}, {"name": "y", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 1602, "type": "ConrainImageSave", "pos": {"0": -5711, "1": 3842}, "size": {"0": 320, "1": 266}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2091, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 2089, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 2090, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 1644, "type": "ImageResize+", "pos": {"0": -8753.**********, "1": 2941.**********}, "size": {"0": 315, "1": 218}, "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2162}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2163], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 0]}, {"id": 1616, "type": "GetImageSize+", "pos": {"0": -8367, "1": 2847}, "size": {"0": 214.20001220703125, "1": 66}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2151}], "outputs": [{"name": "width", "type": "INT", "links": [2125], "slot_index": 0}, {"name": "height", "type": "INT", "links": [2130], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}], "links": [[1497, 1224, 0, 1222, 0, "LATENT"], [1502, 1227, 0, 1224, 1, "GUIDER"], [1503, 1225, 0, 1224, 2, "SAMPLER"], [1881, 1484, 0, 1224, 0, "NOISE"], [1895, 1488, 0, 1227, 0, "MODEL"], [1897, 1489, 0, 1490, 0, "CONDITIONING"], [1900, 1490, 0, 1491, 0, "CONDITIONING"], [1904, 1492, 0, 1491, 1, "CONDITIONING"], [1908, 1491, 2, 1224, 4, "LATENT"], [1920, 1510, 0, 1491, 3, "IMAGE"], [1951, 1532, 0, 1530, 0, "IMAGE"], [1954, 1530, 0, 1531, 2, "CONTROL_NET_STACK"], [1962, 1491, 0, 1531, 0, "CONDITIONING"], [1963, 1492, 0, 1531, 1, "CONDITIONING"], [1964, 1531, 0, 1227, 1, "CONDITIONING"], [1999, 1102, 0, 1551, 0, "*"], [2004, 1551, 0, 1491, 2, "VAE"], [2005, 1551, 0, 1222, 1, "VAE"], [2012, 1510, 0, 1532, 0, "IMAGE"], [2024, 1226, 0, 1224, 3, "SIGMAS"], [2029, 1566, 0, 1510, 0, "*"], [2038, 1572, 0, 1573, 0, "BBOX_DETECTOR"], [2039, 1571, 0, 1574, 0, "SAM_MODEL"], [2040, 1573, 0, 1574, 1, "SEGS"], [2041, 1574, 0, 1575, 0, "MASK"], [2042, 1570, 0, 1575, 1, "MASK"], [2056, 1566, 0, 1587, 0, "*"], [2057, 1587, 0, 1573, 1, "IMAGE"], [2058, 1587, 0, 1570, 0, "IMAGE"], [2059, 1587, 0, 1574, 2, "IMAGE"], [2067, 1568, 0, 1489, 1, "STRING"], [2069, 1591, 0, 1592, 0, "MODEL"], [2070, 1590, 0, 1592, 1, "CLIP"], [2076, 1532, 0, 1530, 1, "IMAGE"], [2081, 1594, 0, 1492, 0, "CLIP"], [2082, 1594, 0, 1489, 0, "CLIP"], [2083, 1593, 0, 1488, 0, "MODEL"], [2084, 1593, 0, 1226, 0, "MODEL"], [2085, 1592, 0, 1593, 0, "*"], [2086, 1592, 1, 1594, 0, "*"], [2089, 1601, 0, 1602, 1, "STRING"], [2090, 1601, 1, 1602, 2, "STRING"], [2091, 1222, 0, 1602, 0, "IMAGE"], [2092, 1575, 0, 1603, 0, "MASK"], [2094, 1603, 0, 1491, 4, "MASK"], [2111, 1611, 0, 1566, 0, "*"], [2125, 1616, 0, 1622, 0, "*"], [2128, 1622, 0, 1611, 1, "INT"], [2130, 1616, 1, 1628, 0, "*"], [2131, 1628, 0, 1611, 2, "INT"], [2151, 1642, 0, 1616, 0, "IMAGE"], [2152, 1642, 0, 1611, 0, "IMAGE"], [2162, 1565, 0, 1644, 0, "IMAGE"], [2163, 1644, 0, 1642, 0, "*"]], "groups": [{"title": "TEXT | GENERATION", "bounding": [-9014, 3105, 3862, 1043], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "", "bounding": [-9016, 1899, 1295, 1176], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-7671, 1903, 1560, 1168], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3719008264462867, "offset": [9058.407381709607, -2004.5914342278284]}}, "version": 0.4, "seed_widgets": {"1484": 0, "1568": 1}}}}}