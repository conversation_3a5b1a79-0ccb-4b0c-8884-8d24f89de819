package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.CreativeBatchDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class CreativeBatchExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CreativeBatchExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CreativeBatchExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CreativeBatchExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CreativeBatchExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria)this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria)this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andModelIdIsNull() {
            addCriterion("model_id is null");
            return (Criteria)this;
        }

        public Criteria andModelIdIsNotNull() {
            addCriterion("model_id is not null");
            return (Criteria)this;
        }

        public Criteria andModelIdEqualTo(Integer value) {
            addCriterion("model_id =", value, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdNotEqualTo(Integer value) {
            addCriterion("model_id <>", value, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdGreaterThan(Integer value) {
            addCriterion("model_id >", value, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("model_id >=", value, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdLessThan(Integer value) {
            addCriterion("model_id <", value, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdLessThanOrEqualTo(Integer value) {
            addCriterion("model_id <=", value, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdIn(List<Integer> values) {
            addCriterion("model_id in", values, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdNotIn(List<Integer> values) {
            addCriterion("model_id not in", values, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdBetween(Integer value1, Integer value2) {
            addCriterion("model_id between", value1, value2, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("model_id not between", value1, value2, "modelId");
            return (Criteria)this;
        }

        public Criteria andModelTypeEqualTo(String modelType) {
            addCriterion("model_type =", modelType, "modelType");
            return (Criteria)this;
        }

        public Criteria andTypeEqualTo(String type) {
            addCriterion("type =", type, "type");
            return (Criteria)this;
        }

        public Criteria andTypeIn(List<String> types) {
            addCriterion("type in", types, "type");
            return (Criteria)this;
        }

        public Criteria andTypeNotIn(List<String> types) {
            addCriterion("type not in", types, "type");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria)this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andShowImageIsNull() {
            addCriterion("show_image is null");
            return (Criteria)this;
        }

        public Criteria andShowImageIsNotNull() {
            addCriterion("show_image is not null");
            return (Criteria)this;
        }

        public Criteria andShowImageEqualTo(String value) {
            addCriterion("show_image =", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotEqualTo(String value) {
            addCriterion("show_image <>", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageGreaterThan(String value) {
            addCriterion("show_image >", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageGreaterThanOrEqualTo(String value) {
            addCriterion("show_image >=", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLessThan(String value) {
            addCriterion("show_image <", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLessThanOrEqualTo(String value) {
            addCriterion("show_image <=", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLike(String value) {
            addCriterion("show_image like", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotLike(String value) {
            addCriterion("show_image not like", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageIn(List<String> values) {
            addCriterion("show_image in", values, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotIn(List<String> values) {
            addCriterion("show_image not in", values, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageBetween(String value1, String value2) {
            addCriterion("show_image between", value1, value2, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotBetween(String value1, String value2) {
            addCriterion("show_image not between", value1, value2, "showImage");
            return (Criteria)this;
        }

        public Criteria andImageProportionIsNull() {
            addCriterion("image_proportion is null");
            return (Criteria)this;
        }

        public Criteria andImageProportionIsNotNull() {
            addCriterion("image_proportion is not null");
            return (Criteria)this;
        }

        public Criteria andImageProportionEqualTo(String value) {
            addCriterion("image_proportion =", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionNotEqualTo(String value) {
            addCriterion("image_proportion <>", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionGreaterThan(String value) {
            addCriterion("image_proportion >", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionGreaterThanOrEqualTo(String value) {
            addCriterion("image_proportion >=", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionLessThan(String value) {
            addCriterion("image_proportion <", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionLessThanOrEqualTo(String value) {
            addCriterion("image_proportion <=", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionLike(String value) {
            addCriterion("image_proportion like", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionNotLike(String value) {
            addCriterion("image_proportion not like", value, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionIn(List<String> values) {
            addCriterion("image_proportion in", values, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionNotIn(List<String> values) {
            addCriterion("image_proportion not in", values, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionBetween(String value1, String value2) {
            addCriterion("image_proportion between", value1, value2, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andImageProportionNotBetween(String value1, String value2) {
            addCriterion("image_proportion not between", value1, value2, "imageProportion");
            return (Criteria)this;
        }

        public Criteria andBatchCntIsNull() {
            addCriterion("batch_cnt is null");
            return (Criteria)this;
        }

        public Criteria andBatchCntIsNotNull() {
            addCriterion("batch_cnt is not null");
            return (Criteria)this;
        }

        public Criteria andBatchCntEqualTo(Integer value) {
            addCriterion("batch_cnt =", value, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntNotEqualTo(Integer value) {
            addCriterion("batch_cnt <>", value, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntGreaterThan(Integer value) {
            addCriterion("batch_cnt >", value, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("batch_cnt >=", value, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntLessThan(Integer value) {
            addCriterion("batch_cnt <", value, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntLessThanOrEqualTo(Integer value) {
            addCriterion("batch_cnt <=", value, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntIn(List<Integer> values) {
            addCriterion("batch_cnt in", values, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntNotIn(List<Integer> values) {
            addCriterion("batch_cnt not in", values, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntBetween(Integer value1, Integer value2) {
            addCriterion("batch_cnt between", value1, value2, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andBatchCntNotBetween(Integer value1, Integer value2) {
            addCriterion("batch_cnt not between", value1, value2, "batchCnt");
            return (Criteria)this;
        }

        public Criteria andPromptIdIsNull() {
            addCriterion("prompt_id is null");
            return (Criteria)this;
        }

        public Criteria andPromptIdIsNotNull() {
            addCriterion("prompt_id is not null");
            return (Criteria)this;
        }

        public Criteria andPromptIdEqualTo(String value) {
            addCriterion("prompt_id =", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdNotEqualTo(String value) {
            addCriterion("prompt_id <>", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdGreaterThan(String value) {
            addCriterion("prompt_id >", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdGreaterThanOrEqualTo(String value) {
            addCriterion("prompt_id >=", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdLessThan(String value) {
            addCriterion("prompt_id <", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdLessThanOrEqualTo(String value) {
            addCriterion("prompt_id <=", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdLike(String value) {
            addCriterion("prompt_id like", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdNotLike(String value) {
            addCriterion("prompt_id not like", value, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdIn(List<String> values) {
            addCriterion("prompt_id in", values, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdNotIn(List<String> values) {
            addCriterion("prompt_id not in", values, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdBetween(String value1, String value2) {
            addCriterion("prompt_id between", value1, value2, "promptId");
            return (Criteria)this;
        }

        public Criteria andPromptIdNotBetween(String value1, String value2) {
            addCriterion("prompt_id not between", value1, value2, "promptId");
            return (Criteria)this;
        }

        public Criteria andResultPathIsNull() {
            addCriterion("result_path is null");
            return (Criteria)this;
        }

        public Criteria andResultPathIsNotNull() {
            addCriterion("result_path is not null");
            return (Criteria)this;
        }

        public Criteria andResultPathEqualTo(String value) {
            addCriterion("result_path =", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathNotEqualTo(String value) {
            addCriterion("result_path <>", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathGreaterThan(String value) {
            addCriterion("result_path >", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathGreaterThanOrEqualTo(String value) {
            addCriterion("result_path >=", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathLessThan(String value) {
            addCriterion("result_path <", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathLessThanOrEqualTo(String value) {
            addCriterion("result_path <=", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathLike(String value) {
            addCriterion("result_path like", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathNotLike(String value) {
            addCriterion("result_path not like", value, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathIn(List<String> values) {
            addCriterion("result_path in", values, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathNotIn(List<String> values) {
            addCriterion("result_path not in", values, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathBetween(String value1, String value2) {
            addCriterion("result_path between", value1, value2, "resultPath");
            return (Criteria)this;
        }

        public Criteria andResultPathNotBetween(String value1, String value2) {
            addCriterion("result_path not between", value1, value2, "resultPath");
            return (Criteria)this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria)this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria)this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria)this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria)this;
        }

        public Criteria andOperatorIdEqualTo(Integer value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotEqualTo(Integer value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdGreaterThan(Integer value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdLessThan(Integer value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIn(List<Integer> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotIn(List<Integer> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdBetween(Integer value1, Integer value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria)this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria)this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria)this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }
        public Criteria andOnlyDownloaded(){
            addCriterion("`ext_info` ->> '$.downloadedImgs' is not null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andUserNickLike(String value) {
            addCriterion("user_nick like", value, "userNick");
            return (Criteria)this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andBizTagEqualTo(String bizTag) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.bizTag')) =", bizTag, "extInfo");
            return (Criteria)this;
        }

        public Criteria andBizTagNotEqualTo(String bizTag) {
            addCriterion(
                "(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.bizTag')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$"
                + ".bizTag')) !='" + bizTag + "')");
            return (Criteria)this;
        }

        public Criteria andSelectNotSyncData() {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.isSyncToImageCase')) =", "", "extInfo");
            return (Criteria)this;
        }

        public Criteria andRelatedOperatorEqualTo(String relatedOperator) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.relatedOperator')) =", relatedOperator, "extInfo");
            return (Criteria)this;
        }

        public Criteria andRelatedOperatorIsNull() {
            addCriterion(
                "(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.relatedOperator')) is null or JSON_UNQUOTE(JSON_EXTRACT"
                + "(ext_info, '$.relatedOperator')) = '')");
            return (Criteria)this;
        }

        public Criteria andOperatorIdOrBizTagEqualTo(Integer value, String bizTag) {
            addCriterion(
                "(operator_id =" + value + " or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.bizTag')) = '" + bizTag + "')");
            return (Criteria)this;
        }

        public Criteria andEnabledModel(Integer userId) {
            addCriterion(
                "(model_type = 'SYSTEM' or model_id is null or model_id in (select id from material_model where "
                + "status = 'ENABLED' and deleted = 0 and user_id = " + userId + "))");
            return (Criteria)this;
        }

        public Criteria andEnabledModel() {
            addCriterion(
                "(model_type = 'SYSTEM' or model_id is null or model_id in (select id from material_model where "
                + "status = 'ENABLED' and deleted = 0 ))");
            return (Criteria)this;
        }

        public Criteria andRefineIsNotNull() {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.refineStatus')) is not null");
            return (Criteria)this;
        }

        public Criteria andRefineStatusEqualTo(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.refineStatus')) =", value, "refineStatus");
            return (Criteria)this;
        }

        public Criteria andNewModelEqualTo(Boolean value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.enableNewModel')) =", String.valueOf(value),
                "enableNewModel");
            return (Criteria)this;
        }

        public Criteria andLikeContains(String value) {
            addCriterion("JSON_SEARCH(ext_info->'$.like', 'one', '" + value + "') IS NOT NULL");
            return (Criteria)this;
        }

        public Criteria andGarmentTypeEquals(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.garmentType')) =", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andGarmentTypeIn(List<String> value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.garmentType')) in", value, "extInfo");
            return (Criteria)this;
        }

        public Criteria andFilterCreativeTypeNotIn(List<String> value) {
            addCriterion("type not in", value, "type");
            return (Criteria)this;
        }

        public Criteria andIsDemoTag() {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.demoTag')) = 'Y'");
            return (Criteria)this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", "%" + value + "%", "title");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsCustomer() {
            addCriterion("user_id in (select user_id from user_vip_view )");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsNotCustomer() {
            addCriterion("user_id not in (select user_id from user_vip_view )");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsNormal() {
            addCriterion("user_id in (select user_id from user_vip_view where vip = 0)");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsVIP() {
            addCriterion("user_id in (select user_id from user_vip_view where vip = 1)");
            return (Criteria)this;
        }

        public Criteria andBizTypeIsNull() {
            addCriterion("biz_type is null");
            return (Criteria)this;
        }

        public Criteria andBizTypeIsNotNull() {
            addCriterion("biz_type is not null");
            return (Criteria)this;
        }

        public Criteria andBizTypeEqualTo(String value) {
            addCriterion("biz_type =", value, "bizType");
            return (Criteria)this;
        }

        public Criteria andBizTypeNotEqualTo(String value) {
            addCriterion("biz_type <>", value, "bizType");
            return (Criteria)this;
        }

        public Criteria andBizTypeIn(List<String> values) {
            addCriterion("biz_type in", values, "bizType");
            return (Criteria)this;
        }

        public Criteria andBizTypeNotIn(List<String> values) {
            addCriterion("biz_type not in", values, "bizType");
            return (Criteria)this;
        }

        public Criteria andElementIdIn(List<Integer> values) {
            String value = values.stream().map(String::valueOf).collect(Collectors.joining(","));
            addCriterion("id in (select batch_id from creative_batch_elements where element_id in (" + value + "))");
            return (Criteria)this;
        }

        public Criteria andSceneIdEqualTo(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.sceneId')) = " , value , "sceneId");
            return (Criteria)this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.startTime')) <", value, "createTime");
            return (Criteria)this;
        }

    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(CreativeBatchDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(
                CreativeBatchDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}