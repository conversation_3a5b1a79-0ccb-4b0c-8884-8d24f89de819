package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CachedOrder implements Serializable {
    private String orderNo;
    private String codeUrl;
    private Integer operatorUserId;
    private Integer masterUserId;
    private Date createTime;
    private String planCode;
    private Integer amountInCents;
    // 订单代理用户id（后台管理员收款）
    private Integer orderAgentUserId;
    private OrderInfoVO orderInfo;
}
