<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.ShowCaseDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.ShowCaseDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="main_url" jdbcType="VARCHAR" property="mainUrl" />
    <result column="show_image" jdbcType="VARCHAR" property="showImage" />
    <result column="face_id" jdbcType="INTEGER" property="faceId" />
    <result column="scene_id" jdbcType="INTEGER" property="sceneId" />
    <result column="model_id" jdbcType="INTEGER" property="modelId" />
    <result column="model_url" jdbcType="VARCHAR" property="modelUrl" />
    <result column="model_mini_url" jdbcType="VARCHAR" property="modelMiniUrl" />
    <result column="order" jdbcType="INTEGER" property="order" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="topped" jdbcType="BIT" property="topped" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="cloth_collocation" jdbcType="LONGVARCHAR" property="clothCollocation" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from show_case
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.ShowCaseDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into show_case (name, type, main_url, 
      show_image, face_id, scene_id, 
      model_id, model_url, model_mini_url, `order`,
      tags, status, topped, memo,
      user_id, operator_id, create_time, 
      modify_time, cloth_collocation)
    values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{mainUrl,jdbcType=VARCHAR}, 
      #{showImage,jdbcType=VARCHAR}, #{faceId,jdbcType=INTEGER}, #{sceneId,jdbcType=INTEGER}, 
      #{modelId,jdbcType=INTEGER}, #{modelUrl,jdbcType=VARCHAR}, #{modelMiniUrl,jdbcType=VARCHAR}, #{order,jdbcType=INTEGER},
      #{tags,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{topped,jdbcType=BIT}, #{memo,jdbcType=VARCHAR},
      #{userId,jdbcType=INTEGER}, #{operatorId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{clothCollocation,jdbcType=LONGVARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.ShowCaseDO">
    update show_case
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      main_url = #{mainUrl,jdbcType=VARCHAR},
      show_image = #{showImage,jdbcType=VARCHAR},
      face_id = #{faceId,jdbcType=INTEGER},
      scene_id = #{sceneId,jdbcType=INTEGER},
      model_id = #{modelId,jdbcType=INTEGER},
      model_url = #{modelUrl,jdbcType=VARCHAR},
      model_mini_url = #{modelMiniUrl,jdbcType=VARCHAR},
      `order` = #{order,jdbcType=INTEGER},
      tags = #{tags,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      topped = #{topped,jdbcType=BIT},
      memo = #{memo,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      cloth_collocation = #{clothCollocation,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select id, name, type, main_url, show_image, face_id, scene_id, model_id, model_url, model_mini_url,
    `order`, tags, status, topped, memo, user_id, operator_id, create_time, modify_time, cloth_collocation
    from show_case
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, name, type, main_url, show_image, face_id, scene_id, model_id, model_url, model_mini_url,
    `order`, tags, status, topped, memo, user_id, operator_id, create_time, modify_time, cloth_collocation
    from show_case
  </select>
</mapper>