package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.SubUserService;
import ai.conrain.aigc.platform.service.component.UserOrganizationService;
import ai.conrain.aigc.platform.service.component.UserPointLogService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.OrganizationTypeEnum;
import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserReviewActionEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerPerformanceQuery;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.query.UserPointLogQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.request.AddDeptRequest;
import ai.conrain.aigc.platform.service.model.request.AddDistributorCustomerForm;
import ai.conrain.aigc.platform.service.model.request.AddDistributorStaffRequest;
import ai.conrain.aigc.platform.service.model.request.UpdateDeptRequest;
import ai.conrain.aigc.platform.service.model.request.UpdateDistributorCustomerForm;
import ai.conrain.aigc.platform.service.model.request.UpdateDistributorStaffRequest;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.request.UserUpdate;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CustomRoleVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorClothLoraModelVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerPointUsageVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerTopupSummary;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerTopupVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorMerchantVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorPerformanceQueryUserOptionsVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.MerchantTopupVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.RelatedAccounts;
import ai.conrain.aigc.platform.service.model.vo.RelatedDistributorInfo;
import ai.conrain.aigc.platform.service.model.vo.UserOptionVO;
import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointLogVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointUsageInfoVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.model.vo.UserReviewInfo;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SALES_TYPE;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;

@Slf4j
@Api("渠道商")
@Validated
@RestController
@RequestMapping("/distributor")
public class DistributorController {

    @Autowired
    private UserService userService;

    @Autowired
    private SubUserService subUserService;

    @Autowired
    private DistributorCustomerService distributorCustomerService;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private CreativeBatchService creativeBatchService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private UserOrganizationService userOrganizationService;

    @Autowired
    private UserPointLogService userPointLogService;

    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private DistributorService distributorService;

    @Autowired
    private UserPointService userPointService;

    @PostMapping("/createStaff")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("新建渠道商的员工账号")
    public Result<Integer> createStaff(@Valid @RequestBody AddDistributorStaffRequest req) {

        // 创建渠道商子账号
        UserRegister reg = new UserRegister();
        reg.setMobile(req.getMobile());
        reg.setNickName(req.getNickName());
        reg.setRoleType(DISTRIBUTOR.getCode());
        reg.setCustomRole(StringUtils.defaultIfBlank(req.getCustomRole(), CustomRoleEnum.SALES_MEMBER.getCode()));
        reg.setOnlyMasterCanCreate(false);
        // 创建【非渠道运营账号】时，初始化关联的商家账号
        reg.setInitDistributorRelatedAccounts(!StringUtils.equals(req.getCustomRole(), CustomRoleEnum.OPS_MEMBER.getCode()));

        UserVO subUser = subUserService.create(reg);

        // 创建子账号与组织关联
        UserOrganizationVO userOrganizationVO = new UserOrganizationVO();
        userOrganizationVO.setUserId(subUser.getId());
        userOrganizationVO.setOrgId(req.getDeptOrgId());
        userOrganizationVO.setCreatorOperatorUserId(OperationContextHolder.getOperatorUserId());
        userOrganizationVO.setModifierOperatorUserId(OperationContextHolder.getOperatorUserId());
        userOrganizationService.insert(userOrganizationVO);

        return Result.success(subUser.getId());
    }

    @PostMapping("/updateStaff")
    @Transactional(rollbackFor = Throwable.class)
    @ApiOperation("更新渠道商的员工账号")
    public Result<?> updateStaff(@Valid @RequestBody UpdateDistributorStaffRequest req) {
        Integer operator = OperationContextHolder.getOperatorUserId();
        Integer userId = req.getId();

        UserVO target = userService.selectById(userId);
        AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "修改用户信息失败");

        //1.修改的权限控制，要么是自己账号，要么修改的是下属子账户
        OrganizationVO operatorOrg = organizationService.queryOrganizationByUserId(operator);
        OrganizationVO targetUserOrg = organizationService.queryOrganizationByUserId(userId);
        boolean isSub = OperationContextHolder.isMasterUser() || organizationService.checkIfSubOrg(operatorOrg.getId(),
            targetUserOrg.getId());

        if (StringUtils.isNotBlank(target.getCustomRole()) && !operator.equals(userId) && !isSub) {
            log.error("修改员工账号信息权限校验非法，修改人：{}, 被修改人：{}", operator, userId);
            throw new BizException(ResultCode.ILLEGAL_PERMISSION, "权限非法");
        }

        //2.如果修改了手机号，则检查手机号码是否已存在
        if (StringUtils.isNotBlank(req.getMobile()) && !StringUtils.equals(req.getMobile(), target.getMobile())) {
            userService.checkMobileExists(req.getMobile());
        }

        //3.更新
        UserVO data = new UserVO();
        data.setId(userId);
        data.setMobile(req.getMobile());
        data.setLoginId(req.getMobile());
        data.setNickName(req.getNickName());
        data.setCustomRole(req.getCustomRole());

        userService.updateByIdSelective(data);

        //部门变更 组织变更
        if (req.getDeptOrgId() != null && !req.getDeptOrgId().equals(targetUserOrg.getId())) {

            //先删除旧组织关联关系
            userOrganizationService.deleteByUserId(userId);

            //再新建组织关联
            UserOrganizationVO userOrganizationVO = new UserOrganizationVO();
            userOrganizationVO.setUserId(userId);
            userOrganizationVO.setOrgId(req.getDeptOrgId());
            userOrganizationVO.setCreatorOperatorUserId(operator);
            userOrganizationVO.setModifierOperatorUserId(operator);
        }

        return Result.success();
    }


    /**
     * 渠道管理员 / 渠道二级管理员：查询当前渠道所有人
     *
     */
    @PostMapping("/queryAllStaffs")
    public Result<List<UserVO>> queryAllStaffs() {

        AssertUtil.assertTrue(CustomRoleEnum.CHANNEL_ADMIN == OperationContextHolder.getDistributorCustomRole()
                        || CustomRoleEnum.SECOND_CHANNEL_ADMIN == OperationContextHolder.getDistributorCustomRole(),
                ResultCode.ILLEGAL_PERMISSION,
                "当前用户不是渠道商管理员或二级管理员，不能查询渠道商员工");

        OrganizationVO userOrg = organizationService.queryOrganizationByUserId(
                OperationContextHolder.getOperatorUserId());
        List<Integer> userIds = organizationService.getUserIdsOfSubOrgIdsByOrgId(userOrg.getId());

        if (CollectionUtils.isNotEmpty(userIds)) {
            UserQuery userQuery = new UserQuery();
            userQuery.setRoleType(DISTRIBUTOR.getCode());
            userQuery.setIds(userIds);
            List<UserVO> staffUsers = userService.queryUsers(userQuery);
            return Result.success(staffUsers);
        }

        return Result.success();
    }

    /**
     * 根据当前人的权限，查销售人员列表
     * 用于添加会员时，进行分配销售
     *
     * @return
     */
    @PostMapping("/queryAllSales")
    public Result<List<UserVO>> queryAllSales() {

        OrganizationVO userOrg = organizationService.queryOrganizationByUserId(
            OperationContextHolder.getOperatorUserId());
        List<Integer> userIds = organizationService.getUserIdsOfSubOrgIdsByOrgId(userOrg.getId());

        if (CollectionUtils.isNotEmpty(userIds)) {
            UserQuery userQuery = new UserQuery();
            userQuery.setRoleType(DISTRIBUTOR.getCode());
            userQuery.setCustomRoleList(
                Arrays.asList(CustomRoleEnum.SALES_MEMBER.getCode(), CustomRoleEnum.CHANNEL_ADMIN.getCode(),
                    CustomRoleEnum.SECOND_CHANNEL_ADMIN.getCode()));
            userQuery.setIds(userIds);
            List<UserVO> staffUsers = userService.queryUsers(userQuery);
            return Result.success(staffUsers);
        }

        return Result.success();
    }

    /**
     * 根据当前人的权限，查运营人员列表
     * 用于添加会员时，进行分配运营
     *
     * @return
     */
    @PostMapping("/queryAllOperators")
    public Result<List<UserVO>> queryAllOperators() {

        OrganizationVO userOrg = organizationService.queryOrganizationByUserId(
            OperationContextHolder.getOperatorUserId());
        List<Integer> userIds = organizationService.getUserIdsOfSubOrgIdsByOrgId(userOrg.getId());

        if (CollectionUtils.isNotEmpty(userIds)) {
            UserQuery userQuery = new UserQuery();
            userQuery.setRoleType(DISTRIBUTOR.getCode());
            userQuery.setCustomRoleList(
                Arrays.asList(CustomRoleEnum.OPS_MEMBER.getCode(), CustomRoleEnum.CHANNEL_ADMIN.getCode(),
                    CustomRoleEnum.SECOND_CHANNEL_ADMIN.getCode()));
            userQuery.setIds(userIds);
            List<UserVO> staffUsers = userService.queryUsers(userQuery);
            return Result.success(staffUsers);
        }

        return Result.success();
    }

    /**
     * 分页查询渠道商客户列表
     *
     * @param query
     * @return
     */
    @PostMapping("/queryCustomersByPage")
    public Result<PageInfo<DistributorMerchantVO>> queryCustomersByPage(
        @Valid @RequestBody DistributorCustomerQuery query) {
        return Result.success(distributorCustomerService.queryDistributorMerchantByPage(query));
    }

    /**
     * 创建渠道商客户
     *
     * @param req
     * @return
     */
    @PostMapping("/createCustomer")
    @Transactional(rollbackFor = Throwable.class)
    public Result<Integer> createCustomer(@Valid @RequestBody AddDistributorCustomerForm req) {
        UserRegister reg = new UserRegister();
        reg.setCorpName(req.getCorpName());
        reg.setMobile(req.getMobile());
        reg.setNickName(req.getNickName());
        reg.setRoleType(RoleTypeEnum.MERCHANT.getCode());
        reg.setUserStatus(UserStatusEnum.UNDER_REVIEW.getCode());
        reg.setUserReviewInfo(getUserReviewInfo());
        reg.setNeedInitMusePoints(req.getNeedInitMusePoints());

        UserVO userVO = userService.create(reg);
        AssertUtil.assertTrue(userVO != null && userVO.getId() != null, "创建商户失败");

        DistributorCustomerVO dc = getDistributorCustomerVO(req, userVO);

        dc = distributorCustomerService.insert(dc);

        autoReviewBySystem(userVO);

        return Result.success(userVO.getId());
    }

    private void autoReviewBySystem(UserVO user) {

        UserVO target = new UserVO();
        target.setId(user.getId());

        UserReviewInfo reviewInfo = user.getUserReviewInfo();
        reviewInfo.setReviewAction(UserReviewActionEnum.PASS.getCode());
        reviewInfo.setReviewerUserId(1);
        reviewInfo.setReviewTime(new Date());

        target.setUserReviewInfo(reviewInfo);
        target.setStatus(UserStatusEnum.ENABLED);

        userService.updateByIdSelective(target);
    }

    private static @org.jetbrains.annotations.NotNull DistributorCustomerVO getDistributorCustomerVO(
        AddDistributorCustomerForm req, UserVO userVO) {
        DistributorCustomerVO dc = new DistributorCustomerVO();
        dc.setCustomerMasterUserId(userVO.getId());
        dc.setDistributorCorpName(OperationContextHolder.getCorpName());
        dc.setDistributorCorpOrgId(OperationContextHolder.getCorpOrgId());
        dc.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());
        dc.setDistributorOperatorUserId(req.getRelatedOperatorId());
        dc.setDistributorSalesUserId(req.getRelatedSalesId());
        dc.setCreatorId(OperationContextHolder.getOperatorUserId());
        return dc;
    }

    private static @org.jetbrains.annotations.NotNull UserReviewInfo getUserReviewInfo() {
        UserReviewInfo reviewInfo = new UserReviewInfo();
        reviewInfo.setCreatorMasterUserId(OperationContextHolder.getMasterUserId());
        reviewInfo.setCreatorCorpOrgId(OperationContextHolder.getCorpOrgId());
        reviewInfo.setCreatorUserId(OperationContextHolder.getOperatorUserId());
        reviewInfo.setCreatorCorpName(OperationContextHolder.getCorpName());
        reviewInfo.setCreatorMobile(OperationContextHolder.getOperatorLoginId());
        reviewInfo.setCreatorUserName(OperationContextHolder.getOperatorNick());
        reviewInfo.setCreateTime(new Date());
        return reviewInfo;
    }

    /**
     * 编辑渠道商客户信息
     *
     * @param req
     * @return
     */
    @PostMapping("/updateCustomer")
    @Transactional(rollbackFor = Throwable.class)
    public Result<?> updateCustomer(@Valid @RequestBody UpdateDistributorCustomerForm req) {

        UserUpdate userUpdate = new UserUpdate();
        userUpdate.setUserId(req.getUserId());
        userUpdate.setMobile(req.getMobile());
        userUpdate.setNickName(req.getNickName());
        userUpdate.setCorpName(req.getCorpName());

        userService.updateBack(userUpdate);

        //更新渠道商客户关联的运营
        DistributorCustomerQuery dcq = new DistributorCustomerQuery();
        dcq.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());
        dcq.setCustomerMasterUserId(req.getUserId());
        List<DistributorCustomerVO> dcList = distributorCustomerService.queryDistributorCustomerList(dcq);
        AssertUtil.assertTrue(dcList != null && dcList.size() == 1, "渠道商客户不存在或存在多条数据");
        DistributorCustomerVO dc = dcList.get(0);

        DistributorCustomerVO target = new DistributorCustomerVO();
        target.setId(dc.getId());
        target.setDistributorOperatorUserId(req.getRelatedOperatorId());
        target.setDistributorSalesUserId(req.getRelatedSalesId());
        distributorCustomerService.updateByIdSelective(target);

        return Result.success();
    }

    @PostMapping("/deleteCustomer")
    @Transactional(rollbackFor = Throwable.class)
    public Result<?> deleteCustomer(@JsonArg @NotNull Integer userId) {

        userService.deleteById(userId);
        distributorService.deleteCustomer(userId);

        return Result.success();
    }

    /**
     * 分页查询渠道商视角的客户服装列表
     *
     * @param query
     * @return
     */
    @PostMapping("/queryLoraListByPage")
    public Result<PageInfo<DistributorClothLoraModelVO>> queryLoraList(@Valid @RequestBody MaterialModelQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1 || StringUtils.isBlank(query.getMaterialType())) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        //查当前渠道商的所有客户
        List<DistributorCustomerVO> customers = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(
            true);
        if (CollectionUtils.isEmpty(customers)) {
            return Result.success(new PageInfo<>());
        }

        query.setUserIds(
            customers.stream().map(DistributorCustomerVO::getCustomerMasterUserId).collect(Collectors.toList()));

        //material type为face或scene
        if (StringUtils.equalsIgnoreCase(query.getMaterialType(), MaterialType.face.name())
                || StringUtils.equalsIgnoreCase(query.getMaterialType(), MaterialType.scene.name())) {
            if (OperationContextHolder.isMasterUser()) {
                query.getUserIds().add(OperationContextHolder.getMasterUserId());
            } else {
                query.setOperatorId(OperationContextHolder.getOperatorUserId());
            }
        }

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        PageInfo<MaterialModelVO> lorasPage = materialModelService.queryPageWithBlobs4DistributorModels(query);
        if (lorasPage == null || CollectionUtils.isEmpty(lorasPage.getList())) {
            return Result.success(new PageInfo<>());
        } else {
            List<MaterialModelVO> materialModels = lorasPage.getList();
            List<Integer> customerIds = materialModels.stream().map(MaterialModelVO::getUserId).collect(
                Collectors.toList());

            DistributorCustomerQuery dcq = new DistributorCustomerQuery();
            dcq.setCustomerMasterUserIds(customerIds);
            dcq.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());

            List<DistributorCustomerVO> customerRelated = distributorCustomerService.queryDistributorCustomerList(dcq);

            List<Integer> operatorIds = customerRelated.stream().map(
                DistributorCustomerVO::getDistributorOperatorUserId).distinct().collect(Collectors.toList());
            List<Integer> salesIds = customerRelated.stream().map(DistributorCustomerVO::getDistributorSalesUserId)
                .distinct().collect(Collectors.toList());

            Map<Integer, DistributorCustomerVO> customerDistributorMap = customerRelated.stream().collect(
                    Collectors.toMap(DistributorCustomerVO::getCustomerMasterUserId, Function.identity()));

            List<Integer> userIds = new ArrayList<>();
            userIds.addAll(operatorIds);
            userIds.addAll(salesIds);

            List<UserVO> distributorStaffUsers = null;
            Map<Integer, UserVO> staffMap = null;

            if (CollectionUtils.isNotEmpty(userIds)) {
                distributorStaffUsers = userService.batchQueryById(userIds);
                staffMap = distributorStaffUsers.stream().collect(Collectors.toMap(UserVO::getId, Function.identity()));
            }

            List<DistributorClothLoraModelVO> list = new ArrayList<>();

            for (MaterialModelVO materialModel : materialModels) {
                DistributorClothLoraModelVO dcm = new DistributorClothLoraModelVO();
                BeanUtils.copyProperties(materialModel, dcm);

                if (staffMap != null) {
                    dcm.setRelatedDistributorInfo(getRelatedDistributorInfo(materialModel, customerDistributorMap, staffMap));
                }

                list.add(dcm);
            }

            PageInfo<DistributorClothLoraModelVO> ret = new PageInfo<>();
            ret.setList(MaterialModelController.pruneForFront(list));
            ret.setHasNextPage(lorasPage.isHasNextPage());
            ret.setSize(lorasPage.getSize());
            ret.setTotalCount(lorasPage.getTotalCount());

            return Result.success(ret);
        }
    }

    @org.jetbrains.annotations.NotNull
    private static RelatedDistributorInfo getRelatedDistributorInfo(MaterialModelVO materialModel, Map<Integer, DistributorCustomerVO> customerDistributorMap, Map<Integer, UserVO> map) {
        RelatedDistributorInfo rdi = new RelatedDistributorInfo();
        rdi.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());
        DistributorCustomerVO distributorCustomerVO = customerDistributorMap.get(materialModel.getUserId());

        if (distributorCustomerVO != null) {
            rdi.setDistributorSalesUserId(
                    distributorCustomerVO.getDistributorSalesUserId());
            rdi.setDistributorOperatorUserId(
                    distributorCustomerVO.getDistributorOperatorUserId());
        }

        if (rdi.getDistributorSalesUserId() != null) {
            UserVO sales = map.get(rdi.getDistributorSalesUserId());
            if (sales != null) {
                rdi.setDistributorSalesNickName(sales.getNickName());
                rdi.setDistributorSalesMobile(sales.getMobile());
            }
        }

        if (rdi.getDistributorOperatorUserId() != null) {
            UserVO operator = map.get(rdi.getDistributorOperatorUserId());
            if (operator != null) {
                rdi.setDistributorOperatorNickName(operator.getNickName());
                rdi.setDistributorOperatorMobile(operator.getMobile());
            }
        }
        return rdi;
    }

    /**
     * 分页查询渠道商视角的客户服装列表
     *
     * @param query
     * @return
     */
    @PostMapping("/queryCreativeRecordsByPage")
    public Result<PageInfo<CreativeBatchVO>> queryCreativeRecordsByPage(@Valid @RequestBody CreativeBatchQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        List<DistributorCustomerVO> customers = null;
        if (Objects.nonNull(query.getRelateDistOpUserId())) {
            DistributorCustomerQuery dcq = new DistributorCustomerQuery();
            dcq.setDistributorOperatorUserId(query.getRelateDistOpUserId());
            customers = distributorCustomerService.queryDistributorCustomerList(dcq);
        } else {
            customers = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(true);
        }

        if (CollectionUtils.isEmpty(customers)) {
            return Result.success(new PageInfo<>());
        }

        query.setUserIds(
            customers.stream().map(DistributorCustomerVO::getCustomerMasterUserId).collect(Collectors.toList()));

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        PageInfo<CreativeBatchVO> pageInfo = creativeBatchService.queryCreativeBatchByPage(query);
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            pageInfo.setList(SecurityUtils.filterExtInfo(pageInfo.getList(), CreativeController.outputKeyWhitelist));
        }
        return Result.success(pageInfo);
    }

    /**
     * 查询用户可见的组织树
     */
    @PostMapping("/getOrganizationTreeByUserId")
    public Result<OrganizationVO> getSubOrganizationTreeByUserId() {
        return Result.success(
            organizationService.getSubOrganizationTreeByUserId(OperationContextHolder.getOperatorUserId()));
    }

    /**
     * 为企业组织创建部门或子部门
     *
     * @param req
     * @return
     */
    @PostMapping("/createDept")
    public Result<Integer> createDept(@Valid @RequestBody AddDeptRequest req) {

        //只能创建子组织
        if (req.getParentId() == null || req.getParentId() < 0) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "父级组织ID非法");
        }

        OrganizationVO org = new OrganizationVO();
        org.setParentId(req.getParentId());
        org.setRoot(false);
        org.setOrgType(OrganizationTypeEnum.CORP_DEPT.getCode());
        org.setName(req.getName());
        org.putExtInfo(KEY_SALES_TYPE, req.getSalesType());

        org = organizationService.create(org);

        // 同时创建二级渠道管理员
        if (ObjectUtils.isNotEmpty(req.getNickName()) &&
                ObjectUtils.isNotEmpty(req.getMobile()) &&
                ObjectUtils.isNotEmpty(req.getCustomRole())) {

            UserRegister reg = new UserRegister();
            reg.setMobile(req.getMobile());
            reg.setNickName(req.getNickName());
            reg.setRoleType(DISTRIBUTOR.getCode());
            reg.setCustomRole(req.getCustomRole());
            reg.setOnlyMasterCanCreate(false);

            UserVO subUser = subUserService.create(reg);

            // 创建子账号与组织关联
            UserOrganizationVO userOrganizationVO = new UserOrganizationVO();
            userOrganizationVO.setUserId(subUser.getId());
            userOrganizationVO.setOrgId(org.getId());
            userOrganizationVO.setCreatorOperatorUserId(OperationContextHolder.getOperatorUserId());
            userOrganizationVO.setModifierOperatorUserId(OperationContextHolder.getOperatorUserId());
            userOrganizationService.insert(userOrganizationVO);
        }

        return Result.success(org.getId());
    }

    @PostMapping("/updateDept")
    public Result<?> updateDept(@Valid @RequestBody UpdateDeptRequest req) {
        OrganizationVO org = new OrganizationVO();
        org.setId(req.getId());
        org.setName(req.getName());
        org.setModifierOperatorUserId(OperationContextHolder.getOperatorUserId());

        organizationService.updateByIdSelective(org);

        return Result.success();
    }

    @PostMapping("/deleteDept")
    public Result<?> deleteDept(@NotNull @JsonArg Integer id) {
        organizationService.deleteById(id);
        return Result.success();
    }

    /**
     * 查询企业的角色列表（用于渠道商添加员工）
     *
     * @return
     */
    @PostMapping("/queryAllCorpRoles")
    public Result<List<CustomRoleVO>> queryAllCorpRoles() {
        CustomRoleEnum customRole = OperationContextHolder.getDistributorCustomRole();
        switch (Objects.requireNonNull(customRole)) {
            case CHANNEL_ADMIN:
                return buildRoles(CustomRoleEnum.values());
            case SECOND_CHANNEL_ADMIN:
                return buildRoles(CustomRoleEnum.OPS_MEMBER, CustomRoleEnum.SALES_MEMBER);
            case SALES_MEMBER:
                return buildRoles(CustomRoleEnum.SALES_MEMBER);
            case OPS_MEMBER:
                return buildRoles(CustomRoleEnum.OPS_MEMBER);
            default:
                throw new IllegalArgumentException("不支持的角色类型:" + customRole.getCode());
        }
    }

    private Result<List<CustomRoleVO>> buildRoles(CustomRoleEnum... roles) {
        return Result.success(Arrays.stream(roles).map(each -> {
            CustomRoleVO r = new CustomRoleVO();
            r.setCode(each.getCode());
            r.setName(each.getDesc());
            return r;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页查询部门下的员工（包含子部门员工）
     */
    @PostMapping("/queryDeptUsersIncludeSubByPage")
    public Result<PageInfo<UserVO>> queryDeptUsersIncludeSubByPage(@NotNull @JsonArg Integer deptOrgId,
                                                                   @NotNull @JsonArg Integer pageNum,
                                                                   @NotNull @JsonArg Integer pageSize) {
        List<Integer> subOrgIds = organizationService.getSubOrgIds(deptOrgId, true);
        if (CollectionUtils.isNotEmpty(subOrgIds)) {
            List<Integer> userIds = userOrganizationService.findAllByOrgIds(subOrgIds).stream().map(
                UserOrganizationVO::getUserId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)) {
                UserQuery userQuery = new UserQuery();
                userQuery.setIds(userIds);
                userQuery.setPageNum(pageNum);
                userQuery.setPageSize(pageSize);
                userQuery.setOrderBy("id desc");
                PageInfo<UserVO> users = userService.queryUserByPage(userQuery);
                if (users != null && CollectionUtils.isNotEmpty(users.getList())) {
                    for (UserVO user : users.getList()) {
                        RelatedAccounts relatedAccounts = userService.queryRelatedAccounts(user.getId());
                        if (relatedAccounts != null) {
                            user.setRelatedAccounts(relatedAccounts);
                        }
                    }
                }
                return Result.success(users);
            } else {
                PageInfo<UserVO> empty = new PageInfo<>();
                empty.setSize(0);
                empty.setHasNextPage(false);
                empty.setTotalCount(0);
                return Result.success(empty);
            }

        } else {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "部门不存在");
        }
    }

    /**
     * 前台分页查询积分使用记录
     * 渠道商端，业绩管理
     *
     * @param query
     * @return
     */
    @PostMapping("/queryUsagePerformanceByPage")
    public Result<PageInfo<DistributorCustomerPointUsageVO>> queryUsagePerformanceByPage(
        @Valid @RequestBody DistributorCustomerPerformanceQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        List<DistributorCustomerVO> distributorCustomerVOS = getDistributorCustomerVOS(query);
        if (CollectionUtils.isEmpty(distributorCustomerVOS)) {
            return Result.success(new PageInfo<>());
        }

        UserPointLogQuery userPointLogQuery = new UserPointLogQuery();
        {
            userPointLogQuery.setDateFrom(query.getDateFrom());
            userPointLogQuery.setDateTo(query.getDateTo());
            userPointLogQuery.setTypeList(
                Arrays.asList(PointLogTypeEnum.CREATIVE_CREATE.getCode(), PointLogTypeEnum.LORA_TRAIN.getCode()));
            userPointLogQuery.setPageNum(query.getPageNum());
            userPointLogQuery.setPageSize(query.getPageSize());
            userPointLogQuery.setOrderBy(query.getOrderBy());

            //只返回有消耗muse点的记录，没消耗muse点的忽略
            userPointLogQuery.setConsumeMusePoint(true);

            if (StringUtils.isBlank(userPointLogQuery.getOrderBy())) {
                userPointLogQuery.setOrderBy("id desc");
            }

            userPointLogQuery.setUserIds(distributorCustomerVOS.stream().map(
                DistributorCustomerVO::getCustomerMasterUserId).distinct().collect(Collectors.toList()));
        }

        PageInfo<UserPointUsageInfoVO> page = userPointLogService.queryPointUsageInfoByPage(userPointLogQuery);

        //1.根据客户id查客户
        Map<Integer, UserVO> userCache = new HashMap<>();

        //2.根据id查客户对应的运营和销售
        Map<Integer, DistributorCustomerVO> customerRelatedCache = distributorCustomerVOS.stream().collect(
            Collectors.toMap(DistributorCustomerVO::getCustomerMasterUserId, Function.identity()));

        List<DistributorCustomerPointUsageVO> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (UserPointUsageInfoVO each : page.getList()) {
                DistributorCustomerPointUsageVO target = new DistributorCustomerPointUsageVO();
                BeanUtils.copyProperties(each, target);

                Integer customerId = each.getUserId();
                DistributorCustomerVO customer = customerRelatedCache.get(customerId);

                if (customer.getDistributorOperatorUserId() != null) {
                    UserVO operator = userCache.computeIfAbsent(customer.getDistributorOperatorUserId(),
                        id -> userService.selectById(id));
                    target.setDistributorOperatorMobile(operator.getMobile());
                    target.setDistributorOperatorNickName(operator.getNickName());
                }

                if (customer.getDistributorSalesUserId() != null) {
                    UserVO sales = userCache.computeIfAbsent(customer.getDistributorSalesUserId(),
                        id -> userService.selectById(id));
                    target.setDistributorSalesMobile(sales.getMobile());
                    target.setDistributorSalesNickName(sales.getNickName());
                }

                list.add(target);
            }
        }

        PageInfo<DistributorCustomerPointUsageVO> ret = new PageInfo<>();
        ret.setList(list);
        ret.setSize(page.getSize());
        ret.setHasNextPage(page.isHasNextPage());
        ret.setTotalCount(page.getTotalCount());
        return Result.success(ret);
    }

    /**
     * 渠道商根据权限，查询商户充值记录
     *
     * @param query
     * @return
     */
    @PostMapping("/queryCustomerTopupByPage")
    public Result<PageInfo<DistributorCustomerTopupVO>> queryCustomerTopupByPage(
        @Valid @RequestBody DistributorCustomerPerformanceQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        List<DistributorCustomerVO> distributorCustomerVOS = getDistributorCustomerVOS(query);
        if (CollectionUtils.isEmpty(distributorCustomerVOS)) {
            return Result.success();
        }

        OrderInfoQuery orderQuery = new OrderInfoQuery();
        {
            orderQuery.setDateFrom(query.getDateFrom());
            orderQuery.setDateTo(query.getDateTo());
            orderQuery.setPageNum(query.getPageNum());
            orderQuery.setPageSize(query.getPageSize());
            orderQuery.setOrderBy(StringUtils.defaultIfBlank(query.getOrderBy(), "id desc"));
            orderQuery.setMasterUserIds(distributorCustomerVOS.stream().map(
                DistributorCustomerVO::getCustomerMasterUserId).distinct().collect(Collectors.toList()));
        }

        PageInfo<MerchantTopupVO> page = orderInfoService.queryMerchantTopupByPage(orderQuery);

        //1.根据客户id查客户
        Map<Integer, UserVO> userCache = new HashMap<>();

        //2.根据id查客户对应的运营和销售
        Map<Integer, DistributorCustomerVO> customerRelatedCache = distributorCustomerVOS.stream().collect(
            Collectors.toMap(DistributorCustomerVO::getCustomerMasterUserId, Function.identity()));

        List<DistributorCustomerTopupVO> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (MerchantTopupVO each : page.getList()) {
                DistributorCustomerTopupVO target = new DistributorCustomerTopupVO();
                BeanUtils.copyProperties(each, target);

                Integer customerId = each.getMasterUserId();
                DistributorCustomerVO customer = customerRelatedCache.get(customerId);

                if (customer.getDistributorOperatorUserId() != null) {
                    UserVO operator = userCache.computeIfAbsent(customer.getDistributorOperatorUserId(),
                        id -> userService.selectById(id));
                    target.setDistributorOperatorMobile(operator.getMobile());
                    target.setDistributorOperatorNickName(operator.getNickName());
                }

                if (customer.getDistributorSalesUserId() != null) {
                    UserVO sales = userCache.computeIfAbsent(customer.getDistributorSalesUserId(),
                        id -> userService.selectById(id));
                    target.setDistributorSalesMobile(sales.getMobile());
                    target.setDistributorSalesNickName(sales.getNickName());
                }

                list.add(target);
            }
        }

        PageInfo<DistributorCustomerTopupVO> details = new PageInfo<>();
        details.setList(list);
        details.setSize(page.getSize());
        details.setHasNextPage(page.isHasNextPage());
        details.setTotalCount(page.getTotalCount());

        return Result.success(details);
    }

    @PostMapping("/queryTopupSummary")
    public Result<DistributorCustomerTopupSummary> queryTopupSummary(
        @Valid @RequestBody DistributorCustomerPerformanceQuery query) {

        List<DistributorCustomerVO> distributorCustomerVOS = getDistributorCustomerVOS(query);
        if (CollectionUtils.isEmpty(distributorCustomerVOS)) {
            return Result.success();
        }

        OrderInfoQuery orderQuery = new OrderInfoQuery();
        {
            orderQuery.setDateFrom(query.getDateFrom());
            orderQuery.setDateTo(query.getDateTo());
            orderQuery.setOrderBy(StringUtils.defaultIfBlank(query.getOrderBy(), "id desc"));
            orderQuery.setMasterUserIds(distributorCustomerVOS.stream().map(
                DistributorCustomerVO::getCustomerMasterUserId).distinct().collect(Collectors.toList()));
        }

        DistributorCustomerTopupSummary summary = new DistributorCustomerTopupSummary();

        //总充值客户数
        int totalTopupCustomerCount = 0;
        //总充值次数
        int totalTopupCount = 0;
        //总充值金额
        BigDecimal totalAmount = new BigDecimal("0");

        //员工充值业绩统计
        List<OrderInfoVO> orders = orderInfoService.queryOrderInfoList(orderQuery);
        if (CollectionUtils.isNotEmpty(orders)) {
            totalTopupCustomerCount = (int)orders.stream().map(OrderInfoVO::getMasterUserId).distinct().count();
            totalTopupCount = orders.size();
            totalAmount = orders.stream().map(OrderInfoVO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        summary.setTotalAmount(totalAmount);
        summary.setTotalTopupCount(totalTopupCount);
        summary.setTotalTopupCustomerCount(totalTopupCustomerCount);

        List<Integer> salesIds = distributorCustomerVOS.stream().map(DistributorCustomerVO::getDistributorSalesUserId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(salesIds)) {
            //构建一个map，key为salesId，value为UserVO
            List<UserVO> salesUserList = userService.batchQueryById(salesIds);
            Map<Integer, UserVO> salesUserMap = salesUserList.stream().collect(
                Collectors.toMap(UserVO::getId, Function.identity()));

            //构建一个map，key为customerId，value为DistributorCustomerVO
            Map<Integer, DistributorCustomerVO> customerRelatedSalesMap = distributorCustomerVOS.stream().collect(
                Collectors.toMap(DistributorCustomerVO::getCustomerMasterUserId, Function.identity()));

            Map<Integer, DistributorCustomerTopupSummary.DistributorCustomerTopupStaffSummary> salesTopupItems
                = new HashMap<>();

            //对于每个销售人员，过滤出orders中它对应的订单子列表
            for (Integer id : salesIds) {
                List<OrderInfoVO> salesOrders = orders.stream().filter(
                    each -> Objects.equals(getSalesId(each, customerRelatedSalesMap), id)).collect(Collectors.toList());
                DistributorCustomerTopupSummary.DistributorCustomerTopupStaffSummary summaryItem
                    = new DistributorCustomerTopupSummary.DistributorCustomerTopupStaffSummary();

                summaryItem.setStaffId(id);
                if (salesUserMap.get(id) != null) {
                    summaryItem.setStaffName(salesUserMap.get(id).getNickName());
                }

                summaryItem.setTotalTopupCustomerCount((int)salesOrders.stream().map(OrderInfoVO::getMasterUserId)
                    .distinct().count());
                summaryItem.setTotalTopupCount(salesOrders.size());
                summaryItem.setTotalAmount(
                    salesOrders.stream().map(OrderInfoVO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                salesTopupItems.put(id, summaryItem);
            }

            summary.setTopupStaffItems(new ArrayList<>(salesTopupItems.values().stream().sorted(Comparator.comparing(
                    DistributorCustomerTopupSummary.DistributorCustomerTopupStaffSummary::getTotalAmount).reversed())
                .collect(Collectors.toList())));
        }

        return Result.success(summary);
    }

    private Integer getSalesId(OrderInfoVO order, Map<Integer, DistributorCustomerVO> map) {
        Integer customerId = order.getMasterUserId();
        Integer salesId = null;
        if (map.containsKey(customerId)) {
            salesId = map.get(customerId).getDistributorSalesUserId();
        }
        return salesId;
    }

    /**
     * 查询充值记录页面的用户过滤器（会员列表、运营列表、销售列表），用于查询过滤
     */
    @PostMapping("/queryUserOptions4TopupPerformance")
    public Result<DistributorPerformanceQueryUserOptionsVO> queryUserOptions4TopupPerformance(
        @Valid @RequestBody DistributorCustomerPerformanceQuery query) {

        List<DistributorCustomerVO> customers = getDistributorCustomerVOS(query);
        if (CollectionUtils.isEmpty(customers)) {
            return Result.success();
        }

        OrderInfoQuery orderInfoQuery = new OrderInfoQuery();
        orderInfoQuery.setDateFrom(query.getDateFrom());
        orderInfoQuery.setDateTo(query.getDateTo());
        orderInfoQuery.setMasterUserIds(
            customers.stream().map(DistributorCustomerVO::getCustomerMasterUserId).collect(Collectors.toList()));

        List<OrderInfoVO> orderInfos = orderInfoService.queryOrderInfoList(orderInfoQuery);
        if (CollectionUtils.isNotEmpty(orderInfos)) {
            List<Integer> customerIdsHasTopup = orderInfos.stream().map(OrderInfoVO::getMasterUserId).distinct()
                .collect(Collectors.toList());

            return getDistributorPerformanceQueryUserOptionsVOResult(customerIdsHasTopup);
        }

        return Result.success();
    }

    private List<DistributorCustomerVO> getDistributorCustomerVOS(DistributorCustomerPerformanceQuery query) {
        if (query.getCustomerId() != null || query.getOperatorId() != null || query.getSalesId() != null) {
            DistributorCustomerQuery dcq = new DistributorCustomerQuery();
            dcq.setCustomerMasterUserId(query.getCustomerId());
            dcq.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());
            dcq.setDistributorOperatorUserId(query.getOperatorId());
            dcq.setDistributorSalesUserId(query.getSalesId());

            return distributorCustomerService.queryDistributorCustomerList(dcq);
        }

        return distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(false);
    }

    /**
     * 查询使用记录页面的用户过滤器（会员列表、运营列表、销售列表），用于查询过滤
     */
    @PostMapping("/queryUserOptions4UsagePerformance")
    public Result<DistributorPerformanceQueryUserOptionsVO> queryUserOptions4UsagePerformance(
        @Valid @RequestBody DistributorCustomerPerformanceQuery query) {
        List<DistributorCustomerVO> customers = getDistributorCustomerVOS(query);
        if (CollectionUtils.isEmpty(customers)) {
            return Result.success();
        }

        UserPointLogQuery userPointLogQuery = new UserPointLogQuery();
        userPointLogQuery.setDateFrom(query.getDateFrom());
        userPointLogQuery.setDateTo(query.getDateTo());
        userPointLogQuery.setTypeList(
            Arrays.asList(PointLogTypeEnum.CREATIVE_CREATE.getCode(), PointLogTypeEnum.LORA_TRAIN.getCode()));
        userPointLogQuery.setUserIds(
            customers.stream().map(DistributorCustomerVO::getCustomerMasterUserId).collect(Collectors.toList()));

        List<UserPointLogVO> list = userPointLogService.queryUserPointLogList(userPointLogQuery);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> customerIds = list.stream().map(UserPointLogVO::getUserId).distinct().collect(
                Collectors.toList());

            return getDistributorPerformanceQueryUserOptionsVOResult(customerIds);
        }

        return Result.success();
    }

    @PostMapping("/queryCustomerMusePoint")
    @ApiOperation("根据会员id查询其muse点")
    public Result<UserPointVO> queryCustomerMusePoint(@JsonArg @NotNull Integer customerMasterId) {
        return Result.success(userPointService.queryImagePoint(customerMasterId));
    }

    @PostMapping("/queryCustomersOptionsByDistributorMasterId")
    public Result<List<UserOptionVO>> queryCustomersOptionsByDistributorMasterId(
        @JsonArg @NotNull Integer distributorMasterId) {
        List<UserVO> userList = distributorService.queryAllCustomersByDistributorMasterId(distributorMasterId);
        return Result.success(userList.stream().map(user -> new UserOptionVO(user.getId(), user.getNickName()))
            .collect(Collectors.toList()));
    }

    private Result<DistributorPerformanceQueryUserOptionsVO> getDistributorPerformanceQueryUserOptionsVOResult(
        List<Integer> customerIds) {
        DistributorCustomerQuery dcq = new DistributorCustomerQuery();
        dcq.setCustomerMasterUserIds(customerIds);
        dcq.setDistributorMasterUserId(OperationContextHolder.getMasterUserId());

        List<DistributorCustomerVO> customerRelated = distributorCustomerService.queryDistributorCustomerList(dcq);
        List<Integer> operatorIds = customerRelated.stream().map(DistributorCustomerVO::getDistributorOperatorUserId)
            .distinct().collect(Collectors.toList());
        List<Integer> salesIds = customerRelated.stream().map(DistributorCustomerVO::getDistributorSalesUserId)
            .distinct().collect(Collectors.toList());

        List<Integer> userIds = new ArrayList<>();
        userIds.addAll(customerIds);
        userIds.addAll(operatorIds);
        userIds.addAll(salesIds);

        List<UserVO> users = userService.batchQueryById(userIds);

        DistributorPerformanceQueryUserOptionsVO ret = new DistributorPerformanceQueryUserOptionsVO();
        ret.setRelatedCustomers(users.stream().filter(u -> customerIds.contains(u.getId())).map(userVO -> {
            UserOptionVO target = new UserOptionVO();
            target.setId(userVO.getId());
            target.setNickName(userVO.getNickName());
            return target;
        }).collect(Collectors.toList()));

        ret.setRelatedOperators(users.stream().filter(u -> operatorIds.contains(u.getId())).map(userVO -> {
            UserOptionVO target = new UserOptionVO();
            target.setId(userVO.getId());
            target.setNickName(userVO.getNickName());
            return target;
        }).collect(Collectors.toList()));

        ret.setRelatedSales(users.stream().filter(u -> salesIds.contains(u.getId())).map(userVO -> {
            UserOptionVO target = new UserOptionVO();
            target.setId(userVO.getId());
            target.setNickName(userVO.getNickName());
            return target;
        }).collect(Collectors.toList()));

        return Result.success(ret);
    }
}