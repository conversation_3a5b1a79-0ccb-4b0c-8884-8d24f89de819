package ai.conrain.aigc.platform.web.controller;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * PromptDict控制器
 *
 * <AUTHOR>
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
@Slf4j
@Api("prompt关键字字典")
@Validated
@RestController
@RequestMapping("/promptDict")
public class PromptDictController {

    /** promptDictService */
    @Autowired
    private PromptDictService promptDictService;

    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据id查询prompt关键字字典", notes = "根据id查询prompt关键字字典[promptDict]")
    public Result<PromptDictVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(promptDictService.selectByIdFromDB(id));
    }

    @PostMapping("/create")
    @ApiOperation(value = "新建prompt关键字字典", notes = "新建prompt关键字字典[promptDict]")
    public Result<Integer> create(@Valid @RequestBody PromptDictVO promptDict) {
        try {
            PromptDictVO data = promptDictService.insert(promptDict);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加prompt关键字字典失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建prompt关键字字典失败");
    }

    @PostMapping("/deleteById")
    @ApiOperation(value = "删除prompt关键字字典", notes = "删除prompt关键字字典")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        promptDictService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    @ApiOperation(value = "更新prompt关键字字典",
        notes = "修改prompt关键字字典[promptDict]，id必填，所有字段按请求直接更新")
    public Result<?> updateById(@Valid @RequestBody PromptDictVO promptDict) {
        promptDictService.updateById(promptDict);
        return Result.success();
    }

    @GetMapping("/queryAll")
    @ApiOperation(value = "全量查询prompt关键字字典", notes = "全量查询prompt关键字字典")
    public Result<List<PromptDictVO>> findAll() {
        return Result.success(promptDictService.queryAll());
    }

    @GetMapping("/querySystemCollocation")
    @ApiOperation(value = "查询系统搭配词", notes = "查询系统搭配词")
    public Result<List<PromptDictVO>> querySystemCollocation() {
        List<PromptDictVO> data = promptDictService.querySystemCollocation();
        return Result.success(pruneForFront(data, false));
    }

    @GetMapping("/querySystemScene")
    @ApiOperation(value = "查询典型场景", notes = "查询典型场景")
    public Result<List<PromptDictVO>> querySystemScene() {
        List<PromptDictVO> data = promptDictService.querySystemScene();
        return Result.success(pruneForFront(data, true));
    }

    @GetMapping("/queryGarmentList")
    @ApiOperation(value = "查询服装类型列表", notes = "查询服装类型列表")
    public Result<List<PromptDictVO>> queryGarmentList() {
        List<PromptDictVO> data = promptDictService.queryGarmentList();
        return Result.success(pruneForFront(data, true));
    }

    @GetMapping("/queryImageByTags/{tagName}")
    @ApiOperation(value = "查询服装类型列表", notes = "查询服装类型列表")
    public Result<List<PromptDictVO>> queryImageByTags(@PathVariable String tagName) {

        if (StringUtils.isBlank(tagName)){
            return Result.failedWithMessage(ResultCode.SYS_ERROR, "标签不能为空");
        }

        // 获取标签列表
        List<PromptDictVO> data = promptDictService.queryListByType(DictTypeEnum.IMAGE_TAGS,tagName);

        // 返回结果
        return Result.success(pruneForFront(data, true));
    }

    /**
     * 对前台查询结果进行剪枝
     *
     * @param data       查询结果
     * @param needPrompt 是否需要prompt
     * @return 结果
     */
    private static List<PromptDictVO> pruneForFront(List<PromptDictVO> data, boolean needPrompt) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        List<PromptDictVO> target = new ArrayList<>();
        for (PromptDictVO item : data) {
            PromptDictVO newItem = new PromptDictVO();
            newItem.setId(item.getId());
            newItem.setType(item.getType());
            newItem.setWord(item.getWord());
            newItem.setTags(item.getTags());
            if (needPrompt) {
                newItem.setPrompt(item.getPrompt());
            }
            target.add(newItem);
        }
        return target;
    }

}