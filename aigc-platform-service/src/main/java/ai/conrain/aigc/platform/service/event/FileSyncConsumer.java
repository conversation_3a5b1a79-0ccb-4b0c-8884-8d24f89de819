/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.event;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.event.FileTransEvent;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.EventConstants.GROUP_FILE_SYNC;
import static ai.conrain.aigc.platform.service.constants.EventConstants.TOPIC_FILE_SYNC;

/**
 * 文件同步消息消费者
 *
 * <AUTHOR>
 * @version : FileSyncConsumer.java, v 0.1 2024/8/16 17:30 renxiao.wu Exp $
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = TOPIC_FILE_SYNC, consumerGroup = GROUP_FILE_SYNC, consumeTimeout = 7 * 1000L)
@ConditionalOnProperty(prefix = "rocketmq", name = "enabled", havingValue = "true", matchIfMissing = true)
public class FileSyncConsumer implements RocketMQListener<FileTransEvent> {

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private ServerHelper serverHelper;

    @Override
    public void onMessage(FileTransEvent event) {
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        long start = System.currentTimeMillis();

        try {
            log.info("【文件同步事件】接收到消息: {}，开始同步文件", event);

            //做个兜底，防止传入的url非文件服务
            String targetFileServerUrl = serverHelper.getFileServerUrl(event.getTargetUrl());
            String originFileServerUrl = serverHelper.getFileServerUrl(event.getOriginUrl());

            if (StringUtils.equals(targetFileServerUrl, originFileServerUrl)) {
                log.info("【文件同步事件】终止，目标地址与当前地址一致，跳过当前同步事件 {}，{}/{}", event,
                    targetFileServerUrl, originFileServerUrl);
                return;
            }

            boolean result;
            if (StringUtils.isNotBlank(event.getOssUrl())) {
                log.info("【文件同步事件】调用通过oss同步的接口");
                result = comfyUIService.fileSyncByOss(event.getFullPath(), event.getOssUrl(), targetFileServerUrl,
                    event.getMd5());
            } else {
                log.info("【文件同步事件】直接调用通过文件服务同步接口");
                result = comfyUIService.fileSync(event.getFullPath(), targetFileServerUrl, originFileServerUrl);
            }

            if (!result) {
                log.warn("【文件同步事件】失败，event={}", event);
                throw new BizException("文件同步失败");
            }

            log.info("【文件同步事件】成功,耗时={}ms,event={}", System.currentTimeMillis() - start, event);

        } catch (Exception e) {
            log.error("【文件同步事件】异常,耗时=" + (System.currentTimeMillis() - start) + "ms，event=" + event, e);
            throw e;
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
        }
    }

}
