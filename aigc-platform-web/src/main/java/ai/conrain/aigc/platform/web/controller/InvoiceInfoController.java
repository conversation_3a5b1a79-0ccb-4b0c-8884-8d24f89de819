package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.InvoiceInfoService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.InvoiceInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * InvoiceInfo控制器
 *
 * <AUTHOR>
 * @version InvoiceInfoService.java v 0.1 2024-06-27 01:42:09
 */
@Slf4j
@Api("发票记录")
@Validated
@RestController
@RequestMapping("/invoiceInfo")
public class InvoiceInfoController {

	/** invoiceInfoService */
	@Autowired
	private InvoiceInfoService invoiceInfoService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询发票记录", notes = "根据id查询发票记录[invoiceInfo]")
	public Result<InvoiceInfoVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(invoiceInfoService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "申请发票记录", notes = "申请发票记录[invoiceInfo]")
	public Result<Integer> create(@JsonArg @NotNull InvoiceTitleVO title, @JsonArg @NotNull Integer orderId){
		title.setMasterUserId(OperationContextHolder.getMasterUserId());
		title.setOperatorUserId(OperationContextHolder.getOperatorUserId());

		return Result.success(invoiceInfoService.applyInvoice(title, orderId));
	}

	@PostMapping("/reverse")
	@ApiOperation(value = "冲销发票记录", notes = "冲销发票记录[invoiceInfo]")
	public Result<?> reverse(@NotNull @JsonArg Integer id) {
		invoiceInfoService.applyReverseInvoice(id);
		return Result.success();
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除发票记录", notes = "删除发票记录")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		invoiceInfoService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新发票记录", notes = "修改发票记录[invoiceInfo]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody InvoiceInfoVO invoiceInfo){
		invoiceInfoService.updateByIdSelective(invoiceInfo);
		return Result.success();
	}

	@PostMapping("/retry")
	@ApiOperation(value = "重试任务")
	public Result<?> retry(@NotNull @JsonArg Integer id) {
		invoiceInfoService.retry(id);
		return Result.success();
	}

	@ApiOperation(value = "批量查询发票记录", notes = "批量查询[invoiceInfo]")
	@PostMapping("/queryList")
	public Result<List<InvoiceInfoVO>> queryInvoiceInfoList(@Valid @RequestBody InvoiceInfoQuery query){
		return Result.success(invoiceInfoService.queryInvoiceInfoList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<InvoiceInfoDO>]")
	public Result<PageInfo<InvoiceInfoVO>> getInvoiceInfoByPage(@Valid @RequestBody InvoiceInfoQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(invoiceInfoService.queryInvoiceInfoByPage(query));
	}
}