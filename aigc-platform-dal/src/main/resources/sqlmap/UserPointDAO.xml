<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.UserPointDAO">
    <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.UserPointDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="point" jdbcType="INTEGER" property="point"/>
        <result column="give_point" jdbcType="INTEGER" property="givePoint"/>
        <result column="experience_point" jdbcType="INTEGER" property="experiencePoint"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , user_id, point, give_point, experience_point, create_time, modify_time
    </sql>
    <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserPointExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from user_point
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_point
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from user_point
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into user_point (user_id, point, give_point, experience_point, create_time,
        modify_time)
        values (#{userId,jdbcType=INTEGER}, #{point,jdbcType=INTEGER}, #{givePoint,jdbcType=INTEGER},
        #{experiencePoint,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into user_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="point != null">
                point,
            </if>
            <if test="givePoint != null">
                give_point,
            </if>
            <if test="experiencePoint != null">
                experience_point,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="point != null">
                #{point,jdbcType=INTEGER},
            </if>
            <if test="givePoint != null">
                #{givePoint,jdbcType=INTEGER},
            </if>
            <if test="experiencePoint != null">
                #{experiencePoint,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserPointExample"
            resultType="java.lang.Long">
        select count(*) from user_point
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update user_point
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=INTEGER},
            </if>
            <if test="record.point != null">
                point = #{record.point,jdbcType=INTEGER},
            </if>
            <if test="record.givePoint != null">
                give_point = #{record.givePoint,jdbcType=INTEGER},
            </if>
            <if test="record.experiencePoint != null">
                experience_point = #{record.experiencePoint,jdbcType=INTEGER},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update user_point
        set id = #{record.id,jdbcType=INTEGER},
        user_id = #{record.userId,jdbcType=INTEGER},
        point = #{record.point,jdbcType=INTEGER},
        give_point = #{record.givePoint,jdbcType=INTEGER},
        experience_point = #{record.experiencePoint,jdbcType=INTEGER},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointDO">
        update user_point
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="point != null">
                point = #{point,jdbcType=INTEGER},
            </if>
            <if test="givePoint != null">
                give_point = #{givePoint,jdbcType=INTEGER},
            </if>
            <if test="experiencePoint != null">
                experience_point = #{experiencePoint,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointDO">
        update user_point
        set user_id          = #{userId,jdbcType=INTEGER},
            point            = #{point,jdbcType=INTEGER},
            give_point       = #{givePoint,jdbcType=INTEGER},
            experience_point = #{experiencePoint,jdbcType=INTEGER},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="lockByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_point
        where user_id = #{userId,jdbcType=INTEGER}
        for update nowait
    </select>
    <select id="queryUserAccumulatedRechargeByUserIdList" resultType="java.util.Map">
        select
        user_id,
        sum(point) as point
        from user_point
        where user_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by user_id
    </select>
</mapper>