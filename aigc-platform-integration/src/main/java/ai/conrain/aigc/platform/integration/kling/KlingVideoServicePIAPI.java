package ai.conrain.aigc.platform.integration.kling;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Service
public class KlingVideoServicePIAPI implements KlingProxy {

    //本地直接走piapi，dev/prod服务器网络不通，走sg服务器代理中转
    private static final String TASK_API_URL = EnvUtil.isLocalEnv() ?  "https://api.piapi.ai/api/v1/task" : "http://47.84.41.128:20310/kling/task";

    //本地直接走piapi，dev/prod服务器网络不通，走sg服务器代理中转
    private static final String ACCOUNT_INFO_URL = EnvUtil.isLocalEnv() ?  "https://api.piapi.ai/account/info" : "http://47.84.41.128:20310/kl/account";

    //local test使用，dev/prod服务器上在sg服务器中配置
    private static final String KLING_API_KEY = "87606cb040c9200e5bbe540aaf5c5f5f677a8089b356d98dca4de42de9b0ea57";
    //local test使用，dev/prod服务器上在sg服务器中配置
    private static final String UA = "Mozilla/5.0 (compatible; curl/8.2.1)";

    @Autowired
    private OssService ossService;

    @Autowired
    @Qualifier("longRestTemplate")
    private RestTemplate longRestTemplate;

    public String createVideoTask(KlingTaskParams params){
        if (params == null || StringUtils.isBlank(params.getPrompt()) || StringUtils.isBlank(params.getImageUrl())) {
            throw new IllegalArgumentException("prompt or imageUrl is null");
        }

        //缺省值
        if (params.getDuration() == null) {
            params.setDuration(5);
        }

        if (StringUtils.isBlank(params.getMode())) {
            params.setMode("pro");
        }

        if (StringUtils.isBlank(params.getVersion())) {
            params.setVersion("1.6");
        }

        // 请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-api-key", KLING_API_KEY);
        // 设置用户代理（必须设置，否则会被cf server 403拦截）
        headers.set("User-Agent", UA);

        JSONObject reqBody = new JSONObject();
        reqBody.put("model", "kling");
        reqBody.put("task_type", "video_generation");
        reqBody.put("input", JSONObject.parseObject(JSONObject.toJSONString(params)));

        // 封装请求
        HttpEntity<String> request = new HttpEntity<>(reqBody.toJSONString(), headers);

        try {
            // 发起 POST 请求
            ResponseEntity<String> response = longRestTemplate.exchange(
                    TASK_API_URL,
                    HttpMethod.POST,
                    request,
                    String.class
            );

            log.info("kling piapi response:{}", response.getBody());

            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                KlingTaskResponseBody body = JSONObject.parseObject(response.getBody(), KlingTaskResponseBody.class);
                if (body != null && body.getResponseData()  != null
                    && StringUtils.isNotBlank(body.getResponseData().getTaskId())) {
                    return body.getResponseData().getTaskId();
                }
            }

        } catch (Exception e) {
            log.error("请求可灵失败", e);
        }

        return null;
    }

    //获取代理账号余额
    public Float getAccountBalanceUSD(){

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-api-key", KLING_API_KEY);
        // 设置用户代理（必须设置，否则会被cf server 403拦截）
        headers.set("User-Agent", UA);

        // 封装请求
        HttpEntity<String> request = new HttpEntity<>(headers);
        try {
            // 发起 GET 请求
            ResponseEntity<String> response = longRestTemplate.exchange(
                    ACCOUNT_INFO_URL,
                    HttpMethod.GET,
                    request,
                    String.class
            );

            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                JSONObject body = JSONObject.parseObject(response.getBody());
                if (body != null && body.getJSONObject("data") != null && body.getJSONObject("data").containsKey("equivalent_in_usd")) {
                    Float amount = body.getJSONObject("data").getFloat("equivalent_in_usd");
                    if (amount != null) {
                        return amount;
                    }
                }
            }

        } catch (Exception e) {
            log.error("请求可灵获取账号信息失败", e);
        }
        return null;
    }

    public KlingTaskRet getVideoTask(String taskId){

        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("taskId is null");
        }

        String url = TASK_API_URL + "/" + taskId;

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-api-key", KLING_API_KEY);
        // 设置用户代理（必须设置，否则会被cf server 403拦截）
        headers.set("User-Agent", UA);

        // 封装请求
        HttpEntity<String> request = new HttpEntity<>(headers);
        try {
            // 发起 GET 请求
            ResponseEntity<String> response = longRestTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    request,
                    String.class
            );

            log.info("kling piapi response:{}", response.getBody());

            KlingTaskRet ret = new KlingTaskRet();

            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                KlingTaskResponseBody body = JSONObject.parseObject(response.getBody(), KlingTaskResponseBody.class);
                if (body != null && body.getResponseData() != null) {
                    ret.setTaskId(body.getResponseData().getTaskId());
                    ret.setTaskType(body.getResponseData().getTaskType());
                    ret.setStatus(KlingTaskStatusEnum.getByName(body.getResponseData().getStatus()));

                    String tmpVideoUrl = body.getResponseData().getOutput().getString("video_url");
                    if (StringUtils.isNotBlank(tmpVideoUrl)) {
                        ret.setOutVideoUrl(tmpVideoUrl);
                        ret.setOssVideoUrl(uploadTempVideoFromStreamToOss(tmpVideoUrl));
                    }
                    return ret;
                }
            }

        } catch (Exception e) {
            log.error("请求可灵失败", e);
        }
        return null;
    }


    private String uploadTempVideoFromStreamToOss(String fileUrl) {
        HttpURLConnection connection = null;
        try {
            // 构建调用 Python Server 的 URL
            String streamUrl = EnvUtil.isLocalEnv() ? fileUrl : "http://47.84.41.128:20310/stream/file?file_url=" + URLEncoder.encode(fileUrl, "UTF-8");

            // 建立连接
            URL url = new URL(streamUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", UA);
            connection.setConnectTimeout(5000); // 设置超时时间
            connection.setReadTimeout(60000);  // 设置读取超时时间

            // 检查响应状态
            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                throw new RuntimeException("Failed to fetch file stream. HTTP response code: " + responseCode);
            }

            // 获取文件名
            String contentDisposition = connection.getHeaderField("Content-Disposition");
            String fileName = extractFileName(contentDisposition, fileUrl);

            // 获取输入流
            try (InputStream inputStream = connection.getInputStream()) {
                if (inputStream != null) {
                    // 上传文件流到 OSS
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
                    String ossFileName = sdf.format(new Date()) + + System.currentTimeMillis() + "_" + fileName;
                    String ossUrl = ossService.upload(ossFileName, inputStream);
                    if (StringUtils.isNotBlank(ossUrl)) {
                        return ossUrl;
                    }
                }
            }
            throw new RuntimeException("Failed to upload file to OSS");
        } catch (Throwable t) {
            log.error("Error while uploading video from stream. URL: " + fileUrl, t);
            throw new RuntimeException(t);
        } finally {
            // 断开连接
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    private String extractFileName(String contentDisposition, String fileUrl) throws UnsupportedEncodingException {
        if (contentDisposition != null && contentDisposition.contains("filename=")) {
            return contentDisposition.split("filename=")[1].replace("\"", "").trim();
        }
        // 如果 Content-Disposition 不存在或未包含文件名，则从 URL 提取
        String decodedUrl = URLDecoder.decode(fileUrl, String.valueOf(StandardCharsets.UTF_8));
        String path = decodedUrl.split("\\?")[0];
        return path.substring(path.lastIndexOf("/") + 1);
    }
}
