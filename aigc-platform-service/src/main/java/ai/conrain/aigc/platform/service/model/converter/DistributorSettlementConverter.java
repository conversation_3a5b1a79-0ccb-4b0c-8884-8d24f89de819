package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO;
import ai.conrain.aigc.platform.service.enums.DistributorSettleStatusEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.enums.SalesTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.query.DistributorSettlementQuery;
import ai.conrain.aigc.platform.dal.example.DistributorSettlementExample;
import ai.conrain.aigc.platform.service.model.vo.DistributorSettleStats;
import ai.conrain.aigc.platform.service.model.vo.DistributorSettlementVO;

import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SALES_TYPE;

/**
 * DistributorSettlementConverter
 *
 * @version DistributorSettlementService.java v 0.1 2025-05-22 05:39:41
 */
public class DistributorSettlementConverter {

    /**
     * DO -> VO
     */
    public static DistributorSettlementVO do2VO(DistributorSettlementDO from) {
        DistributorSettlementVO to = new DistributorSettlementVO();
        to.setId(from.getId());
        to.setPrincipalType(PrincipalTypeEnum.getByCode(from.getPrincipalType()));
        to.setPrincipalId(from.getPrincipalId());
        to.setSettleId(from.getSettleId());
        to.setStatus(DistributorSettleStatusEnum.getByCode(from.getStatus()));
        to.setSettleType(from.getSettleType());
        to.setTotalAmount(from.getTotalAmount());
        to.setSettleAmount(from.getSettleAmount());
        to.setOrderNum(from.getOrderNum());
        to.setOutBizNo(from.getOutBizNo());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setSettleTime(from.getSettleTime());
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    public static DistributorSettleStats do2Stats(DistributorSettlementDO from) {
        DistributorSettleStats to = new DistributorSettleStats();
        if (null == from) {
            //数据为空，说明未结算过
            to.setTotalAmount(BigDecimalUtils.newZero());
            to.setSettleAmount(BigDecimalUtils.newZero());
            to.setOrderNum(0);
        } else {
            to.setTotalAmount(from.getTotalAmount());
            to.setSettleAmount(from.getSettleAmount());
            to.setOrderNum(from.getOrderNum());
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static DistributorSettlementDO vo2DO(DistributorSettlementVO from) {
        DistributorSettlementDO to = new DistributorSettlementDO();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType().getCode());
        to.setPrincipalId(from.getPrincipalId());
        to.setSettleId(from.getSettleId());
        to.setStatus(from.getStatus().getCode());
        to.setSettleType(from.getSettleType());
        to.setTotalAmount(from.getTotalAmount());
        to.setSettleAmount(from.getSettleAmount());
        to.setOrderNum(from.getOrderNum());
        to.setOutBizNo(from.getOutBizNo());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setSettleTime(from.getSettleTime());
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        }
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static DistributorSettlementQuery do2Query(DistributorSettlementDO from) {
        DistributorSettlementQuery to = new DistributorSettlementQuery();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType());
        to.setPrincipalId(from.getPrincipalId());
        to.setSettleId(from.getSettleId());
        to.setStatus(from.getStatus());
        to.setSettleType(from.getSettleType());
        to.setTotalAmount(from.getTotalAmount());
        to.setSettleAmount(from.getSettleAmount());
        to.setOrderNum(from.getOrderNum());
        to.setOutBizNo(from.getOutBizNo());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setSettleTime(from.getSettleTime());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static DistributorSettlementDO query2DO(DistributorSettlementQuery from) {
        DistributorSettlementDO to = new DistributorSettlementDO();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType());
        to.setPrincipalId(from.getPrincipalId());
        to.setSettleId(from.getSettleId());
        to.setStatus(from.getStatus());
        to.setSettleType(from.getSettleType());
        to.setTotalAmount(from.getTotalAmount());
        to.setSettleAmount(from.getSettleAmount());
        to.setOrderNum(from.getOrderNum());
        to.setOutBizNo(from.getOutBizNo());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setSettleTime(from.getSettleTime());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static DistributorSettlementExample query2Example(DistributorSettlementQuery from) {
        DistributorSettlementExample to = new DistributorSettlementExample();
        DistributorSettlementExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalType())) {
            c.andPrincipalTypeEqualTo(from.getPrincipalType());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalId())) {
            c.andPrincipalIdEqualTo(from.getPrincipalId());
        }
        if (!ObjectUtils.isEmpty(from.getSettleId())) {
            c.andSettleIdEqualTo(from.getSettleId());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getSettleType())) {
            c.andSettleTypeEqualTo(from.getSettleType());
        }
        if (!ObjectUtils.isEmpty(from.getTotalAmount())) {
            c.andTotalAmountEqualTo(from.getTotalAmount());
        }
        if (!ObjectUtils.isEmpty(from.getSettleAmount())) {
            c.andSettleAmountEqualTo(from.getSettleAmount());
        }
        if (!ObjectUtils.isEmpty(from.getOrderNum())) {
            c.andOrderNumEqualTo(from.getOrderNum());
        }
        if (!ObjectUtils.isEmpty(from.getOutBizNo())) {
            c.andOutBizNoEqualTo(from.getOutBizNo());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpId())) {
            c.andDistributorCorpIdEqualTo(from.getDistributorCorpId());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpName())) {
            c.andDistributorCorpNameEqualTo(from.getDistributorCorpName());
        }
        if (!ObjectUtils.isEmpty(from.getSettleTime())) {
            c.andSettleTimeEqualTo(from.getSettleTime());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getSettleTimeStart())) {
            c.andSettleTimeGreaterThanOrEqualTo(DateUtils.parseShort(from.getSettleTimeStart()));
        }

        if (!ObjectUtils.isEmpty(from.getSettleTimeEnd())) {
            c.andSettleTimeLessThanOrEqualTo(DateUtils.parseShortLastTime(from.getSettleTimeEnd()));
        }

        //逻辑删除过滤
        for (DistributorSettlementExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<DistributorSettlementVO> doList2VOList(List<DistributorSettlementDO> list) {
        return CommonUtil.listConverter(list, DistributorSettlementConverter::do2VO);
    }

    /**
     * model -> config
     * @param from not null
     * @param master can be null
     * @param user not null
     * @param subOrg can be null
     */
    public static DistributorSettleConfigVO vo2Biz(SettleConfigModel from, UserVO master, UserVO user,
                                                   OrganizationVO masterOrg, OrganizationVO subOrg) {

        DistributorSettleConfigVO to = new DistributorSettleConfigVO();

        if (Objects.isNull(master)) {
            to.setChannelAdminId(user.getId());
            to.setChannelAdminNickName(user.getNickName());
            to.setMasterCorpId(user.getCorpOrgId());
            to.setMasterCorpName(user.getCorpName());
        } else {
            to.setChannelAdminId(master.getId());
            to.setChannelAdminNickName(master.getNickName());
            to.setMasterCorpId(master.getCorpOrgId());
            to.setMasterCorpName(master.getCorpName());
        }

        to.setPrincipalId(user.getId());
        to.setSubChannelAdminNickName(user.getNickName());
        to.setCustomRole(user.getCustomRole());

        if (Objects.nonNull(masterOrg) && Objects.nonNull(masterOrg.getExtInfo())
                && masterOrg.getExtInfo().containsKey(KEY_SALES_TYPE)) {
            String salesType = masterOrg.getExtInfo().getString(KEY_SALES_TYPE);
            to.setSalesType(salesType);
            // 如果是直营就结算到 个人, 否则 结算到组织
            to.setPrincipalType(SalesTypeEnum.isDirect(salesType) ?
                    PrincipalTypeEnum.USER.getCode() : PrincipalTypeEnum.CORP.getCode());
        }

        if (Objects.nonNull(subOrg)) {
            to.setSubCorpId(subOrg.getId());
            to.setSubCorpName(subOrg.getName());
        }

        to.setSettleConfig(from);

        return to;
    }
}