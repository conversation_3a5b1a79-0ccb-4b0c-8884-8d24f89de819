<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.MaterialModelDAO">
    <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="show_image" jdbcType="VARCHAR" property="showImage"/>
        <result column="lora_name" jdbcType="VARCHAR" property="loraName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="operator_id" jdbcType="INTEGER" property="operatorId"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="operator_nick" jdbcType="VARCHAR" property="operatorNick"/>
        <result column="user_nick" jdbcType="VARCHAR" property="userNick"/>
        <result column="train_detail" jdbcType="VARCHAR" property="trainDetail"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="main_type" jdbcType="VARCHAR" property="mainType"/>
        <result column="main_id" jdbcType="INTEGER" property="mainId"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        <result column="tags" jdbcType="LONGVARCHAR" property="tags"/>
        <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <trim prefix="(" suffix=")">
                <foreach collection="oredCriteria" item="criteria" separator="or">
                    <if test="criteria.valid">
                        <trim prefix="(" prefixOverrides="and" suffix=")">
                            <foreach collection="criteria.criteria" item="criterion">
                                <choose>
                                    <when test="criterion.noValue">
                                        and ${criterion.condition}
                                    </when>
                                    <when test="criterion.singleValue">
                                        and ${criterion.condition} #{criterion.value}
                                    </when>
                                    <when test="criterion.betweenValue">
                                        and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                    </when>
                                    <when test="criterion.listValue">
                                        and ${criterion.condition}
                                        <foreach close=")" collection="criterion.value" item="listItem" open="("
                                                 separator=",">
                                            #{listItem}
                                        </foreach>
                                    </when>
                                </choose>
                            </foreach>
                        </trim>
                    </if>
                </foreach>
            </trim>
            <choose>
                <when test="relatedOperatorType == 'all' or relatedOperatorType == null or relatedOperatorType == ''">
                    <!-- 不添加额外条件 -->
                </when>
                <when test="relatedOperatorType == 'unset'">
                    AND (JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.relatedOperatorMobile')) IS NULL
                    OR JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.relatedOperatorMobile')) = '')
                </when>
                <otherwise>
                    and
                    <if test="relatedOrOperatorId != null">
                        ( operator_id = #{relatedOrOperatorId} or
                    </if>
                    JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.relatedOperatorMobile')) = #{relatedOperatorType}
                    <if test="relatedOrOperatorId != null">
                        )
                    </if>
                </otherwise>
            </choose>
            <if test="onlyUnconfirmedLora == true">
                AND (JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.loraConfirmed')) IS NULL
                OR JSON_UNQUOTE(JSON_EXTRACT(train_detail, '$.loraConfirmed')) != 'Y')
            </if>
            <if test="nameOrUserLike != null">
                and (name like CONCAT('%', #{nameOrUserLike}, '%') or user_nick like CONCAT('%', #{nameOrUserLike},
                '%'))
            </if>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , name, type, user_id, show_image, lora_name, status, operator_id, deleted, create_time,
        modify_time, train_detail, material_type, main_type, main_id
    </sql>
    <sql id="Blob_Column_List">
        tags
        , ext_info
    </sql>

    <sql id="View_Column_List">
        operator_nick
        , user_nick
    </sql>

    <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.MaterialModelExample"
            resultMap="ResultMapWithBLOBs">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        ,
        <include refid="View_Column_List"/>
        from material_model_view
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>

    <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.MaterialModelExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>,
        <include refid="View_Column_List"/>
        from material_model_view
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>

    <select id="selectIdByExample" parameterType="ai.conrain.aigc.platform.dal.example.MaterialModelExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        id
        from material_model
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>


    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from material_model
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from material_model
        where id = #{id,jdbcType=INTEGER}
        and deleted =
        <choose>
            <when test="andLogicalDeleted">
                true
            </when>
            <otherwise>
                false
            </otherwise>
        </choose>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from material_model
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into material_model (name, type, user_id, show_image,
        lora_name, status, operator_id,
        deleted, create_time, modify_time,
        tags, ext_info, train_detail, material_type, main_type, main_id)
        values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER},
        #{showImage,jdbcType=VARCHAR},
        #{loraName,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER},
        #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP},
        #{tags,jdbcType=LONGVARCHAR}, #{extInfo,jdbcType=LONGVARCHAR}, #{trainDetail}, #{materialType,jdbcType=VARCHAR},
        #{mainType,jdbcType=VARCHAR}, #{mainId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into material_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="showImage != null">
                show_image,
            </if>
            <if test="loraName != null">
                lora_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="tags != null">
                tags,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
            <if test="trainDetail != null">
                train_detail,
            </if>
            <if test="materialType != null">
                material_type,
            </if>
            <if test="mainType != null">
                main_type,
            </if>
            <if test="mainId != null">
                main_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="showImage != null">
                #{showImage,jdbcType=VARCHAR},
            </if>
            <if test="loraName != null">
                #{loraName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tags != null">
                #{tags,jdbcType=LONGVARCHAR},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="trainDetail != null">
                #{trainDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="materialType != null">
                #{materialType,jdbcType=VARCHAR},
            </if>
            <if test="mainType != null">
                #{mainType,jdbcType=VARCHAR},
            </if>
            <if test="mainId != null">
                #{mainId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.MaterialModelExample"
            resultType="java.lang.Long">
        select count(*) from material_model_view
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update material_model
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.type != null">
                type = #{record.type,jdbcType=VARCHAR},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=INTEGER},
            </if>
            <if test="record.showImage != null">
                show_image = #{record.showImage,jdbcType=VARCHAR},
            </if>
            <if test="record.loraName != null">
                lora_name = #{record.loraName,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.operatorId != null">
                operator_id = #{record.operatorId,jdbcType=INTEGER},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=BIT},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.tags != null">
                tags = #{record.tags,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.extInfo != null">
                ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.trainDetail != null">
                train_detail = #{record.trainDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.materialType != null">
                material_type = #{record.materialType,jdbcType=VARCHAR},
            </if>
            <if test="record.mainType != null">
                main_type = #{record.mainType,jdbcType=VARCHAR},
            </if>
            <if test="record.mainId != null">
                main_id = #{record.mainId,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        update material_model
        set id = #{record.id,jdbcType=INTEGER},
        name = #{record.name,jdbcType=VARCHAR},
        type = #{record.type,jdbcType=VARCHAR},
        user_id = #{record.userId,jdbcType=INTEGER},
        show_image = #{record.showImage,jdbcType=VARCHAR},
        lora_name = #{record.loraName,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=VARCHAR},
        operator_id = #{record.operatorId,jdbcType=INTEGER},
        deleted = #{record.deleted,jdbcType=BIT},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
        tags = #{record.tags,jdbcType=LONGVARCHAR},
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
        train_detail = #{record.trainDetail,jdbcType=LONGVARCHAR},
        material_type = #{record.materialType,jdbcType=VARCHAR},
        main_type = #{record.mainType,jdbcType=VARCHAR},
        main_id = #{record.mainId,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update material_model
        set id = #{record.id,jdbcType=INTEGER},
        name = #{record.name,jdbcType=VARCHAR},
        type = #{record.type,jdbcType=VARCHAR},
        user_id = #{record.userId,jdbcType=INTEGER},
        show_image = #{record.showImage,jdbcType=VARCHAR},
        lora_name = #{record.loraName,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=VARCHAR},
        operator_id = #{record.operatorId,jdbcType=INTEGER},
        deleted = #{record.deleted,jdbcType=BIT},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
        tags = #{record.tags,jdbcType=LONGVARCHAR},
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
        train_detail = #{record.trainDetail,jdbcType=LONGVARCHAR},
        material_type = #{record.materialType,jdbcType=VARCHAR},
        main_type = #{record.mainType,jdbcType=VARCHAR},
        main_id = #{record.mainId,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        update material_model
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="showImage != null">
                show_image = #{showImage,jdbcType=VARCHAR},
            </if>
            <if test="loraName != null">
                lora_name = #{loraName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tags != null">
                tags = #{tags,jdbcType=LONGVARCHAR},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="trainDetail != null">
                train_detail = #{trainDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="materialType != null">
                material_type = #{materialType,jdbcType=VARCHAR},
            </if>
            <if test="mainType != null">
                main_type = #{mainType,jdbcType=VARCHAR},
            </if>
            <if test="mainId != null">
                main_id = #{mainId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        update material_model
        set name         = #{name,jdbcType=VARCHAR},
            type         = #{type,jdbcType=VARCHAR},
            user_id      = #{userId,jdbcType=INTEGER},
            show_image   = #{showImage,jdbcType=VARCHAR},
            lora_name    = #{loraName,jdbcType=VARCHAR},
            status       = #{status,jdbcType=VARCHAR},
            operator_id  = #{operatorId,jdbcType=INTEGER},
            deleted      = #{deleted,jdbcType=BIT},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            modify_time  = #{modifyTime,jdbcType=TIMESTAMP},
            tags         = #{tags,jdbcType=LONGVARCHAR},
            ext_info     = #{extInfo,jdbcType=LONGVARCHAR},
            train_detail = #{trainDetail,jdbcType=LONGVARCHAR},
            material_type = #{materialType,jdbcType=VARCHAR},
            main_type    = #{mainType,jdbcType=VARCHAR},
            main_id      = #{mainId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        update material_model
        set name        = #{name,jdbcType=VARCHAR},
            type        = #{type,jdbcType=VARCHAR},
            user_id     = #{userId,jdbcType=INTEGER},
            show_image  = #{showImage,jdbcType=VARCHAR},
            lora_name   = #{loraName,jdbcType=VARCHAR},
            status      = #{status,jdbcType=VARCHAR},
            operator_id = #{operatorId,jdbcType=INTEGER},
            deleted     = #{deleted,jdbcType=BIT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            tags        = #{tags,jdbcType=LONGVARCHAR},
            main_type    = #{mainType,jdbcType=VARCHAR},
            main_id     = #{mainId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="logicalDeleteByExample" parameterType="map">
        update material_model set deleted = true
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        update material_model
        set deleted = true
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="statsDelivery" parameterType="java.util.Map"
            resultType="ai.conrain.aigc.platform.dal.entity.StatsDeliveryDO">
        select t.create_time                                                                      as dt,
               count(*)                                                                           as total,
               sum(if(s.vip = 1, 1, 0))                                                           as vipTotal,
               sum(if(s.vip = 1 and t.status = 'ENABLED', 1, 0))                                  as vipDelivery,
               sum(if(s.vip = 0, 1, 0))                                                           as normalTotal,
               sum(if(s.vip = 0 and t.status = 'ENABLED', 1, 0))                                  as normalDelivery,
               sum(if(diff is null and status = 'ENABLED' and s.vip = 1, 1, 0))                   as vipUnknown,
               sum(if(diff &lt;= 24 and status = 'ENABLED' and s.vip = 1, 1, 0))                  as vip24,
               sum(if(diff &lt;= 48 and diff &gt; 24 and status = 'ENABLED' and s.vip = 1, 1, 0)) as vip48,
               sum(if(diff &gt; 48 and status = 'ENABLED' and s.vip = 1, 1, 0))                   as vipOther,
               sum(if(diff is null and status = 'ENABLED' and s.vip = 0, 1, 0))                   as normalUnknown,
               sum(if(diff &lt;= 24 and status = 'ENABLED' and s.vip = 0, 1, 0))                  as normal24,
               sum(if(diff &lt;= 48 and diff &gt; 24 and status = 'ENABLED' and s.vip = 0, 1, 0)) as normal48,
               sum(if(diff &gt; 48 and status = 'ENABLED' and s.vip = 0, 1, 0))                   as normalOther,
               sum(if(auto_delivery, 1, 0))                                                       as autoDelivery
        from (select id, `user_id`, `status`, date (create_time) create_time,
             timestampdiff(hour, create_time, str_to_date(json_unquote(json_extract(ext_info, '$.deliveryTime')),
                                                          '%Y-%m-%d %H:%i:%s')) diff, json_extract(ext_info, '$.autoDelivery') is not null as auto_delivery
        from `material_model`
        where `deleted` != 1
          and `type` = 'custom'
          and `main_type` in ('MAIN', 'NORMAL')
            ) t left join (
        select a.id user_id, a.role_type, if(b.vip is not null, 1, 0) vip
        from user a left join (
            select distinct id as user_id, 1 as vip from `user`
            where user_type = 'MASTER' and deleted = 0 and (
            memo like '%VIP%' or `id` in (
            select distinct (master_user_id) from `order_info` where deleted = 0
            )
            )
            ) b
        on a.id = b.user_id
            ) s on t.`user_id` = s.user_id
        where t.`status` != 'DISABLED'
          and s.role_type not in ('OPERATOR'
            , 'ADMIN')
          and create_time &gt;= #{startDate,jdbcType=VARCHAR}
          and create_time &lt;= #{endDate,jdbcType=VARCHAR}
        group by t.create_time
        order by t.create_time
    </select>

    <select id="lockByPrimaryKey" resultType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from material_model
        where id = #{id,jdbcType=INTEGER} and deleted = 0 for update nowait
    </select>

    <!-- 根据基础名称前缀统计服装数量 -->
    <select id="countCopyClothesWithPrefix" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM material_model
        WHERE deleted = 0
        <if test="startDate != null">
            AND create_time >= #{startDate,jdbcType=VARCHAR}
        </if>
        <if test="endDate != null">
            AND create_time &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="baseNameList != null and baseNameList.size() > 0">
            AND (
            <foreach collection="baseNameList" item="baseName" separator=" OR ">
                name LIKE CONCAT(#{baseName}, '%')
            </foreach>
            )
        </if>
    </select>


    <select id="queryMaterialModelByDeliveryDate"
            resultType="ai.conrain.aigc.platform.dal.entity.MaterialModelDO">
        SELECT
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        FROM material_model
        WHERE deleted = 0
        AND JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.deliveryTime')) &gt;= #{startDate,jdbcType=VARCHAR}
        AND JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.deliveryTime')) &lt;= #{endDate,jdbcType=VARCHAR}
    </select>

    <select id="selectSubIds" resultType="java.lang.Integer">
        select id
        from material_model
        where main_id = #{id,jdbcType=INTEGER}
            and main_type = 'SUB'
            and deleted = 0
    </select>

    <select id="selectSubModelStatus" resultType="ai.conrain.aigc.platform.dal.entity.SubModelStatusDO">
        select main_id mainId, count(*) total,
            sum(if(train_detail ->> '$.lora.status' = 'COMPLETED' and (train_detail ->> '$.loraConfirmed' is null or train_detail ->> '$.loraConfirmed' != 'Y'),1,0)) needConfirmCnt,
            sum(if(`status` = 'TESTING',1,0)) testingCnt
        FROM `material_model`
        WHERE main_type = 'SUB'
            and `status` in ('IN_TRAINING','TESTING')
            and deleted != 1
            and main_id in
        <foreach collection="mainIds" item="item" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        group by main_id
    </select>

    <select id="statsQueuedModel" resultType="ai.conrain.aigc.platform.dal.entity.StatsQueuedModelDO">
        select sum(if(train_detail ->> '$.prepareView.status' not in ('RUNNING','COMPLETED')
                or train_detail ->> '$.cutout.status' not in ('RUNNING','COMPLETED')
                or train_detail ->> '$.label.status' not in ('RUNNING','COMPLETED')
                ,1,0)) queuedPreProcess
        ,sum(if(train_detail ->> '$.label.status'  in ('COMPLETED')
                and train_detail ->> '$.loraConfirmed' = 'Y'
                and (train_detail ->> '$.lora.status' is null or train_detail ->> '$.lora.status' not in ('RUNNING','COMPLETED') )
                ,1,0)) queuedTrain
        from `material_model`
        where `deleted` = 0 and `status` = 'IN_TRAINING'
            and `main_type` in ('NORMAL','SUB')
    </select>

</mapper>