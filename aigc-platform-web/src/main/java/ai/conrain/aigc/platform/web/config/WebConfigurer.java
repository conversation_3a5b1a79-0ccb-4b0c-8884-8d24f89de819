/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.config;

import ai.conrain.aigc.platform.service.resolver.BigDecimalDeserializer;
import ai.conrain.aigc.platform.service.resolver.JsonArgResolver;
import ai.conrain.aigc.platform.web.interceptor.OperationContextInterceptor;
import ai.conrain.aigc.platform.web.interceptor.PermissionCheckInterceptor;
import ai.conrain.aigc.platform.web.interceptor.TraceInitInterceptor;
import ai.conrain.aigc.platform.web.interceptor.UserVisitMonitor;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.math.BigDecimal;
import java.util.List;

/**
 * web层配置
 *
 * <AUTHOR>
 * @version : WebConfigurer.java, v 0.1 2023/9/3 18:39 renxiao.wu Exp $
 */
@Configuration
public class WebConfigurer implements WebMvcConfigurer {

    //不进行登录态拦截
    private static final String[] noNeedLoginPathPatterns = {"/swagger-ui.html", "/swagger-resources/**",
                                                             "/healthcheck", "/wx/pay/notify", "/alipay/notify", "/notify/**", "/error", "/"};

    //不设置trace和session拦截
    private static final String[] noNeedTracePathPatterns = {"/swagger-ui.html", "/swagger-resources/**",
                                                             "/healthcheck", "/error", "/"};

    /** 操作上下文拦截器 */
    @Autowired
    private OperationContextInterceptor operationContextInterceptor;
    @Autowired
    private TraceInitInterceptor traceInitInterceptor;
    @Autowired
    private PermissionCheckInterceptor permissionCheckInterceptor;

    @Autowired
    private UserVisitMonitor userVisitMonitor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceInitInterceptor).excludePathPatterns(noNeedTracePathPatterns).order(0);
        registry.addInterceptor(operationContextInterceptor).excludePathPatterns(noNeedTracePathPatterns).order(10);
        registry.addInterceptor(permissionCheckInterceptor).excludePathPatterns(noNeedLoginPathPatterns).order(20);
        registry.addInterceptor(userVisitMonitor).excludePathPatterns(noNeedLoginPathPatterns).order(20);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new JsonArgResolver());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins("*").allowCredentials(true).allowedMethods("GET", "POST").maxAge(
            3600 * 24);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customizeJackson() {
        return builder -> {
            SimpleModule module = new SimpleModule();
            module.addDeserializer(BigDecimal.class, new BigDecimalDeserializer());
            builder.modules(module);
        };
    }
}