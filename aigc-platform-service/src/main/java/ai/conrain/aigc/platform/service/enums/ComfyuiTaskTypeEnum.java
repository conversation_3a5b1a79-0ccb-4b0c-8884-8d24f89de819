package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum ComfyuiTaskTypeEnum {

    prepareView("prepare_view", "准备预览", "views"),

    cutout("cutout", "抠图", "cutout"),

    label("label", "打标", "label"),

    lora("lora", "训练lora", null),

    ;

    private final String code;
    private final String desc;
    private final String folderName;

    ComfyuiTaskTypeEnum(String code, String desc, String folderName) {
        this.code = code;
        this.desc = desc;
        this.folderName = folderName;
    }

    public static ComfyuiTaskTypeEnum getByCode(String code) {
        for (ComfyuiTaskTypeEnum each : values()) {
            if (StringUtils.equals(each.getCode(), code)) {
                return each;
            }
        }
        return null;
    }
}