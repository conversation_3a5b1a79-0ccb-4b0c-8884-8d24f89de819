package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.CreativeElementDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class CreativeElementExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CreativeElementExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CreativeElementExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CreativeElementExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CreativeElementExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria)this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria)this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria)this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria)this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", "%" + value + "%", "name");
            return (Criteria)this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria)this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria)this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria)this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria)this;
        }

        public Criteria andConfigKeyIsNull() {
            addCriterion("config_key is null");
            return (Criteria)this;
        }

        public Criteria andConfigKeyIsNotNull() {
            addCriterion("config_key is not null");
            return (Criteria)this;
        }

        public Criteria andConfigKeyEqualTo(String value) {
            addCriterion("config_key =", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyNotEqualTo(String value) {
            addCriterion("config_key <>", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyGreaterThan(String value) {
            addCriterion("config_key >", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyGreaterThanOrEqualTo(String value) {
            addCriterion("config_key >=", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyLessThan(String value) {
            addCriterion("config_key <", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyLessThanOrEqualTo(String value) {
            addCriterion("config_key <=", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyLike(String value) {
            addCriterion("config_key like", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyNotLike(String value) {
            addCriterion("config_key not like", value, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyIn(List<String> values) {
            addCriterion("config_key in", values, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyNotIn(List<String> values) {
            addCriterion("config_key not in", values, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyBetween(String value1, String value2) {
            addCriterion("config_key between", value1, value2, "configKey");
            return (Criteria)this;
        }

        public Criteria andConfigKeyNotBetween(String value1, String value2) {
            addCriterion("config_key not between", value1, value2, "configKey");
            return (Criteria)this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("level is null");
            return (Criteria)this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("level is not null");
            return (Criteria)this;
        }

        public Criteria andLevelEqualTo(Integer value) {
            addCriterion("level =", value, "level");
            return (Criteria)this;
        }

        public Criteria andLevelNotEqualTo(Integer value) {
            addCriterion("level <>", value, "level");
            return (Criteria)this;
        }

        public Criteria andLevelGreaterThan(Integer value) {
            addCriterion("level >", value, "level");
            return (Criteria)this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("level >=", value, "level");
            return (Criteria)this;
        }

        public Criteria andLevelLessThan(Integer value) {
            addCriterion("level <", value, "level");
            return (Criteria)this;
        }

        public Criteria andLevelLessThanOrEqualTo(Integer value) {
            addCriterion("level <=", value, "level");
            return (Criteria)this;
        }

        public Criteria andLevelIn(List<Integer> values) {
            addCriterion("level in", values, "level");
            return (Criteria)this;
        }

        public Criteria andLevelNotIn(List<Integer> values) {
            addCriterion("level not in", values, "level");
            return (Criteria)this;
        }

        public Criteria andLevelBetween(Integer value1, Integer value2) {
            addCriterion("level between", value1, value2, "level");
            return (Criteria)this;
        }

        public Criteria andLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("level not between", value1, value2, "level");
            return (Criteria)this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria)this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria)this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria)this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria)this;
        }

        public Criteria andShowImageIsNull() {
            addCriterion("show_image is null");
            return (Criteria)this;
        }

        public Criteria andShowImageIsNotNull() {
            addCriterion("show_image is not null");
            return (Criteria)this;
        }

        public Criteria andShowImageEqualTo(String value) {
            addCriterion("show_image =", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotEqualTo(String value) {
            addCriterion("show_image <>", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageGreaterThan(String value) {
            addCriterion("show_image >", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageGreaterThanOrEqualTo(String value) {
            addCriterion("show_image >=", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLessThan(String value) {
            addCriterion("show_image <", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLessThanOrEqualTo(String value) {
            addCriterion("show_image <=", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageLike(String value) {
            addCriterion("show_image like", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotLike(String value) {
            addCriterion("show_image not like", value, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageIn(List<String> values) {
            addCriterion("show_image in", values, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotIn(List<String> values) {
            addCriterion("show_image not in", values, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageBetween(String value1, String value2) {
            addCriterion("show_image between", value1, value2, "showImage");
            return (Criteria)this;
        }

        public Criteria andShowImageNotBetween(String value1, String value2) {
            addCriterion("show_image not between", value1, value2, "showImage");
            return (Criteria)this;
        }

        public Criteria andOrderIsNull() {
            addCriterion("order is null");
            return (Criteria)this;
        }

        public Criteria andOrderIsNotNull() {
            addCriterion("order is not null");
            return (Criteria)this;
        }

        public Criteria andOrderEqualTo(Integer value) {
            addCriterion("order =", value, "order");
            return (Criteria)this;
        }

        public Criteria andOrderNotEqualTo(Integer value) {
            addCriterion("order <>", value, "order");
            return (Criteria)this;
        }

        public Criteria andOrderGreaterThan(Integer value) {
            addCriterion("order >", value, "order");
            return (Criteria)this;
        }

        public Criteria andOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("order >=", value, "order");
            return (Criteria)this;
        }

        public Criteria andOrderLessThan(Integer value) {
            addCriterion("order <", value, "order");
            return (Criteria)this;
        }

        public Criteria andOrderLessThanOrEqualTo(Integer value) {
            addCriterion("order <=", value, "order");
            return (Criteria)this;
        }

        public Criteria andOrderIn(List<Integer> values) {
            addCriterion("order in", values, "order");
            return (Criteria)this;
        }

        public Criteria andOrderNotIn(List<Integer> values) {
            addCriterion("order not in", values, "order");
            return (Criteria)this;
        }

        public Criteria andOrderBetween(Integer value1, Integer value2) {
            addCriterion("order between", value1, value2, "order");
            return (Criteria)this;
        }

        public Criteria andOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("order not between", value1, value2, "order");
            return (Criteria)this;
        }

        public Criteria andMemoIsNull() {
            addCriterion("memo is null");
            return (Criteria)this;
        }

        public Criteria andMemoIsNotNull() {
            addCriterion("memo is not null");
            return (Criteria)this;
        }

        public Criteria andMemoEqualTo(String value) {
            addCriterion("memo =", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoNotEqualTo(String value) {
            addCriterion("memo <>", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoGreaterThan(String value) {
            addCriterion("memo >", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoGreaterThanOrEqualTo(String value) {
            addCriterion("memo >=", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoLessThan(String value) {
            addCriterion("memo <", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoLessThanOrEqualTo(String value) {
            addCriterion("memo <=", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoLike(String value) {
            addCriterion("memo like", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoNotLike(String value) {
            addCriterion("memo not like", value, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoIn(List<String> values) {
            addCriterion("memo in", values, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoNotIn(List<String> values) {
            addCriterion("memo not in", values, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoBetween(String value1, String value2) {
            addCriterion("memo between", value1, value2, "memo");
            return (Criteria)this;
        }

        public Criteria andMemoNotBetween(String value1, String value2) {
            addCriterion("memo not between", value1, value2, "memo");
            return (Criteria)this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria)this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria)this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria)this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria)this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria)this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria)this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria)this;
        }

        public Criteria andTypeIncludes(String type) {
            addCriterion("FIND_IN_SET('" + type + "',type)");
            return (Criteria)this;
        }

        public Criteria andTypeIncludes(List<String> type) {
            String value = type.stream().map(s -> "FIND_IN_SET('" + s + "',type) > 0").collect(
                Collectors.joining(" and "));
            addCriterion("(" + value + ")");
            return (Criteria)this;
        }

        public Criteria andTypeOrIncludes(List<String> type) {
            String value = type.stream().map(s -> "FIND_IN_SET('" + s + "',type) > 0").collect(
                Collectors.joining(" or "));
            addCriterion("(" + value + ")");
            return (Criteria)this;
        }

        public Criteria andTypeExcludes(String type) {
            addCriterion("not FIND_IN_SET('" + type + "',type)");
            return (Criteria)this;
        }

        public Criteria andTypeExcludes(List<String> type) {
            String value = type.stream().map(s -> "FIND_IN_SET('" + s + "',type) <= 0").collect(
                Collectors.joining(" and "));
            addCriterion("(" + value + ")");
            return (Criteria)this;
        }

        public Criteria andIsExperimental() {
            addCriterion("ext_info -> '$.experimental' = true");
            return (Criteria)this;
        }

        public Criteria andIsNotExperimental() {
            addCriterion("(ext_info -> '$.experimental' is null or ext_info -> '$.experimental' = false)");
            return (Criteria)this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria)this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria)this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria)this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria)this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria)this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria)this;
        }

        public Criteria andPrivatelyOpen2UserRoleTypeEqualTo(String value) {
            addCriterion("privatelyOpen2UserRoleType =", value, "privatelyOpen2UserRoleType");
            return (Criteria)this;
        }

        public Criteria andPrivatelyOpen2UserIdEqualTo(Integer value) {
            addCriterion("privatelyOpen2UserId =", value, "privatelyOpen2UserId");
            return (Criteria)this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria)this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria)this;
        }

        public Criteria andUserTypeIsCustomer() {
            addCriterion("user_id in (select user_id from user_vip_view )");
            return (Criteria)this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria)this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdIsNull() {
            addCriterion("exclusive_id is null");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdIsNotNull() {
            addCriterion("exclusive_id is not null");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdEqualTo(Integer value) {
            addCriterion("exclusive_id =", value, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdNotEqualTo(Integer value) {
            addCriterion("exclusive_id <>", value, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdGreaterThan(Integer value) {
            addCriterion("exclusive_id >", value, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("exclusive_id >=", value, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdLessThan(Integer value) {
            addCriterion("exclusive_id <", value, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdLessThanOrEqualTo(Integer value) {
            addCriterion("exclusive_id <=", value, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdIn(List<Integer> values) {
            addCriterion("exclusive_id in", values, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdNotIn(List<Integer> values) {
            addCriterion("exclusive_id not in", values, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdBetween(Integer value1, Integer value2) {
            addCriterion("exclusive_id between", value1, value2, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andExclusiveIdNotBetween(Integer value1, Integer value2) {
            addCriterion("exclusive_id not between", value1, value2, "exclusiveId");
            return (Criteria)this;
        }

        public Criteria andBelongIsNull() {
            addCriterion("belong is null");
            return (Criteria)this;
        }

        public Criteria andBelongIsNotNull() {
            addCriterion("belong is not null");
            return (Criteria)this;
        }

        public Criteria andBelongEqualTo(String value) {
            addCriterion("belong =", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongNotEqualTo(String value) {
            addCriterion("belong <>", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongGreaterThan(String value) {
            addCriterion("belong >", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongGreaterThanOrEqualTo(String value) {
            addCriterion("belong >=", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongLessThan(String value) {
            addCriterion("belong <", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongLessThanOrEqualTo(String value) {
            addCriterion("belong <=", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongLike(String value) {
            addCriterion("belong like", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongNotLike(String value) {
            addCriterion("belong not like", value, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongIn(List<String> values) {
            addCriterion("belong in", values, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongNotIn(List<String> values) {
            addCriterion("belong not in", values, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongBetween(String value1, String value2) {
            addCriterion("belong between", value1, value2, "belong");
            return (Criteria)this;
        }

        public Criteria andBelongNotBetween(String value1, String value2) {
            addCriterion("belong not between", value1, value2, "belong");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria)this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria)this;
        }

        public Criteria andOperatorIdEqualTo(Integer value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotEqualTo(Integer value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdGreaterThan(Integer value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdLessThan(Integer value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdIn(List<Integer> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotIn(List<Integer> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdBetween(Integer value1, Integer value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdIsNull() {
            addCriterion("lora_model_id is null");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdIsNotNull() {
            addCriterion("lora_model_id is not null");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdEqualTo(Integer value) {
            addCriterion("lora_model_id =", value, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdNotEqualTo(Integer value) {
            addCriterion("lora_model_id <>", value, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdGreaterThan(Integer value) {
            addCriterion("lora_model_id >", value, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("lora_model_id >=", value, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdLessThan(Integer value) {
            addCriterion("lora_model_id <", value, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdLessThanOrEqualTo(Integer value) {
            addCriterion("lora_model_id <=", value, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdIn(List<Integer> values) {
            addCriterion("lora_model_id in", values, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdNotIn(List<Integer> values) {
            addCriterion("lora_model_id not in", values, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdBetween(Integer value1, Integer value2) {
            addCriterion("lora_model_id between", value1, value2, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andLoraModelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("lora_model_id not between", value1, value2, "loraModelId");
            return (Criteria)this;
        }

        public Criteria andIsNewIsNull() {
            addCriterion("is_new is null");
            return (Criteria)this;
        }

        public Criteria andIsNewIsNotNull() {
            addCriterion("is_new is not null");
            return (Criteria)this;
        }

        public Criteria andIsNewEqualTo(Boolean value) {
            addCriterion("is_new =", value, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewNotEqualTo(Boolean value) {
            addCriterion("is_new <>", value, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewGreaterThan(Boolean value) {
            addCriterion("is_new >", value, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_new >=", value, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewLessThan(Boolean value) {
            addCriterion("is_new <", value, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewLessThanOrEqualTo(Boolean value) {
            addCriterion("is_new <=", value, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewIn(List<Boolean> values) {
            addCriterion("is_new in", values, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewNotIn(List<Boolean> values) {
            addCriterion("is_new not in", values, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewBetween(Boolean value1, Boolean value2) {
            addCriterion("is_new between", value1, value2, "isNew");
            return (Criteria)this;
        }

        public Criteria andIsNewNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_new not between", value1, value2, "isNew");
            return (Criteria)this;
        }

        public Criteria andOpenScopeEqualTo(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.openScope')) = ", value, "openScope");
            return (Criteria)this;
        }

        public Criteria andOpenScopePrivate() {
            addCriterion(
                "(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.openScope')) is not null and JSON_UNQUOTE(JSON_EXTRACT"
                + "(ext_info, '$.openScope')) != 'ALL')");
            return (Criteria)this;
        }

        public Criteria andOpenScopePublic() {
            addCriterion(
                "(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.openScope')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, "
                + "'$.openScope')) = 'ALL')");
            return (Criteria)this;
        }

        public Criteria andOpenScopePublicOrEquals(Integer userId) {
            addCriterion(
                "((JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.openScope')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, "
                + "'$.openScope')) = 'ALL') or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.openScope')) = '" + userId
                + "')");
            return (Criteria)this;
        }

        public Criteria andExtValueIsNotNull(String key) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) is not null ");
            return (Criteria)this;
        }

        public Criteria andExtValueIsNotBlank(String key) {
            addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key
                         + "')) is not null and JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) != '')");
            return (Criteria)this;
        }

        public Criteria andExtValueEquals(String key, String value) {
            addCriterion("(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key
                         + "')) is not null and JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$." + key + "')) = '" + value
                         + "')");
            return (Criteria)this;
        }

        public Criteria andExtValueIsBlank(String key) {
            addCriterion(String.format("(ext_info->>'$.%s' is null or ext_info->>'$.%s' ='')", key, key));
            return (Criteria)this;
        }

        public Criteria andExtValueNotEquals(String key, String value) {
            addCriterion(String.format("ext_info->>'$.%s' != '%s'", key, value));
            return (Criteria)this;
        }

        public Criteria andExtValueIncludesAny(String key, List<String> values) {
            String array = values.stream()
                .map(v -> String.format("\"%s\"", v))
                .collect(Collectors.joining(","));

            addCriterion(String.format("(ext_info->'$.%s' is not null and JSON_OVERLAPS(ext_info->'$.%s', '[%s]'))", key, key, array));
            return (Criteria)this;
        }

        public Criteria andTrainFinished(List<Integer> ids) {
            String value = ids.stream().map(String::valueOf).collect(Collectors.joining(","));
            addCriterion(
                "( (JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.isLora')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, "
                + "'$.isLora')) != 'Y' or id in (" + value
                + ")) or (JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.modelFinished')) is not null and JSON_UNQUOTE"
                + "(JSON_EXTRACT(ext_info, '$.modelFinished')) = 'Y'))");
            return (Criteria)this;
        }

        public Criteria andIsFaceOnlyLora() {
            addCriterion(
                "( config_key != 'FACE' or (JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.faceLora')) is not null and "
                + "JSON_UNQUOTE" + "(JSON_EXTRACT(ext_info, '$.faceLora')) != ''))");
            return (Criteria)this;
        }

        public Criteria andSwapTypeEqualTo(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.swapType')) =", value, "swapType");
            return (Criteria)this;
        }

        public Criteria andIsLora() {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.isLora')) = 'Y'");
            return (Criteria)this;
        }

        public Criteria andIsNotLora() {
            addCriterion(
                "(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.isLora')) = null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$"
                + ".isLora')) != 'Y')");
            return (Criteria)this;
        }

        public Criteria andTrainTypeEqualTo(String value) {
            addCriterion("JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.trainType')) =", value, "trainType");
            return (Criteria)this;
        }

        public Criteria andIdOrParentIn(List<Integer> ids) {
            String value = ids.stream().map(String::valueOf).collect(Collectors.joining(","));
            addCriterion("(id in (" + value + ") or parent_id in (" + value + "))");
            return (Criteria)this;
        }

        /**
         * 含有子级数据 - 使用完整表引用
         */
        public Criteria andHasChildren() {
            addCriterion("exists (select 1 from creative_element ce_child where ce_child.parent_id = creative_element_view.id and ce_child.deleted = 0)");
            return (Criteria)this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(CreativeElementDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(
                CreativeElementDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}