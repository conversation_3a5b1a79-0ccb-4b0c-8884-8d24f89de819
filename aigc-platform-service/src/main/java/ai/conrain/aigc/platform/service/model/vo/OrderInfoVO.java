package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * OrderInfoVO
 *
 * @version OrderInfoService.java v 0.1 2024-06-20 11:43:37
 */
@Data
public class OrderInfoVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 订单号 */
	@ApiModelProperty(name = "orderNo", value = "订单号")
	private String orderNo;

	/** 关联的主用户id */
	@ApiModelProperty(name = "masterUserId", value = "关联的主用户id")
	private Integer masterUserId;

	/** 关联的主用户昵称快照 */
	@ApiModelProperty(name = "masterUserNick", value = "关联的主用户昵称快照")
	private String masterUserNick;

	/** 关联的主用户登录账号快照 */
	@ApiModelProperty(name = "masterUserLoginId", value = "关联的主用户登录账号快照")
	private String masterUserLoginId;

	private String masterCorpName;

	/** 关联的操作员用户id */
	@ApiModelProperty(name = "operatorUserId", value = "关联的操作员用户id")
	private Integer operatorUserId;

	/** 关联的操作员用户昵称快照 */
	@ApiModelProperty(name = "operatorUserNick", value = "关联的操作员用户昵称快照")
	private String operatorUserNick;

	/** 关联的操作员用户登录账号快照 */
	@ApiModelProperty(name = "operatorUserLoginId", value = "关联的操作员用户登录账号快照")
	private String operatorUserLoginId;

	/** 订单原始金额，不可修改（单位元） */
	@ApiModelProperty(name = "originalAmount", value = "订单原始金额，不可修改（单位元）")
	private BigDecimal originalAmount;

	/** 订单支付金额 */
	@ApiModelProperty(name = "payAmount", value = "订单支付金额")
	private BigDecimal payAmount;

	/** 支付信息 */
	@ApiModelProperty(name = "payDetail", value = "支付信息")
	private String payDetail;

	/** 订单状态 */
	@ApiModelProperty(name = "orderStatus", value = "订单状态")
	private String orderStatus;

	/** 产品码 */
	@ApiModelProperty(name = "productCode", value = "产品码")
	private String productCode;

	private String productName;

	/** 产品详情 */
	@ApiModelProperty(name = "productDetail", value = "产品详情")
	private String productDetail;

	/** 订单完结时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "finishTime", value = "订单完结时间")
	private Date finishTime;

	/** 过期时间（一年过期，返回日期字符串） */
	private String expireTime;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private String extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	// 渠道商master id
	private Integer distributorMasterUserId;

	// 渠道商
	private String distributorCorpName;

	/**
	 * 开票状态
	 * @see ai.conrain.aigc.platform.service.enums.InvoiceStatus
	 */
	private String invoiceStatus;

	/**
	 * 开票状态名
	 */
	private String invoiceStatusName;

	/**
	 * 发票文件url
	 */
	private String invoiceFileUrl;

	/**
	 * 销售id
	 */
	@ApiModelProperty(name = "distributorSalesUserId", value = "销售id")
	private Integer distributorSalesUserId;

	/**
	 * 销售昵称
	 */
	@ApiModelProperty(name = "distributorSalesUserNickName", value = "销售昵称")
	private String distributorSalesNickName;

}