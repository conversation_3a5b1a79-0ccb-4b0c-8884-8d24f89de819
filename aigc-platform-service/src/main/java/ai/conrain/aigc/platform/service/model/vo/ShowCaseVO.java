package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.ShowCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * ShowCaseVO
 *
 * @version ShowCaseService.java v 0.1 2024-11-25 05:41:27
 */
@Data
public class ShowCaseVO implements Serializable, ModifyTimeClz {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 案例名称 */
    @ApiModelProperty(name = "name", value = "案例名称")
    private String name;

    /** 类型，IMAGE、VIDEO */
    @ApiModelProperty(name = "type", value = "类型，IMAGE、VIDEO")
    private ShowCaseTypeEnum type;

    /** 结果图/视频地址url */
    @ApiModelProperty(name = "mainUrl", value = "结果图/视频地址url")
    private String mainUrl;

    /** 展示图url */
    @ApiModelProperty(name = "showImage", value = "展示图url")
    private String showImage;

    /** 模特id */
    @ApiModelProperty(name = "faceId", value = "模特id")
    private Integer faceId;

    /** 场景id */
    @ApiModelProperty(name = "sceneId", value = "场景id")
    private Integer sceneId;

    /** 服装模型id */
    @ApiModelProperty(name = "modelId", value = "服装模型id")
    private Integer modelId;

    /** 服装url */
    @ApiModelProperty(name = "modelUrl", value = "服装url")
    private String modelUrl;

    /** 服装缩略图url */
    @ApiModelProperty(name = "modelMiniUrl", value = "服装缩略图url")
    private String modelMiniUrl;

    /** 排序 */
    @ApiModelProperty(name = "order", value = "排序")
    private Integer order;

    /** 标签列表，多个以逗号隔开 */
    @ApiModelProperty(name = "tags", value = "标签列表，多个以逗号隔开")
    private List<String> tags;

    /** 状态，ENABLED、DISABLED */
    @ApiModelProperty(name = "status", value = "状态，ENABLED、DISABLED")
    private MaterialModelStatusEnum status;

    @ApiModelProperty(name = "topped", value = "是否置顶，ENABLED、DISABLED")
    private boolean topped;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 用户id */
    @ApiModelProperty(name = "userId", value = "用户id")
    private Integer userId;

    /** 操作者id */
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 服装搭配信息 */
    @ApiModelProperty(name = "clothCollocation", value = "服装搭配信息")
    private ClothCollocationModel clothCollocation;

    public String getTypeName() {
        return type.getDesc();
    }

}