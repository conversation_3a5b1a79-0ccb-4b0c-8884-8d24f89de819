package ai.conrain.aigc.platform.service.model.request;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class PoseSampleDiagramRequest  implements CreativeRequest {

    /** 配置（0 场景  1 模特） */
    @NotNull(message = "模特不能为空！")
    private Map<Integer, List<Integer>> configs;

    /** 图片数量 */
    @NotNull(message = "图片数量不能为空！")
    private Integer imgNum;

    /** 图片比例 */
    @NotBlank(message = "图片比例不能为空！")
    private String proportion;

    /** 场景 id */
    @NotNull(message = "场景 id 不能为空！")
    private Integer sceneId;

    /** 当前姿势场景 id */
    private Integer currentPoseId;


    // 设置场景 id 设置进入configs
    public void setConfigSceneId(Integer sceneId) {
        if (sceneId == null) {
            return;
        }
        
        if (configs == null) {
            configs = new HashMap<>();
        }
        
        // 场景的 key 为 2
        List<Integer> sceneIds = new ArrayList<>();
        sceneIds.add(sceneId);
        configs.put(2, sceneIds);
    }


}
