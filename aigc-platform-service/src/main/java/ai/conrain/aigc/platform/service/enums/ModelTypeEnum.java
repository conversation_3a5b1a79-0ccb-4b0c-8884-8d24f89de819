/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 模型类型枚举
 *
 * <AUTHOR>
 * @version : ModelTypeEnum.java, v 0.1 2024/5/23 14:49 renxiao.wu Exp $
 */
@Getter
public enum ModelTypeEnum {
    SYSTEM("SYSTEM", "官方Lora"),

    CUSTOM("CUSTOM", "自定义Lora"),
    ;

    /** 枚举码 */
    private String code;

    /** 枚举描述 */
    private String desc;

    private ModelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static ModelTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (ModelTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
