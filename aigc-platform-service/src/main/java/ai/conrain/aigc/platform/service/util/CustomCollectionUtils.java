package ai.conrain.aigc.platform.service.util;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 集合工具类
 */
public class CustomCollectionUtils {

    /**
     * 从对象列表中提取子列表属性并展平
     *
     * @param sourceList 源对象列表
     * @param mapper 映射函数，用于从源对象中提取子列表
     * @param <T> 源对象类型
     * @param <R> 结果列表元素类型
     * @return 展平后的列表
     */
    public static <T, R> List<R> flatMapList(List<T> sourceList, Function<T, List<R>> mapper) {
        if (sourceList == null) {
            return null;
        }
        return sourceList.stream()
            .flatMap(item -> {
                List<R> subList = mapper.apply(item);
                return subList != null ? subList.stream() : Stream.empty();
            })
            .collect(Collectors.toList());
    }
}
