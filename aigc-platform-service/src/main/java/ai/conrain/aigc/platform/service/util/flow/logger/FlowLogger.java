package ai.conrain.aigc.platform.service.util.flow.logger;

/**
 * 流程日志工具类
 * 用于处理层级化的日志打印
 */
public class FlowLogger {
    private static final String BRANCH = "├── ";
    private static final String LAST = "└── ";
    private static final String VERTICAL = "│   ";
    private static final String SPACE = "    ";
    
    private int level = 0;
    
    public void start(String message) {
        println("=== " + message + " ===");
    }
    
    public void branch(String message) {
        println(BRANCH + message);
    }
    
    public void end(String message) {
        println(LAST + message);
    }
    
    public void increaseLevel() {
        level++;
    }
    
    public void decreaseLevel() {
        if (level > 0) {
            level--;
        }
    }
    
    public void info(String message) {
        println(getIndent() + BRANCH + message);
    }
    
    public void success(String message) {
        println(getIndent() + LAST + message);
    }
    
    public void error(String message) {
        System.err.println(getIndent() + BRANCH + "错误: " + message);
    }
    
    public void warn(String message) {
        println(getIndent() + BRANCH + "警告: " + message);
    }
    
    private void println(String message) {
        System.out.println(message);
    }
    
    private String getIndent() {
        StringBuilder indent = new StringBuilder();
        for (int i = 0; i < level; i++) {
            indent.append(VERTICAL);
        }
        return indent.toString();
    }
} 