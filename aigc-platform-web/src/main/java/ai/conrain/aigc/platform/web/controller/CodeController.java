package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.CodeService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.CodeQuery;
import ai.conrain.aigc.platform.service.model.vo.CodeVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Code控制器
 *
 * <AUTHOR>
 * @version CodeService.java v 0.1 2025-05-19 02:24:41
 */
@Slf4j
@Api("码")
@Validated
@RestController
@RequestMapping("/code")
public class CodeController {

	/** codeService */
	@Autowired
	private CodeService codeService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询码", notes = "根据id查询码[code]")
	public Result<CodeVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(codeService.selectById(id));
	}
	
	@PostMapping("/createRegisterPromotionCode")
	@ApiOperation(value = "生成推广注册码", notes = "生成推广注册码")
	public Result<Integer> createRegisterPromotionCode(@JsonArg Integer relatedUserId, @JsonArg String externalNick) {
		if (relatedUserId == null && StringUtils.isBlank(externalNick)) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "推广注册码关联用户id和外部昵称不能同时为空");
		}

		if (relatedUserId != null && StringUtils.isNotBlank(externalNick)) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "推广注册码不能同时关联用户id和外部昵称");
		}

		CodeVO code = codeService.createRegisterPromotionCode(relatedUserId, externalNick);
		if (code != null) {
			return Result.success(code.getId());
		} else {
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "生成推广注册码失败");
		}
	}

	@PostMapping("/create")
	@ApiOperation(value = "新建码", notes = "新建码[code]")
	public Result<Integer> create(@Valid @RequestBody CodeVO code){
		try {
			CodeVO data = codeService.insert(code);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加码失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建码失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除码", notes = "删除码")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		codeService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新码", notes = "修改码[code]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody CodeVO code){
		codeService.updateByIdSelective(code);
		return Result.success();
	}

	@ApiOperation(value = "批量查询码", notes = "批量查询[code]")
	@PostMapping("/queryList")
	public Result<List<CodeVO>> queryCodeList(@Valid @RequestBody CodeQuery query){
		if (!OperationContextHolder.isAdmin()) {
			if (OperationContextHolder.isMasterUser()) {
				query.setCreatorMasterId(OperationContextHolder.getMasterUserId());
			} else {
				query.setCreatorId(OperationContextHolder.getOperatorUserId());
			}
		}
		return Result.success(codeService.queryCodeList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<CodeDO>]")
	public Result<PageInfo<CodeVO>> getCodeByPage(@Valid @RequestBody CodeQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		if (!OperationContextHolder.isAdmin()) {
			if (OperationContextHolder.isMasterUser()) {
				query.setCreatorMasterId(OperationContextHolder.getMasterUserId());
			} else {
				query.setCreatorId(OperationContextHolder.getOperatorUserId());
			}
		}
		return Result.success(codeService.queryCodeByPage(query));
	}
}