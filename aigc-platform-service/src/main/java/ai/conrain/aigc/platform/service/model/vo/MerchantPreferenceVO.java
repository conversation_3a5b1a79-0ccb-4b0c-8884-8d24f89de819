package ai.conrain.aigc.platform.service.model.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.enums.PreferenceTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * MerchantPreferenceVO
 *
 * @version MerchantPreferenceService.java v 0.1 2024-11-12 08:02:24
 */
@Data
public class MerchantPreferenceVO implements IExtModel, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 用户id */
    @ApiModelProperty(name = "userId", value = "用户id")
    private Integer userId;

    /** 类型，AUTO_DELIVERY、CREATIVE */
    @ApiModelProperty(name = "type", value = "类型，AUTO_DELIVERY、CREATIVE")
    private PreferenceTypeEnum type;

    @ApiModelProperty(name = "tags", value = "标签列表")
    private List<String> tags;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 模特id配置 */
    @ApiModelProperty(name = "faces", value = "模特id配置")
    private List<Integer> faces;

    /** 场景id配置 */
    @ApiModelProperty(name = "scenes", value = "场景id配置")
    private List<Integer> scenes;

    /** 操作者id */
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    /** 是否开启自动创作 */
    @ApiModelProperty(name = "enableAutoCreative", value = "是否开启自动创作")
    private Boolean enableAutoCreative;

    /** 图片数量 */
    @ApiModelProperty(name = "imageNum", value = "图片数量")
    private Integer imageNum;

    /** 图片尺寸 */
    @ApiModelProperty(name = "imageProportion", value = "图片尺寸")
    private String imageProportion;

    /** 扩展信息 */
    @ApiModelProperty(name = "extInfo", value = "扩展信息")
    private JSONObject extInfo;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 服装搭配信息 */
    @ApiModelProperty(name = "clothCollocation", value = "服装搭配信息")
    private ClothCollocationModel clothCollocation;

}