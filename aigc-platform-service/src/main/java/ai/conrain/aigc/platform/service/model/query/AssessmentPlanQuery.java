package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * AssessmentPlanQuery
 *
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
@Data
public class AssessmentPlanQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 结算主体类型 */
    @ApiModelProperty(name = "principalType", value = "结算主体类型")
    private String principalType;

    /** 结算主体 id */
    @ApiModelProperty(name = "principalId", value = "结算主体 id")
    private Integer principalId;

    /** 考核类型 */
    @ApiModelProperty(name = "type", value = "考核类型")
    private String type;

    /** 考核任务状态 */
    @ApiModelProperty(name = "status", value = "考核任务状态")
    private String status;

    /** 考核计划开始日期 */
    @ApiModelProperty(name = "planFromDate", value = "考核计划开始日期")
    private Date planFromDate;

    /** 考核计划结束日期 */
    @ApiModelProperty(name = "planEndDate", value = "考核计划结束日期")
    private Date planEndDate;

    /** 创建人用户id */
    @ApiModelProperty(name = "creatorUserId", value = "创建人用户id")
    private Integer creatorUserId;

    /** 修改人用户id */
    @ApiModelProperty(name = "modifyUserId", value = "修改人用户id")
    private Integer modifyUserId;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;


    /** 考核指标 */
    @ApiModelProperty(name = "kpiTarget", value = "考核指标")
    private String kpiTarget;

    /** 实际完成情况 */
    @ApiModelProperty(name = "kpiActual", value = "实际完成情况")
    private String kpiActual;

    /** 扩展字段 */
    @ApiModelProperty(name = "extInfo", value = "扩展字段")
    private String extInfo;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}