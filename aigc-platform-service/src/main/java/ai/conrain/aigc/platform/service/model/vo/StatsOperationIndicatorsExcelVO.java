package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


@Data
public class StatsOperationIndicatorsExcelVO {

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    @ExcelProperty("统计类型")
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    @ExcelProperty("统计日期")
    private String statsDate;

    /** 用户 id（渠道/销售/运营） */
    @ExcelProperty({"用户信息","用户ID"})
    private Integer userId;

    /** 名称（渠道/销售/运营） */
    @ExcelProperty({"用户信息","用户昵称"})
    private String name;

    /** 客户总数 */
    @ExcelProperty({"客户/服装信息","客户总数"})
    private Integer customerTotalCount = 0;

    /** 上传服装总数 */
    @ExcelProperty({"客户/服装信息","服装总数"})
    private Integer customerUploadMaterialCount = 0;

    /** 客户转换量（新签 3999 以上） */
    @ExcelProperty("客户转换量")
    private Integer customerConversionCount = 0;

    /** 客户消耗点数 */
    @ExcelProperty({"缪斯点消耗情况","客户消耗点数"})
    private Integer customerConsumptionPoints = 0;

    /** 平均消耗点数 */
    @ExcelProperty({"缪斯点消耗情况","平均消耗点数"})
    private String customerConsumptionPointsAvg = "0";

    /** 活跃客户率 */
    @ExcelProperty({"活跃客户信息","活跃客户率"})
    private String customerActivityRate  = "0";
    @ExcelProperty({"活跃客户信息","周期内活跃人数（同一客户会被统计多次）"})
    private String customerActivityRateMolecular  = "0";

    /** 客户复购率 */
    @ExcelProperty({"客户复购信息","客户复购率"})
    private String customerRepurchaseRate = "0";
    @ExcelProperty({"客户复购信息","周期内客户复购人数（同一客户会被统计多次）"})
    private String customerRepurchaseRateMolecular = "0";

    /** 定制模特比例 */
    @ExcelProperty({"定制模特信息","定制模特比例"})
    private String customModelCustomers = "0";
    @ExcelProperty({"定制模特信息","截止当前日期定制模特人数（同一客户仅会被统计一次）"})
    private String customModelCustomersMolecular = "0";

    /** 定制场景比例 */
    @ExcelProperty({"定制场景信息","定制场景比例"})
    private String customSceneCustomers = "0";
    @ExcelProperty({"定制场景信息","截止当前日期定制场景人数（同一客户仅会被统计一次）"})
    private String customSceneCustomersMolecular = "0";

    /** 视频数量 */
    @ExcelProperty({"视频数量信息","视频数量"})
    private Integer videoCount = 0;
    @ExcelProperty({"视频数量信息","客均"})
    private String videoCountAvg = "0.00";

    /** 大于 60 天未充值的客户 */
    @ExcelProperty("大于60天未充值的客户")
    private Integer customerProtectionMetrics = 0;

    /** 交付服装量 */
    @ExcelProperty("交付服装量")
    private Integer deliveryClothingCount = 0;

    /** 审核服装量 */
    @ExcelProperty("审核服装量")
    private Integer approveClothingCount = 0;

    /** 服装返点率 */
    @ExcelProperty("服装返点率")
    private String garmentRebateRate  = "0";

}
