/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.acm.listener;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import ai.conrain.aigc.platform.service.acm.AcmResource;

/**
 * acm资源配置注解
 *
 * <AUTHOR>
 * @version : ResourceConfig.java, v 0.1 2023/10/2 12:12 renxiao.wu Exp $
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ResourceConfig {
    AcmResource value();
}
