{"client_id": "d7a50357900e4ca4afc07db4ad63269a", "prompt": {"177": {"inputs": {"text": "product/20250406/100002/171045", "text_b": "product_1436420", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "png", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "235": {"inputs": {"torchscript_jit": "default", "image": ["236", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "236": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "rescale", "rescale_factor": 2, "resize_width": "1536", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["410", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "248": {"inputs": {"width": ["261", 4], "height": ["261", 5], "batch_size": 1, "color": ""}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "258": {"inputs": {"image": ["235", 0]}, "class_type": "ImageRGBA2RGB", "_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}}, "261": {"inputs": {"image": ["236", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "263": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["266", 0], "image": ["316", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "266": {"inputs": {"target_size": ["367", 0], "image": ["316", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "268": {"inputs": {"text": ["283", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "273": {"inputs": {"noise": ["277", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "274": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "275": {"inputs": {"scheduler": "sgm_uniform", "steps": "20", "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "277": {"inputs": {"noise_seed": 342179368376059}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["268", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["378", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "282": {"inputs": {"prompts": "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing { mgs2222 brown cargo pants} underneath. The model is wearing { a gray cap.wearing outdoor sports sunglasses}. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is {holding trekking poles}. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,", "seed": 2048}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "283": {"inputs": {"text": ["282", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "284": {"inputs": {"seed": 1536}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "285": {"inputs": {"seed": 1152}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "296": {"inputs": {"lora_name": "online_product/DT220SLN罗马道-黑色-白色_11148_20250401_112136/DT220SLN罗马道-黑色-白色_11148_20250401_112136-flux/DT220SLN罗马道-黑色-白色_11148_20250401_112136-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["298", 0], "clip": ["298", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "服装lora"}}, "297": {"inputs": {"lora_name": "online_product/梦特娇-改版Hubert-3张_copy_11508_20250403_082259/梦特娇-改版Hubert-3张_copy_11508_20250403_082259-flux/梦特娇-改版Hubert-3张_copy_11508_20250403_082259-flux.safetensors", "strength_model": "0.8", "strength_clip": "0.8", "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "298": {"inputs": {"lora_name": "online_product/冲锋衣户外-男_10489_20250327_110132/冲锋衣户外-男_10489_20250327_110132-flux/冲锋衣户外-男_10489_20250327_110132-flux.safetensors", "strength_model": "0.8", "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "风格lora"}}, "316": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["258", 0], "destination": ["248", 0], "mask": ["235", 1]}, "class_type": "ConrainImageCompositeMasked", "_meta": {"title": "conrain image composite masked"}}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "331": {"inputs": {"prompts": "a mgm3004 male model, short light brown hair styled with texture,", "seed": 1335}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["263", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "365": {"inputs": {"image": ["410", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax", "_meta": {"title": "Integer Maximum"}}, "373": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "推理加速开关"}}, "375": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "377": {"inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "378": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["296", 0], "model2": ["379", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "合并PW和flux模型"}}, "379": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "PW模型"}}, "388": {"inputs": {"text": "", "clip": ["393", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "389": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "390": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "391": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "392": {"inputs": {"noise_mask": true, "positive": ["412", 1], "negative": ["412", 2], "vae": ["416", 2], "pixels": ["414", 0], "mask": ["402", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "393": {"inputs": {"ckpt_name": "epicrealismXL_v9Unflux.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "397": {"inputs": {"text": ["331", 0], "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "398": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality", "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "399": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["436", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "400": {"inputs": {"x": 0, "y": 0, "operation": "subtract", "destination": ["404", 0], "source": ["401", 0]}, "class_type": "MaskComposite", "_meta": {"title": "MaskComposite"}}, "401": {"inputs": {"invert_mask": false, "grow": -32, "blur": 8, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["402", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "402": {"inputs": {"invert_mask": false, "grow": 32, "blur": 4, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["414", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "403": {"inputs": {"invert_mask": false, "grow": 72, "blur": 40, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["414", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "404": {"inputs": {"invert_mask": false, "grow": 32, "blur": 8, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["402", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "405": {"inputs": {"text": ["331", 0], "clip": ["393", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "406": {"inputs": {"pixels": ["413", 0], "vae": ["393", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "407": {"inputs": {"samples": ["406", 0], "mask": ["400", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "408": {"inputs": {"samples": ["454", 0], "vae": ["393", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "409": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["414", 0], "image_target": ["408", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "410": {"inputs": {"images": ["422", 0], "face": ["399", 0], "crop": ["409", 0], "mask": ["403", 0], "warp": ["414", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "411": {"inputs": {"seed": 769812174955577, "steps": 5, "cfg": 1, "sampler_name": "euler", "scheduler": "kl_optimal", "denoise": 0.8, "model": ["412", 0], "positive": ["392", 0], "negative": ["392", 1], "latent_image": ["392", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "412": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["389", 0], "insightface": ["390", 0], "control_net": ["391", 0], "image": ["446", 0], "model": ["416", 0], "positive": ["397", 0], "negative": ["398", 0], "image_kps": ["414", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "413": {"inputs": {"samples": ["411", 0], "vae": ["416", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "414": {"inputs": {"crop_size": 1024, "crop_factor": 1.5, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["399", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "416": {"inputs": {"ckpt_name": "sdxl/Epicrealismxl_Hades.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "417": {"inputs": {"target_size": ["420", 0], "image": ["269", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "418": {"inputs": {"value": 2048}, "class_type": "easy int", "_meta": {"title": "Int"}}, "420": {"inputs": {"mode": false, "a": ["421", 0], "b": ["418", 0]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "421": {"inputs": {"mode": true, "a": ["453", 0], "b": ["453", 1]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "422": {"inputs": {"upscale_method": "bicubic", "scale_by": ["417", 0], "image": ["269", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "429": {"inputs": {"mode": true, "a": ["435", 0], "b": ["435", 1]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "430": {"inputs": {"mode": false, "a": ["434", 0], "b": ["429", 0]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "431": {"inputs": {"target_size": ["430", 0], "image": ["469", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "433": {"inputs": {"upscale_method": "bicubic", "scale_by": ["431", 0], "image": ["469", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "434": {"inputs": {"value": 2048}, "class_type": "easy int", "_meta": {"title": "Int"}}, "435": {"inputs": {"image": ["469", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "436": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["422", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "446": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["447", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "447": {"inputs": {"threshold": 0.5, "min_size": 64, "max_size": 1024, "image": ["433", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "453": {"inputs": {"image": ["269", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "454": {"inputs": {"seed": 17, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "sgm_uniform", "denoise": 0.5, "model": ["393", 0], "positive": ["405", 0], "negative": ["388", 0], "latent_image": ["407", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "462": {"inputs": {"image": "pasted/image (1181).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "469": {"inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}, "class_type": "ImageBatchOneOrMore", "_meta": {"title": "Batch Images One or More"}}, "474": {"inputs": {"Input": ["478", 0], "image1": ["475", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "475": {"inputs": {"image": "pasted/image (1182).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "476": {"inputs": {"image": "pasted/image (1183).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "477": {"inputs": {"Input": ["479", 0], "image1": ["476", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "478": {"inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "479": {"inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 485, "last_link_id": 820, "nodes": [{"id": 177, "type": "Text String", "pos": [6533.8974609375, -787.8736572265625], "size": [315, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250406/100002/171045", "product_1436420", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 185, "type": "Text Concatenate", "pos": [7660.8974609375, -641.8736572265625], "size": [315, 178], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "type": "String to Text", "pos": [6160.8974609375, -574.8736572265625], "size": [315, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 201, "type": "String to Text", "pos": [6138.8974609375, -768.8736572265625], "size": [315, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 232, "type": "Text Concatenate", "pos": [7015.8974609375, -820.8736572265625], "size": [250, 142], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": [6526.8974609375, -916.8736572265625], "size": [315, 58], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 240, "type": "Note", "pos": [6948.8974609375, -1650.87353515625], "size": [260, 110], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 268, "type": "CLIPTextEncode", "pos": [1356.314453125, -1442.6668701171875], "size": [285.6000061035156, 54], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 513}, {"name": "text", "type": "STRING", "link": 453, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [464], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 269, "type": "VAEDecode", "pos": [2655.130859375, -1454.5645751953125], "size": [210, 46], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [611, 777], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 270, "type": "VAELoader", "pos": [2319.339111328125, -1210.8780517578125], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 271, "type": "DualCLIPLoader", "pos": [-974.3479614257812, -943.7987060546875], "size": [315, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [622, 664, 664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "type": "UNETLoader", "pos": [-969.5924682617188, -1139.4793701171875], "size": [315, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [621, 645, 647, 647], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": [2306.54931640625, -1559.1309814453125], "size": [236.8000030517578, 112.51068878173828], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 456, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 274, "type": "KSamplerSelect", "pos": [2001, -1191.7747802734375], "size": [210, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 275, "type": "BasicScheduler", "pos": [1992.************, -1071.4217529296875], "size": [210, 106], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", "20", 1]}, {"id": 276, "type": "BasicGuider", "pos": [1984.70556640625, -1433.122314453125], "size": [161.1999969482422, 46], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 277, "type": "RandomNoise", "pos": [1854.52099609375, -1600.************], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [456], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [342179368376059, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 278, "type": "FluxGuidance", "pos": [1690.1234130859375, -1421.2938232421875], "size": [211.60000610351562, 58], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 464}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "EmptySD3LatentImage", "pos": [1709.2464599609375, -1186.37353515625], "size": [210, 86.50716400146484], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1152", "1536", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 280, "type": "ModelSamplingFlux", "pos": [1692.************, -937.4694213867188], "size": [210, 122], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 651, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1152", "1536"]}, {"id": 282, "type": "ConrainRandomPrompts", "pos": [779.6383056640625, -1020.9454956054688], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [472, 473], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing { mgs2222 brown cargo pants} underneath. The model is wearing { a gray cap.wearing outdoor sports sunglasses}. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is {holding trekking poles}. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,", 2048, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 283, "type": "ShowText|pysssss", "pos": [1304.6009521484375, -1079.596923828125], "size": [256.63372802734375, 226], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 472, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [453], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 284, "type": "CR Seed", "pos": [1336.303466796875, -556.4685668945312], "size": [243.4204864501953, 102], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"]}, {"id": 285, "type": "CR Seed", "pos": [1322.303466796875, -742.4697875976562], "size": [243.4204864501953, 102], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"]}, {"id": 286, "type": "ConrainRandomPrompts", "pos": [789.3619995117188, -736.4147338867188], "size": [411.6590881347656, 124], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 287, "type": "Reroute", "pos": [2526.195556640625, -891.2860107421875], "size": [75, 26], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 473, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": [2491.195556640625, -760.2861328125], "size": [75, 26], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 320, "type": "ConrainTextSave", "pos": [8231.892578125, -569.8736572265625], "size": [315, 106], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "type": "ConrainRandomPrompts", "pos": [2518.53955078125, -1076.130859375], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [575, 786, 575], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a mgm3004 male model, short light brown hair styled with texture,", 1335, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 349, "type": "ConrainPythonExecutor", "pos": [1669.60546875, -666.1307373046875], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": [1692.6005859375, -565.1295776367188], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": [1901.601806640625, -688.1304931640625], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": [2151.55419921875, -692.1304931640625], "size": [210, 34], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": [1972.60302734375, -558.1295776367188], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": [2205.5419921875, -537.129638671875], "size": [210, 34], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 365, "type": "Image Size to Number", "pos": [7407.1708984375, -1615.321533203125], "size": [229.20001220703125, 126], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 783, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": [7701.1708984375, -1540.321533203125], "size": [210, 67.1211166381836], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 373, "type": "JWStringToInteger", "pos": [-205.92677307128906, -1002.7108764648438], "size": [210, 58], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [644, 644], "slot_index": 0}], "title": "推理加速开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 375, "type": "ApplyFBCacheOnModel", "pos": [-483.5779724121094, -705.3702392578125], "size": [315, 154], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 647}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [642], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "type": "CR Model Input Switch", "pos": [170.24545288085938, -701.9577026367188], "size": [257.191650390625, 78.78076171875], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 642, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 645, "shape": 7}, {"name": "Input", "type": "INT", "link": 644, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [646], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["1"]}, {"id": 378, "type": "ModelMergeFlux1", "pos": [284, -1048], "size": [315, 1566], "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 650}, {"name": "model2", "type": "MODEL", "link": 663}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [651], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "type": "UNETLoader", "pos": [-971.5514526367188, -1367.3934326171875], "size": [315, 82], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [662, 662], "slot_index": 0, "shape": 3}], "title": "PW模型", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 385, "type": "Reroute", "pos": [-474.9272766113281, -1071.6295166015625], "size": [75, 26], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 662}], "outputs": [{"name": "", "type": "MODEL", "links": [663], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 386, "type": "Reroute", "pos": [415.603759765625, -950.938232421875], "size": [75, 26], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 664}], "outputs": [{"name": "", "type": "CLIP", "links": [665], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 388, "type": "CLIPTextEncode", "pos": [4070.31640625, 219.6317138671875], "size": [400, 200], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 671}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [770], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 389, "type": "InstantIDModelLoader", "pos": [4470.31640625, -880.367431640625], "size": [315, 58], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [710], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "type": "InstantIDFaceAnalysis", "pos": [4450.31640625, -820.367431640625], "size": [315, 58], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [711], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 391, "type": "ControlNetLoader", "pos": [4460.31640625, -760.367431640625], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [712], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 392, "type": "InpaintModelConditioning", "pos": [4970.31640625, -860.367431640625], "size": [210, 138], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 672, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 673}, {"name": "vae", "type": "VAE", "link": 674}, {"name": "pixels", "type": "IMAGE", "link": 675}, {"name": "mask", "type": "MASK", "link": 676}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [707], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [708], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [709], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 393, "type": "CheckpointLoaderSimple", "pos": [3690.31640625, -40.3681526184082], "size": [315, 98], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [680], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [671, 691], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [694, 698], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["epicrealismXL_v9Unflux.safetensors"]}, {"id": 396, "type": "LoraLoaderModelOnly", "pos": [4070.31640625, -20.3681697845459], "size": [250.94566345214844, 82], "flags": {}, "order": 39, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 680}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["open_lora/FLUX.1-Turbo-Alpha.safetensors", 1]}, {"id": 397, "type": "CLIPTextEncode", "pos": [4160.31640625, -880.367431640625], "size": [210, 96], "flags": {"collapsed": false}, "order": 49, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 681}, {"name": "text", "type": "STRING", "link": 788, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [715], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 398, "type": "CLIPTextEncode", "pos": [4140.31640625, -760.367431640625], "size": [239.4051971435547, 91.89370727539062], "flags": {"collapsed": false}, "order": 41, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 683}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [716], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality"], "color": "#232", "bgcolor": "#353"}, {"id": 399, "type": "OrderedFaceFilter", "pos": [3710.31640625, -360.3682861328125], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 684}], "outputs": [{"name": "filtered", "type": "FACE", "links": [702, 720], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 400, "type": "MaskComposite", "pos": [5110.31640625, -360.3682861328125], "size": [210, 126], "flags": {"collapsed": false}, "order": 91, "mode": 0, "inputs": [{"name": "destination", "type": "MASK", "link": 685}, {"name": "source", "type": "MASK", "link": 686}], "outputs": [{"name": "MASK", "type": "MASK", "links": [696], "slot_index": 0}], "properties": {"Node name for S&R": "MaskComposite"}, "widgets_values": [0, 0, "subtract"]}, {"id": 401, "type": "MaskFastGrow", "pos": [4820.31640625, -380.3682861328125], "size": [210, 178], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 687}], "outputs": [{"name": "MASK", "type": "MASK", "links": [686], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, -32, 8, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 402, "type": "MaskFastGrow", "pos": [4270.31640625, -380.3682861328125], "size": [210, 178], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 688}], "outputs": [{"name": "MASK", "type": "MASK", "links": [676, 687, 690], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 32, 4, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 403, "type": "MaskFastGrow", "pos": [4280.31640625, -620.3678588867188], "size": [210, 178], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 689}], "outputs": [{"name": "MASK", "type": "MASK", "links": [704], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 72, 40, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 404, "type": "MaskFastGrow", "pos": [4540.31640625, -380.3682861328125], "size": [210, 178], "flags": {"collapsed": false}, "order": 89, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 690}], "outputs": [{"name": "MASK", "type": "MASK", "links": [685], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 32, 8, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 405, "type": "CLIPTextEncode", "pos": [4100.31640625, 129.6317138671875], "size": [221.6905059814453, 76], "flags": {"collapsed": true}, "order": 48, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 691}, {"name": "text", "type": "STRING", "link": 787, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [769], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["woman"], "color": "#222", "bgcolor": "#000"}, {"id": 406, "type": "VAEEncode", "pos": [4150.31640625, 329.6318054199219], "size": [140, 46], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 693}, {"name": "vae", "type": "VAE", "link": 694}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [695], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 407, "type": "SetLatentNoiseMask", "pos": [4410.31640625, 329.6318054199219], "size": [176.39999389648438, 46], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 695}, {"name": "mask", "type": "MASK", "link": 696}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [771], "slot_index": 0}], "properties": {"Node name for S&R": "SetLatentNoiseMask"}, "widgets_values": []}, {"id": 408, "type": "VAEDecode", "pos": [5120.31640625, -0.3681756854057312], "size": [140, 46], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 697}, {"name": "vae", "type": "VAE", "link": 698}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [700], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 409, "type": "easy imageColorMatch", "pos": [5310.31640625, -30.368154525756836], "size": [210, 102], "flags": {"collapsed": false}, "order": 97, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 699}, {"name": "image_target", "type": "IMAGE", "link": 700}], "outputs": [{"name": "image", "type": "IMAGE", "links": [703], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 410, "type": "WarpFacesBack", "pos": [5140.31640625, 129.6317138671875], "size": [182.46627807617188, 157.38844299316406], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 701}, {"name": "face", "type": "FACE", "link": 702}, {"name": "crop", "type": "IMAGE", "link": 703}, {"name": "mask", "type": "MASK", "link": 704}, {"name": "warp", "type": "WARP", "link": 705}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [781], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 411, "type": "K<PERSON><PERSON><PERSON>", "pos": [5260.31640625, -840.367431640625], "size": [261.8017578125, 262], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 706}, {"name": "positive", "type": "CONDITIONING", "link": 707}, {"name": "negative", "type": "CONDITIONING", "link": 708}, {"name": "latent_image", "type": "LATENT", "link": 709}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [718], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [769812174955577, "randomize", 5, 1, "euler", "kl_optimal", 0.8]}, {"id": 412, "type": "ApplyInstantID", "pos": [4700.31640625, -860.367431640625], "size": [210, 266], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 710, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 711, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 712, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 713, "label": "image"}, {"name": "model", "type": "MODEL", "link": 714, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 715, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 716, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 717, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [706], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [672], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [673], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 413, "type": "VAEDecode", "pos": [5400.31640625, -320.3682556152344], "size": [140, 46], "flags": {"collapsed": false}, "order": 92, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 718}, {"name": "vae", "type": "VAE", "link": 719}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [693], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 414, "type": "CropFaces", "pos": [3950.31640625, -350.3682861328125], "size": [221.15121459960938, 146], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 720}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [675, 699, 717], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [688, 689], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [705], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 1.5, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 416, "type": "CheckpointLoaderSimple", "pos": [3710.31640625, -890.367431640625], "size": [254.65628051757812, 146.35491943359375], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [714], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [681, 683], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [674, 719], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/Epicrealismxl_Hades.safetensors"]}, {"id": 417, "type": "UpscaleSizeCalculator", "pos": [3296.6396484375, -553.9926147460938], "size": [220, 113.94757080078125], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 776}, {"name": "target_size", "type": "INT", "link": 722, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [729], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [2048]}, {"id": 418, "type": "easy int", "pos": [3286.6396484375, -813.9918212890625], "size": [210, 63.99684524536133], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "int", "type": "INT", "links": [725], "slot_index": 0}], "properties": {"Node name for S&R": "easy int"}, "widgets_values": [2048]}, {"id": 420, "type": "ImpactMinMax", "pos": [3286.6396484375, -693.9922485351562], "size": [210, 85.42294311523438], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 724}, {"name": "b", "type": "*", "link": 725}], "outputs": [{"name": "INT", "type": "INT", "links": [722], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [false]}, {"id": 421, "type": "ImpactMinMax", "pos": [3286.6396484375, -963.9913940429688], "size": [210, 78], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 726}, {"name": "b", "type": "*", "link": 727}], "outputs": [{"name": "INT", "type": "INT", "links": [724], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [true]}, {"id": 422, "type": "ImageScaleBy", "pos": [3710.31640625, -510.3682861328125], "size": [210, 82], "flags": {"collapsed": false}, "order": 80, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 790}, {"name": "scale_by", "type": "FLOAT", "link": 729, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [701, 750], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["bicubic", 0.5]}, {"id": 430, "type": "ImpactMinMax", "pos": [4975.810546875, -1250.80322265625], "size": [210, 85.89134216308594], "flags": {"collapsed": true}, "order": 60, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 741}, {"name": "b", "type": "*", "link": 742}], "outputs": [{"name": "INT", "type": "INT", "links": [744], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [false]}, {"id": 431, "type": "UpscaleSizeCalculator", "pos": [4845.810546875, -1170.8026123046875], "size": [220, 94], "flags": {"collapsed": true}, "order": 63, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 808}, {"name": "target_size", "type": "INT", "link": 744, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [748], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [2048]}, {"id": 434, "type": "easy int", "pos": [4835.810546875, -1240.80322265625], "size": [210, 63.99684524536133], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"name": "int", "type": "INT", "links": [741], "slot_index": 0}], "properties": {"Node name for S&R": "easy int"}, "widgets_values": [2048]}, {"id": 435, "type": "GetImageSize+", "pos": [4795.810546875, -1300.803466796875], "size": [214.20001220703125, 66], "flags": {"collapsed": true}, "order": 55, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 807}], "outputs": [{"name": "width", "type": "INT", "links": [739], "slot_index": 0}, {"name": "height", "type": "INT", "links": [740], "slot_index": 1}, {"name": "count", "type": "INT", "slot_index": 2}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 436, "type": "DetectFaces", "pos": [3990.31640625, -600.367919921875], "size": [210, 126], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 750}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [684], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 453, "type": "GetImageSize+", "pos": [3286.6396484375, -1083.991455078125], "size": [214.20001220703125, 66], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 774}], "outputs": [{"name": "width", "type": "INT", "links": [726], "slot_index": 0}, {"name": "height", "type": "INT", "links": [727], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 454, "type": "K<PERSON><PERSON><PERSON>", "pos": [4700.31640625, 59.63178634643555], "size": [246.57957458496094, 275.6719970703125], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 768}, {"name": "positive", "type": "CONDITIONING", "link": 769}, {"name": "negative", "type": "CONDITIONING", "link": 770}, {"name": "latent_image", "type": "LATENT", "link": 771}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [697], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [17, "fixed", 8, 1, "euler", "sgm_uniform", 0.5]}, {"id": 455, "type": "Reroute", "pos": [2920.9287109375, -1452.91015625], "size": [75, 26], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 777}], "outputs": [{"name": "", "type": "IMAGE", "links": [774, 776, 789], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 456, "type": "Reroute", "pos": [5490.31640625, 129.6317138671875], "size": [75, 26], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 781}], "outputs": [{"name": "", "type": "IMAGE", "links": [782, 783], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 457, "type": "Reroute", "pos": [3770.6259765625, -642.3026733398438], "size": [75, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 786, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [787, 788], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 458, "type": "Reroute", "pos": [3459.513427734375, -1455.8084716796875], "size": [75, 26], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 789}], "outputs": [{"name": "", "type": "IMAGE", "links": [790], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 355, "type": "ImageCrop", "pos": [7702.8974609375, -944.8736572265625], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [603], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [754.6295166015625, -1641.997314453125], "size": [489.4413757324219, 126], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 646}, {"name": "clip", "type": "CLIP", "link": 665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [627], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [628, 625], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/梦特娇-改版Hubert-3张_copy_11508_20250403_082259/梦特娇-改版Hubert-3张_copy_11508_20250403_082259-flux/梦特娇-改版Hubert-3张_copy_11508_20250403_082259-flux.safetensors", "0.8", "0.8"], "color": "#232", "bgcolor": "#353"}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [755, -1446], "size": [491.7470703125, 126], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 627}, {"name": "clip", "type": "CLIP", "link": 628}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/冲锋衣户外-男_10489_20250327_110132/冲锋衣户外-男_10489_20250327_110132-flux/冲锋衣户外-男_10489_20250327_110132-flux.safetensors", "0.8", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [749.9860229492188, -1247.851806640625], "size": [499.25970458984375, 126], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [514, 650, 654], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [513, 510, 513, 630, 631], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/DT220SLN罗马道-黑色-白色_11148_20250401_112136/DT220SLN罗马道-黑色-白色_11148_20250401_112136-flux/DT220SLN罗马道-黑色-白色_11148_20250401_112136-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 433, "type": "ImageScaleBy", "pos": [4845.810546875, -1100.802001953125], "size": [210, 82], "flags": {"collapsed": true}, "order": 66, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 809}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["bicubic", 0.5]}, {"id": 469, "type": "ImageBatchOneOrMore", "pos": [4548.06884765625, -1231.3271484375], "size": [201.60000610351562, 126], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 803}, {"name": "image2", "type": "IMAGE", "link": 806, "shape": 7}, {"name": "image3", "type": "IMAGE", "link": 814, "shape": 7}, {"name": "image4", "type": "IMAGE", "link": null, "shape": 7}, {"name": "image5", "type": "IMAGE", "link": null, "shape": 7}, {"name": "image6", "type": "IMAGE", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807, 808, 809], "slot_index": 0}], "properties": {"Node name for S&R": "ImageBatchOneOrMore"}, "widgets_values": []}, {"id": 448, "type": "PreviewImage", "pos": [5332.01318359375, -1362.76904296875], "size": [223.88575744628906, 246], "flags": {}, "order": 73, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 762}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 429, "type": "ImpactMinMax", "pos": [4975.810546875, -1310.803466796875], "size": [210, 82.59356689453125], "flags": {"collapsed": true}, "order": 58, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 739}, {"name": "b", "type": "*", "link": 740}], "outputs": [{"name": "INT", "type": "INT", "links": [742], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [true]}, {"id": 447, "type": "DetectFaces", "pos": [5014.69189453125, -1674.0845947265625], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 812}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [760], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.5, 64, 1024]}, {"id": 446, "type": "CropFaces", "pos": [5281.49609375, -1665.6295166015625], "size": [221.15121459960938, 146], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 760}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [713, 762], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 474, "type": "CR Image Input Switch", "pos": [4150, -1250], "size": [210, 74], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 813, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 816, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [806], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 477, "type": "CR Image Input Switch", "pos": [4140, -1110], "size": [210, 74], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 815, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 817, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [814], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 216, "type": "ConrainImageSave", "pos": [8217.89453125, -947.8736572265625], "size": [320, 266], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 603, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 263, "type": "ImageScaleBy", "pos": [8283.8857421875, -1574.87353515625], "size": [228.9691162109375, 78], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": [7969.8974609375, -1462.87353515625], "size": [220, 118], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1536"], "color": "#494949", "bgcolor": "#353535"}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": [7391.8974609375, -1376.87353515625], "size": [252, 146], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 258, "type": "ImageRGBA2RGB", "pos": [6959.8974609375, -1252.87353515625], "size": [252, 26], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 248, "type": "EmptyImage", "pos": [6973.8974609375, -1469.87353515625], "size": [231.5089111328125, 120.12616729736328], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 261, "type": "Image Size to Number", "pos": [6524.8974609375, -1556.87353515625], "size": [229.20001220703125, 126], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 235, "type": "InspyrenetRembg", "pos": [6518.8974609375, -1330.87353515625], "size": [230, 90], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 236, "type": "CR Upscale Image", "pos": [6062.8974609375, -1399.87353515625], "size": [315, 222], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 782, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1536", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 478, "type": "CR Text", "pos": [3760, -1270], "size": [210, 96], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [816]}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"]}, {"id": 479, "type": "CR Text", "pos": [3760, -1110], "size": [210, 96], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [817]}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"]}, {"id": 462, "type": "LoadImage", "pos": [3771.17578125, -1662.66845703125], "size": [235.8109893798828, 290], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [803], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1181).png", "image"]}, {"id": 475, "type": "LoadImage", "pos": [4128.70947265625, -1670.5030517578125], "size": [249.60922241210938, 290], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [813], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1182).png", "image"]}, {"id": 476, "type": "LoadImage", "pos": [4503.70849609375, -1672.0340576171875], "size": [234.48504638671875, 290], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [815], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1183).png", "image"]}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [603, 355, 0, 216, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [645, 272, 0, 377, 1, "MODEL"], [646, 377, 0, 297, 0, "MODEL"], [647, 272, 0, 375, 0, "MODEL"], [650, 296, 0, 378, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [671, 393, 1, 388, 0, "CLIP"], [672, 412, 1, 392, 0, "CONDITIONING"], [673, 412, 2, 392, 1, "CONDITIONING"], [674, 416, 2, 392, 2, "VAE"], [675, 414, 0, 392, 3, "IMAGE"], [676, 402, 0, 392, 4, "MASK"], [680, 393, 0, 396, 0, "MODEL"], [681, 416, 1, 397, 0, "CLIP"], [683, 416, 1, 398, 0, "CLIP"], [684, 436, 0, 399, 0, "FACE"], [685, 404, 0, 400, 0, "MASK"], [686, 401, 0, 400, 1, "MASK"], [687, 402, 0, 401, 0, "MASK"], [688, 414, 1, 402, 0, "MASK"], [689, 414, 1, 403, 0, "MASK"], [690, 402, 0, 404, 0, "MASK"], [691, 393, 1, 405, 0, "CLIP"], [693, 413, 0, 406, 0, "IMAGE"], [694, 393, 2, 406, 1, "VAE"], [695, 406, 0, 407, 0, "LATENT"], [696, 400, 0, 407, 1, "MASK"], [697, 454, 0, 408, 0, "LATENT"], [698, 393, 2, 408, 1, "VAE"], [699, 414, 0, 409, 0, "IMAGE"], [700, 408, 0, 409, 1, "IMAGE"], [701, 422, 0, 410, 0, "IMAGE"], [702, 399, 0, 410, 1, "FACE"], [703, 409, 0, 410, 2, "IMAGE"], [704, 403, 0, 410, 3, "MASK"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [707, 392, 0, 411, 1, "CONDITIONING"], [708, 392, 1, 411, 2, "CONDITIONING"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [714, 416, 0, 412, 4, "MODEL"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [717, 414, 0, 412, 7, "IMAGE"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [722, 420, 0, 417, 1, "INT"], [724, 421, 0, 420, 0, "*"], [725, 418, 0, 420, 1, "*"], [726, 453, 0, 421, 0, "*"], [727, 453, 1, 421, 1, "*"], [729, 417, 0, 422, 1, "FLOAT"], [739, 435, 0, 429, 0, "*"], [740, 435, 1, 429, 1, "*"], [741, 434, 0, 430, 0, "*"], [742, 429, 0, 430, 1, "*"], [744, 430, 0, 431, 1, "INT"], [748, 431, 0, 433, 1, "FLOAT"], [750, 422, 0, 436, 0, "IMAGE"], [760, 447, 0, 446, 0, "FACE"], [762, 446, 0, 448, 0, "IMAGE"], [768, 396, 0, 454, 0, "MODEL"], [769, 405, 0, 454, 1, "CONDITIONING"], [770, 388, 0, 454, 2, "CONDITIONING"], [771, 407, 0, 454, 3, "LATENT"], [774, 455, 0, 453, 0, "IMAGE"], [776, 455, 0, 417, 0, "IMAGE"], [777, 269, 0, 455, 0, "*"], [781, 410, 0, 456, 0, "*"], [782, 456, 0, 236, 0, "IMAGE"], [783, 456, 0, 365, 0, "IMAGE"], [786, 331, 0, 457, 0, "*"], [787, 457, 0, 405, 1, "STRING"], [788, 457, 0, 397, 1, "STRING"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [803, 462, 0, 469, 0, "IMAGE"], [806, 474, 0, 469, 1, "IMAGE"], [807, 469, 0, 435, 0, "IMAGE"], [808, 469, 0, 431, 0, "IMAGE"], [809, 469, 0, 433, 0, "IMAGE"], [812, 433, 0, 447, 0, "IMAGE"], [813, 475, 0, 474, 0, "IMAGE"], [814, 477, 0, 469, 2, "IMAGE"], [815, 476, 0, 477, 0, "IMAGE"], [816, 478, 0, 474, 2, "INT"], [817, 479, 0, 477, 2, "INT"]], "groups": [{"id": 3, "title": "换背景", "bounding": [5945.7255859375, -1753.57763671875, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "出图", "bounding": [631.6390380859375, -1742.131591796875, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "保存图片", "bounding": [5943.24755859375, -1050.6004638671875, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "模型加载", "bounding": [-1031.672119140625, -1730.5921630859375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "换脸", "bounding": [3680.31640625, -960.367431640625, 1904.325927734375, 793.1136474609375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "精修脸部边缘", "bounding": [3680.31640625, -110.36812591552734, 1918.953857421875, 535.1592407226562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "人脸原图", "bounding": [3680.31640625, -1800.369140625, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "限制最大尺寸", "bounding": [3211.508544921875, -1232.4637451171875, 364.15802001953125, 850.4071655273438], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3073560549142836, "offset": [-1826.6520434542772, 2182.3016624588004]}}, "version": 0.4, "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "454": 0}}}}}