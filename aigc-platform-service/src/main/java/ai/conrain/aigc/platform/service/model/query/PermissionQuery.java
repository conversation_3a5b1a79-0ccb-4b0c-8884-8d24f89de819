package ai.conrain.aigc.platform.service.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PermissionQuery
 *
 * @version PermissionService.java v 0.1 2024-01-20 01:21:37
 */
@Data
public class PermissionQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 执行动作 */
    @ApiModelProperty(name = "action", value = "执行动作")
    private String action;

    /** 权限名称 */
    @ApiModelProperty(name = "name", value = "权限名称")
    private String name;

    /** 权限配置 */
    @ApiModelProperty(name = "config", value = "权限配置")
    private String config;

    /** 是否允许子账号执行 */
    @ApiModelProperty(name = "allowedSub", value = "是否允许子账号执行")
    private Boolean allowedSub;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 创建时间 */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    @ApiModelProperty(name = "pageSize", value = "翻页参数：页面大小，翻页查询时必填")
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    @ApiModelProperty(name = "pageNum", value = "翻页参数：页数（从1开始），翻页查询时必填")
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    @ApiModelProperty(name = "orderBy", value = "排序指令（示例：\"id asc\"）")
    private String orderBy;

}