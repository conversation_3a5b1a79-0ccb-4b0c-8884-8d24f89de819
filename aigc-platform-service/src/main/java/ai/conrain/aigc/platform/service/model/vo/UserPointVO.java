package ai.conrain.aigc.platform.service.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * UserPointVO
 *
 * @version UserPointService.java v 0.1 2024-05-15 10:58:45
 */
@Data
public class UserPointVO implements Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(name = "id", value = "id")
    private Integer id;

    /** 用户id */
    @ApiModelProperty(name = "userId", value = "用户id")
    private Integer userId;

    /** 算力点 */
    @ApiModelProperty(name = "point", value = "算力点（带膨胀系数）")
    private Integer point;

    @ApiModelProperty(name = "givePoint", value = "赠送点数")
    private Integer givePoint;

    /** 体验点 */
    @ApiModelProperty(name = "experiencePoint", value = "体验点")
    private Integer experiencePoint;

    @ApiModelProperty(name = "imagePoint", value = "图片算力muse点")
    private BigDecimal imagePoint;

    /** 创建时间 */
    @JsonIgnore
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @JsonIgnore
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** 用户昵称 */
    private String nickName;


}