package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.alibaba.schedulerx.shade.org.apache.commons.lang.ObjectUtils;

import ai.conrain.aigc.platform.dal.dao.ImageCaseDAO;
import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.ImageCaseTagDO;
import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.dal.entity.StatsQueuedModelDO;
import ai.conrain.aigc.platform.dal.entity.SubModelStatusDO;
import ai.conrain.aigc.platform.dal.example.MaterialModelExample;
import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ComfyuiTaskService;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.MerchantPreferenceService;
import ai.conrain.aigc.platform.service.component.ModelPointService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserFavorService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.EventConstants;
import ai.conrain.aigc.platform.service.constants.LoraActivateKeys;
import ai.conrain.aigc.platform.service.enums.AgeRangeEnum;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.DictTagsEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ElementStatusEnum;
import ai.conrain.aigc.platform.service.enums.FavorTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.helper.CreateTestImgHelper;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothAngleDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothComplexResult;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.CommonMaterialDetail;
import ai.conrain.aigc.platform.service.model.biz.ConfirmCanDeliveryInfo;
import ai.conrain.aigc.platform.service.model.biz.CutoutTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.biz.LabelExtTagsDetail;
import ai.conrain.aigc.platform.service.model.biz.LabelTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.LoraSubTask;
import ai.conrain.aigc.platform.service.model.biz.LoraTaskParams;
import ai.conrain.aigc.platform.service.model.biz.LoraTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.biz.MerchantPreferenceDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.event.AutoDeliveryEvent;
import ai.conrain.aigc.platform.service.model.event.CreateTestImgEvent;
import ai.conrain.aigc.platform.service.model.event.CreateTestImgEvent.CreateTestRequest;
import ai.conrain.aigc.platform.service.model.event.LoraUploadEvent;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.query.UserFavorQuery;
import ai.conrain.aigc.platform.service.model.request.AutoGenImgParam;
import ai.conrain.aigc.platform.service.model.request.ConfirmLoraReq;
import ai.conrain.aigc.platform.service.model.request.QueryAutoGenElementRecommendRequest;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.model.vo.LabelFileEditReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.MerchantPreferenceVO;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;
import ai.conrain.aigc.platform.service.model.vo.ModelTrainDetailVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.model.vo.SyncImageCaseReq;
import ai.conrain.aigc.platform.service.model.vo.UserFavorVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.MaterialUploadUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.PromptModificationUtil;
import ai.conrain.aigc.platform.service.util.PromptUtils;
import ai.conrain.aigc.platform.service.util.WaterMarkUtils;
import com.google.common.collect.Lists;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.CREATIVE_ELEMENT_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_RANGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AUTO_DELIVERY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AUTO_TRAIN;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_COLOR_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_TYPE_CONFIGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_COMPLEX_REASONS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DEMO_TAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_GARMENT_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_INCLUDES_BRA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_INIT_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_COMPLEX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_MODIFY_LABEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_SEND_TEST_EVENT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODEL_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODIFY_LABEL_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_OP_VERSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCENE_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SUB_LORA_NAMES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_IMAGE_CNT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_IMAGE_FINISHED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRAIN_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_WITHOUT_DEDUCTION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.TEST_IMG_MODE_CARTESIAN_PRO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.TEST_IMG_MODE_RANDOM;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.female;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.highResModelShowImgUrl;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.lowerGarment;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.male;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.unisex;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.woman;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.AUTO_DELIVERY_MERCHANT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.DECIDE_CLOTH_COMPLEX_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_CLOTH_DETAILS_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_CLOTH_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_FACE_MINI_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_SCENE_PROMPT;

/**
 * MaterialModelService实现
 *
 * <AUTHOR>
 * @version MaterialModelService.java v 0.1 2024-05-09 06:10:02
 */
@Slf4j
@Service
public class MaterialModelServiceImpl implements MaterialModelService {
    /** DAO */
    @Autowired
    private MaterialModelDAO materialModelDAO;
    @Autowired
    private UserService userService;

    @Lazy
    @Autowired
    private ComfyuiTaskService comfyuiTaskService;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private OssService ossService;

    @Autowired
    private MaterialInfoService materialInfoService;

    @Autowired
    private ModelPointService modelPointService;

    @Lazy
    @Autowired
    private CreativeBatchService creativeBatchService;

    @Lazy
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Lazy
    @Autowired
    private DistributorCustomerService distributorCustomerService;

    @Lazy
    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private OssHelper ossHelper;

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private MerchantPreferenceService merchantPreferenceService;
    @Autowired
    private PromptDictService promptDictService;
    @Autowired
    private ImageCaseService imageCaseService;
    @Autowired
    private ImageCaseDAO imageCaseDAO;

    @Autowired
    private CommonTaskService commonTaskService;

    @Autowired
    private UserPointService userPointService;
    @Autowired
    private PromptModificationUtil promptModificationUtil;

    @Autowired
    private UserFavorService userFavorService;
    @Autowired
    private CreateTestImgHelper createTestImgHelper;
    @Autowired
    private UserProfileService userProfileService;

    @Override
    public MaterialModelVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        MaterialModelDO data = materialModelDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        MaterialModelVO model = MaterialModelConverter.do2VO(data);

        //初始化服装类型属性
        if (model.getClothTypeConfigs() == null) {
            model.setClothTypeConfigs(MaterialModelConverter.convert2ClothTypeConfig(model));
        }

        //初始化背面属性
        List<MaterialModelVO> list = Collections.singletonList(model);
        initBackViewProps(list);
        fillClothMaterialInfo(list);

        return model;
    }

    @Override
    public MaterialModelVO lockById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        try {
            MaterialModelDO data = materialModelDAO.lockByPrimaryKey(id);
            if (null == data) {
                return null;
            }

            return MaterialModelConverter.do2VO(data);

            //如果加锁失败，这里会返回null，由上层处理
        } catch (Throwable t) {
            log.error("lockById error", t);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public MaterialModelVO cloneLora(Integer modelId, String cloneLoraName, JSONObject extInfo, boolean fullCopy) {

        MaterialModelVO model = this.selectById(modelId);
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "不存在的素材模型id:" + modelId);

        if (model.getMainType() != MainTypeEnum.MAIN) {
            return cloneLora(model, cloneLoraName, extInfo, fullCopy, null);
        }

        MaterialModelVO main = cloneLora(model, cloneLoraName, extInfo, fullCopy, null);

        List<MaterialModelVO> subList = querySubModel(modelId);
        for (MaterialModelVO sub : subList) {
            cloneLora(sub, cloneLoraName, extInfo, fullCopy, main.getId());
        }

        return main;
    }

    @Override
    public MaterialModelVO copyToSystem(Integer modelId) {
        MaterialModelVO model = this.selectById(modelId);
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "不存在的素材模型id:" + modelId);
        AssertUtil.assertTrue(
            model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getOriginalMaterialId() != null,
            ResultCode.PARAM_INVALID, "material id为空");

        String name = getCloneLoraName(model);

        model.setName(name);
        model.setStatus(MaterialModelStatusEnum.TESTING.getCode());
        model.setExtInfo(model.getExtInfo());
        model.setType(ModelTypeEnum.SYSTEM);
        model.setCreateTime(new Date());
        model.setModifyTime(new Date());
        model.setUserId(OperationContextHolder.getMasterUserId());
        model.setOperatorId(OperationContextHolder.getOperatorUserId());
        model.addExtInfo(KEY_ORIGIN_MODEL, model.getId());
        model.setId(null);

        return this.insert(model);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        MaterialModelVO model = lockById(id);

        if (model.getMainType() == MainTypeEnum.MAIN) {
            List<MaterialModelVO> subList = querySubModel(id);
            for (MaterialModelVO sub : subList) {
                int x = materialModelDAO.logicalDeleteByPrimaryKey(sub.getId());
                AssertUtil.assertTrue(x == 1, ResultCode.BIZ_FAIL, "删除MaterialModel失败");
            }
        }

        int n = materialModelDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除MaterialModel失败");
    }

    @Override
    public MaterialModelVO insert(MaterialModelVO materialModel) {
        AssertUtil.assertNotNull(materialModel, ResultCode.PARAM_INVALID, "materialModel is null");
        AssertUtil.assertTrue(materialModel.getId() == null, ResultCode.PARAM_INVALID, "materialModel.id is present");

        //模型名称的正则表达式：只允许中文、英文、数字、下划线或中划线
        String regex = "^[\u4e00-\u9fa5a-zA-Z0-9-_]+$";
        AssertUtil.assertTrue(Pattern.matches(regex, materialModel.getName()), ResultCode.PARAM_INVALID,
            "materialModel.name is illegal:" + materialModel.getName());

        //创建时间、修改时间兜底
        if (materialModel.getCreateTime() == null) {
            materialModel.setCreateTime(new Date());
        }

        if (materialModel.getModifyTime() == null) {
            materialModel.setModifyTime(new Date());
        }

        MaterialModelDO data = MaterialModelConverter.vo2DO(materialModel);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        int n = materialModelDAO.insertSelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建MaterialModel失败");
        AssertUtil.assertNotNull(data.getId(), "新建MaterialModel返回id为空");
        materialModel.setId(data.getId());
        return materialModel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByIdSelective(MaterialModelVO targetModel) {
        AssertUtil.assertNotNull(targetModel, ResultCode.PARAM_INVALID, "targetModel is null");
        AssertUtil.assertTrue(targetModel.getId() != null, ResultCode.PARAM_INVALID, "targetModel.id is null");

        MaterialModelVO currentModel = lockById(targetModel.getId());
        AssertUtil.assertNotNull(currentModel, ResultCode.SYS_ERROR, "targetModel is null");

        if (!OperationContextHolder.getRoleType().isBackRole() && !OperationContextHolder.isDistributorRole()) {
            AssertUtil.assertOperatePermission(currentModel.getUserId(), "无权限修改其他用户创建的素材模型");
        }

        //后台页面，更新了服装的类型（上装｜下装等）
        if (StringUtils.isNotBlank(targetModel.getClothType())) {
            AssertUtil.assertNotNull(ClothTypeEnum.getByCode(targetModel.getClothType()),
                "clothType参数错误:" + targetModel.getClothType());
            LoraTrainDetail trainDetail = (LoraTrainDetail)ObjectUtils.defaultIfNull(
                targetModel.getClothLoraTrainDetail(), currentModel.getClothLoraTrainDetail());
            if (trainDetail != null) {
                trainDetail.setClothType(MaterialUploadUtil.getClothType4TrainDetail(targetModel.getClothType()));
                targetModel.setClothLoraTrainDetail(trainDetail);
            }

            if (currentModel.getClothLoraTrainDetail() != null
                && currentModel.getClothLoraTrainDetail().getOriginalMaterialId() != null) {
                MaterialInfoVO materialInfoVO = materialInfoService.selectById(
                    currentModel.getClothLoraTrainDetail().getOriginalMaterialId());
                if (materialInfoVO != null && !StringUtils.equals(materialInfoVO.getSubType(),
                    targetModel.getClothType())) {
                    materialInfoVO.setSubType(targetModel.getClothType());
                    materialInfoService.updateByIdSelective(materialInfoVO);
                }
            }
        }

        //兼容处理，只有当opVersion上传时才进行校验
        Integer opVersion = targetModel.getOpVersion();
        if (opVersion != null) {
            Integer originOpVersion = currentModel.getExtInfo(KEY_OP_VERSION, Integer.class);
            if (originOpVersion != null && !opVersion.equals(originOpVersion)) {
                log.error("更新模型失败，opVersion不一致，currentModel:{},targetModel:{}", originOpVersion, opVersion);
                throw new BizException(ResultCode.CANNOT_ACQUIRE_LOCK_MODEL,
                    "当前模型配置已被其他人修改过，请保存数据并刷新后重试");
            }
            targetModel.addExtInfo(KEY_OP_VERSION, opVersion + 1);
        }

        List<MaterialModelVO> subList = currentModel.getMainType() == MainTypeEnum.MAIN ? querySubModel(
            currentModel.getId()) : null;

        //设置服装类型的个性化配置信息
        if (OperationContextHolder.isBackRole() && CollectionUtils.isNotEmpty(targetModel.getClothTypeConfigs())
            && !StringUtils.equals(targetModel.getStatus(), MaterialModelStatusEnum.IN_TRAINING.getCode())) {
            //这里做一个防御性编码，防止上游传入的extInfo是空的
            if (MapUtils.isEmpty(targetModel.getExtInfo())) {
                targetModel.setExtInfo(currentModel.getExtInfo());
            }
            targetModel.addExtInfo(KEY_CLOTH_TYPE_CONFIGS, JSONArray.toJSON(targetModel.getClothTypeConfigs()));

            if (currentModel.getMainType() == MainTypeEnum.MAIN) {
                MaterialModelConverter.syncSubModelClothTypeConfig(subList, targetModel);
            }
        }

        //基于主模型状态，变更子模型状态
        if (currentModel.getMainType() == MainTypeEnum.MAIN && CollectionUtils.isNotEmpty(subList)) {
            if (StringUtils.equals(targetModel.getStatus(), MaterialModelStatusEnum.DISABLED.getCode())
                || StringUtils.equals(targetModel.getStatus(), MaterialModelStatusEnum.ENABLED.getCode())) {
                subList.forEach(sub -> sub.setStatus(targetModel.getStatus()));
            }

            subList.forEach(this::updateByIdSelective);
        }

        //记录交付时间
        if (StringUtils.equals(targetModel.getStatus(), MaterialModelStatusEnum.ENABLED.getCode())
            && StringUtils.isBlank(targetModel.getExtInfo(KEY_DELIVERY_TIME, String.class))) {
            targetModel.addExtInfo(KEY_DELIVERY_TIME, DateUtils.formatTime(new Date()));
        }

        //退muse点
        if (StringUtils.equals(targetModel.getExtInfo(CommonConstants.needRefundMusePoint, String.class), YES)
            && !StringUtils.equals(currentModel.getExtInfo().getString(CommonConstants.refundMusePoint), YES)) {
            userPointService.revertByTrainModel(currentModel);
            targetModel.addExtInfo(CommonConstants.refundMusePoint, YES);
        }

        //修改时间必须更新
        targetModel.setModifyTime(new Date());
        MaterialModelDO data = MaterialModelConverter.vo2DO(targetModel);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = materialModelDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MaterialModel失败，影响行数:" + n);

        promptModificationUtil.recordMaterialModelChanges(currentModel, targetModel, "MaterialModel");

        //增加示例图创作
        if (CollectionUtils.isNotEmpty(targetModel.getExampleImages())) {
            creativeBatchService.changeExampleImages(targetModel.getId(), currentModel.getUserId(),
                targetModel.getExampleImages());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExtInfoByIdSelective(MaterialModelVO targetModel) {
        AssertUtil.assertNotNull(targetModel, ResultCode.PARAM_INVALID, "targetModel is null");
        AssertUtil.assertTrue(targetModel.getId() != null, ResultCode.PARAM_INVALID, "targetModel.id is null");

        MaterialModelVO currentModel = lockById(targetModel.getId());
        AssertUtil.assertNotNull(currentModel, ResultCode.SYS_ERROR, "targetModel is null");

        if (!OperationContextHolder.getRoleType().isBackRole() && !OperationContextHolder.isDistributorRole()) {
            AssertUtil.assertOperatePermission(currentModel.getUserId(), "无权限修改其他用户创建的素材模型");
        }

        MaterialModelVO updateModel = new MaterialModelVO();
        updateModel.setId(targetModel.getId());
        updateModel.setExtInfo(targetModel.getExtInfo());
        //修改时间必须更新
        updateModel.setModifyTime(new Date());
        MaterialModelDO data = MaterialModelConverter.vo2DO(updateModel);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = materialModelDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MaterialModel失败，影响行数:" + n);
    }

    @Override
    public void innerUpdate(MaterialModelVO materialModel) {
        AssertUtil.assertNotNull(materialModel, ResultCode.PARAM_INVALID, "materialModel is null");
        AssertUtil.assertTrue(materialModel.getId() != null, ResultCode.PARAM_INVALID, "materialModel.id is null");

        //修改时间必须更新
        materialModel.setModifyTime(new Date());
        MaterialModelDO data = MaterialModelConverter.vo2DO(materialModel);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = materialModelDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MaterialModel失败，影响行数:" + n);
    }

    @Override
    public List<MaterialModelVO> queryMaterialModelList(MaterialModelQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        MaterialModelExample example = MaterialModelConverter.query2Example(query);

        List<MaterialModelDO> list = materialModelDAO.selectByExampleWithBLOBs(example);
        return MaterialModelConverter.doList2VOList(list);
    }

    @Override
    public List<MaterialModelVO> queryListWithBlogs(MaterialModelQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        MaterialModelExample example = MaterialModelConverter.query2Example(query);

        List<MaterialModelDO> list = materialModelDAO.selectByExampleWithBLOBs(example);
        List<MaterialModelVO> result = MaterialModelConverter.doList2VOList(list);

        if (CollectionUtils.isNotEmpty(result)) {
            List<UserVO> userList = userService.batchQueryById(result.stream().map(MaterialModelVO::getUserId)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll));

            result.forEach(item -> {
                item.setUserNick(userList.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getNickName).orElse(null));
                item.setUserRole(userList.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getRoleType).orElse(null));
            });
        }

        return result;
    }

    @Override
    public PageInfo<MaterialModelVO> queryMetaInfoByPage(MaterialModelQuery query) {
        PageInfo<MaterialModelVO> page = new PageInfo<>();

        MaterialModelExample example = MaterialModelConverter.query2Example(query);

        long totalCount = materialModelDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<MaterialModelDO> list = materialModelDAO.selectByExample(example);
        List<MaterialModelVO> dataList = MaterialModelConverter.doList2VOList(list);

        page.setList(dataList);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 带条件批量分页查询素材模型列表
     *
     * @param query 查询条件
     *              return 结果
     */
    @Override
    public PageInfo<MaterialModelVO> queryPageWithBlobs4DistributorModels(MaterialModelQuery query) {

        //兼容渠道商管理页功能，全部资产，分页查询，特殊处理：销售分页查看名下客户的资产，或自己上传的资产
        return getMaterialModelVOPageInfoByExample(MaterialModelConverter.convertDistributorModelsQuery2Example(query));
    }

    @Override
    public PageInfo<MaterialModelVO> queryPageWithBlobs(MaterialModelQuery query) {

        if (query.isOnlyVIPOrCustomerLora()) {
            List<UserVO> vipOrPaidCustomerUserList = userService.queryAllVIPOrPaidCustomer();
            if (CollectionUtils.isNotEmpty(vipOrPaidCustomerUserList)) {
                query.setUserIds(vipOrPaidCustomerUserList.stream().map(UserVO::getId).collect(Collectors.toList()));
            }
        }

        //兼容渠道商管理页功能，全部资产，分页查询，特殊处理：销售分页查看名下客户的资产，或自己上传的资产
        MaterialModelExample example = OperationContextHolder.getRoleType() == RoleTypeEnum.DISTRIBUTOR
            ? MaterialModelConverter.convertDistributorModelsQuery2Example(query)
            : MaterialModelConverter.query2Example(query);

        //填充训练中的MAIN的统计数据
        PageInfo<MaterialModelVO> pageInfo = getMaterialModelVOPageInfoByExample(example);
        List<MaterialModelVO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }

        fillSubModelStatus(list);

        return pageInfo;
    }

    @NotNull
    private PageInfo<MaterialModelVO> getMaterialModelVOPageInfoByExample(MaterialModelExample example) {
        example.setCurrentUserId(OperationContextHolder.getMasterUserId());
        PageInfo<MaterialModelVO> page = new PageInfo<>();

        long totalCount = materialModelDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<MaterialModelDO> list = materialModelDAO.selectByExampleWithBLOBs(example);
        List<MaterialModelVO> dataList = MaterialModelConverter.doList2VOList(list);

        if (CollectionUtils.isNotEmpty(dataList)) {
            List<Integer> userIds = dataList.stream().map(MaterialModelVO::getUserId).distinct().collect(
                Collectors.toList());
            Map<Integer, Integer> engineerByUser = userProfileService.queryPromptEngineers(userIds);
            userIds.addAll(engineerByUser.values());
            List<UserVO> userList = userService.batchQueryById(userIds);

            dataList.forEach(item -> {
                item.setUserNick(userList.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getNickName).orElse(null));
                item.setPromptEngineerNick(userList.stream().filter(
                        user -> user.getId().equals(engineerByUser.get(item.getUserId()))).findFirst()
                    .map(UserVO::getNickName).orElse(null));
                item.setUserCorpName(userList.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getCorpName).orElse(null));
                item.setUserRole(userList.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(UserVO::getRoleType).orElse(null));
                item.setVipCustomer(userList.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                    .map(u -> u.getMemo() != null && u.getMemo().toLowerCase().contains("vip")).orElse(false));
            });

            //补充的上传素材原始信息
            fillClothMaterialInfo(dataList);

            //补充所属渠道商公司名
            DistributorCustomerQuery distributorCustomerQuery = new DistributorCustomerQuery();
            distributorCustomerQuery.setCustomerMasterUserIds(userIds);
            List<DistributorCustomerVO> distributorCustomerVOS
                = distributorCustomerService.queryDistributorCustomerList(distributorCustomerQuery);
            if (CollectionUtils.isNotEmpty(distributorCustomerVOS)) {
                dataList.forEach(model -> {
                    model.setRelatedDistributorCorpName(distributorCustomerVOS.stream().filter(
                            it -> it.getCustomerMasterUserId().equals(model.getUserId())).findFirst()
                        .map(DistributorCustomerVO::getDistributorCorpName).orElse(null));
                });
            }

            //是否付费客户
            OrderInfoQuery orderInfoQuery = new OrderInfoQuery();
            orderInfoQuery.setMasterUserIds(userIds);

            List<OrderInfoVO> orders = orderInfoService.queryOrderInfoList(orderInfoQuery);
            if (CollectionUtils.isNotEmpty(orders)) {
                List<Integer> paid = orders.stream().map(OrderInfoVO::getMasterUserId).distinct().collect(
                    Collectors.toList());
                dataList.forEach(model -> {
                    model.setPaidCustomer(paid.contains(model.getUserId()));
                });
            }

            //初始化背面属性
            initBackViewProps(dataList);
        }

        page.setList(dataList);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public Long queryMaterialModelCount(MaterialModelQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        MaterialModelExample example = MaterialModelConverter.query2Example(query);
        return materialModelDAO.countByExample(example);
    }

    /**
     * 带条件分页查询素材模型
     */
    @Override
    public PageInfo<MaterialModelVO> queryMaterialModelByPage(MaterialModelQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<MaterialModelVO> page = new PageInfo<>();

        MaterialModelExample example = MaterialModelConverter.query2Example(query);
        long totalCount = materialModelDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<MaterialModelDO> list = materialModelDAO.selectByExampleWithBLOBs(example);
        List<MaterialModelVO> resultList = MaterialModelConverter.doList2VOList(list);

        if (CollectionUtils.isNotEmpty(resultList)) {
            List<Integer> modelIds = resultList.stream().map(MaterialModelVO::getId).collect(Collectors.toList());
            List<ModelPointVO> pointModels = modelPointService.batchQueryByModels(modelIds,
                OperationContextHolder.getMasterUserId());

            if (CollectionUtils.isNotEmpty(pointModels)) {
                resultList.forEach(item -> {
                    pointModels.stream().filter(point -> point.getModelId().equals(item.getId())).findFirst().ifPresent(
                        point -> item.setModelPoint(point.getPoint()));
                });
            }

            resultList.forEach(item -> item.setClothTypeConfigs(MaterialModelConverter.convert2ClothTypeConfig(item)));

            //初始化背面属性
            initBackViewProps(resultList);

            fillClothMaterialInfo(resultList);

            //补充关联的创作元素信息
            fillRelatedElements(resultList);
        }

        page.setList(resultList);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    private void fillRelatedElements(List<MaterialModelVO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<Integer> elementIds = resultList.stream().filter(
            item -> item.getExtInfo() != null && item.getExtInfo().containsKey(CREATIVE_ELEMENT_ID)
                    && item.getExtInfo().get(CREATIVE_ELEMENT_ID) != null).map(MaterialModelVO::getId).collect(
            Collectors.toList());

        if (CollectionUtils.isEmpty(elementIds)) {
            return;
        }
        List<CreativeElementVO> elements = creativeElementService.batchQueryByIds(elementIds);
        if (CollectionUtils.isEmpty(elements)) {
            return;
        }
        for (MaterialModelVO item : resultList) {
            CreativeElementVO element = elements.stream().filter(
                e -> e.getLoraModelId() != null && e.getLoraModelId().equals(item.getId())).findFirst().orElse(null);

            if (element == null) {
                continue;
            }

            item.setRelatedElementVO(element);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void pollingLoraStatus(Integer modelId) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");

        MaterialModelVO model = this.lockById(modelId);
        if (model == null) {
            log.info("当前模型锁定失败，可能已经被删除或正在被其它事务锁定，忽略modelId={}", modelId);
            return;
        }
        if (!StringUtils.equals(model.getStatus(), MaterialModelStatusEnum.IN_TRAINING.getCode())) {
            log.info("当前模型状态{}不是训练中，忽略", model.getStatus());
            return;
        }
        LoraTrainDetail detail = model.getClothLoraTrainDetail();
        if (detail == null) {
            log.info("训练模型详情为空，忽略（历史脏数据）, MaterialModelVO.id={}", model.getId());
            return;
        }

        log.info("model id={}未完成训练开始推进", model.getId());
        log.info("model trainDetail:{}, extInfo:{}", CommonUtil.toJSONString(model.getClothLoraTrainDetail()),
            CommonUtil.toJSONString(model.getExtInfo()));

        //判断是否需要上传原始素材
        boolean checkUploadOriginalMaterial = uploadOriginalMaterialIfNecessary(model);
        if (!checkUploadOriginalMaterial) {
            log.warn("尝试上传失败，稍后再试 model id={}", model.getId());
            return;
        }

        boolean loraSuccess = false;

        //脸：抠图，打标和训练
        if (MaterialType.face.name().equals(detail.getMaterialType())) {
            loraSuccess = handleCutoutLabelAndTrain(detail, model, null);

            //场景
        } else if (MaterialType.scene.name().equals(detail.getMaterialType())) {

            ModelProcessChain processChain = new ModelProcessChainBuilder(detail, model).withPrepareViewStage()
                .withCutoutStage().withLabelAndLoraStage().build();

            loraSuccess = processChain.process();
        }

        //cloth
        else {
            //预处理
            ComfyuiTaskVO prepareViewTask = handleTask(detail, detail.getPrepareView(), model,
                ComfyuiTaskTypeEnum.prepareView, LoraTrainDetail::setPrepareView, null);
            if (prepareViewTask != null && prepareViewTask.getTaskStatus() == QueueResult.QueueCodeEnum.COMPLETED) {
                loraSuccess = handleCutoutLabelAndTrain(detail, model, prepareViewTask);
            }
        }

        model.setClothLoraTrainDetail(detail);

        //lora训练成功
        if (loraSuccess) {
            if (StringUtils.equals(detail.getMaterialType(), MaterialType.cloth.name())
                && model.getMainType() != MainTypeEnum.SUB) {
                try {
                    //开始生成测试图(10张3：4)
                    startCreateTestImages(detail, model, false);
                } catch (Throwable t) {
                    log.warn("lora训练成功且同步成功，但后处理失败，忽略", t);
                }
            } else if (StringUtils.equals(detail.getMaterialType(), MaterialType.face.name()) || StringUtils.equals(
                detail.getMaterialType(), MaterialType.scene.name())) {

                Integer elementId = model.getExtInfo(CommonConstants.CREATIVE_ELEMENT_ID, Integer.class);
                if (elementId != null) {
                    CreativeElementVO elementVO = initRelatedElementConfig(elementId, model);
                    if (elementVO != null) {
                        model.setShowImage(elementVO.getShowImage());
                    }
                }
            }
        }

        this.updateByIdSelective(model);

        log.info("model id={}训练本次推进结束", model.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pollingMainStatus(Integer modelId) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");
        MaterialModelVO model = lockById(modelId);

        if (model == null) {
            log.info("当前模型锁定失败，可能已经被删除或正在被其它事务锁定，忽略modelId={}", modelId);
            return;
        }

        if (!StringUtils.equals(model.getStatus(), MaterialModelStatusEnum.IN_TRAINING.getCode())) {
            log.info("当前模型状态{}不是训练中，忽略", model.getStatus());
            return;
        }

        LoraTrainDetail detail = model.getClothLoraTrainDetail();

        List<MaterialModelVO> subList = querySubModel(modelId);

        AssertUtil.assertNotEmpty(subList, ResultCode.BIZ_FAIL, "模型主模型关联的子模型数量为0");

        //上一步是否完成
        final boolean[] isPreCompleted = {true};
        //prepareView 状态同步
        fillSubTask(isPreCompleted, false, new SubTaskCallback() {
            @Override
            public List<LoraSubTask> getSubTasks() {
                return subList.stream().map(e -> e.getClothLoraTrainDetail().getPrepareView()).collect(
                    Collectors.toList());
            }

            @Override
            public LoraSubTask getMainTask() {
                return detail.getPrepareView();
            }

            @Override
            public void setSubTasks(LoraSubTask subTask) {
                detail.setPrepareView(subTask);
            }
        });

        //cutout 状态同步
        fillSubTask(isPreCompleted, true, new SubTaskCallback() {
            @Override
            public List<LoraSubTask> getSubTasks() {
                return subList.stream().map(e -> e.getClothLoraTrainDetail().getCutout()).collect(Collectors.toList());
            }

            @Override
            public LoraSubTask getMainTask() {
                return detail.getCutout();
            }

            @Override
            public void setSubTasks(LoraSubTask subTask) {
                detail.setCutout(subTask);
            }
        });

        //label 状态同步
        fillSubTask(isPreCompleted, true, new SubTaskCallback() {
            @Override
            public List<LoraSubTask> getSubTasks() {
                return subList.stream().map(e -> e.getClothLoraTrainDetail().getLabel()).collect(Collectors.toList());
            }

            @Override
            public LoraSubTask getMainTask() {
                return detail.getLabel();
            }

            @Override
            public void setSubTasks(LoraSubTask subTask) {
                detail.setLabel(subTask);
            }
        });

        //lora 状态同步
        fillSubTask(isPreCompleted, true, new SubTaskCallback() {
            @Override
            public List<LoraSubTask> getSubTasks() {
                return subList.stream().map(e -> e.getClothLoraTrainDetail().getLora()).collect(Collectors.toList());
            }

            @Override
            public LoraSubTask getMainTask() {
                return detail.getLora();
            }

            @Override
            public void setSubTasks(LoraSubTask subTask) {
                detail.setLora(subTask);
            }
        });

        if (subList.stream().anyMatch(
            e -> e.getClothLoraTrainDetail().getCutoutAgain() != null && e.getClothLoraTrainDetail()
                .getCutoutAgain())) {
            detail.setCutoutAgain(true);
        }

        if (subList.stream().allMatch(e -> StringUtils.equals(YES, e.getClothLoraTrainDetail().getLoraConfirmed()))) {
            detail.setLoraConfirmed(YES);
        }

        // 所有状态都变更未testing之后，才变更状态
        if (subList.stream().allMatch(
            e -> Objects.requireNonNull(MaterialModelStatusEnum.getByCode(e.getStatus())).isTrainCompleted())) {
            model.setStatus(MaterialModelStatusEnum.TESTING.getCode());

            List<ClothTypeConfig> clothTypeConfigs = MaterialModelConverter.convert2ClothTypeConfig(subList);
            model.setClothTypeConfigs(clothTypeConfigs);

            List<String> colorImageList = MaterialModelConverter.buildColorImageList(subList);
            model.addExtInfo(KEY_CLOTH_COLOR_IMAGES, JSONArray.toJSONString(colorImageList));

            model.addExtInfo(KEY_SUB_LORA_NAMES,
                JSONArray.toJSON(subList.stream().map(MaterialModelVO::getLoraName).collect(Collectors.toList())));
            detail.setLoraType(ModelVersionEnum.FLUX.getLoraType());

            // 主模型生成图片
            startCreateTestImages(detail, model, false);

            autoDelivery(model, model.getExtInfo());
        }

        this.updateByIdSelective(model);

        log.info("model id={}训练本次推进结束", model.getId());
    }

    @Override
    public void updateExtInfo(MaterialModelReq req) {
        // 参数校验
        AssertUtil.assertNotNull(req, ResultCode.PARAM_INVALID, "req is null");
        AssertUtil.assertNotNull(req.getId(), ResultCode.PARAM_INVALID, "id is null");

        // 参数提取
        Integer id = req.getId();
        Integer isManualReplacement = req.getIsManualReplacement();
        Boolean isLoraSystemReload = req.getIsLoraSystemReload();

        // 若 isManualReplacement为空或者0 并且 isLoraSystemReload 为 false 则不再进行更新
        if ((isManualReplacement == null || isManualReplacement == 0) && !isLoraSystemReload) {
            return;
        }

        // 获取模型信息
        MaterialModelVO materialModelVO = selectById(id);
        if (materialModelVO == null) {
            return;
        }

        // 获取模型扩展信息
        JSONObject extInfo = Optional.ofNullable(materialModelVO.getExtInfo()).orElseGet(JSONObject::new);
        materialModelVO.setExtInfo(extInfo);

        // 获取模型扩展信息中手动替换次数和系统重载次数
        Integer manualReplacementCount = parseIntegerOrDefault(extInfo.get(CommonConstants.IS_MANUAL_REPLACEMENT), 0);
        Integer loraSystemReloadCount = parseIntegerOrDefault(extInfo.get(CommonConstants.IS_LORA_SYSTEM_RELOAD), 0);

        // 更新模型扩展信息
        if (isManualReplacement != null && isManualReplacement > 0) {
            extInfo.put(CommonConstants.IS_MANUAL_REPLACEMENT, manualReplacementCount + isManualReplacement);
        }
        // 更新模型扩展信息
        if (isLoraSystemReload) {
            loraSystemReloadCount++;
            extInfo.put(CommonConstants.IS_LORA_SYSTEM_RELOAD, loraSystemReloadCount);
        }

        // 更新扩展参数信息
        materialModelVO.setExtInfo(extInfo);

        // 更新模型信息
        innerUpdate(materialModelVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncToImageCase(SyncImageCaseReq req) {
        // 参数校验
        AssertUtil.assertNotNull(req, ResultCode.PARAM_INVALID, "req is null");
        AssertUtil.assertNotBlank(req.getUrl(), ResultCode.PARAM_INVALID, "url is empty");
        AssertUtil.assertNotBlank(req.getFilePath(), ResultCode.PARAM_INVALID, "filePath is empty");

        log.info("[服饰图片同步]MaterialModelServiceImpl::syncToImageCase::开始同步图片案例, req: {}",
            JSONObject.toJSONString(req));

        try {
            // 构建ImageCaseVO
            ImageCaseVO imageCaseVO = buildImageCaseVO(req);

            // 执行插入逻辑
            ImageCaseVO result = imageCaseService.insert(imageCaseVO);

            // 查询标签信息并批量插入
            insertImageCaseTags(result.getId(), req.getTrainingMaterial());

            log.info("[服饰图片同步]MaterialModelServiceImpl::syncToImageCase::同步图片案例成功, imageCase id: {}",
                result.getId());
        } catch (Exception e) {
            log.error("[服饰图片同步]MaterialModelServiceImpl::syncToImageCase::同步图片案例失败, req: {}, error: {}",
                JSONObject.toJSONString(req), e.getMessage());
        }
    }

    @Override
    public CreativeElementVO initRelatedElementConfig(Integer modelId) {
        MaterialModelVO model = selectById(modelId);
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "模型不存在");

        Integer elementId = model.getExtInfo(CommonConstants.CREATIVE_ELEMENT_ID, Integer.class);

        return initRelatedElementConfig(elementId, model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncTestImageStatus(MaterialModelVO model) {
        Integer testImageCnt = model.getExtInfo(KEY_TEST_IMAGE_CNT, Integer.class);
        AssertUtil.assertNotNull(testImageCnt, ResultCode.BIZ_FAIL, "testImageCnt is null");
        int cnt = creativeBatchService.queryFinishedSysGenImageCntByModelId(model.getId());
        if (cnt < testImageCnt) {
            log.info("【测试图片生成状态同步】自动出图未完成，跳过，id={}, currentGen: {}, totalCnt: {}", model.getId(),
                cnt, testImageCnt);
            return;
        }

        MaterialModelVO target = lockById(model.getId());

        log.info("【测试图片生成状态同步】自动出图已完成，变更状态，id={}, currentGen: {}, totalCnt: {}", model.getId(),
            cnt, testImageCnt);

        target.addExtInfo(KEY_TEST_IMAGE_FINISHED, YES);
        innerUpdate(target);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliver(MaterialModelVO model) {
        MaterialModelVO current = this.lockById(model.getId());
        AssertUtil.assertNotNull(current, "模型不存在");

        if (MaterialModelStatusEnum.ENABLED.getCode().equals(current.getStatus())) {
            return;
        }
        log.info("模型被启用modelId={},operator={}", current.getId(), OperationContextHolder.getOperatorUserId());
        current.addExtInfo(KEY_DELIVERY_OPERATOR, OperationContextHolder.getOperatorUserId());
        if (StringUtils.isBlank(current.getExtInfo(KEY_DELIVERY_TIME, String.class))) {
            current.addExtInfo(KEY_DELIVERY_TIME, DateUtils.formatTime(new Date()));
        }
        MaterialModelVO updateMaterialModel = new MaterialModelVO();
        updateMaterialModel.setId(current.getId());
        updateMaterialModel.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
        updateMaterialModel.setExtInfo(current.getExtInfo());
        updateMaterialModel.setModifyTime(new Date());
        int n = materialModelDAO.updateByPrimaryKeySelective(MaterialModelConverter.vo2DO(updateMaterialModel));
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MaterialModel失败，影响行数:" + n);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignTo(Integer modelId, Integer userId) {
        MaterialModelVO model = this.lockById(modelId);
        AssertUtil.assertNotNull(model, "模型不存在model id=" + modelId);

        model.setUserId(userId);
        model.setOperatorId(userId);

        updateByIdSelective(model);

        ModelPointVO modelPointVO = modelPointService.selectByModelId(modelId, userId);
        if (modelPointVO == null) {
            modelPointService.init(modelId, userId);
        }

        //精选图也跟着转移
        creativeBatchService.assignExampleImages(modelId, userId);
    }

    /**
     * 将模特lora或场景lora模型，指定给某个商家
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignElementModelToUser(Integer modelId, Integer userId, boolean exclusive, boolean free) {

        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
        AssertUtil.assertNotNull(exclusive, ResultCode.PARAM_INVALID, "exclusive is null");

        MaterialModelVO model = this.lockById(modelId);
        AssertUtil.assertNotNull(model, "模型不存在model id=" + modelId);
        AssertUtil.assertTrue(
            model.getMaterialType() == MaterialType.face || model.getMaterialType() == MaterialType.scene,
            "模型类型错误，这不是模特lora或场景lora");

        AssertUtil.assertTrue(model.getExtInfo() != null && model.getExtInfo().containsKey(CREATIVE_ELEMENT_ID)
                              && model.getExtInfo().get(CREATIVE_ELEMENT_ID) != null,
            "模型数据错误，模型没有关联的creativeElementId");

        Integer relatedElementId = Integer.parseInt(model.getExtInfo().get(CREATIVE_ELEMENT_ID).toString());
        AssertUtil.assertNotNull(relatedElementId, ResultCode.PARAM_INVALID, "relatedElementId is null");

        CreativeElementVO elementVO = creativeElementService.selectById(relatedElementId);
        AssertUtil.assertNotNull(elementVO, ResultCode.PARAM_INVALID, "element is empty");

        //更新model
        model.setUserId(userId);
        model.setOperatorId(userId);
        updateByIdSelective(model);

        //更新material info
        Integer materialId = model.getClothLoraTrainDetail().getOriginalMaterialId();
        AssertUtil.assertNotNull(materialId, ResultCode.PARAM_INVALID, "materialId is null");
        MaterialInfoVO materialInfoVO = materialInfoService.selectById(materialId);
        AssertUtil.assertNotNull(materialInfoVO, ResultCode.PARAM_INVALID, "materialInfoVO is null");

        materialInfoVO.setUserId(userId);
        materialInfoVO.setOperatorId(userId);
        materialInfoService.updateByIdSelective(materialInfoVO);

        UserVO merchant = userService.selectById(userId);
        AssertUtil.assertNotNull(merchant, ResultCode.PARAM_INVALID, "merchant is null");

        //更新element
        elementVO.setUserId(userId);
        elementVO.setOperatorId(userId);
        elementVO.setBelong(merchant.getRoleType().isBackRole() ? ModelTypeEnum.SYSTEM : ModelTypeEnum.CUSTOM);
        elementVO.addExtInfo(CommonConstants.KEY_OPEN_SCOPE, exclusive ? userId.toString() : CommonConstants.ALL);
        creativeElementService.updateById(elementVO);

        //模特或场景，自动添加到用户收藏
        UserFavorQuery userFavorQuery = new UserFavorQuery();
        userFavorQuery.setType(FavorTypeEnum.ELEMENT.getCode());
        userFavorQuery.setItemId(elementVO.getId());
        userFavorQuery.setUserId(userId);
        userFavorQuery.setPageNum(1);
        userFavorQuery.setPageSize(1);

        PageInfo<UserFavorVO> userFavorVOPageInfo = userFavorService.queryUserFavorByPage(userFavorQuery);
        if (userFavorVOPageInfo != null && CollectionUtils.isNotEmpty(userFavorVOPageInfo.getList())) {
            log.info("当前用户里已经有当前元素的收藏userId={},elementId={}", userId, elementVO.getId());
        } else {
            UserFavorVO f = new UserFavorVO();
            f.setType(FavorTypeEnum.ELEMENT);
            f.setItemId(elementVO.getId());
            f.setModelId(modelId);
            f.setUserId(userId);
            f.setOperatorId(userId);
            userFavorService.insert(f);
        }

        //扣除模特训练的muse点（只针对目标账号是商家的情况扣费）
        if (merchant.getRoleType() == RoleTypeEnum.MERCHANT && !free) {
            userPointService.consumeByTrainModel(userId, model, exclusive);
        }
    }

    public void startCreateTestImages(LoraTrainDetail detail, MaterialModelVO target, boolean forceSend) {
        Boolean isSendTestEvent = target.getExtInfo(KEY_IS_SEND_TEST_EVENT, Boolean.class);
        if (!forceSend && isSendTestEvent != null && isSendTestEvent) {
            log.info("【测试图生成事件】已发出，不再重复发送");
            return;
        }

        String clothStyleType = target.getExtInfo(CommonConstants.clothStyleType, String.class);

        //如果服装是男女同款时，则需要从选择的模特来判断性别
        if (StringUtils.equals(clothStyleType, unisex)) {
            clothStyleType = female;
        }
        try {
            MerchantPreferenceVO preference = null;
            if ((CollectionUtils.isNotEmpty(detail.getTestFaces()) && CollectionUtils.isNotEmpty(
                detail.getTestScenes()))) {
                List<String> imgProportions = CollectionUtils.isNotEmpty(detail.getTestImgProportions())
                    ? detail.getTestImgProportions() : Collections.singletonList(
                    ProportionTypeEnum.THREE_FOUR.getCode());

                int testNum = detail.getTestNum() != null ? detail.getTestNum() : 5;
                int imgNumPerGroup = detail.getImgNumPerGroup() != null ? detail.getImgNumPerGroup() : 20;

                log.info("模型训练后，发送测试出图延迟事件,id={},testNum={},imgNumPerGroup={}", target.getId(), testNum,
                    imgNumPerGroup);

                sendEvent(detail.getTestFaces(), detail.getTestScenes(), testNum, imgNumPerGroup, imgProportions,
                    detail.getTestClothCollocation(), detail.getTestImgMode(), target, false, true, null, null);
            } else if ((preference = merchantPreferenceService.queryEnableCreativePreference(target.getUserId(),
                clothStyleType)) != null) {

                int testNum = preference.getImageNum() > 60 ? 5 : (preference.getImageNum() > 30 ? 2 : 1);
                int imgNumPerGroup = preference.getImageNum() / testNum;

                log.info("模型训练后，发送自动出图延迟事件,id={},testNum={},imgNumPerGroup={},userId={}", target.getId(),
                    testNum, imgNumPerGroup, target.getUserId());

                Boolean withoutDeduction = preference.getExtInfo(KEY_WITHOUT_DEDUCTION, Boolean.class);

                sendEvent(preference.getFaces(), preference.getScenes(), testNum, imgNumPerGroup,
                    Collections.singletonList(preference.getImageProportion()), null, TEST_IMG_MODE_RANDOM, target,
                    true, withoutDeduction != null && withoutDeduction, null, null);
            }

            Integer autoGenUser = CommonUtil.mockAutoCreativeContext().getCurrentUserId();
            if ((preference = merchantPreferenceService.queryEnableCreativePreference(autoGenUser, clothStyleType))
                != null) {

                List<CreateTestRequest> createTestRequestPairs = buildTestRequestPairs(preference.getFaces(),
                    preference.getScenes(), preference.getImageProportion(), target, detail);

                //分组数量由出图角度对的数量决定
                List<List<String>> cameraAnglePairs = MaterialModelUtils.buildTestCameraAngles(
                    StringUtils.equals(lowerGarment, detail.getClothType()));
                int testNum = cameraAnglePairs.size();
                int imgNumPerGroup = preference.getImageNum() / testNum;

                target.addExtInfo(KEY_TEST_IMAGE_CNT, preference.getImageNum());

                log.info("模型训练后，发送自动出图延迟事件，用于运营审图,id={},testNum={},imgNumPerGroup={},userId={}",
                    target.getId(), testNum, imgNumPerGroup, target.getUserId());

                sendEvent(preference.getFaces(), preference.getScenes(), testNum, imgNumPerGroup,
                    Collections.singletonList(preference.getImageProportion()), preference.getClothCollocation(),
                    TEST_IMG_MODE_CARTESIAN_PRO, target, false, true, cameraAnglePairs, createTestRequestPairs);
            }
        } catch (Exception e) {
            log.error("startCreateTestImages error,ignore", e);
        }
    }

    @Override
    public void changeExampleImages(MaterialModelVO materialModel) {
        MaterialModelVO data = this.selectById(materialModel.getId());
        creativeBatchService.changeExampleImages(data.getId(), data.getUserId(), materialModel.getExampleImages());
    }

    @Override
    public void clearExampleImages(Integer modelId) {
        MaterialModelVO data = this.selectById(modelId);
        creativeBatchService.clearExampleImages(data.getId(), data.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchModifyGarment(List<Integer> idList) {
        //TODO by半泉:临时功能待删除

        for (Integer id : idList) {
            MaterialModelVO model = this.lockById(id);
            AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

            String garmentType = model.getExtInfo(KEY_GARMENT_TYPE, String.class);
            if (StringUtils.isNotBlank(garmentType)) {
                log.info("批量订正服装类型，id={}已存在 garmentType={},跳过", id, garmentType);
                continue;
            }

            List<ClothTypeConfig> clothTypeConfigs = MaterialModelConverter.convert2ClothTypeConfig(model);
            garmentType = getGarmentType(clothTypeConfigs, model);
            if (StringUtils.isNotBlank(garmentType)) {
                model.addExtInfo(KEY_GARMENT_TYPE, garmentType);
            }

            MaterialModelDO data = MaterialModelConverter.vo2DO(model);
            //逻辑删除标过滤
            data.setDeleted(false);
            int n = materialModelDAO.updateByPrimaryKeySelective(data);
            AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MaterialModel失败，影响行数:" + n);

            log.info("批量订正服装类型，完成id={}的服装类型设置，garmentType={}", id, garmentType);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateColorImage(Integer id, Integer index, String imgUrl) {
        MaterialModelVO model = selectById(id);
        AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

        List<String> colorList = model.getExtInfo(KEY_CLOTH_COLOR_IMAGES, List.class);
        if (CollectionUtils.isNotEmpty(colorList)) {
            if (colorList.size() >= index) {
                colorList.set(index - 1, imgUrl);
            } else {
                colorList.add(imgUrl);
            }
        } else {
            colorList = new ArrayList<>();
            colorList.add(imgUrl);
        }
        model.addExtInfo(KEY_CLOTH_COLOR_IMAGES, JSONArray.toJSONString(colorList));
        updateByIdSelective(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableColor(Integer id, Integer index, Boolean enable) {
        MaterialModelVO model = selectById(id);
        AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

        List<ClothTypeConfig> configs = MaterialModelConverter.convert2ClothTypeConfig(model);
        for (ClothTypeConfig config : configs) {
            List<ClothColorDetail> colorList = config.getColorList();
            for (ClothColorDetail each : colorList) {
                if (each.getIndex() == index) {
                    each.setEnable(enable);
                }
            }
        }

        model.addExtInfo(KEY_CLOTH_TYPE_CONFIGS, JSONArray.toJSONString(configs));
        updateByIdSelective(model);
    }

    @Override
    public void confirmTrainLora(ConfirmLoraReq req, Integer operatorId, String operatorNick) {
        MaterialModelVO model = selectById(req.getId());

        confirmTrainLora(req, model, model.getClothLoraTrainDetail(), operatorId, operatorNick);
    }

    /**
     * 确认可交付
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmCanDeliver(Integer loraId, Integer operatorId, String operatorNick) {
        AssertUtil.assertNotNull(loraId, "loraId不能为空");

        MaterialModelVO loraModel = lockById(loraId);
        AssertUtil.assertNotNull(loraModel, "模型不存在model id=" + loraId);

        //只有testing状态才允许确认可交付
        AssertUtil.assertTrue(loraModel.getStatus().equals(MaterialModelStatusEnum.TESTING.getCode()),
            "模型状态异常，loraModel id=" + loraId);

        if (loraModel.getClothLoraTrainDetail() != null
            && loraModel.getClothLoraTrainDetail().getConfirmCanDeliverInfo() != null && StringUtils.equals(
            loraModel.getClothLoraTrainDetail().getConfirmCanDeliverInfo().getConfirmCanDeliver(), YES)) {
            log.info("模型已确认可交付忽略重复请求，loraModel id={}", loraId);
        } else {

            //同步打标到自动生成图片任务
            syncConfirmDeliver2AutoGenTask(loraModel);

            loraModel.getClothLoraTrainDetail().setConfirmCanDeliverInfo(
                getConfirmCanDeliveryInfo(operatorId, operatorNick));
            this.updateByIdSelective(loraModel);
        }
    }

    /**
     * 构建测试任务请求对
     *
     * @param faces           模特列表
     * @param scenes          场景列表
     * @param imageProportion 图片尺寸
     * @param target          服装模型
     * @param detail          训练详情
     * @return 请求对
     */
    private List<CreateTestRequest> buildTestRequestPairs(List<Integer> faces, List<Integer> scenes,
                                                          String imageProportion, MaterialModelVO target,
                                                          LoraTrainDetail detail) {

        List<CreateTestRequest> requestPairs = new ArrayList<>();
        //1.5张固定出图
        CreateTestRequest fixedRequest = new CreateTestRequest();
        fixedRequest.setSceneId(scenes.get(0));
        fixedRequest.setFaceId(faces.get(0));
        fixedRequest.setImgProportions(imageProportion);
        fixedRequest.setNum(5);

        requestPairs.add(fixedRequest);

        //2.1.如果用户已设置自动出图配置
        if (detail.getAutoGenImg() != null && detail.getAutoGenImg() && detail.getAutoGenImgParam() != null) {
            AutoGenImgParam param = detail.getAutoGenImgParam();
            String proportion = param.getAutoGenImgProportions().get(0);
            int totalTestImgNum = param.getAutoGenTotalImgNum();
            int batchNum = CollectionUtils.size(param.getAutoGenFaceIds()) * CollectionUtils.size(
                param.getAutoGenSceneIds());
            int avgImgNumOfBatch = totalTestImgNum / batchNum;
            int leftImgNum = totalTestImgNum % batchNum;

            int idx = 0;
            for (Integer face : param.getAutoGenFaceIds()) {
                for (Integer scene : param.getAutoGenSceneIds()) {
                    CreateTestRequest each = new CreateTestRequest();
                    //最后一个批次叠加上剩余张数
                    each.setNum(avgImgNumOfBatch + (++idx == batchNum ? leftImgNum : 0));
                    each.setImgProportions(proportion);
                    each.setFaceId(face);
                    each.setSceneId(scene);
                    requestPairs.add(each);
                }
            }
            log.info("自动出图配置初始化，用户已设置出图配置，id={},requestPairs={}", target.getId(), requestPairs);
        } else {
            //2.2.未设置自动出图配置，则根据服装款式，随机检索出5张图片尺寸
            LoraSubTask label = detail.getLabel();
            ComfyuiTaskVO task = comfyuiTaskService.selectById(label.getTaskId());
            LabelTaskRetDetail retDetail = CommonUtil.parseObject(task.getRetDetail(), LabelTaskRetDetail.class);
            if (retDetail == null) {
                log.info("自动出图配置初始化，未找到打标任务结果，跳过，id={}", target.getId());
                return requestPairs;
            }

            QueryAutoGenElementRecommendRequest req = new QueryAutoGenElementRecommendRequest();
            req.setClothType(target.getClothType());
            req.setClothStyleType(detail.getClothStyleType());
            req.setClothImgUrls(Collections.singletonList(retDetail.getFullbodyFrontViewImgUrl()));
            String ageRange = target.getExtInfo(KEY_AGE_RANGE, String.class);
            req.setAgeRange(AgeRangeEnum.getByCodeWithDefault(ageRange, AgeRangeEnum.ADULT).getCode());
            req.setExcludeElementIds(Collections.singletonList(22606));
            List<CreativeElementVO> elements = creativeElementService.queryRecommendAutoGenElementsByCloth(req);
            if (CollectionUtils.isEmpty(elements)) {
                log.info("自动出图配置初始化，推荐的场景为空，跳过,id={},req={}", target.getId(), req);
                return requestPairs;
            }

            int totalImgNum = 20;
            elements = elements.subList(0, Math.min(4, elements.size() - 1));
            for (CreativeElementVO element : elements) {
                CreateTestRequest each = new CreateTestRequest();
                //最后一个批次叠加上剩余张数
                each.setNum(totalImgNum / elements.size());
                each.setImgProportions(imageProportion);
                each.setFaceId(faces.get(0));
                each.setSceneId(element.getId());
                requestPairs.add(each);
            }
            log.info("自动出图配置初始化，用户未设置出图配置，id={},requestPairs={}", target.getId(), requestPairs);
        }

        return requestPairs;
    }

    private void syncConfirmDeliver2AutoGenTask(MaterialModelVO loraModel) {
        if (loraModel.getClothLoraTrainDetail().getAutoGenImgTaskId() != null) {
            CommonTaskVO commonTaskVO = commonTaskService.lockById(
                loraModel.getClothLoraTrainDetail().getAutoGenImgTaskId());
            if (commonTaskVO != null) {
                JSONObject ext = commonTaskVO.getExtInfo();
                if (ext == null) {
                    ext = new JSONObject();
                }

                ext.put(CommonConstants.KEY_CONFIRM_CAN_DELIVER, YES);
                commonTaskVO.setExtInfo(ext);

                commonTaskService.updateByIdSelective(commonTaskVO);
            }
        }
    }

    @NotNull
    private static ConfirmCanDeliveryInfo getConfirmCanDeliveryInfo(Integer operatorId, String operatorNick) {
        ConfirmCanDeliveryInfo info = new ConfirmCanDeliveryInfo();
        info.setConfirmCanDeliver(YES);
        info.setConfirmCanDeliverTime(DateUtils.formatTime(new Date()));
        info.setConfirmCanDeliverOperatorId(
            (Integer)ObjectUtils.defaultIfNull(operatorId, OperationContextHolder.getOperatorUserId()));
        info.setConfirmCanDeliverOperatorNick(
            (String)ObjectUtils.defaultIfNull(operatorNick, OperationContextHolder.getOperatorNick()));
        return info;
    }

    @Override
    public void supplyColor(List<Integer> ids) {

        for (Integer id : ids) {
            MaterialModelVO model = this.lockById(id);
            AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

            LoraSubTask label = model.getClothLoraTrainDetail().getLabel();

            List<LabelExtTagsDetail> extTagsDetails = comfyuiTaskService.loadExtTags(label.getTaskId());

            List<ClothTypeConfig> origin = MaterialModelConverter.convert2ClothTypeConfig(model);

            List<ClothTypeConfig> target = MaterialModelConverter.convert2ClothTypeConfig(extTagsDetails,
                model.getVersion().getCode(), model.getTags(), model.getClothLoraTrainDetail().getActivateKey());

            List<ClothTypeConfig> mergeList = MaterialModelConverter.mergeColor(origin, target);

            model.addExtInfo(KEY_CLOTH_TYPE_CONFIGS, JSONArray.toJSONString(mergeList));
            updateByIdSelective(model);

            log.info("批量补充服装颜色，完成id={}的设置", id);
        }
    }

    @Override
    public void batchUploadOss(List<Integer> ids) {
        for (Integer id : ids) {
            MaterialModelVO model = this.lockById(id);
            AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

            LoraTrainDetail detail = model.getClothLoraTrainDetail();
            String loraFilePath = detail.getLoraModelRetFilePath();
            AssertUtil.assertNotBlank(loraFilePath, "loraFilePath为空");

            if (detail.getLora() == null) {
                log.error("【Lora上传oss事件】失败，lora的任务为空,id={}", id);
                continue;
            }

            String fileServerUrl = serverHelper.getFileServerUrl(detail.getLora().getServerUrl());

            if (!EnvUtil.isLocalEnv()) {
                // 1.发送上传lora文件到oss的事件
                LoraUploadEvent event = new LoraUploadEvent(loraFilePath, fileServerUrl, model.getId());
                rocketMQTemplate.syncSendOrderly(EventConstants.TOPIC_UPLOAD_LORA_TO_OSS,
                    MessageBuilder.withPayload(event).build(), "lora-upload-oss", 3000, 0);
            }

            log.info("【Lora上传oss事件】手动批量上传到oss,loraFileUrl={},filePath={}", detail.getLoraRetFileUrl(),
                loraFilePath);
        }
    }

    @Override
    public void batchCreateTestImages(List<Integer> ids) {
        for (Integer id : ids) {
            MaterialModelVO model = this.lockById(id);
            AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

            LoraTrainDetail detail = model.getClothLoraTrainDetail();

            startCreateTestImages(detail, model, true);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retrainLora(Integer modelId, boolean autoTrain, String labelType, String cut4ScaleUp) {
        MaterialModelVO data = lockById(modelId);
        AssertUtil.assertNotNull(data, "模型不存在model id=" + modelId);

        reTrainModel(data, autoTrain, labelType, cut4ScaleUp);

        //如果是主模型，则同步子模型
        if (data.getMainType() == MainTypeEnum.MAIN) {
            querySubModel(modelId).forEach(item -> reTrainModel(item, autoTrain, labelType, cut4ScaleUp));
        }

    }

    @Override
    public ModelTrainDetailVO getTrainDetail(Integer id) {
        MaterialModelVO model = selectById(id);
        AssertUtil.assertNotNull(model, "模型不存在model id=" + id);

        ModelTrainDetailVO ret = new ModelTrainDetailVO();
        BeanUtils.copyProperties(model, ret);

        //补充操作员和商家名称
        if (StringUtils.isBlank(ret.getOperatorNick())) {
            List<UserVO> users = userService.batchQueryById(Lists.newArrayList(ret.getOperatorId(), ret.getUserId()));
            ret.setOperatorNick(users.stream().filter(user -> user.getId().equals(ret.getOperatorId())).findFirst()
                .map(UserVO::getNickName).orElse(null));
            ret.setUserNick(users.stream().filter(user -> user.getId().equals(ret.getUserId())).findFirst()
                .map(UserVO::getNickName).orElse(null));
        }

        //原始上传素材
        if (ret.getClothLoraTrainDetail() != null && ret.getClothLoraTrainDetail().getOriginalMaterialId() != null) {
            MaterialInfoVO materialInfoVO = materialInfoService.selectById(
                ret.getClothLoraTrainDetail().getOriginalMaterialId());
            if (materialInfoVO != null && materialInfoVO.getMaterialDetail() != null) {
                ret.setMaterialDetail(materialInfoVO.getMaterialDetail());
            }
        }

        LoraTrainDetail clothLoraTrainDetail = model.getClothLoraTrainDetail();
        if (clothLoraTrainDetail != null) {
            BeanUtils.copyProperties(clothLoraTrainDetail, ret);

            if (clothLoraTrainDetail.getPrepareView() != null && QueueResult.QueueCodeEnum.COMPLETED.equals(
                clothLoraTrainDetail.getPrepareView().getStatus())) {
                Integer taskId = clothLoraTrainDetail.getPrepareView().getTaskId();
                ComfyuiTaskVO t = comfyuiTaskService.selectById(taskId);
                JSONObject retDetail = CommonUtil.parseObject(t.getRetDetail(), JSONObject.class);
                String finishTime = DateUtils.formatTime(t.getModifyTime());
                if (retDetail != null) {
                    finishTime = retDetail.getString(CommonConstants.KEY_FINISH_TIME);
                }
                ret.setPrepareViewFinishTime(finishTime);
            }

            //抠图成功
            if (clothLoraTrainDetail.getCutout() != null && QueueResult.QueueCodeEnum.COMPLETED.equals(
                clothLoraTrainDetail.getCutout().getStatus())) {
                Integer taskId = clothLoraTrainDetail.getCutout().getTaskId();
                ComfyuiTaskVO t = comfyuiTaskService.selectById(taskId);
                CutoutTaskRetDetail retDetail = CommonUtil.parseObject(t.getRetDetail(), CutoutTaskRetDetail.class);
                if (retDetail != null) {
                    ret.setCutoutFinishTime(retDetail.getFinishTime());
                    String cutoutRetDir = retDetail.getCutoutRetDir();
                    String fileServerUrl = serverHelper.getFileServerUrlByTask(t);
                    fileServerUrl = fetchEnableFileServer(fileServerUrl, model.getId(), model.getUserId());

                    if (StringUtils.isBlank(fileServerUrl)) {
                        if (retDetail.getCutoutFiles() != null) {
                            ret.setCutoutFiles(ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(
                                retDetail.getCutoutFiles()));
                        }
                    } else {
                        String md5 = comfyUIService.calcDirMd5(cutoutRetDir, fileServerUrl);
                        if (md5 != null && !StringUtils.equals(md5, retDetail.getCutoutDirMd5())) {
                            log.info("抠图目录有变更，需要拉取新的视图");
                            List<FileVO> cutoutFiles = comfyUIService.viewFiles(cutoutRetDir,
                                new String[] {"img", "text"}, fileServerUrl);
                            if (cutoutFiles == null && retDetail.getCutoutFiles() != null) {
                                ret.setCutoutFiles(ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(
                                    retDetail.getCutoutFiles()));
                                log.warn("view files接口出现异常，返回为null，此处以retDetail为准");
                            } else {
                                if (cutoutFiles != null) {
                                    ret.setCutoutFiles(
                                        ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(cutoutFiles));
                                }

                                //更新抠图任务结果详情中的图片和md5
                                ComfyuiTaskVO target = new ComfyuiTaskVO();
                                target.setId(t.getId());
                                retDetail.setCutoutDirMd5(md5);
                                retDetail.setCutoutFiles(cutoutFiles);
                                target.setRetDetail(JSONObject.toJSONString(retDetail));

                                comfyuiTaskService.updateByIdSelective(target);
                            }
                        } else if (retDetail.getCutoutFiles() != null) {
                            ret.setCutoutFiles(ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(
                                retDetail.getCutoutFiles()));
                        }
                    }
                }
            }

            //打标成功
            if (clothLoraTrainDetail.getLabel() != null && QueueResult.QueueCodeEnum.COMPLETED.equals(
                clothLoraTrainDetail.getLabel().getStatus())) {
                Integer taskId = clothLoraTrainDetail.getLabel().getTaskId();
                ComfyuiTaskVO t = comfyuiTaskService.selectById(taskId);
                LabelTaskRetDetail retDetail = CommonUtil.parseObject(t.getRetDetail(), LabelTaskRetDetail.class);
                if (retDetail != null) {
                    ret.setLabelFinishTime(retDetail.getFinishTime());
                    String fileServerUrl = serverHelper.getFileServerUrlByTask(t);
                    fileServerUrl = fetchEnableFileServer(fileServerUrl, model.getId(), model.getUserId());
                    String md5 = comfyUIService.calcDirMd5(retDetail.getTagRetDir(), fileServerUrl);

                    if (md5 == null || !StringUtils.equals(md5, retDetail.getLabelFilesMd5())
                        || CollectionUtils.isEmpty(retDetail.getLabelFiles())) {
                        log.info("打标目录有变更，需要拉取新的视图");
                        List<FileVO> files = comfyUIService.viewFiles(retDetail.getTagRetDir(),
                            MaterialUploadUtil.getLabelFileTypes(model), fileServerUrl);

                        if (files == null && retDetail.getLabelFiles() != null) {
                            ret.setLabelRetFiles(ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(
                                retDetail.getLabelFiles()));
                            log.warn("view files接口出现异常，返回为null，此处以retDetail为准");
                        } else {
                            if (files != null) {
                                ret.setLabelRetFiles(
                                    ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(files));
                            }

                            //更新抠图任务结果详情中的图片和md5
                            ComfyuiTaskVO target = new ComfyuiTaskVO();
                            target.setId(t.getId());
                            retDetail.setLabelFilesMd5(md5);
                            retDetail.setLabelFiles(files);
                            target.setRetDetail(JSONObject.toJSONString(retDetail));

                            comfyuiTaskService.updateByIdSelective(target);
                        }
                    } else if (retDetail.getLabelFiles() != null) {
                        ret.setLabelRetFiles(ai.conrain.aigc.platform.integration.utils.FileUtils.sortFileVOs(
                            retDetail.getLabelFiles()));
                    }

                    if (CollectionUtils.isNotEmpty(ret.getLabelRetFiles())) {
                        ret.setSplitTags(getTags(ret));
                        ret.setDetailGarmentTypes(getDetailGarmentTypesGroupedByColor(ret));
                    }
                }
            }

            //lora
            if (clothLoraTrainDetail.getLora() != null) {
                Integer taskId = clothLoraTrainDetail.getLora().getTaskId();
                ComfyuiTaskVO t = comfyuiTaskService.selectById(taskId);
                ret.setLoraStatus(t.getTaskStatus().name());

                String startTime = DateUtils.formatTime(t.getCreateTime());
                if (t.getExtInfo() != null && t.getExtInfo().containsKey(CommonConstants.KEY_START_RUN_TIME)
                    && StringUtils.isNotBlank(t.getExtInfo().getString(CommonConstants.KEY_START_RUN_TIME))) {
                    startTime = t.getExtInfo().getString(CommonConstants.KEY_START_RUN_TIME);
                }
                ret.setLoraStartTime(startTime);

                LoraTaskRetDetail retDetail = CommonUtil.parseObject(t.getRetDetail(), LoraTaskRetDetail.class);
                if (retDetail != null) {
                    ret.setLoraFinishTime(retDetail.getFinishTime());
                }
            }

            //补充默认prompt
            if (StringUtils.isBlank(clothLoraTrainDetail.getClothDetailsPrompt())) {
                String clothDetailsPrompt = systemConfigService.queryValueByKey(TRAIN_LABEL_CLOTH_PROMPT);
                clothDetailsPrompt = CommonUtil.unescapeLineBreak(clothDetailsPrompt);
                clothDetailsPrompt = ComfyUIUtils.parseParams(clothDetailsPrompt);
                clothLoraTrainDetail.setClothDetailsPrompt(clothDetailsPrompt);
            }
        }

        //设置运营偏好
        Integer userId = model.getUserId();
        MerchantPreferenceDetail merchantPreference = merchantPreferenceService.queryDetailByUserId(userId);
        ret.setMerchantPreference(merchantPreference);

        return ret;
    }

    @Override
    public void updateLabelFiles(LabelFileEditReq req) {
        AssertUtil.assertNotNull(req.getLabelId(), "labelId不能为空");

        ComfyuiTaskVO task = comfyuiTaskService.selectById(req.getLabelId());
        String fileServerUrl = serverHelper.getFileServerUrlByTask(task);
        Integer modelId = task.getExtValue(KEY_MODEL_ID, Integer.class);
        fileServerUrl = fetchEnableFileServer(fileServerUrl, modelId, task.getUserId());
        Integer serverId = task.getExtValue(KEY_SERVER_ID, Integer.class);

        MaterialModelVO model = selectById(modelId);
        model.addExtInfo(KEY_IS_MODIFY_LABEL, YES);
        model.addExtInfo(KEY_MODIFY_LABEL_OPERATOR, OperationContextHolder.getOperatorUserId());
        if (model.getExtInfo(KEY_RELATED_OPERATOR, Integer.class) == null) {
            model.addExtInfo(KEY_RELATED_OPERATOR, OperationContextHolder.getOperatorUserId());
        }
        updateByIdSelective(model);

        for (LabelFileEditReq.LabelFileEditItem item : req.getItems()) {
            AssertUtil.assertNotBlank(item.getFilePath(), "文件路径不能为空");

            // 检查文件是否存在
            boolean exists = comfyUIService.checkFileExists(item.getFilePath(), fileServerUrl);
            if (!exists) {
                log.error("指定的文件不存在，不进行处理：{}", item.getFilePath());
                continue;
            }

            // 解析文件路径
            String[] fileParts = CommonUtil.parseFilePath(item.getFilePath());

            //删除文件
            if (item.isDeleted()) {
                // 删除文件
                fileDispatch.removeImageOrTxtFile(fileParts[0], fileParts[1], fileServerUrl);

                //替换图片
            } else if (StringUtils.isNotBlank(item.getUpdateImgUrl())) {

                // 创建临时目录
                String localDir = "/tmp/" + CommonUtil.uuid() + "/";
                String inputDir = "/home/<USER>/aigc/ComfyUI/input/";

                if (!item.getFilePath().startsWith(inputDir)) {
                    log.error("文件路径非法，它不是input下的子目录：{}", item.getFilePath());
                    continue;
                }

                // 下载图片(获取oss路径)
                String ossFilePath = CommonUtil.getFilePathAndNameFromURL(item.getUpdateImgUrl());
                String localPath = ossService.downloadFile(ossFilePath, localDir, fileParts[1] + fileParts[2]);
                File file = new File(localPath);

                if (!file.exists()) {
                    log.error("图片下载失败：{}, {}", localPath, item.getUpdateImgUrl());
                } else {
                    log.info("图片下载成功:{}", localPath);
                    boolean uploadSuccess = false;
                    int retryCount = 0;

                    // Step1:重新处理图片( 训练资料 )
                    if (item.isTrainingMaterial()) {
                        try {
                            String[] ossUrls
                                = ai.conrain.aigc.platform.service.util.FileUtils.processAndUploadRgbaImage(file,
                                fileParts[1], ossService);
                            if (ossUrls != null && ossUrls.length == 2) {
                                String maskOssUrl = ossUrls[0];

                                // maskOss
                                if (maskOssUrl != null) {
                                    SyncImageCaseReq syncImageCaseReq = new SyncImageCaseReq();
                                    syncImageCaseReq.setUrl(maskOssUrl);

                                    syncImageCaseReq.setFileDir(fileParts[0]);
                                    syncImageCaseReq.setType("img");
                                    syncImageCaseReq.setFileName(fileParts[1] + fileParts[2]);
                                    syncImageCaseReq.setFilePath(item.getFilePath());
                                    syncImageCaseReq.setImgUrl(item.getUpdateImgUrl());

                                    // 设置为训练材料图片
                                    syncImageCaseReq.setTrainingMaterial(Boolean.TRUE);

                                    // 同步到图片案例中
                                    syncToImageCase(syncImageCaseReq);
                                }
                            }
                        } catch (IOException e) {
                            log.error("图片处理失败：{}, {}", localPath, item.getUpdateImgUrl());
                        }
                    }

                    while (!uploadSuccess && retryCount < 10) {
                        try {
                            fileDispatch.uploadFile(StringUtils.stripStart(item.getFilePath(), inputDir),
                                Files.newInputStream(file.toPath()), fileServerUrl, serverId);

                            log.info("文件上传到comfyui成功: {}, {}", localPath, item.getUpdateImgUrl());
                            uploadSuccess = true;
                        } catch (Throwable e) {
                            retryCount++;
                            log.error("文件上传到comfyui失败(第{}次): {}, {}", retryCount, localPath,
                                item.getUpdateImgUrl(), e);
                        }
                    }

                    if (!uploadSuccess) {
                        throw new BizException("文件上传到comfyui失败");
                    }

                    // 清理本地临时文件
                    try {
                        org.apache.commons.io.FileUtils.deleteDirectory(new File(localDir));
                        log.info("文件夹删除成功:{}", localDir);
                    } catch (IOException e) {
                        log.error("文件夹删除失败:{}", localDir, e);
                    }
                }

                //替换文本
            } else if (StringUtils.isNotBlank(item.getUpdateTextContent())) {
                String context = item.getUpdateTextContent();

                if (task != null && task.getReqParams() != null) {
                    LoraTaskParams loraTaskParams = task.getReqParams().toJavaObject(LoraTaskParams.class);
                    if (loraTaskParams != null && StringUtils.equals(loraTaskParams.getMaterialType(),
                        MaterialType.scene.name())) {

                        if (!CommonUtil.isValidJson(context)) {
                            throw new BizException(ResultCode.SYS_ERROR, "风格场景打标词必须为json格式");
                        }

                        String filePath = fileParts[0] + "/" + fileParts[1] + ".txt";
                        // 检查文件是否存在
                        exists = comfyUIService.checkFileExists(filePath, fileServerUrl);
                        if (exists) {
                            JSONObject json = JSONObject.parseObject(context,
                                com.alibaba.fastjson.parser.Feature.OrderedField);
                            json.remove("text");
                            String labelText = MaterialModelUtils.formatSceneLabelContent(json);
                            json.put("text", labelText);
                            context = JSON.toJSONString(json, SerializerFeature.SortField);

                            log.info("更新风格lora的txt打标文件,{}，开始更新文件内容:{}", filePath, labelText);
                            fileDispatch.updateTextFileContent(filePath, labelText, fileServerUrl);
                        }
                    }
                }

                fileDispatch.updateTextFileContent(item.getFilePath(), context, fileServerUrl);
            }
        }

        //刷新打标文件
        refreshLabelFile(task, fileServerUrl, model);
    }

    /**
     * 刷新打标文件
     *
     * @param task          任务
     * @param fileServerUrl 文件服务
     * @param model         模型
     */
    private void refreshLabelFile(ComfyuiTaskVO task, String fileServerUrl, MaterialModelVO model) {
        LabelTaskRetDetail retDetail = CommonUtil.parseObject(task.getRetDetail(), LabelTaskRetDetail.class);

        if (retDetail == null) {
            return;
        }

        String md5 = comfyUIService.calcDirMd5(retDetail.getTagRetDir(), fileServerUrl);

        if (StringUtils.isBlank(md5) || !StringUtils.equals(md5, retDetail.getLabelFilesMd5())
            || CollectionUtils.isEmpty(retDetail.getLabelFiles())) {
            log.info("打标目录有变更，需要拉取新的视图");
            List<FileVO> files = comfyUIService.viewFiles(retDetail.getTagRetDir(),
                MaterialUploadUtil.getLabelFileTypes(model), fileServerUrl);

            AssertUtil.assertTrue(CollectionUtils.isNotEmpty(files), ResultCode.BIZ_FAIL, "拉取打标文件异常");

            //更新抠图任务结果详情中的图片和md5
            ComfyuiTaskVO target = new ComfyuiTaskVO();
            target.setId(task.getId());
            retDetail.setLabelFilesMd5(md5);
            retDetail.setLabelFiles(files);
            target.setRetDetail(JSONObject.toJSONString(retDetail));

            comfyuiTaskService.updateByIdSelective(target);
        }
    }

    private static List<String> getTags(ModelTrainDetailVO ret) {
        Set<String> tags = new LinkedHashSet<>();
        for (FileVO each : ret.getLabelRetFiles()) {
            if (StringUtils.isNotBlank(each.getTextContent()) && !each.getFileDir().endsWith("label")) {
                String[] split = each.getTextContent().split(",");
                for (String s : split) {
                    if (StringUtils.isNotBlank(s.trim())) {
                        tags.add(s.trim());
                    }
                }
            }
        }
        return new ArrayList<>(tags);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDemoTag(Integer id) {
        MaterialModelVO data = lockById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "任务不存在");

        String demoTag = data.getExtInfo(KEY_DEMO_TAG, String.class);
        String target = !StringUtils.equals(YES, demoTag) ? YES : NO;

        data.addExtInfo(KEY_DEMO_TAG, target);
        innerUpdate(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExperimental(Integer id, boolean experimental) {
        MaterialModelVO materialModelVO = lockById(id);
        AssertUtil.assertNotNull(materialModelVO, "素材不存在");

        materialModelVO.addExtInfo(CommonConstants.KEY_EXPERIMENTAL, experimental);

        MaterialModelDO materialModelDO = new MaterialModelDO();
        materialModelDO.setId(id);
        materialModelDO.setExtInfo(materialModelVO.getExtInfo().toJSONString());
        int n = materialModelDAO.updateByPrimaryKeySelective(materialModelDO);
        AssertUtil.assertTrue(n == 1, "素材不存在");
    }

    @Override
    public String queryDetailShowImage(Integer modelId) {
        MaterialModelVO modelVO = selectById(modelId);
        AssertUtil.assertNotNull(modelVO, ResultCode.PARAM_INVALID, "素材不存在");

        if (modelVO.getExtInfo() != null && modelVO.getExtInfo(highResModelShowImgUrl) != null) {
            return modelVO.getExtInfo(highResModelShowImgUrl, String.class);
        }

        Integer infoId = modelVO.getClothLoraTrainDetail().getOriginalMaterialId();
        MaterialInfoVO materialInfoVO = materialInfoService.selectById(infoId);

        if (materialInfoVO == null || materialInfoVO.getMaterialDetail() == null) {
            return modelVO.getShowImage();
        }

        String url = getHighResModelShowImgUrl(modelVO);
        if (StringUtils.isNotBlank(url)) {
            modelVO.addExtInfo(highResModelShowImgUrl, url);
            innerUpdate(modelVO);

            return url;
        } else {
            return modelVO.getShowImage();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reInitTrainedModel(int modelId) {
        MaterialModelVO model = lockById(modelId);
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "素材不存在");
        MaterialModelStatusEnum status = MaterialModelStatusEnum.getByCode(model.getStatus());
        AssertUtil.assertTrue(status == MaterialModelStatusEnum.ENABLED || status == MaterialModelStatusEnum.TESTING,
            ResultCode.BIZ_FAIL, "素材状态异常");

        LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();
        ComfyuiTaskVO loraTask = comfyuiTaskService.selectById(trainDetail.getLora().getTaskId());
        ComfyuiTaskVO labelTask = comfyuiTaskService.selectById(trainDetail.getLabel().getTaskId());

        boolean result = onLoraSuccess(loraTask, trainDetail, model, labelTask);
        AssertUtil.assertTrue(result, ResultCode.BIZ_FAIL, "异常");

        innerUpdate(model);
    }

    @Override
    public List<MaterialModelVO> queryMaterialModelByDeliveryDate(String startDate, String endDate) {
        List<MaterialModelDO> list = materialModelDAO.queryMaterialModelByDeliveryDate(startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return MaterialModelConverter.doList2VOList(list);
    }

    @Override
    public List<Integer> querySubIds(Integer id) {
        return materialModelDAO.selectSubIds(id);
    }

    @Override
    public List<MaterialModelVO> querySubModel(Integer modelId) {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setMainId(modelId);
        query.setMainType(MainTypeEnum.SUB.getCode());
        query.setOrderBy("id asc");
        return queryMaterialModelList(query);
    }

    @Override
    public StatsQueuedModelDO statsQueuedModel() {
        return materialModelDAO.statsQueuedModel();
    }

    /**
     * 获取 garment_type 描述，从以下6个文件中获取其中detail_garment_type字段的值
     * cloth_description.txt/full_back.txt/lower_back.txt/lower_front.txt/upper_back.txt/upper_front.txt
     * <p>
     * {
     * "c1": {
     * "color": "light mint green and white stripes",
     * "garment_type": "shirt",
     * "detail_garment_type": "A light mint green and white striped long-sleeve shirt",
     * "detail_description": "The shirt features horizontal stripes throughout.",
     * "description": "A light mint green and white striped long-sleeve shirt. The shirt features horizontal stripes
     * throughout."
     * },
     * "c2": {
     * "color": "black and white striped",
     * "garment_type": "shirt",
     * "detail_garment_type": "A black and white striped long-sleeve shirt",
     * "detail_description": "The shirt features horizontal stripes throughout.",
     * "description": "A black and white striped long-sleeve shirt. The shirt features horizontal stripes throughout."
     * }
     * }
     *
     * @param ret
     * @return
     */
    @org.jetbrains.annotations.NotNull
    private static List<String> getDetailGarmentTypesGroupedByColor(ModelTrainDetailVO ret) {
        List<String> targetKeys = Arrays.asList("c1", "c2", "c3");

        Set<String> set = new HashSet<>();
        for (FileVO each : ret.getLabelRetFiles()) {
            if (StringUtils.isNotBlank(each.getTextContent()) && each.getFileDir().endsWith("label")
                && CommonUtil.isValidJson(each.getTextContent())) {
                JSONObject json = JSONObject.parseObject(each.getTextContent());
                for (String key : targetKeys) {
                    if (json.containsKey(key)) {
                        String detailGarmentType = json.getJSONObject(key).getString("detail_garment_type");
                        set.add(detailGarmentType);
                    }
                }
            }
        }

        return new ArrayList<>(set);
    }

    /**
     * 执行确认训练流程
     *
     * @param req          请求
     * @param model        模型
     * @param trainDetail  训练详情
     * @param operatorId   操作员id
     * @param operatorNick 操作员昵称
     */
    public void confirmTrainLora(ConfirmLoraReq req, MaterialModelVO model, LoraTrainDetail trainDetail,
                                 Integer operatorId, String operatorNick) {

        if (model == null) {
            return;
        }
        MaterialModelVO target = new MaterialModelVO();
        target.setId(model.getId());

        trainDetail.setLoraConfirmed("Y");
        trainDetail.setLoraType(req.getLoraType());
        trainDetail.setLoraModelName(trainDetail.getClothSubDir() + "-" + req.getLoraType());
        trainDetail.setLoraConfirmedTime(DateUtils.formatTime(new Date()));
        trainDetail.setLoraConfirmedOperatorId(operatorId);
        trainDetail.setLoraConfirmedOperatorNick(operatorNick);
        trainDetail.setMaxTrainStep(req.getMaxTrainStep().toString());

        trainDetail.setLr(req.getLr());
        trainDetail.setContentOrStyle(req.getContentOrStyle());
        trainDetail.setRank(req.getRank());
        trainDetail.setAlpha(req.getAlpha());
        trainDetail.setDropout(req.getDropout());
        trainDetail.setResolution(req.getResolution());

        if (CollectionUtils.isNotEmpty(req.getTestFaces()) && CollectionUtils.isNotEmpty(req.getTestScenes())) {
            trainDetail.setTestFaces(req.getTestFaces());
            trainDetail.setTestScenes(req.getTestScenes());
            trainDetail.setTestNum(req.getTestNum() != null ? req.getTestNum() : 5);
            trainDetail.setTestImgProportions(req.getTestImgProportions());
            trainDetail.setImgNumPerGroup(req.getImgNumPerGroup());
            trainDetail.setTestImgMode(req.getTestImgMode());
            trainDetail.setTestClothCollocation(req.getTestClothCollocation());
        }

        if (MapUtils.isEmpty(req.getTrainExtInfo())) {
            trainDetail.setTrainExtInfo("");
        } else {
            trainDetail.setTrainExtInfo(MaterialModelUtils.buildTrainExtInfo(req.getTrainExtInfo()));
        }

        target.setClothLoraTrainDetail(trainDetail);

        updateByIdSelective(target);
    }

    private boolean uploadOriginalMaterialIfNecessary(MaterialModelVO model) {
        LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();
        AssertUtil.assertNotNull(trainDetail, "模型训练详情为空model id=" + model.getId());

        if (trainDetail.getLora() != null) {
            log.info("lora任务已经存在，不用检查上传素材");
            return true;
        }

        ServerVO fileServer = serverHelper.getFileServerByUserId(model.getUserId(), DispatchTypeEnum.LORA_PRE_PROCESS);
        if (fileServer == null) {
            log.warn("用户{}没有可用的服务器，无法上传素材", model.getUserId());
            return false;
        }

        String fileServerUrl = serverHelper.getFileServerUrl(fileServer.getId());

        log.info("素材上传的server：{}", fileServerUrl);

        String clothDir = trainDetail.getClothDir();
        Integer imgNum = comfyUIService.queryImageCnt(clothDir, null, fileServerUrl);

        Integer materialId = trainDetail.getOriginalMaterialId();
        AssertUtil.assertNotNull(materialId, "模型训练详情中material id为空");

        MaterialInfoVO materialInfoVO = materialInfoService.selectById(materialId);
        AssertUtil.assertNotNull(materialInfoVO, "模型训练详情中material id对应的素材不存在");
        AssertUtil.assertNotNull(materialInfoVO.getMaterialDetail(), "模型训练详情中material id对应的素材详情不存在");

        //上传图片，连衣裙和上下装，不传入下半身图片到lora抠图和训练
        int expectedImgNum = MaterialUploadUtil.getExpectedImgNum(materialInfoVO);

        if (imgNum == null || imgNum < expectedImgNum) {
            log.info("model id={}原始素材未完成上传，开始全量上传", model.getId());
            try {
                return upload2ComfyUIServer(trainDetail, materialInfoVO.getMaterialDetail(),
                    materialInfoVO.getSubType(), model.getUserId(), fileServer, null);
            } catch (Exception e) {
                log.error("原始素材上传失败model id=" + model.getId(), e);
                return false;
            }

            //imgNum == expectedImgNum
        } else {
            List<String> inCompleteImgs = comfyUIService.findInCompleteImgs(clothDir, fileServerUrl);

            if (CollectionUtils.isNotEmpty(inCompleteImgs)) {
                List<Integer> imgIndexList = inCompleteImgs.stream().map(inCompleteImg -> {
                    String[] arr = inCompleteImg.split("_");
                    return Integer.parseInt(arr[0]) - 1;
                }).collect(Collectors.toList());

                log.warn(
                    "远程服务器{}上的文件数足够，但文件内容不完整，部分文件需要重新上传，目录：{}, 不完整文件列表：{}，对应indexList: {}",
                    fileServerUrl, clothDir, JSONArray.toJSONString(inCompleteImgs),
                    JSONArray.toJSONString(imgIndexList));

                try {
                    return upload2ComfyUIServer(trainDetail, materialInfoVO.getMaterialDetail(),
                        materialInfoVO.getSubType(), model.getUserId(), fileServer, imgIndexList);
                } catch (Exception e) {
                    log.error("原始素材上传失败model id=" + model.getId(), e);
                    return false;
                }
            }

            log.info("model id={}需要上传的素材数量为{}，comfyui server的图片数量为{}，不需要上传", model.getId(),
                expectedImgNum, imgNum);
            return true;
        }
    }

    private String getLoraName(LoraTrainDetail detail) {
        String lorasDir = "/home/<USER>/aigc/ComfyUI/models/loras";
        AssertUtil.assertTrue(
            detail.getLoraModelRetFilePath() != null && detail.getLoraModelRetFilePath().startsWith(lorasDir),
            "detail.getLoraModelRetFilePath()非法：" + detail.getLoraModelRetFilePath());
        return CommonUtil.getRelativePath(detail.getLoraModelRetFilePath(), lorasDir);
    }

    private CreativeElementVO initRelatedElementConfig(Integer elementId, MaterialModelVO model) {
        if (model.getClothLoraTrainDetail() == null) {
            return null;
        }

        CreativeElementVO e = creativeElementService.selectById(elementId);
        if (e == null) {
            return null;
        }

        ComfyuiTaskVO label = comfyuiTaskService.selectById(model.getClothLoraTrainDetail().getLabel().getTaskId());

        LabelTaskRetDetail retDetail = JSONObject.parseObject(label.getRetDetail(), LabelTaskRetDetail.class);

        if (e.getConfigKey().equalsIgnoreCase(ElementConfigKeyEnum.FACE.name())) {
            String faceImgRelativePath = null;
            if (model.getClothLoraTrainDetail().getLabel() != null) {

                FileVO showImgFile = getShowImgByLabelRet(label);

                if (showImgFile != null && label.getExtValue(KEY_SERVER_URL, String.class) != null) {
                    e.setShowImage(showImgFile.getImgUrl());
                    String faceImgPath = Paths.get(showImgFile.getFileDir(), showImgFile.getFileName()).toAbsolutePath()
                        .toString();

                    faceImgRelativePath = "face/" + model.getId() + "_" + showImgFile.getFileName();
                    String targetFaceImgPath = "/home/<USER>/aigc/ComfyUI/input/" + faceImgRelativePath;

                    String fileServerUrl = serverHelper.getFileServerUrl(
                        label.getExtValue(KEY_SERVER_URL, String.class));
                    if (StringUtils.isBlank(fileServerUrl)) {
                        fileServerUrl = serverHelper.getFileServerUrlByUser(model.getUserId());
                    }
                    boolean copySuccess = comfyUIService.fileCopy(faceImgPath, targetFaceImgPath, fileServerUrl);
                    AssertUtil.assertTrue(copySuccess, "模特lora成功但复制脸的图片失败");

                    //通知文件同步脸图片
                    fileDispatch.notifyFileSync(fileServerUrl, targetFaceImgPath, false);
                }
            }

            String labelType = model.getExtInfo(KEY_LABEL_TYPE, String.class);
            if (LabelTypeEnum.MINI == LabelTypeEnum.getByCode(labelType)) {
                e.addExtInfo(KEY_LABEL_TYPE, labelType);
            }
            log.info("开始重置lora模特元素，labelType={}", labelType);
            e.addExtInfo(KEY_TRAIN_TYPE, model.getClothLoraTrainDetail().getContentOrStyle());

            creativeElementService.resetLoraFace(e, retDetail.getLabelFiles(), model.getLoraName(),
                faceImgRelativePath);

            return e;

        }

        if (e.getConfigKey().equalsIgnoreCase(ElementConfigKeyEnum.SCENE.name())) {

            String sceneType = model.getExtInfo(KEY_SCENE_TYPE, String.class);
            CreativeBizTypeEnum bizType = CreativeBizTypeEnum.getByCode(sceneType);

            //如果是look图场景和T台秀场等场景时，添加类型
            if (bizType != null && CollectionUtils.isNotEmpty(bizType.getIncludesTypes())) {

                if (CollectionUtils.isEmpty(e.getType())) {
                    e.setType(new ArrayList<>());
                } else {
                    e.setType(new ArrayList<>(e.getType()));
                }

                e.getType().addAll(bizType.getIncludesTypes());
            }

            e.addExtInfo(KEY_TRAIN_TYPE, model.getClothLoraTrainDetail().getContentOrStyle());

            creativeElementService.resetStyleScene(e, retDetail.getLabelFiles(), model.getLoraName());

            return e;
        }

        throw new IllegalArgumentException("不支持的配置类型:" + e.getConfigKey());
    }

    private FileVO getShowImgByLabelRet(ComfyuiTaskVO label) {
        if (label != null && label.getRetDetail() != null) {
            LabelTaskRetDetail ret = CommonUtil.parseObject(label.getRetDetail(), LabelTaskRetDetail.class);
            if (ret != null && CollectionUtils.isNotEmpty(ret.getLabelFiles())) {
                for (FileVO file : ret.getLabelFiles()) {
                    if (StringUtils.equals(file.getType(), "img")) {
                        return file;
                    }
                }
            }
        }
        return null;
    }

    private boolean handleCutoutLabelAndTrain(LoraTrainDetail detail, MaterialModelVO model,
                                              ComfyuiTaskVO prepareViewTask) {
        //抠图
        ComfyuiTaskVO cutoutTask = handleTask(detail, detail.getCutout(), model, ComfyuiTaskTypeEnum.cutout,
            LoraTrainDetail::setCutout, prepareViewTask);
        if (cutoutTask.getTaskStatus() == QueueResult.QueueCodeEnum.COMPLETED) {

            MaterialType materialType = MaterialType.valueOf(detail.getMaterialType());
            LabelTypeEnum labelType = LabelTypeEnum.getByCode(model.getExtInfo(KEY_LABEL_TYPE, String.class));
            if (materialType == MaterialType.face && labelType == LabelTypeEnum.MINI) {
                initFaceMiniLabelInfo(detail, model, cutoutTask);
            }

            //如果是放大流程，那么设置默认的full body和upper body等变更为镜头方式
            if (materialType == MaterialType.cloth && StringUtils.equals(YES, detail.getCut4ScaleUp())) {
                JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
                float flag = switchJson.getFloatValue("useShot");
                if (GrayscaleTestUtils.isHit(flag, "服装打标-使用镜头打标词")) {
                    log.info("服装打标-使用镜头打标词灰度命中,modelId={}", model.getId());
                    detail.setUseShot(YES);
                }
            }

            return handleLabelAndLora(detail, model, cutoutTask);
        } else {

            //在抠图未完成前，对打标类型进行初始化
            MaterialType materialType = MaterialType.valueOf(detail.getMaterialType());
            if (materialType == MaterialType.cloth && !StringUtils.equals(YES,
                model.getExtInfo(KEY_INIT_LABEL_TYPE, String.class))) {

                ClothAngleDetail angleDetail = JSON.parseObject(prepareViewTask.getRetDetail()).getObject(
                    CommonConstants.KEY_MAIN_IMAGES, ClothAngleDetail.class);

                initLabelType(model, detail, angleDetail, cutoutTask.getExtInfo(KEY_SERVER_URL, String.class));
            }

        }
        return false;
    }

    /**
     * 重新初始化模特极简打标信息
     *
     * @param detail     训练详情
     * @param model      模型
     * @param cutoutTask 抠图任务
     */
    private void initFaceMiniLabelInfo(LoraTrainDetail detail, MaterialModelVO model, ComfyuiTaskVO cutoutTask) {
        String serverUrl = cutoutTask.getExtInfo(KEY_SERVER_URL, String.class);
        CutoutTaskRetDetail retDetail = CommonUtil.parseObject(cutoutTask.getRetDetail(), CutoutTaskRetDetail.class);
        if (retDetail == null) {
            return;
        }
        FileVO img = retDetail.getCutoutFiles().stream().filter(file -> StringUtils.equals(file.getType(), "img")).min(
            Comparator.comparing(FileVO::getFileName)).orElse(null);
        AssertUtil.assertNotNull(img, "抠图结果为空");

        String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);
        String prompt = systemConfigService.queryValueByKey(TRAIN_LABEL_FACE_MINI_PROMPT);
        String callResult = comfyUIService.callLLM(prompt, detail.getCutoutRetDir() + "/" + img.getFileName(),
            fileServerUrl);
        //原始语句：European, creamy peach skin, long light brown curly hair
        callResult = CommonUtil.parseJsonStringFromGpt(callResult);
        String race = StringUtils.trim(StringUtils.substringBefore(callResult, ","));
        callResult = StringUtils.substringAfter(callResult, ",");
        String skin = StringUtils.trim(StringUtils.substringBefore(callResult, ","));
        String hairstyle = StringUtils.trim(StringUtils.substringAfter(callResult, ","));
        String genderType = StringUtils.equals(woman, detail.getClothStyleType()) ? female : male;
        //填充为：a mgm3004 Creamy peach skin European female model, long straight auburn hair,
        String miniActivateKey = String.format("a mgm3004 %s %s %s model, %s", skin, race, genderType, hairstyle);

        detail.setCaptionPrompt("");
        detail.setActivateKey(miniActivateKey);

        log.info("初始化模特极简打标信息,id={},miniActivateKey={}", model.getId(), miniActivateKey);
    }

    /**
     * 初始化打标类型
     *
     * @param model       模型
     * @param detail      训练详情
     * @param angleDetail 服装角度图片详情
     * @param serverUrl   服务器地址
     */
    private void initLabelType(MaterialModelVO model, LoraTrainDetail detail, ClothAngleDetail angleDetail,
                               String serverUrl) {
        if (angleDetail == null) {
            log.info("未获取到服装角度图片详情,跳过初始化打标类型,id={}", model.getId());
            return;
        }

        LabelTypeEnum labelType = LabelTypeEnum.getByCode(model.getExtInfo(KEY_LABEL_TYPE, String.class));

        //只处理默认的
        if (labelType != null && labelType != LabelTypeEnum.DEFAULT) {
            log.info("当前类型为非默认,跳过初始化打标类型,id={},labelType={}", model.getId(), labelType);
            return;
        }
        log.info("开始初始化打标类型,id={}", model.getId());
        String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);

        String clothDir = detail.getClothDir();
        //1.找到服装正面/背面的图，优先取半身的图
        String front = angleDetail.getFrontHalfImg();
        front = StringUtils.isBlank(front) ? angleDetail.getFrontFullImg() : front;
        String back = angleDetail.getBackHalfImg();
        back = StringUtils.isBlank(back) ? angleDetail.getBackFullImg() : back;
        //2.服装类型，上半身、下半身、套装
        String clothType = detail.getClothType();
        String promptTemplate = systemConfigService.queryValueByKey(DECIDE_CLOTH_COMPLEX_PROMPT);
        String prompt = PromptUtils.buildClothComplexPrompt(promptTemplate, clothType);

        //3.判断正面是否复杂
        boolean isComplex = false;
        List<String> complexReasons = null;
        if (StringUtils.isNotBlank(front)) {
            String callResult = comfyUIService.callLLM(prompt, clothDir + "/" + front, fileServerUrl);
            callResult = CommonUtil.parseJsonStringFromGpt(callResult);
            ClothComplexResult result = Objects.requireNonNull(JSON.parseObject(callResult)).toJavaObject(
                ClothComplexResult.class);
            if (result.isComplex()) {
                log.info("判定当前服装正面复杂=true");
                isComplex = true;
                complexReasons = result.getComplexReasons();
            }
        }

        //4.判断背面是否复杂
        if (!isComplex && StringUtils.isNotBlank(back)) {
            String callResult = comfyUIService.callLLM(prompt, clothDir + "/" + back, fileServerUrl);
            callResult = CommonUtil.parseJsonStringFromGpt(callResult);
            ClothComplexResult result = Objects.requireNonNull(JSON.parseObject(callResult)).toJavaObject(
                ClothComplexResult.class);
            if (result.isComplex()) {
                log.info("判定当前服装背面复杂=true");
                isComplex = true;
                complexReasons = result.getComplexReasons();
            }
        }

        //5.如果是复杂服装，变更打标类型为精准打标
        if (isComplex) {
            model.addExtInfo(KEY_LABEL_TYPE, LabelTypeEnum.DETAILS.getCode());

            String captionPrompt = systemConfigService.queryValueByKey(TRAIN_LABEL_CLOTH_DETAILS_PROMPT);
            captionPrompt = CommonUtil.unescapeLineBreak(captionPrompt);
            captionPrompt = ComfyUIUtils.parseParams(captionPrompt);
            detail.setClothDetailsPrompt(captionPrompt);

            model.addExtInfo(KEY_IS_COMPLEX, YES);
            model.addExtInfo(KEY_COMPLEX_REASONS, complexReasons);
        } else {
            model.addExtInfo(KEY_IS_COMPLEX, NO);
        }

        log.info("判定当前服装复杂结束，当前复杂度={}，原因：{}", isComplex, complexReasons);
        model.addExtInfo(KEY_INIT_LABEL_TYPE, YES);
    }

    private boolean handleLabelAndLora(LoraTrainDetail detail, MaterialModelVO model, ComfyuiTaskVO cutoutTask) {
        //打标
        ComfyuiTaskVO labelTask = handleTask(detail, detail.getLabel(), model, ComfyuiTaskTypeEnum.label,
            LoraTrainDetail::setLabel, cutoutTask);

        if (labelTask.getTaskStatus() == QueueResult.QueueCodeEnum.COMPLETED) {

            //更新展示图片
            updateModelShowImgByLabelRet(labelTask, model);

            Object loraConfirmed = detail.getLoraConfirmed();
            if (loraConfirmed == null) {
                loraConfirmed = model.getExtInfo(CommonConstants.loraConfirmed);
                if (loraConfirmed == null && autoTrain(model, detail)) {
                    loraConfirmed = "Y";
                }
            }

            if (loraConfirmed != null && "Y".equals(loraConfirmed.toString())) {
                log.info("model id={}已经人工确认，开始推进lora训练", model.getId());

                //推进lora训练
                ComfyuiTaskVO loraTask = handleTask(detail, detail.getLora(), model, ComfyuiTaskTypeEnum.lora,
                    LoraTrainDetail::setLora, labelTask);

                //lora训练完成，下载oss url并同步文件到4090（如有需要）
                //note：这里不异步化，onLoraSuccess内会更新target变量（内存），由外层代码更新到db
                if (detail.getLora() != null && detail.getLora().getStatus() == QueueResult.QueueCodeEnum.COMPLETED) {
                    return onLoraSuccess(loraTask, detail, model, labelTask);
                }
            } else {
                log.info("model id={}没有人工确认，不开始lora训练", model.getId());
            }
        }
        return false;
    }

    private void updateModelShowImgByLabelRet(ComfyuiTaskVO label, MaterialModelVO model) {
        try {
            String retDetail = label.getRetDetail();
            if (CommonUtil.isValidJson(retDetail)) {
                LabelTaskRetDetail labelTaskRetDetail = JSONObject.parseObject(retDetail, LabelTaskRetDetail.class);
                if (StringUtils.isNotBlank(labelTaskRetDetail.getFullbodyFrontViewImgUrl())) {
                    model.setShowImage(ossHelper.reprocessImage(labelTaskRetDetail.getFullbodyFrontViewImgUrl()));
                    model.addExtInfo(CommonConstants.highResModelShowImgUrl,
                        labelTaskRetDetail.getFullbodyFrontViewImgUrl());
                }
            }
        } catch (Throwable t) {
            log.warn("updateModelShowImgByLabelRet error", t);
        }
    }

    private boolean upload2ComfyUIServer(LoraTrainDetail trainDetail, JSONObject materialDetail, String clothType,
                                         Integer userId, ServerVO fileServer, List<Integer> imgIndexList) {

        String uploadRootDirName = StringUtils.defaultIfBlank(trainDetail.getUploadRootDirName(),
            MaterialUploadUtil.getMaterialUploadBaseDir());
        String clothDirName = trainDetail.getClothSubDir();

        String comfyuiDir = String.format("%s/%s", uploadRootDirName, clothDirName);
        String localSaveDir = "/tmp/" + comfyuiDir;

        List<String> successfulUploads = new ArrayList<>(); // 成功上传的文件列表
        List<String> failedUploads = new ArrayList<>(); // 失败上传的文件列表

        List imgs = null;

        if (MaterialType.face.name().equals(trainDetail.getMaterialType()) || MaterialType.scene.name().equals(
            trainDetail.getMaterialType())) {
            imgs = materialDetail.toJavaObject(CommonMaterialDetail.class).getImgUrls();
        } else {
            imgs = MaterialUploadUtil.getClothMaterialImgs4Upload(
                materialDetail.toJavaObject(ClothMaterialDetail.class), clothType);
        }

        for (int i = 0; i < imgs.size(); ++i) {

            //imgIndexList包含所有需要上传的图片序列号列表，如果为空，则上传所有图片，如果不为空则只上传imgIndexList中的图片，imgIndexList从0开始，对应文件名1_xx.jpg
            if (CollectionUtils.isNotEmpty(imgIndexList) && !imgIndexList.contains(i)) {
                continue;
            }

            //material type=face/scene
            if (imgs.get(i) instanceof String) {
                String imgUrl = (String)imgs.get(i);
                uploadImage(imgUrl, localSaveDir, comfyuiDir, i, userId, fileServer, failedUploads, successfulUploads,
                    trainDetail.getMaterialType());

                //material type=cloth
            } else {
                ClothMaterialImg img = (ClothMaterialImg)imgs.get(i);
                uploadImage(img.getImgUrl(), localSaveDir, comfyuiDir, i, userId, fileServer, failedUploads,
                    successfulUploads, img.getViewTags(), img.getColorGroupNumber());
            }
        }

        log.info("{} 上传成功的文件数量: {}, 列表: {}。上传失败的文件数量: {}, 列表: {}", clothDirName,
            successfulUploads.size(), successfulUploads, failedUploads.size(), failedUploads);

        // 清理本地临时文件 && 检查远程文件完整性
        int totalSize = imgs.size();
        if (CollectionUtils.isNotEmpty(imgIndexList)) {
            totalSize = imgIndexList.size();
        }

        if (successfulUploads.size() == totalSize) {

            try {
                FileUtils.deleteDirectory(new File(localSaveDir));
                log.info("文件夹删除成功:{}", localSaveDir);
            } catch (IOException e) {
                log.error("文件夹删除失败:{}", localSaveDir, e);
            }

            return true;
        } else {
            log.info("有上传失败的文件，无法清理文件夹 {}", localSaveDir);
            return false;
        }
    }

    /**
     * 发送事件
     *
     * @param testFaces        模特配置
     * @param testScenes       场景配置
     * @param testNum          图片组数
     * @param imgNumPerGroup   每组图片张数
     * @param imgProportions   图片尺寸列表
     * @param collocation      搭配信息
     * @param testImgMode      测试图片模式
     * @param target           素材模型
     * @param byUser           是否由用户触发
     * @param withoutDeduction 是否不需要扣点,byUser时才有效
     * @param cameraAnglePairs 出图角度对
     */
    private void sendEvent(List<Integer> testFaces, List<Integer> testScenes, Integer testNum, Integer imgNumPerGroup,
                           List<String> imgProportions, ClothCollocationModel collocation, String testImgMode,
                           MaterialModelVO target, boolean byUser, boolean withoutDeduction,
                           List<List<String>> cameraAnglePairs, List<CreateTestRequest> requests) {
        CreateTestImgEvent event = new CreateTestImgEvent();
        event.setEventId(MDC.get("traceId"));
        event.setCreateTime(DateUtils.formatFullTime(new Date()));
        event.setModelId(target.getId());
        event.setModelName(target.getName());
        event.setTestFaces(testFaces);
        event.setTestScenes(testScenes);
        event.setTestNum(testNum);
        event.setTestImgProportions(imgProportions);
        event.setImgNumPerGroup(imgNumPerGroup);
        event.setTestImgMode(testImgMode);
        event.setTestClothCollocation(collocation);
        event.setCameraAnglePairs(cameraAnglePairs);
        if (byUser) {
            event.setUserId(target.getUserId());
            event.setWithoutDeduction(withoutDeduction);
        }
        event.setCreateTestRequestPairs(requests);

        //延时10分钟发送测试消息
        // messageDelayLevel	1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
        log.info("【测试图生成事件】发出延时消息:{}", event);
        if (EnvUtil.isLocalEnv()) {
            try {
                createTestImgHelper.batchCreateByEvent(event);
            } catch (Exception e) {
                log.error("【测试图生成事件】发送延时消息失败:{}", event, e);
                throw new BizException(ResultCode.SYS_ERROR, "【测试图生成事件】发送延时消息失败");
            }
        } else {
            rocketMQTemplate.syncSend(EventConstants.TOPIC_CREATE_TEST_IMGS, MessageBuilder.withPayload(event).build(),
                3000, 15);
        }

        target.addExtInfo(KEY_IS_SEND_TEST_EVENT, true);
    }

    private void uploadImage(String imgUrl, String localSaveDir, String comfyuiDir, int index, Integer userId,
                             ServerVO fileServer, List<String> failedUploads, List<String> successfulUploads,
                             String materialType) {

        AssertUtil.assertNotBlank(materialType, "素材类型为空");

        String ossFilePath = CommonUtil.getFilePathAndNameFromURL(imgUrl);
        String localPath = ossService.downloadFile(ossFilePath, localSaveDir, materialType);
        File file = new File(localPath);

        if (!file.exists()) {
            log.error("文件下载失败：{}, {}", localPath, imgUrl);
            failedUploads.add(file.getName()); // 添加到失败列表
        } else {
            log.info("文件下载成功:{}", localPath);
            String comfyuiFileName = (index + 1) + "_" + file.getName();
            uploadFileToComfyUI(comfyuiDir, comfyuiFileName, file, userId, fileServer, failedUploads,
                successfulUploads);
        }
    }

    private void uploadImage(String imgUrl, String localSaveDir, String comfyuiDir, int index, Integer userId,
                             ServerVO fileServer, List<String> failedUploads, List<String> successfulUploads,
                             String viewTags, Integer colorGroupNumber) {
        String ossFilePath = CommonUtil.getFilePathAndNameFromURL(imgUrl);
        String localPath = ossService.downloadFile(ossFilePath, localSaveDir,
            viewTags + "_c" + ObjectUtils.defaultIfNull(colorGroupNumber, 1));
        File file = new File(localPath);

        if (!file.exists()) {
            log.error("文件下载失败：{}, {}", localPath, imgUrl);
            failedUploads.add(file.getName()); // 添加到失败列表
        } else {
            log.info("文件下载成功:{}", localPath);
            String comfyuiFileName = (index + 1) + "_" + file.getName();
            uploadFileToComfyUI(comfyuiDir, comfyuiFileName, file, userId, fileServer, failedUploads,
                successfulUploads);
        }
    }

    private void uploadFileToComfyUI(String comfyuiDir, String comfyuiFileName, File file, Integer userId,
                                     ServerVO fileServer, List<String> failedUploads, List<String> successfulUploads) {
        boolean uploadSuccess = false;
        int retryCount = 0;

        while (!uploadSuccess && retryCount < 5) {
            try {
                fileDispatch.uploadFile(comfyuiDir + "/" + comfyuiFileName, Files.newInputStream(file.toPath()), userId,
                    true, fileServer);

                successfulUploads.add(comfyuiFileName); // 添加到成功列表
                uploadSuccess = true;
            } catch (Throwable e) {
                retryCount++;
                log.error("文件上传到comfyui失败(第{}次): {}, {}", retryCount, file.getAbsolutePath(), comfyuiFileName,
                    e);
                if (retryCount == 5) {
                    failedUploads.add(comfyuiFileName); // 添加到失败列表
                }
            }
        }
    }

    /**
     * 获取最迟的任务
     *
     * @param list 子任务列表
     * @return 最迟的任务
     */
    private LoraSubTask getLatestTask(List<LoraSubTask> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        //只要有一笔是null的，直接置为null
        if (list.stream().anyMatch(Objects::isNull)) {
            return null;
        }

        //只要有一笔为未完成时，取任意一笔未完成的状态
        List<LoraSubTask> notCompleted = list.stream().filter(t -> t.getStatus() != QueueCodeEnum.COMPLETED).collect(
            Collectors.toList());

        if (CollectionUtils.isNotEmpty(notCompleted)) {
            return notCompleted.get(0);
        }

        //如果都完成，则取任意一笔

        return list.get(0);
    }

    /**
     * 获取最早的任务
     *
     * @param list 子任务列表
     * @return 最迟的任务
     */
    private LoraSubTask getEarliestTask(List<LoraSubTask> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        //全部都是null的直接置为null
        if (list.stream().allMatch(Objects::isNull)) {
            return null;
        }

        //只要有一笔为空，或者状态不为完成，则返回处理中状态
        if (list.stream().anyMatch(t -> t == null || t.getStatus() != QueueCodeEnum.COMPLETED)) {

            List<LoraSubTask> notNull = list.stream().filter(Objects::nonNull).collect(Collectors.toList());

            LoraSubTask sub = notNull.get(0);
            sub.setStatus(QueueCodeEnum.RUNNING);

            return sub;
        }

        //如果都完成，则取任意一笔
        return list.get(0);
    }

    private interface SubTaskCallback {
        List<LoraSubTask> getSubTasks();

        LoraSubTask getMainTask();

        void setSubTasks(LoraSubTask subTask);
    }

    /**
     * 填充子任务
     *
     * @param preCompleted 上一步是否已完成
     * @param callback     回调
     */
    private void fillSubTask(boolean[] preCompleted, boolean needLatest, SubTaskCallback callback) {
        // 上一步是否完成
        boolean isPreCompleted = preCompleted[0];
        if (!isPreCompleted) {
            return;
        }

        LoraSubTask mainTask = callback.getMainTask();

        //状态未同步
        if (mainTask == null || mainTask.getStatus() != QueueCodeEnum.COMPLETED) {
            List<LoraSubTask> subTasks = callback.getSubTasks();
            LoraSubTask task = needLatest ? getLatestTask(subTasks) : getEarliestTask(subTasks);

            //如果当前子任务有未完成的，则状态不同步
            if (task == null) {
                return;
            }

            callback.setSubTasks(task);

            isPreCompleted = task.getStatus() == QueueCodeEnum.COMPLETED;
        }

        preCompleted[0] = isPreCompleted;
    }

    private ComfyuiTaskVO buildTask(MaterialModelVO model, ComfyuiTaskTypeEnum type, IExtModel preTask) {
        ComfyuiTaskVO task = new ComfyuiTaskVO();
        task.setUserId(model.getUserId());
        task.setOperatorId(model.getOperatorId());
        task.setTaskType(type);
        task.setReqParams(CommonUtil.java2JSONObject(model.getClothLoraTrainDetail()));
        if (preTask != null) {
            task.addExtInfo(KEY_SERVER_ID, preTask.getExtValue(KEY_SERVER_ID, Integer.class));
            task.addExtInfo(KEY_SERVER_URL, preTask.getExtValue(KEY_SERVER_URL, String.class));
        }
        task.addExtInfo(KEY_MODEL_ID, model.getId());

        return task;
    }

    private ComfyuiTaskVO handleTask(LoraTrainDetail clothLoraTrainDetail, LoraSubTask sub, MaterialModelVO model,
                                     ComfyuiTaskTypeEnum t1, BiConsumer<LoraTrainDetail, LoraSubTask> s1,
                                     IExtModel preTask) {

        log.info("handleTask,sub={},t1={}", sub, t1);

        if (sub == null) {
            log.info("{}任务未创建，开始创建", t1.getDesc());
            ComfyuiTaskVO task = comfyuiTaskService.createTask(buildTask(model, t1, preTask));
            s1.accept(clothLoraTrainDetail, LoraSubTask.fromComfyuiTask(task));
            return task;
        }

        //子任务未完成,继续推进
        if (sub.getStatus() != QueueResult.QueueCodeEnum.COMPLETED) {
            log.info("【{}任务未完成,继续推进】", t1.getDesc());
            ComfyuiTaskVO comfyuiTaskVO = comfyuiTaskService.pollingTaskStatus(sub.getTaskId());
            sub.setStatus(comfyuiTaskVO.getTaskStatus());
            sub.setServerUrl(comfyuiTaskVO.getExtValue(KEY_SERVER_URL, String.class));
            s1.accept(clothLoraTrainDetail, sub);

            return comfyuiTaskVO;
        } else {

            //任务已经完成，进下一条
            log.info("【{}任务已经完成】", t1.getDesc());
            return comfyuiTaskService.selectById(sub.getTaskId());
        }
    }

    private boolean onLoraSuccess(ComfyuiTaskVO loraTask, LoraTrainDetail detail, MaterialModelVO materialModelVO,
                                  ComfyuiTaskVO labelTask) {
        log.info("pollingLoraStatus, lora成功, detail:{}", detail);

        AssertUtil.assertNotNull(loraTask, "lora训练成功但loraTask为空");

        //如果是类型是flux且lora的oss地址没有带flux标，说明是订正的数据，需要重新上传lora
        if (!MaterialModelUtils.alreadyUploadedLora(detail) || (StringUtils.equals(detail.getLoraType(),
            CommonConstants.FLUX) && !StringUtils.contains(detail.getLoraRetFileUrl(), "-flux"))) {

            String loraFilePath = detail.getLoraModelRetFilePath();
            AssertUtil.assertNotBlank(loraFilePath, "loraFilePath为空");

            String fileServerUrl = serverHelper.getFileServerUrlByTask(loraTask);

            if (!EnvUtil.isLocalEnv()) {
                // 1.发送上传lora文件到oss的事件
                log.info("【Lora上传oss事件】发送延时消息，下载并备份lora到oss事件,loraFileUrl={},filePath={}",
                    detail.getLoraRetFileUrl(), loraFilePath);
                LoraUploadEvent event = new LoraUploadEvent(loraFilePath, fileServerUrl, materialModelVO.getId());
                rocketMQTemplate.syncSendOrderly(EventConstants.TOPIC_UPLOAD_LORA_TO_OSS,
                    MessageBuilder.withPayload(event).build(), "lora-upload-oss", 3000, 0);
            }
        }

        materialModelVO.setStatus(MaterialModelStatusEnum.TESTING.getCode());

        JSONObject loraRet = CommonUtil.parseObject(loraTask.getRetDetail());
        AssertUtil.assertNotNull(loraRet, "lora成功但task.retDetail为空或非json");

        //lora文件名
        materialModelVO.setLoraName(getLoraName(detail));
        AssertUtil.assertNotNull(labelTask, "lora成功但labelTask为空");

        LabelTaskRetDetail labelRet = CommonUtil.parseObject(labelTask.getRetDetail(), LabelTaskRetDetail.class);
        AssertUtil.assertNotNull(labelRet, "lora成功但labelRet为空或非json");

        if (StringUtils.isNotBlank(labelRet.getTags())) {
            materialModelVO.setTags(labelRet.getTags());
        }
        JSONObject ext = materialModelVO.getExtInfo();
        if (ext == null) {
            ext = new JSONObject();
        }

        //设置多角度的补充激活信息
        if (CollectionUtils.isNotEmpty(labelRet.getExtTagsList())) {
            String version = StringUtils.equals(detail.getLoraType(), CommonConstants.FLUX)
                ? ModelVersionEnum.FLUX.getCode() : ModelVersionEnum.SDXL.getCode();

            List<ClothTypeConfig> clothTypeConfigs = MaterialModelConverter.convert2ClothTypeConfig(
                labelRet.getExtTagsList(), version, materialModelVO.getTags(), detail.getActivateKey());
            ext.put(KEY_CLOTH_TYPE_CONFIGS,
                JSONObject.toJSONString(clothTypeConfigs, Feature.IgnoreErrorGetter.ordinal()));
            String garmentType = getGarmentType(clothTypeConfigs, materialModelVO);
            if (StringUtils.isNotBlank(garmentType)) {
                ext.put(KEY_GARMENT_TYPE, garmentType);
            }
            if (clothTypeConfigs != null) {
                boolean includesBra = clothTypeConfigs.stream().anyMatch(ClothTypeConfig::isIncludesBra);
                ext.put(KEY_INCLUDES_BRA, includesBra ? YES : NO);
            }
        }

        if (CollectionUtils.isNotEmpty(labelRet.getColorImages())) {
            ext.put(KEY_CLOTH_COLOR_IMAGES, JSONArray.toJSONString(labelRet.getColorImages()));
        }

        if (StringUtils.isNotBlank(labelRet.getFeatures())) {
            ext.put(CommonConstants.features, labelRet.getFeatures());
        }
        if (labelRet.getUserPreferFeatures() != null) {
            ext.put(CommonConstants.userPreferFeatures, JSONObject.toJSONString(labelRet.getUserPreferFeatures()));
        }
        if (labelRet.getAiGenFeatures() != null) {
            ext.put(CommonConstants.aiGenFeatures, JSONObject.toJSONString(labelRet.getAiGenFeatures()));
        }
        log.info("补充特征：{}", ext.toJSONString());

        //自动交付
        autoDelivery(materialModelVO, ext);

        materialModelVO.setExtInfo(ext);

        return true;
    }

    /**
     * 初始化背面属性
     *
     * @param dataList 列表
     */
    @SuppressWarnings("DataFlowIssue")
    private void initBackViewProps(List<MaterialModelVO> dataList) {
        List<Integer> ids = dataList.stream().filter(
            e -> e.getClothLoraTrainDetail() != null && e.getClothLoraTrainDetail().getLabel() != null).map(
            e -> e.getClothLoraTrainDetail().getLabel().getTaskId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<ComfyuiTaskVO> taskList = comfyuiTaskService.batchQueryByIds(ids);

        for (MaterialModelVO model : dataList) {
            if (model.getClothLoraTrainDetail() == null || model.getClothLoraTrainDetail().getLabel() == null) {
                continue;
            }

            ComfyuiTaskVO task = taskList.stream().filter(
                info -> info.getId().equals(model.getClothLoraTrainDetail().getLabel().getTaskId())).findFirst().orElse(
                null);

            if (task != null && task.getRetDetail() != null) {
                LabelTaskRetDetail labelRet = CommonUtil.parseObject(task.getRetDetail(), LabelTaskRetDetail.class);
                AssertUtil.assertNotNull(labelRet, "lora成功但labelRet为空或非json");

                //只要有一个有back view，则说明包含背面
                boolean hasBackView = labelRet.getLabelFiles() != null && labelRet.getLabelFiles().stream().anyMatch(
                    file -> StringUtils.contains(file.getTextContent(), CameraAngleEnum.BACK_VIEW.getCode()));

                model.setHasBackView(hasBackView);
            }

            if (!model.isHasBackView() && CollectionUtils.isNotEmpty(model.getClothTypeConfigs())
                && model.getClothTypeConfigs().stream().anyMatch(
                config -> config.getType().contains(CameraAngleEnum.BACK_VIEW.getCode()))) {
                log.info("从中model模型中获取到背面信息，id={}", model.getId());
                model.setHasBackView(true);
            }
        }
    }

    private void fillClothMaterialInfo(List<MaterialModelVO> items) {
        List<Integer> materialInfoIds = new ArrayList<>();
        for (MaterialModelVO item : items) {
            if (item.getClothLoraTrainDetail() != null
                && item.getClothLoraTrainDetail().getOriginalMaterialId() != null) {
                materialInfoIds.add(item.getClothLoraTrainDetail().getOriginalMaterialId());
            }
        }

        if (CollectionUtils.isEmpty(materialInfoIds)) {
            log.warn("没有找到对应的素材信息列表");
            return;
        }

        List<MaterialInfoVO> materialInfoList = materialInfoService.batchQueryByIds(materialInfoIds);
        Map<Integer, MaterialInfoVO> materialInfoMap = materialInfoList.stream().collect(
            Collectors.toMap(MaterialInfoVO::getId, Function.identity()));
        for (MaterialModelVO item : items) {
            if (item.getClothLoraTrainDetail() != null
                && item.getClothLoraTrainDetail().getOriginalMaterialId() != null) {
                MaterialInfoVO originalMaterial = materialInfoMap.get(
                    item.getClothLoraTrainDetail().getOriginalMaterialId());
                //补充的上传素材原始信息
                if (originalMaterial != null) {
                    //服装分类（上下装等）
                    if (originalMaterial.getType().equals(MaterialType.cloth.name())
                        && originalMaterial.getSubType() != null) {
                        ClothTypeEnum clothTypeEnum = Objects.requireNonNull(
                            ClothTypeEnum.getByCode(originalMaterial.getSubType()));
                        item.setClothType(clothTypeEnum.getCode());
                        item.setClothTypeDesc(clothTypeEnum.getDesc());
                    }
                }
            }
        }
    }

    /**
     * 自动训练
     *
     * @param model  模型信息
     * @param detail 训练详情
     * @return true，自动训练
     */
    private boolean autoTrain(MaterialModelVO model, LoraTrainDetail detail) {
        //判断是否在白名单中
        if (!systemConfigService.isAutoTrain(model.getUserId()) && !StringUtils.equals(YES, detail.getAutoTrain())) {
            return false;
        }

        log.info("当前服装归属用户{}在自动训练白名单中，自动执行确认训练操作", model.getUserId());

        ConfirmLoraReq req = new ConfirmLoraReq();
        req.setId(model.getId());
        req.setLoraType(CommonConstants.FLUX);

        // 设置最大训练步数
        req.setMaxTrainStep(
            EnvUtil.isLocalEnv() ? 1 : (StringUtils.equals(YES, detail.getMultiColors()) ? 3000 : 2500));

        // 设置分辨率
        req.setResolution(StringUtils.isNotBlank(detail.getResolution()) ? detail.getResolution() : "1024");

        // 设置rank为空
        req.setRank(StringUtils.isNotBlank(detail.getRank()) ? detail.getRank() : "32");

        //场景lora默认style类型，其他content
        String contentOrStyle = "content";
        //if (StringUtils.equals(detail.getMaterialType(), MaterialType.scene.name())) {
        //    log.info("自动训练，场景lora默认为style,id={}", model.getId());
        //    contentOrStyle = "style";
        //}
        req.setContentOrStyle(
            StringUtils.isNotBlank(detail.getContentOrStyle()) ? detail.getContentOrStyle() : contentOrStyle);

        // 设置alpha
        req.setAlpha(StringUtils.isNotBlank(detail.getAlpha()) ? detail.getAlpha() : "16");

        // 设置dropout
        req.setDropout(StringUtils.isNotBlank(detail.getDropout()) ? detail.getDropout() : "0.2");

        // 设置lr
        req.setLr(StringUtils.isNotBlank(detail.getLr()) ? detail.getLr() : "0.00020");

        List<MerchantPreferenceVO> configs = merchantPreferenceService.queryDeliveryPreference(model.getUserId());
        //计算命中的偏好配置
        MerchantPreferenceVO hit = matchPreference(configs, model);
        if (hit != null) {
            req.setTestNum(3);
            req.setTestFaces(hit.getFaces());
            req.setTestScenes(hit.getScenes());
            req.setTestClothCollocation(hit.getClothCollocation());
        }

        //执行确认训练流程
        OperationContext mockContext = CommonUtil.mockAutoCreativeContext();
        confirmTrainLora(req, model, detail, mockContext.getCurrentUserId(), mockContext.getUserNick());

        model.addExtInfo(KEY_AUTO_TRAIN, true);

        return true;
    }

    /**
     * 计算匹配的偏好
     *
     * @param configs 配置
     * @param model   模型
     * @return 匹配命中的偏好
     */
    private MerchantPreferenceVO matchPreference(List<MerchantPreferenceVO> configs, MaterialModelVO model) {
        String clothStyleType = model.getExtInfo(CommonConstants.clothStyleType, String.class);
        if (StringUtils.equals(unisex, clothStyleType)) {
            clothStyleType = female;
        }
        String faceGender = clothStyleType + "-model";
        String sceneGender = CommonUtil.capitalizeFirstLetter(clothStyleType);

        List<Integer> faces = new ArrayList<>();
        List<Integer> scenes = new ArrayList<>();

        for (MerchantPreferenceVO config : configs) {
            List<Integer> ids = new ArrayList<>();
            //fix npe
            if (CollectionUtils.isNotEmpty(config.getFaces())) {
                ids.addAll(config.getFaces());
            }

            //fix npe
            if (CollectionUtils.isNotEmpty(config.getScenes())) {
                ids.addAll(config.getScenes());
            }

            if (CollectionUtils.isEmpty(ids)) {
                continue;
            }

            List<CreativeElementVO> elements = creativeElementService.batchQueryByIds(ids);
            for (CreativeElementVO e : elements) {
                if (StringUtils.equals(e.getConfigKey(), ElementConfigKeyEnum.FACE.name()) && e.getType().contains(
                    ModelVersionEnum.FLUX.getCode()) && e.getType().contains(faceGender)) {
                    faces.add(e.getId());
                }

                if (StringUtils.equals(e.getConfigKey(), ElementConfigKeyEnum.SCENE.name()) && e.getType().contains(
                    ModelVersionEnum.FLUX.getCode()) && e.getType().contains(sceneGender)) {
                    scenes.add(e.getId());
                }
            }

            if (CollectionUtils.isNotEmpty(faces) && CollectionUtils.isNotEmpty(scenes)) {
                MerchantPreferenceVO preference = new MerchantPreferenceVO();
                preference.setFaces(faces);
                preference.setScenes(scenes);
                preference.setClothCollocation(config.getClothCollocation());
                log.info("自动训练判断是否命中偏好,Y,modelId={},preference={}", model.getId(), preference);
                return preference;
            }
        }

        log.info("自动训练判断是否命中偏好,N,modelId={},不会执行自动出图", model.getId());
        return null;
    }

    /**
     * 自动交付
     *
     * @param model 模型信息
     * @param ext   扩展信息
     */
    private void autoDelivery(MaterialModelVO model, JSONObject ext) {
        //判断是否在白名单中
        if (!systemConfigService.isInJsonArray(AUTO_DELIVERY_MERCHANT, model.getUserId())) {
            return;
        }

        ext.put(KEY_AUTO_DELIVERY, true);

        //线下环境直接变更状态
        if (EnvUtil.isLocalEnv()) {
            log.info("当前服装归属用户{}在自动交付白名单中，自动执行审核通过操作", model.getUserId());
            model.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
            ext.put(KEY_DELIVERY_TIME, DateUtils.formatTime(new Date()));

            //同步打标到自动生成图片任务
            if (model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getAutoGenImg()) {
                syncConfirmDeliver2AutoGenTask(model);
                model.getClothLoraTrainDetail().setConfirmCanDeliverInfo(getConfirmCanDeliveryInfo(null, null));
            }

            return;
        }

        //如果商家选择了上传服装时自动出图，则同步到自动生成图片任务中，由自动生成图片任务Job调度触发生成图片和变更状态
        if (model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getAutoGenImg()) {
            syncConfirmDeliver2AutoGenTask(model);
            model.getClothLoraTrainDetail().setConfirmCanDeliverInfo(getConfirmCanDeliveryInfo(null, null));

            //否则，延时20分钟直接交付
        } else {

            //延时20分钟发送自动交付请求
            // messageDelayLevel	1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
            log.info("当前服装归属用户{}在自动交付白名单中，发送延迟消息完成自动交付，20分钟后将自动变更状态",
                model.getUserId());
            rocketMQTemplate.syncSend(EventConstants.TOPIC_AUTO_DELIVERY,
                MessageBuilder.withPayload(new AutoDeliveryEvent(model.getId())).build(), 3000, 15);
        }
    }

    /**
     * 获取服装类型
     *
     * @param clothTypeConfigs 服装类型配置
     * @param materialModelVO  服装模型
     * @return 服装类型
     */
    private String getGarmentType(List<ClothTypeConfig> clothTypeConfigs, MaterialModelVO materialModelVO) {
        if (CollectionUtils.isEmpty(clothTypeConfigs)) {
            return null;
        }

        //优先取正面全身
        ClothTypeConfig frontWhole = clothTypeConfigs.stream().filter(
            e -> e.getType().contains(CameraAngleEnum.FRONT_VIEW.getCode()) && e.getType()
                .contains(CameraAngleEnum.WHOLE_BODY.getCode())).findFirst().orElse(null);

        if (frontWhole != null && CollectionUtils.isNotEmpty(frontWhole.getColorList())) {
            String garmentType = frontWhole.getColorList().get(0).getGarmentType();

            initGarmentTypeDict(garmentType);

            return garmentType;
        }

        for (ClothTypeConfig config : clothTypeConfigs) {
            if (CollectionUtils.isNotEmpty(config.getColorList())) {
                log.warn("未获取到正面全身的服装类型,直接取第一个值,id={}", materialModelVO.getId());
                String garmentType = config.getColorList().get(0).getGarmentType();

                initGarmentTypeDict(garmentType);

                return garmentType;
            }
        }

        log.error("未获取到正面全身的服装类型,返回null,id={}", materialModelVO.getId());

        return null;
    }

    /**
     * 初始化服装类型字典
     *
     * @param garmentType 服装类型
     */
    private void initGarmentTypeDict(String garmentType) {
        if (StringUtils.isBlank(garmentType)) {
            return;
        }
        PromptDictVO promptDict = promptDictService.queryByTypeAndPrompt(DictTypeEnum.GARMENT_TYPE, garmentType);
        if (promptDict != null) {
            return;
        }

        promptDict = new PromptDictVO();
        promptDict.setType(DictTypeEnum.GARMENT_TYPE);
        promptDict.setTags(Collections.singletonList(DictTagsEnum.SYSTEM.getCode()));
        promptDict.setPrompt(garmentType);
        promptDict.setWord(garmentType);
        promptDictService.insert(promptDict);
    }

    /**
     * 安全地解析对象为Integer，如果解析失败则返回默认值
     *
     * @param obj          要解析的对象
     * @param defaultValue 默认值
     * @return Integer值
     */
    private Integer parseIntegerOrDefault(Object obj, Integer defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        try {
            return obj instanceof String ? Integer.valueOf((String)obj)
                : obj instanceof Number ? ((Number)obj).intValue() : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 构建ImageCaseVO对象
     *
     * @param req 请求参数
     * @return ImageCaseVO对象
     */
    private ImageCaseVO buildImageCaseVO(SyncImageCaseReq req) {
        ImageCaseVO imageCaseVO = new ImageCaseVO();
        imageCaseVO.setUrl(req.getUrl());
        imageCaseVO.setStorePath(req.getFilePath());
        imageCaseVO.setStatus(MaterialModelStatusEnum.ENABLED);
        imageCaseVO.setCreateTime(new Date());
        imageCaseVO.setModifyTime(new Date());
        imageCaseVO.setSyncStatus(false);
        imageCaseVO.setReSyncCount(0);
        imageCaseVO.setMemo("人工介入上传服饰图片...");

        // 设置操作人
        Integer currentUserId = OperationContextHolder.getContext().getCurrentUserId();
        imageCaseVO.setUserId(currentUserId);
        imageCaseVO.setOperatorId(currentUserId);

        // 构建扩展信息
        JSONObject extInfo = new JSONObject();
        Optional.ofNullable(req.getFileDir()).ifPresent(v -> extInfo.put(CommonConstants.FILE_DIR, v));
        Optional.ofNullable(req.getFileName()).ifPresent(v -> extInfo.put(CommonConstants.FILE_NAME, v));
        Optional.ofNullable(req.getImgUrl()).ifPresent(v -> extInfo.put(CommonConstants.IMG_URL, v));
        Optional.ofNullable(req.getTextContent()).ifPresent(v -> extInfo.put(CommonConstants.TEXT_CONTENT, v));
        Optional.ofNullable(req.getType()).ifPresent(v -> extInfo.put(CommonConstants.TYPE, v));
        Optional.ofNullable(req.getTrainingMaterial()).ifPresent(v -> extInfo.put(CommonConstants.IS_NEED_UPLOAD, v));
        imageCaseVO.setExtInfo(extInfo);

        // 返回构建完成的ImageCaseVO对象
        return imageCaseVO;
    }

    /**
     * 批量插入图片案例标签
     *
     * @param imageCaseId      图片案例ID
     * @param trainingMaterial 训练材料
     */
    private void insertImageCaseTags(Integer imageCaseId, Boolean trainingMaterial) {
        List<PromptDictVO> promptDictList = promptDictService.queryListByType(DictTypeEnum.IMAGE_TAGS,
            ImageCaseTypeEnum.MANUAL_REPLACEMENT);

        if (CollectionUtils.isEmpty(promptDictList)) {
            log.info("[服饰图片同步]MaterialModelServiceImpl::insertImageCaseTags::没有找到对应的标签信息");
            return;
        }

        log.info(
            "[服饰图片同步]MaterialModelServiceImpl::insertImageCaseTags::开始批量插入标签, imageCaseId: {}, tagCount: {}",
            imageCaseId, promptDictList.size());

        // 批量构建标签对象
        List<ImageCaseTagDO> tagList = promptDictList.stream().map(
            dict -> new ImageCaseTagDO(imageCaseId, dict.getId())).collect(Collectors.toList());

        // 若为训练材料
        if (trainingMaterial) {
            // 获取同步到服务器的标签
            PromptDictVO promptDictVO = promptDictService.queryByTypeAndPrompt(DictTypeEnum.IMAGE_TAGS,
                CommonConstants.NEED);

            // 批量构建标签对象
            if (promptDictVO != null) {
                tagList.add(new ImageCaseTagDO(imageCaseId, promptDictVO.getId()));
            }
        }

        // 批量插入标签
        tagList.forEach(imageCaseDAO::insertTag);

        log.info(
            "[服饰图片同步]MaterialModelServiceImpl::insertImageCaseTags::批量插入标签完成, imageCaseId: {}, insertedCount: {}",
            imageCaseId, tagList.size());
    }

    /**
     * 获取可用的文件服务
     *
     * @param storeServerUrl 存储服务器地址
     * @param modelId        模型idf
     * @param userId         用户id
     * @return 文件服务地址
     */
    private String fetchEnableFileServer(String storeServerUrl, Integer modelId, Integer userId) {
        String fileServerUrl = serverHelper.getFileServerUrl(storeServerUrl);
        if (StringUtils.isNotBlank(fileServerUrl)) {
            return fileServerUrl;
        }

        ServerVO fileServer = serverHelper.getFileServerByUserId(userId, DispatchTypeEnum.LORA_PRE_PROCESS);
        fileServerUrl = serverHelper.getFileServerUrl(fileServer);
        log.warn("找不到对应可用的文件服务，开始查找其他对应服务器，id={}，origin={},target={}", modelId, storeServerUrl,
            fileServerUrl);
        return fileServerUrl;
    }

    /**
     * 获取可用的文件服务，且指定的文件夹目录必须存在
     */
    private String fetchEnableFileServerExistPath(String storeServerUrl, Integer userId, String mustExistPath) {
        AssertUtil.assertNotBlank(mustExistPath, "mustExistPath不能为空");

        if (StringUtils.isNotBlank(storeServerUrl)) {
            String fileServerUrl = serverHelper.getFileServerUrl(storeServerUrl);

            if (StringUtils.isNotBlank(fileServerUrl) && comfyUIService.checkFolderExists(mustExistPath,
                fileServerUrl)) {
                return fileServerUrl;
            }
        }

        List<ServerVO> allFileServers = serverHelper.getFileServersByUserId(userId, DispatchTypeEnum.LORA_PRE_PROCESS);
        for (ServerVO server : allFileServers) {
            if (comfyUIService.checkFolderExists(mustExistPath, serverHelper.getFileServerUrl(server))) {
                return serverHelper.getFileServerUrl(server);
            }
        }

        log.error("fetchEnableFileServerExistPath,找不到对应可用的文件服务，origin={}, userId={}, mustExistPath={}",
            storeServerUrl, userId, mustExistPath);
        return null;
    }

    /**
     * 获取高清的正面图url
     *
     * @param model 服装模型
     * @return url
     */
    private String getHighResModelShowImgUrl(MaterialModelVO model) {
        if (model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getLabel() != null
            && model.getClothLoraTrainDetail().getLabel().getStatus() == QueueResult.QueueCodeEnum.COMPLETED) {

            ComfyuiTaskVO task = comfyuiTaskService.selectById(model.getClothLoraTrainDetail().getLabel().getTaskId());
            return comfyuiTaskService.getFullBodyFrontImgUrlByLabelRet(task);
        }
        return null;
    }

    /**
     * 模型处理阶段接口
     */
    private interface ModelProcessStage {
        /**
         * 处理当前阶段
         *
         * @param context 处理上下文
         * @return 处理结果，true表示成功继续，false表示终止链
         */
        boolean process(ModelProcessContext context);

        /**
         * 设置下一个处理阶段
         *
         * @param next 下一个处理阶段
         */
        void setNext(ModelProcessStage next);
    }

    /**
     * 模型处理上下文，用于在责任链各阶段之间传递数据
     */
    private class ModelProcessContext {
        private final LoraTrainDetail detail;
        private final MaterialModelVO model;
        private ComfyuiTaskVO prepareViewTask;
        private ComfyuiTaskVO cutoutTask;
        private ComfyuiTaskVO labelTask;
        private ComfyuiTaskVO loraTask;
        private boolean success = false;

        public ModelProcessContext(LoraTrainDetail detail, MaterialModelVO model) {
            this.detail = detail;
            this.model = model;
        }
    }

    /**
     * 抽象处理阶段，实现基本的责任链逻辑
     */
    private abstract class AbstractModelProcessStage implements ModelProcessStage {
        protected ModelProcessStage next;

        @Override
        public void setNext(ModelProcessStage next) {
            this.next = next;
        }

        /**
         * 继续处理链中的下一个阶段
         *
         * @param context 处理上下文
         * @return 处理结果
         */
        protected boolean processNext(ModelProcessContext context) {
            if (next != null) {
                return next.process(context);
            }
            return context.success;
        }
    }

    /**
     * 预处理阶段(PrepareView)
     */
    private class PrepareViewStage extends AbstractModelProcessStage {
        @Override
        public boolean process(ModelProcessContext context) {
            LoraTrainDetail detail = context.detail;
            MaterialModelVO model = context.model;

            // 判断是否需要预处理
            if (MaterialType.scene.name().equals(detail.getMaterialType()) && !StringUtils.equals(
                detail.getPreprocessCensoredFace(), YES)) {
                // 不需要预处理，直接进入下一阶段
                log.info("模型id={}不需要预处理，跳过PrepareView阶段", model.getId());
                return processNext(context);
            }

            // 执行预处理任务
            ComfyuiTaskVO prepareViewTask = handleTask(detail, detail.getPrepareView(), model,
                ComfyuiTaskTypeEnum.prepareView, LoraTrainDetail::setPrepareView, null);

            context.prepareViewTask = prepareViewTask;

            // 判断预处理任务是否完成
            if (prepareViewTask == null || prepareViewTask.getTaskStatus() != QueueResult.QueueCodeEnum.COMPLETED) {
                log.info("模型id={}预处理任务未完成，终止处理链", model.getId());
                return false;
            }

            // 预处理完成，继续下一阶段
            return processNext(context);
        }
    }

    /**
     * 抠图阶段(Cutout)
     */
    private class CutoutStage extends AbstractModelProcessStage {
        @Override
        public boolean process(ModelProcessContext context) {
            LoraTrainDetail detail = context.detail;
            MaterialModelVO model = context.model;
            ComfyuiTaskVO prepareViewTask = context.prepareViewTask;

            // 判断是否有水印
            if (StringUtils.isNotBlank(detail.getWaterMarkDesc()) || StringUtils.isNotBlank(
                detail.getWatermarkOrigin())) {
                // 有水印，初始化水印信息
                if (StringUtils.isBlank(detail.getWatermarkOrigin())) {
                    detail.setWatermarkOrigin(WaterMarkUtils.parseOriginFromDesc(detail.getWaterMarkDesc()));
                    detail.setWatermarkWidth(30);
                    detail.setWatermarkHeight(10);
                    detail.setWaterMarkDesc(null);
                }
                // 执行抠图任务
                ComfyuiTaskVO cutoutTask = handleTask(detail, detail.getCutout(), model, ComfyuiTaskTypeEnum.cutout,
                    LoraTrainDetail::setCutout, prepareViewTask);

                context.cutoutTask = cutoutTask;

                // 判断抠图任务是否完成
                if (cutoutTask == null || cutoutTask.getTaskStatus() != QueueResult.QueueCodeEnum.COMPLETED) {
                    log.info("模型id={}抠图任务未完成，终止处理链", model.getId());
                    return false;
                }

                // 抠图完成，继续下一阶段
                return processNext(context);
            } else {
                return processNext(context);
            }
        }
    }

    /**
     * 打标和训练阶段(Label and Lora)
     */
    private class LabelAndLoraStage extends AbstractModelProcessStage {
        @Override
        public boolean process(ModelProcessContext context) {
            LoraTrainDetail detail = context.detail;
            MaterialModelVO model = context.model;
            ComfyuiTaskVO cutoutTask = context.cutoutTask;

            // 执行打标和训练
            boolean success = handleLabelAndLora(detail, model, cutoutTask);
            context.success = success;

            return success;
        }
    }

    /**
     * 模型处理责任链
     */
    private class ModelProcessChain {
        private final ModelProcessStage firstStage;
        private final ModelProcessContext context;

        public ModelProcessChain(ModelProcessStage firstStage, ModelProcessContext context) {
            this.firstStage = firstStage;
            this.context = context;
        }

        /**
         * 执行处理链
         *
         * @return 处理结果
         */
        public boolean process() {
            if (firstStage != null) {
                return firstStage.process(context);
            }
            return false;
        }
    }

    /**
     * 模型处理责任链构建器
     */
    private class ModelProcessChainBuilder {
        private final ModelProcessContext context;
        private ModelProcessStage firstStage;
        private ModelProcessStage lastStage;

        public ModelProcessChainBuilder(LoraTrainDetail detail, MaterialModelVO model) {
            this.context = new ModelProcessContext(detail, model);
        }

        /**
         * 添加预处理阶段
         */
        public ModelProcessChainBuilder withPrepareViewStage() {
            PrepareViewStage stage = new PrepareViewStage();
            addStage(stage);
            return this;
        }

        /**
         * 添加抠图阶段
         */
        public ModelProcessChainBuilder withCutoutStage() {
            CutoutStage stage = new CutoutStage();
            addStage(stage);
            return this;
        }

        /**
         * 添加打标和训练阶段
         */
        public ModelProcessChainBuilder withLabelAndLoraStage() {
            LabelAndLoraStage stage = new LabelAndLoraStage();
            addStage(stage);
            return this;
        }

        /**
         * 添加处理阶段到责任链
         */
        private void addStage(ModelProcessStage stage) {
            if (firstStage == null) {
                firstStage = stage;
                lastStage = stage;
            } else {
                lastStage.setNext(stage);
                lastStage = stage;
            }
        }

        /**
         * 构建责任链
         */
        public ModelProcessChain build() {
            return new ModelProcessChain(firstStage, context);
        }
    }

    private MaterialModelVO cloneMaterialModel(String name, MaterialModelVO model, MaterialInfoVO copyMaterial,
                                               boolean fullCopy, Integer mainId) {
        MaterialModelVO copyModel = new MaterialModelVO();
        copyModel.setName(name);
        copyModel.setUserId(OperationContextHolder.getMasterUserId());
        copyModel.setOperatorId(OperationContextHolder.getOperatorUserId());
        copyModel.setStatus(MaterialModelStatusEnum.IN_TRAINING.getCode());
        copyModel.setShowImage(model.getShowImage());
        copyModel.setMainType(model.getMainType());
        if (mainId != null) {
            copyModel.setMainId(mainId);
        }
        copyModel.setMaterialType(model.getMaterialType());

        LoraTrainDetail originTrainDetail = model.getClothLoraTrainDetail();
        LoraTrainDetail trainDetail = fullCopy ? CommonUtil.deepCopy(originTrainDetail) : new LoraTrainDetail();

        String uploadRootDirName = MaterialUploadUtil.getMaterialUploadBaseDir();
        String clothDirName = String.format("%s_%s_%s", CommonUtil.removeWhitespace(name), copyMaterial.getId(),
            new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()));

        String targetClothDir = String.format("/home/<USER>/aigc/ComfyUI/input/%s/%s", uploadRootDirName,
            clothDirName);

        trainDetail.setUploadRootDirName(uploadRootDirName);
        trainDetail.setClothSubDir(clothDirName);
        trainDetail.setClothDir(targetClothDir);
        trainDetail.setOriginalMaterialId(copyMaterial.getId());
        trainDetail.setOriginalClothName(name);
        trainDetail.setOriginClothType(copyMaterial.getSubType());
        trainDetail.setClothStyleType(originTrainDetail.getClothStyleType());
        trainDetail.setMultiColors(originTrainDetail.getMultiColors());
        trainDetail.setMatchPrefer(originTrainDetail.getMatchPrefer());
        trainDetail.setClothType(originTrainDetail.getClothType());
        trainDetail.setMaterialType(originTrainDetail.getMaterialType());
        trainDetail.setClothDetailsPrompt(originTrainDetail.getClothDetailsPrompt());
        trainDetail.setCutoutOnlyUpscale(originTrainDetail.getCutoutOnlyUpscale());
        trainDetail.setTrainExtInfo(originTrainDetail.getTrainExtInfo());
        trainDetail.setActivateKey(originTrainDetail.getActivateKey());
        trainDetail.setRepeatTimes(originTrainDetail.getRepeatTimes());
        trainDetail.setCaptionPrompt(originTrainDetail.getCaptionPrompt());
        trainDetail.setColorNumber(originTrainDetail.getColorNumber());
        trainDetail.setCut4ScaleUp(originTrainDetail.getCut4ScaleUp());

        //克隆原始模型的服装目录到新目录（必须要有打标任务，才会复制）
        String originalClothDir = originTrainDetail.getClothDir();
        if (StringUtils.isNotBlank(originalClothDir)) {

            if (model.getMainType() != MainTypeEnum.MAIN) {
                // 之后NORMAL和SUB才进行复制
                String fileServerUrl = fetchEnableFileServerExistPath(originTrainDetail.getLabel().getServerUrl(),
                    model.getUserId(), originalClothDir);

                if (StringUtils.isBlank(fileServerUrl)) {
                    log.error("找不到可用的文件服务去复制目录:{}，克隆失败!", originalClothDir);
                    throw new BizException("找不到可用的文件服务去复制目录");
                }

                fileDispatch.folderCopy(originalClothDir, targetClothDir, fileServerUrl);
            }

            if (originTrainDetail.getPrepareView() != null) {
                ComfyuiTaskVO task = cloneComfyuiTaskVO(
                    comfyuiTaskService.selectById(originTrainDetail.getPrepareView().getTaskId()), trainDetail);
                trainDetail.setPrepareView(LoraSubTask.fromComfyuiTask(task));
            }

            if (originTrainDetail.getCutout() != null) {
                ComfyuiTaskVO task = cloneComfyuiTaskVO(
                    comfyuiTaskService.selectById(originTrainDetail.getCutout().getTaskId()), trainDetail);
                trainDetail.setCutout(LoraSubTask.fromComfyuiTask(task));
            }

            if (originTrainDetail.getLabel() != null) {
                ComfyuiTaskVO task = cloneComfyuiTaskVO(
                    comfyuiTaskService.selectById(originTrainDetail.getLabel().getTaskId()), trainDetail);
                trainDetail.setLabel(LoraSubTask.fromComfyuiTask(task));
            }
        }

        trainDetail.setLoraModelDir(MaterialUploadUtil.getLoraBaseDir() + clothDirName + "/");

        if (fullCopy) {
            copyModel.setLoraName(model.getLoraName());
            copyModel.setTags(model.getTags());
            copyModel.setVersion(model.getVersion());
            copyModel.setExtInfo(model.getExtInfo());
            copyModel.setStatus(model.getStatus());
        } else {
            JSONObject ext = new JSONObject();

            if (model.getExtInfo() != null) {
                if (model.getExtInfo().containsKey(highResModelShowImgUrl)) {
                    ext.put(highResModelShowImgUrl, model.getExtInfo().getString(highResModelShowImgUrl));
                }
                ext.put(CommonConstants.clothStyleType,
                    StringUtils.defaultIfBlank(model.getExtInfo().getString(CommonConstants.clothStyleType),
                        CommonConstants.female));
                ext.put(KEY_LABEL_TYPE, model.getExtInfo().getString(KEY_LABEL_TYPE));
            }
            ext.put(KEY_AGE_RANGE, model.getExtInfo(KEY_AGE_RANGE, String.class));

            copyModel.setExtInfo(ext);
        }

        copyModel.setClothLoraTrainDetail(trainDetail);
        return copyModel;
    }

    private ComfyuiTaskVO cloneComfyuiTaskVO(ComfyuiTaskVO task, LoraTrainDetail newTrainDetail) {
        ComfyuiTaskVO clone = new ComfyuiTaskVO();
        clone.setUserId(OperationContextHolder.getOperatorUserId());
        clone.setOperatorId(OperationContextHolder.getOperatorUserId());
        clone.setTaskType(task.getTaskType());
        clone.setTaskStatus(task.getTaskStatus());
        clone.setExtInfo(task.getExtInfo());
        //将原来的模型id删除
        if (clone.getExtInfo() != null) {
            clone.getExtInfo().remove(KEY_MODEL_ID);
        }

        String retDetail = task.getRetDetail();
        if (CommonUtil.isValidJson(retDetail)) {
            switch (task.getTaskType()) {
                case prepareView:
                    clone.setRetDetail(task.getRetDetail());
                    break;
                case cutout: {
                    CutoutTaskRetDetail detail = JSONObject.parseObject(retDetail, CutoutTaskRetDetail.class);
                    detail.setCutoutRetDir(newTrainDetail.getCutoutRetDir());
                    detail.setCutoutFiles(null);
                    detail.setCutoutDirMd5(null);
                    clone.setRetDetail(JSONObject.toJSONString(detail));
                    break;
                }
                case label: {
                    LabelTaskRetDetail detail = JSONObject.parseObject(retDetail, LabelTaskRetDetail.class);
                    detail.setTagRetDir(newTrainDetail.getLabelRetDir());
                    detail.setLabelFiles(null);
                    detail.setLabelFilesMd5(null);
                    clone.setRetDetail(JSONObject.toJSONString(detail));
                    break;
                }
                default:
                    throw new RuntimeException("不支持的类型");
            }
        }

        clone = comfyuiTaskService.insert(clone);
        return clone;
    }

    /**
     * 克隆Lora模型
     *
     * @param model         目标模型
     * @param cloneLoraName 克隆的lora名称
     * @param extInfo       扩展信息
     * @param fullCopy      是否全量复制
     * @param mainId        主模型id，可空，克隆子模型时必填
     * @return 克隆的模型
     */
    private MaterialModelVO cloneLora(MaterialModelVO model, String cloneLoraName, JSONObject extInfo, boolean fullCopy,
                                      Integer mainId) {
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "不存在的素材模型id");

        AssertUtil.assertTrue(
            model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getOriginalMaterialId() != null,
            ResultCode.PARAM_INVALID, "material id为空");

        String name = StringUtils.isNotBlank(cloneLoraName) ? cloneLoraName : getCloneLoraName(model);

        //克隆原始上传素材
        MaterialInfoVO materialInfo = materialInfoService.selectById(
            model.getClothLoraTrainDetail().getOriginalMaterialId());
        AssertUtil.assertNotNull(materialInfo, ResultCode.PARAM_INVALID,
            "不存在的原始素材id:" + model.getClothLoraTrainDetail().getOriginalMaterialId());

        MaterialInfoVO copyMaterial = cloneMaterialInfoVO(materialInfo, name);

        //新建model
        MaterialModelVO copyModel = cloneMaterialModel(name, model, copyMaterial, fullCopy, mainId);

        if (copyModel.getExtInfo() == null) {
            copyModel.setExtInfo(new JSONObject());
        }
        if (MapUtils.isNotEmpty(extInfo)) {
            copyModel.getExtInfo().putAll(extInfo);
        }
        copyModel.addExtInfo(CommonConstants.KEY_REVIEWER_ID, OperationContextHolder.getOperatorUserId());
        copyModel.addExtInfo(CommonConstants.KEY_WORK_SCHEDULED_TIME, new Date());

        MaterialModelVO insert = this.insert(copyModel);

        LoraTrainDetail trainDetail = insert.getClothLoraTrainDetail();
        if (trainDetail.getPrepareView() != null) {
            ComfyuiTaskVO task = comfyuiTaskService.selectById(trainDetail.getPrepareView().getTaskId());
            task.addExtInfo(KEY_MODEL_ID, insert.getId());
            comfyuiTaskService.updateByIdSelective(task);
        }

        if (trainDetail.getCutout() != null) {
            ComfyuiTaskVO task = comfyuiTaskService.selectById(trainDetail.getCutout().getTaskId());
            task.addExtInfo(KEY_MODEL_ID, insert.getId());
            comfyuiTaskService.updateByIdSelective(task);
        }

        if (trainDetail.getLabel() != null) {
            ComfyuiTaskVO task = comfyuiTaskService.selectById(trainDetail.getLabel().getTaskId());
            task.addExtInfo(KEY_MODEL_ID, insert.getId());
            comfyuiTaskService.updateByIdSelective(task);
        }

        if (trainDetail.getLora() != null) {
            ComfyuiTaskVO task = comfyuiTaskService.selectById(trainDetail.getLora().getTaskId());
            task.addExtInfo(KEY_MODEL_ID, insert.getId());
            comfyuiTaskService.updateByIdSelective(task);
        }

        return insert;
    }

    private MaterialInfoVO cloneMaterialInfoVO(MaterialInfoVO materialInfo, String name) {
        MaterialInfoVO copyMaterial = new MaterialInfoVO();
        BeanUtils.copyProperties(materialInfo, copyMaterial);
        copyMaterial.setId(null);
        copyMaterial.setName(name);
        copyMaterial.setUserId(OperationContextHolder.getMasterUserId());
        copyMaterial.setCreateTime(new Date());
        copyMaterial.setModifyTime(new Date());

        copyMaterial = materialInfoService.insert(copyMaterial);
        return copyMaterial;
    }

    /**
     * 填充字模型状态数据
     *
     * @param list 列表
     */
    private void fillSubModelStatus(List<MaterialModelVO> list) {
        List<Integer> mainList = list.stream().filter(
            e -> e.getMainType() == MainTypeEnum.MAIN && StringUtils.equals(e.getStatus(),
                MaterialModelStatusEnum.IN_TRAINING.getCode())).map(MaterialModelVO::getId).collect(
            Collectors.toList());

        if (CollectionUtils.isEmpty(mainList)) {
            return;
        }

        List<SubModelStatusDO> subModelStatusList = materialModelDAO.selectSubModelStatus(mainList);
        if (CollectionUtils.isEmpty(subModelStatusList)) {
            return;
        }

        for (MaterialModelVO item : list) {
            if (!(item.getMainType() == MainTypeEnum.MAIN && StringUtils.equals(item.getStatus(),
                MaterialModelStatusEnum.IN_TRAINING.getCode()))) {
                continue;
            }

            SubModelStatusDO subStatus = subModelStatusList.stream().filter(e -> e.getMainId().equals(item.getId()))
                .findFirst().orElse(null);
            if (subStatus != null) {
                item.setSubNeedConfirmCnt(subStatus.getNeedConfirmCnt());
                item.setSubTestingCnt(subStatus.getTestingCnt());
                item.setSubTotal(subStatus.getTotal()); // 克隆的时候colorNumber是空的，已修复，待过段时间去掉
            }
        }
    }

    private @org.jetbrains.annotations.NotNull String getCloneLoraName(MaterialModelVO model) {
        String name = model.getName() + "_copy";
        MaterialModelQuery modelQuery = new MaterialModelQuery();
        modelQuery.setNameLike(name);
        List<MaterialModelVO> existed = this.queryMaterialModelList(modelQuery);
        if (CollectionUtils.isNotEmpty(existed)) {
            name = name + "_" + existed.size();
        }
        return name;
    }

    /**
     * 重新训练
     *
     * @param model       目标模型
     * @param autoTrain   是否自动训练
     * @param labelType   打标类型
     * @param cut4ScaleUp 是否细节放大
     */
    private void reTrainModel(MaterialModelVO model, boolean autoTrain, String labelType, String cut4ScaleUp) {
        LabelTypeEnum labelTypeEnum = LabelTypeEnum.getByCode(labelType);

        MaterialModelVO target = new MaterialModelVO();
        target.setId(model.getId());
        target.setExtInfo(model.getExtInfo());
        target.setStatus(MaterialModelStatusEnum.IN_TRAINING.getCode());

        LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();

        trainDetail.setCutoutAgain(true);
        trainDetail.setCutoutAgainOperatorId(OperationContextHolder.getOperatorUserId());
        trainDetail.setCutoutAgainOperatorNick(OperationContextHolder.getOperatorNick());
        trainDetail.setCutoutAgainTime(DateUtils.formatTime(new Date()));

        //衣服lora，抠图环节是否通过剪裁放大衣服比例
        if (StringUtils.isNotBlank(cut4ScaleUp)) {
            trainDetail.setCut4ScaleUp(cut4ScaleUp);
        }

        if (autoTrain) {
            trainDetail.setAutoTrain(YES);
        } else {
            trainDetail.setAutoTrain(NO);
        }
        trainDetail.setLoraRetFileUrl(null);

        if (StringUtils.equals(trainDetail.getMaterialType(), MaterialType.scene.name())) {
            String captionPrompt = systemConfigService.queryValueByKey(TRAIN_LABEL_SCENE_PROMPT);
            captionPrompt = CommonUtil.unescapeLineBreak(captionPrompt);
            captionPrompt = ComfyUIUtils.parseParams(captionPrompt);

            trainDetail.setCaptionPrompt(captionPrompt);
            trainDetail.setActivateKey(LoraActivateKeys.getActivateKey(trainDetail.getMaterialType(), false));
            //这里先直接写死content
            trainDetail.setContentOrStyle("content");
        } else {
            labelTypeEnum = labelTypeEnum != null ? labelTypeEnum : LabelTypeEnum.getByCode(
                model.getExtInfo(KEY_LABEL_TYPE, String.class));
            if (labelTypeEnum == null) {
                labelTypeEnum = LabelTypeEnum.DEFAULT;
            }

            String key = StringUtils.equals(trainDetail.getMaterialType(), MaterialType.face.name())
                ? labelTypeEnum.getFacePromptKey() : labelTypeEnum.getPromptKey();

            String captionPrompt = systemConfigService.queryValueByKey(key);
            captionPrompt = CommonUtil.unescapeLineBreak(captionPrompt);
            captionPrompt = ComfyUIUtils.parseParams(captionPrompt);

            if (StringUtils.equals(trainDetail.getMaterialType(), MaterialType.face.name())) {
                trainDetail.setCaptionPrompt(captionPrompt);
                trainDetail.setActivateKey(labelTypeEnum == LabelTypeEnum.MINI ? ""
                    : LoraActivateKeys.getActivateKey(trainDetail.getMaterialType(), false));

            } else if (StringUtils.equals(trainDetail.getMaterialType(), MaterialType.cloth.name())) {
                trainDetail.setClothDetailsPrompt(captionPrompt);
            }

            target.addExtInfo(KEY_LABEL_TYPE, labelTypeEnum.getCode());
        }

        trainDetail.setPrepareView(null);
        trainDetail.setCutout(null);
        trainDetail.setLabel(null);
        trainDetail.setLora(null);

        trainDetail.setLoraConfirmed(null);
        trainDetail.setLoraConfirmedTime(null);

        target.setClothLoraTrainDetail(trainDetail);
        updateByIdSelective(target);

        Integer elementId = model.getExtInfo(CREATIVE_ELEMENT_ID, Integer.class);
        if (elementId != null) {
            CreativeElementVO element = creativeElementService.selectById(elementId);
            element.setStatus(ElementStatusEnum.TEST);
            creativeElementService.updateById(element);
        }
    }

}