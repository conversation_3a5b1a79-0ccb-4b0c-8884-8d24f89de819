package ai.conrain.aigc.platform.web.controller;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.StatsOperationIndicatorsService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.StatsOperationIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsOperationIndicatorsVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * StatsOperationIndicators控制器
 *
 * <AUTHOR>
 * @version StatsOperationIndicatorsService.java v 0.1 2025-05-08 05:41:54
 */
@Slf4j
@Api("")
@Validated
@RestController
@RequestMapping("/statsOperationIndicators")
public class StatsOperationIndicatorsController {

	/** statsOperationIndicatorsService */
	@Autowired
	private StatsOperationIndicatorsService statsOperationIndicatorsService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询", notes = "根据id查询[statsOperationIndicators]")
	public Result<StatsOperationIndicatorsVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(statsOperationIndicatorsService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建", notes = "新建[statsOperationIndicators]")
	public Result<Integer> create(@Valid @RequestBody StatsOperationIndicatorsVO statsOperationIndicators){
		try {
			StatsOperationIndicatorsVO data = statsOperationIndicatorsService.insert(statsOperationIndicators);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除", notes = "删除")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		statsOperationIndicatorsService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新", notes = "修改[statsOperationIndicators]，id必填，其余字段非空时才更新")
	public Result<?> updateByIdSelective(@Valid @RequestBody StatsOperationIndicatorsVO statsOperationIndicators){
		statsOperationIndicatorsService.updateByIdSelective(statsOperationIndicators);
		return Result.success();
	}

	@ApiOperation(value = "批量查询", notes = "批量查询[statsOperationIndicators]")
	@PostMapping("/queryList")
	public Result<List<StatsOperationIndicatorsVO>> queryStatsOperationIndicatorsList(@Valid @RequestBody StatsOperationIndicatorsQuery query){
		return Result.success(statsOperationIndicatorsService.queryStatsOperationIndicatorsList(query));
	}
	
	@PostMapping("/queryByPage")
	@ApiOperation(value = "分页查询", notes = "分页查询返回对象[PageInfo<StatsOperationIndicatorsDO>]")
	public Result<PageInfo<StatsOperationIndicatorsVO>> getStatsOperationIndicatorsByPage(@Valid @RequestBody StatsOperationIndicatorsQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsOperationIndicatorsService.queryStatsOperationIndicatorsByPage(query));
	}
}