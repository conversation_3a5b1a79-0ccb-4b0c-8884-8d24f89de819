/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 创作服务
 *
 * <AUTHOR>
 * @version : CreativeTemplate.java, v 0.1 2024/7/18 14:34 renxiao.wu Exp $
 */
public interface CreativeService<T extends CreativeRequest> {
    /**
     * 创建批次
     *
     * @param request 请求
     * @return 结果
     */
    CreativeBatchVO create(T request) throws IOException;

    /**
     * 构建工作流json
     *
     * @param task     任务
     * @param modelVO  模型id
     * @param elements 创作元素
     * @return 工作流json
     */
    String buildFlow(CreativeTaskVO task, MaterialModelVO modelVO, List<CreativeElementVO> elements);

    /**
     * 修正工作流
     * @param flow
     * @param context
     * @param task
     * @param flowKey
     * @return
     */
    String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey);

    /**
     * 获取创作类型
     *
     * @return 创作类型
     */
    CreativeTypeEnum getCreativeType();
}
