<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.DistributorSettlementDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="principal_type" jdbcType="VARCHAR" property="principalType" />
    <result column="principal_id" jdbcType="INTEGER" property="principalId" />
    <result column="settle_id" jdbcType="VARCHAR" property="settleId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="settle_type" jdbcType="TINYINT" property="settleType" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="out_biz_no" jdbcType="VARCHAR" property="outBizNo" />
    <result column="distributor_corp_id" jdbcType="INTEGER" property="distributorCorpId" />
    <result column="distributor_corp_name" jdbcType="VARCHAR" property="distributorCorpName" />
    <result column="settle_time" jdbcType="TIMESTAMP" property="settleTime" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, principal_type, principal_id, settle_id, status, settle_type, total_amount, settle_amount, 
    order_num, out_biz_no, distributor_corp_id, distributor_corp_name, settle_time, ext_info, 
    deleted, create_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.DistributorSettlementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_settlement
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_settlement
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from distributor_settlement
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_settlement (principal_type, principal_id, settle_id, 
      status, settle_type, total_amount, 
      settle_amount, order_num, out_biz_no, 
      distributor_corp_id, distributor_corp_name, 
      settle_time, ext_info, deleted, 
      create_time, modify_time)
    values (#{principalType,jdbcType=VARCHAR}, #{principalId,jdbcType=INTEGER}, #{settleId,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{settleType,jdbcType=TINYINT}, #{totalAmount,jdbcType=DECIMAL}, 
      #{settleAmount,jdbcType=DECIMAL}, #{orderNum,jdbcType=INTEGER}, #{outBizNo,jdbcType=VARCHAR}, 
      #{distributorCorpId,jdbcType=INTEGER}, #{distributorCorpName,jdbcType=VARCHAR}, 
      #{settleTime,jdbcType=TIMESTAMP}, #{extInfo,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        principal_type,
      </if>
      <if test="principalId != null">
        principal_id,
      </if>
      <if test="settleId != null">
        settle_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="settleType != null">
        settle_type,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="settleAmount != null">
        settle_amount,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="outBizNo != null">
        out_biz_no,
      </if>
      <if test="distributorCorpId != null">
        distributor_corp_id,
      </if>
      <if test="distributorCorpName != null">
        distributor_corp_name,
      </if>
      <if test="settleTime != null">
        settle_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="principalType != null">
        #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        #{principalId,jdbcType=INTEGER},
      </if>
      <if test="settleId != null">
        #{settleId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="settleType != null">
        #{settleType,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="settleAmount != null">
        #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="outBizNo != null">
        #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="distributorCorpId != null">
        #{distributorCorpId,jdbcType=INTEGER},
      </if>
      <if test="distributorCorpName != null">
        #{distributorCorpName,jdbcType=VARCHAR},
      </if>
      <if test="settleTime != null">
        #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.DistributorSettlementExample" resultType="java.lang.Long">
    select count(*) from distributor_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.principalType != null">
        principal_type = #{record.principalType,jdbcType=VARCHAR},
      </if>
      <if test="record.principalId != null">
        principal_id = #{record.principalId,jdbcType=INTEGER},
      </if>
      <if test="record.settleId != null">
        settle_id = #{record.settleId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.settleType != null">
        settle_type = #{record.settleType,jdbcType=TINYINT},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.settleAmount != null">
        settle_amount = #{record.settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=INTEGER},
      </if>
      <if test="record.outBizNo != null">
        out_biz_no = #{record.outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorCorpId != null">
        distributor_corp_id = #{record.distributorCorpId,jdbcType=INTEGER},
      </if>
      <if test="record.distributorCorpName != null">
        distributor_corp_name = #{record.distributorCorpName,jdbcType=VARCHAR},
      </if>
      <if test="record.settleTime != null">
        settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_settlement
    set id = #{record.id,jdbcType=INTEGER},
      principal_type = #{record.principalType,jdbcType=VARCHAR},
      principal_id = #{record.principalId,jdbcType=INTEGER},
      settle_id = #{record.settleId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      settle_type = #{record.settleType,jdbcType=TINYINT},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      settle_amount = #{record.settleAmount,jdbcType=DECIMAL},
      order_num = #{record.orderNum,jdbcType=INTEGER},
      out_biz_no = #{record.outBizNo,jdbcType=VARCHAR},
      distributor_corp_id = #{record.distributorCorpId,jdbcType=INTEGER},
      distributor_corp_name = #{record.distributorCorpName,jdbcType=VARCHAR},
      settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO">
    update distributor_settlement
    <set>
      <if test="principalType != null">
        principal_type = #{principalType,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        principal_id = #{principalId,jdbcType=INTEGER},
      </if>
      <if test="settleId != null">
        settle_id = #{settleId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="settleType != null">
        settle_type = #{settleType,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="settleAmount != null">
        settle_amount = #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="outBizNo != null">
        out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="distributorCorpId != null">
        distributor_corp_id = #{distributorCorpId,jdbcType=INTEGER},
      </if>
      <if test="distributorCorpName != null">
        distributor_corp_name = #{distributorCorpName,jdbcType=VARCHAR},
      </if>
      <if test="settleTime != null">
        settle_time = #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO">
    update distributor_settlement
    set principal_type = #{principalType,jdbcType=VARCHAR},
      principal_id = #{principalId,jdbcType=INTEGER},
      settle_id = #{settleId,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      settle_type = #{settleType,jdbcType=TINYINT},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      settle_amount = #{settleAmount,jdbcType=DECIMAL},
      order_num = #{orderNum,jdbcType=INTEGER},
      out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      distributor_corp_id = #{distributorCorpId,jdbcType=INTEGER},
      distributor_corp_name = #{distributorCorpName,jdbcType=VARCHAR},
      settle_time = #{settleTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update distributor_settlement set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update distributor_settlement set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="lockById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `distributor_settlement`
    where id = #{id,jdbcType=INTEGER}
    and deleted = 0
    for update nowait
  </select>
  <select id="querySettleStats" resultMap="BaseResultMap">
    select `distributor_corp_id`, count(1) `order_num`, sum(total_amount) `total_amount`, sum(settle_amount) `settle_amount`
    from (
      select a.*
      from (
        select `order_no`, `total_amount`, `settle_amount`, `distributor_corp_id`
        from `order_settlement`
        where `status` = 2
      ) a
      left join `order_info` b
      on a.order_no = b.order_no
      where b.`finish_time` BETWEEN STR_TO_DATE(CONCAT(#{month}, '01'), '%Y%m%d') AND DATE_ADD(LAST_DAY(STR_TO_DATE(CONCAT(#{month}, '01'), '%Y%m%d')), INTERVAL 1 DAY) - INTERVAL 1 SECOND
    ) t
    group by `distributor_corp_id`
  </select>
  <select id="statsDistributorSettle" parameterType="ai.conrain.aigc.platform.dal.example.DistributorSettlementExample" resultMap="BaseResultMap">
    select distributor_corp_id, sum(total_amount) total_amount, sum(settle_amount) settle_amount, sum(order_num) order_num
    from distributor_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    and status = 1
    group by distributor_corp_id
  </select>
</mapper>