<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.UserPointLogDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="related_id" jdbcType="INTEGER" property="relatedId" />
    <result column="point" jdbcType="INTEGER" property="point" />
    <result column="give_point" jdbcType="INTEGER" property="givePoint" />
    <result column="experience_point" jdbcType="INTEGER" property="experiencePoint" />
    <result column="model_point" jdbcType="INTEGER" property="modelPoint" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, type, related_id, point, give_point, experience_point, model_point,
    operator_id, memo, create_time, modify_time
  </sql>
  <sql id="Blob_Column_List">
    ext_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.UserPointLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_point_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserPointLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_point_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_point_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from user_point_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_point_log (user_id, type, related_id,
      point, give_point, experience_point,
      model_point, operator_id, memo,
      create_time, modify_time, ext_info
      )
    values (#{userId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, #{relatedId,jdbcType=INTEGER},
      #{point,jdbcType=INTEGER}, #{givePoint,jdbcType=INTEGER}, #{experiencePoint,jdbcType=INTEGER},
      #{modelPoint,jdbcType=INTEGER}, #{operatorId,jdbcType=INTEGER}, #{memo,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{extInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_point_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="relatedId != null">
        related_id,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="givePoint != null">
        give_point,
      </if>
      <if test="experiencePoint != null">
        experience_point,
      </if>
      <if test="modelPoint != null">
        model_point,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="point != null">
        #{point,jdbcType=INTEGER},
      </if>
      <if test="givePoint != null">
        #{givePoint,jdbcType=INTEGER},
      </if>
      <if test="experiencePoint != null">
        #{experiencePoint,jdbcType=INTEGER},
      </if>
      <if test="modelPoint != null">
        #{modelPoint,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.UserPointLogExample" resultType="java.lang.Long">
    select count(*) from user_point_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_point_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedId != null">
        related_id = #{record.relatedId,jdbcType=INTEGER},
      </if>
      <if test="record.point != null">
        point = #{record.point,jdbcType=INTEGER},
      </if>
      <if test="record.givePoint != null">
        give_point = #{record.givePoint,jdbcType=INTEGER},
      </if>
      <if test="record.experiencePoint != null">
        experience_point = #{record.experiencePoint,jdbcType=INTEGER},
      </if>
      <if test="record.modelPoint != null">
        model_point = #{record.modelPoint,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update user_point_log
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      related_id = #{record.relatedId,jdbcType=INTEGER},
      point = #{record.point,jdbcType=INTEGER},
      give_point = #{record.givePoint,jdbcType=INTEGER},
      experience_point = #{record.experiencePoint,jdbcType=INTEGER},
      model_point = #{record.modelPoint,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_point_log
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      related_id = #{record.relatedId,jdbcType=INTEGER},
      point = #{record.point,jdbcType=INTEGER},
      give_point = #{record.givePoint,jdbcType=INTEGER},
      experience_point = #{record.experiencePoint,jdbcType=INTEGER},
      model_point = #{record.modelPoint,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      memo = #{record.memo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    update user_point_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="relatedId != null">
        related_id = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="givePoint != null">
        give_point = #{givePoint,jdbcType=INTEGER},
      </if>
      <if test="experiencePoint != null">
        experience_point = #{experiencePoint,jdbcType=INTEGER},
      </if>
      <if test="modelPoint != null">
        model_point = #{modelPoint,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    update user_point_log
    set user_id = #{userId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      related_id = #{relatedId,jdbcType=INTEGER},
      point = #{point,jdbcType=INTEGER},
      give_point = #{givePoint,jdbcType=INTEGER},
      experience_point = #{experiencePoint,jdbcType=INTEGER},
      model_point = #{modelPoint,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointLogDO">
    update user_point_log
    set user_id = #{userId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      related_id = #{relatedId,jdbcType=INTEGER},
      point = #{point,jdbcType=INTEGER},
      give_point = #{givePoint,jdbcType=INTEGER},
      experience_point = #{experiencePoint,jdbcType=INTEGER},
      model_point = #{modelPoint,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="lockByBizId" parameterType="ai.conrain.aigc.platform.dal.entity.UserPointLogDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_point_log
    where related_id = #{relatedId,jdbcType=INTEGER}
      and type = #{type,jdbcType=VARCHAR}
    for update nowait
  </select>
</mapper>