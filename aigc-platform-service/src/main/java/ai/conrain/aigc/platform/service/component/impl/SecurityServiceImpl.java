package ai.conrain.aigc.platform.service.component.impl;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import ai.conrain.aigc.platform.service.component.SecurityService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.stereotype.Service;

/**
 * 密码服务
 */
@Slf4j
@Service
public class SecurityServiceImpl implements SecurityService, InitializingBean {
    /** spring自带密码编码器 */
    private BCryptPasswordEncoder passwordEncoder;

    /** 系统配置服务 */
    @Autowired
    private SystemConfigService systemConfigService;

    public String encodePassword(String password) {
        if (log.isDebugEnabled()) {
            log.debug("对密码进行加密：{}", password);
        }
        return passwordEncoder.encode(password);
    }

    public boolean matchePassword(String password, String encodedPassword) {
        return passwordEncoder.matches(password, encodedPassword);
    }

    @Override
    public String genRandomKey() {
        return KeyGenerators.string().generateKey();
    }

    @Override
    public String encrypt(String target, String salt) {
        if (StringUtils.isBlank(target) || StringUtils.isBlank(salt)) {
            log.warn("通过AES+盐值加密失败，参数非法，target={},salt={}", target, salt);
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(salt.getBytes(), "AES"));

            byte[] encryptStr = cipher.doFinal(target.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptStr);
        } catch (Exception e) {
            log.error("通过AES+盐值加密异常", e);
            return null;
        }
    }

    @Override
    public String decrypt(String target, String salt) {
        if (StringUtils.isBlank(target) || StringUtils.isBlank(salt)) {
            log.warn("通过AES+盐值解密失败，参数非法，target={},salt={}", target, salt);
            return null;
        }
        try {
            byte[] encryptByte = Base64.getDecoder().decode(target);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(salt.getBytes(), "AES"));
            byte[] decryptBytes = cipher.doFinal(encryptByte);
            return new String(decryptBytes);
        } catch (Exception e) {
            log.error("通过AES+盐值解密异常", e);
            return null;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        passwordEncoder = new BCryptPasswordEncoder();
    }
}
