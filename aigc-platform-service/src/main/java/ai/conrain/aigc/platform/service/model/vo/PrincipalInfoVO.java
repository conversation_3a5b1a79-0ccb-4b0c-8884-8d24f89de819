package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import com.alibaba.fastjson.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * PrincipalInfoVO
 *
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
@Data
public class PrincipalInfoVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 主体类型 */
	@ApiModelProperty(name = "principalType", value = "主体类型")
	private PrincipalTypeEnum principalType;

	/** 关联的用户id */
	@ApiModelProperty(name = "principalId", value = "关联的用户id")
	private Integer principalId;

	/** key */
	@ApiModelProperty(name = "infoKey", value = "key")
	private String infoKey;

	/** 创建人用户id */
	@ApiModelProperty(name = "creatorUserId", value = "创建人用户id")
	private Integer creatorUserId;

	/** 修改人用户id */
	@ApiModelProperty(name = "modifyUserId", value = "修改人用户id")
	private Integer modifyUserId;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

	/** 结算配置 */
	@ApiModelProperty(name = "info", value = "结算配置")
	private JSONObject infoValue;

	/** 扩展信息 */
	@ApiModelProperty(name = "extInfo", value = "扩展信息")
	private JSONObject extInfo;

}