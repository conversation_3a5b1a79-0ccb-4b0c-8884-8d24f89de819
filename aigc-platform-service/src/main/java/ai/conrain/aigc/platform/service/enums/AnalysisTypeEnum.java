package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 分析类型枚举
 */
@Getter
public enum AnalysisTypeEnum {

    /**
     * AB测试实验计划
     */
    TEST_PLAN("TEST_PLAN", "AB测试实验计划"),


    OTHER_INFO("other_info","其他类型 TODO")
    ;


    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    AnalysisTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 Code 获取枚举信息
     *
     * @param code 类型编码
     * @return 枚举信息
     */
    public static AnalysisTypeEnum getByCode(String code) {
        for (AnalysisTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}
