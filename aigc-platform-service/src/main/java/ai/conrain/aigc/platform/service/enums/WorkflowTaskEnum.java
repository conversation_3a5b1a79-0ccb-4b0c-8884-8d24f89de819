package ai.conrain.aigc.platform.service.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作流任务枚举
 */
@Getter
@AllArgsConstructor
public enum WorkflowTaskEnum {
    INITIAL_REVIEW("initial_review", "初审"),
    PROMPT_USER_REVIEW("prompt_user_review", "prompt工程师初审"),
    AGAIN_REVIEW("again_review", "复审"),
    PROMPT_USER_AGAIN_REVIEW("prompt_user_again_review", "prompt工程师复审"),
    REVIEW("review", "审核"),
    ;

    /** 类型 */
    private final String type;

    /** 名称 */
    private final String name;

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 工作流任务
     */
    public static WorkflowTaskEnum of(String type) {
        return Arrays.stream(values()).filter(t -> t.type.equals(type)).findFirst().orElse(null);
    }
}
