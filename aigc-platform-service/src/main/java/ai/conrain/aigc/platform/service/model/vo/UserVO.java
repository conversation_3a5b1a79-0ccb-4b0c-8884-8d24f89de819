package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.dal.entity.IdModel;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * UserVO
 *
 * @version UserService.java v 0.1 2024-01-20 01:18:37
 */
@Data
public class UserVO implements Serializable, IdModel {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** 用户id */
    @ApiModelProperty(name = "id", value = "用户id")
    private Integer id;

    /** 昵称 */
    @ApiModelProperty(name = "nickName", value = "昵称")
    private String nickName;

    /** 真实姓名 */
    @ApiModelProperty(name = "realName", value = "真实姓名")
    private String realName;

    /** 登录id */
    @ApiModelProperty(name = "loginId", value = "登录id")
    private String loginId;

    /** 登录密码 */
    @JsonIgnore
    @ApiModelProperty(name = "pswd", value = "登录密码")
    private String pswd;

    /** 手机号码 */
    @ApiModelProperty(name = "mobile", value = "手机号码")
    private String mobile;

    /** 角色类型 */
    @ApiModelProperty(name = "roleType", value = "角色类型")
    private RoleTypeEnum roleType;

    /**
     * 自定义角色配置，默认为空，渠道商账户有值
     */
    private String customRole;
    private String customRoleName;

    // 公司名称
    private String corpName;

    private Integer corpOrgId;

    // 部门组织id
    private Integer deptOrgId;
    // 部门名
    private String deptOrgName;

    /** 用户类型，MASTER、SUB */
    @ApiModelProperty(name = "userType", value = "用户类型，MASTER、SUB")
    private UserTypeEnum userType;

    /** 主账号id */
    @ApiModelProperty(name = "masterId", value = "主账号id")
    private Integer masterId;

    /** 状态，ENABLED、DISABLED */
    @ApiModelProperty(name = "status", value = "状态，ENABLED、DISABLED")
    private UserStatusEnum status;

    private String statusDesc;

    /** 操作者id */
    @JsonIgnore
    @ApiModelProperty(name = "operatorId", value = "操作者id")
    private Integer operatorId;

    /** 配置 */
    @ApiModelProperty(name = "profiles", value = "配置")
    private JSONObject profiles;

    /** 注册来源 */
    @JsonIgnore
    @ApiModelProperty(name = "registerFrom", value = "注册来源")
    private String registerFrom;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 登录失败次数 */
    @JsonIgnore
    @ApiModelProperty(name = "loginFailCount", value = "登录失败次数")
    private Integer loginFailCount;

    /** 最后登录时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "lastLoginTime", value = "最后登录时间")
    private Date lastLoginTime;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 主账号昵称 */
    @ApiModelProperty(name = "masterNick", value = "主账号昵称")
    private String masterNick;

    private String masterLoginId;

    private UserStatusEnum masterStatus;

    @ApiModelProperty(name = "imagePoint", value = "图片算力点")
    private BigDecimal imagePoint = BigDecimal.ZERO;

    @ApiModelProperty(name = "givePoint", value = "赠送算力点")
    private Integer givePoint = 0;

    @ApiModelProperty(name = "experiencePoint", value = "体验点")
    private Integer experiencePoint = 0;

    // 最近来访日期，yyyyMMDD
    private String lastVisitDate;

    /** 用户审核信息，快照信息，里面的销售昵称和渠道公司名称可能都过时了；目前没开审核流程，因此这里没有实际作用 */
    @Deprecated
    private UserReviewInfo userReviewInfo;

    /**
     * 企业信息
     */
    private OrganizationVO organizationVO;

    // 关联的渠道商信息，只有关联了渠道商的商家账号有值
    private Integer relatedDistributorCorpId;
    private String relatedDistributorCorpName;
    private Integer relatedSalesUserId;
    private String relatedSalesUserName;

    /** 账号以及子账号数据 */
    private List<UserVO> children;

    /** 用户退点率 */
    private String refundRate;

    //邀请注册人，示例，张三@深圳服饰协会
    private String inviteRegister;

    //当前账号的关联账号信息，目前只有渠道商账号有值，渠道商账号关联的演示账号和虚拟商家账号
    private RelatedAccounts relatedAccounts;

    @ApiModelProperty(name = "contractDate", value = "合同签署时间")
    private String contractDate;
}