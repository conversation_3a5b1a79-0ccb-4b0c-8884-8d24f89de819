package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum TagType {
    FACE_FIX("FACE_FIX"),
    MODEL_DESC("MODEL_DESC"),
    POSE("POSE"),
    FACIAL_EXPRESSION("FACIAL_EXPRESSION"),
    FACIAL_FEATURES("FACIAL_FEATURES"),
    HAIR("HAIR"),
    NEGATIVE("NEGATIVE"),
    IMG_BACKGROUND("IMG_BACKGROUND"),
    CAMERA_VIEW("CAMERA_VIEW"),
    PHOTO_STYLE("PHOTO_STYLE");

    private final String value;

    TagType(String value) {
        this.value = value;
    }
}
