package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.dal.entity.StatsDeliveryDO;
import ai.conrain.aigc.platform.dal.entity.StatsQueuedModelDO;
import ai.conrain.aigc.platform.dal.entity.SubModelStatusDO;
import ai.conrain.aigc.platform.dal.example.MaterialModelExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MaterialModelDAO {
    long countByExample(MaterialModelExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(MaterialModelDO record);

    int insertSelective(MaterialModelDO record);

    List<MaterialModelDO> selectByExampleWithBLOBs(MaterialModelExample example);

    List<MaterialModelDO> selectByExample(MaterialModelExample example);

    List<MaterialModelDO> selectIdByExample(MaterialModelExample example);

    MaterialModelDO selectByPrimaryKey(Integer id);

    MaterialModelDO lockByPrimaryKey(Integer id);

    MaterialModelDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id,
                                                        @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") MaterialModelDO record,
                                 @Param("example") MaterialModelExample example);

    int updateByExampleWithBLOBs(@Param("record") MaterialModelDO record,
                                 @Param("example") MaterialModelExample example);

    int updateByExample(@Param("record") MaterialModelDO record, @Param("example") MaterialModelExample example);

    int updateByPrimaryKeySelective(MaterialModelDO record);

    int updateByPrimaryKeyWithBLOBs(MaterialModelDO record);

    int updateByPrimaryKey(MaterialModelDO record);

    int logicalDeleteByExample(@Param("example") MaterialModelExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    List<StatsDeliveryDO> statsDelivery(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计Copy服装数量
     *
     * @param baseNameList Copy服装的基础名称列表
     * @param startDate    开始时间
     * @param endDate      结束时间
     * @return 匹配的服装数量
     */
    int countCopyClothesWithPrefix(@Param("baseNameList") List<String> baseNameList,
                                   @Param("startDate") String startDate,
                                   @Param("endDate") String endDate);

    /**
     * 根据交付日期查询素材模型
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 素材模型列表
     */
    List<MaterialModelDO> queryMaterialModelByDeliveryDate(@Param("startDate") String startDate,
                                                           @Param("endDate") String endDate);

    /**
     * 根据主ID查询子ID列表
     *
     * @param id 父ID
     * @return 子ID列表
     */
    List<Integer> selectSubIds(Integer id);

    /**
     * 统计子模型状态数据
     *
     * @param mainIds 主模型id列表
     * @return 子模型状态数据
     */
    List<SubModelStatusDO> selectSubModelStatus(List<Integer> mainIds);

    /**
     * 统计模型等待队列数据
     *
     * @return 模型等待队列数据
     */
    StatsQueuedModelDO statsQueuedModel();
}