package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.request.RegisterRequest;
import ai.conrain.aigc.platform.service.model.request.SmsLoginRequest;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.request.UserUpdate;
import ai.conrain.aigc.platform.service.model.vo.ActiveUserVO;
import ai.conrain.aigc.platform.service.model.vo.RelatedAccounts;
import ai.conrain.aigc.platform.service.model.vo.SalesInfoVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import java.util.List;


/**
 * 用户信息 Service定义
 *
 * <AUTHOR>
 * @version UserService.java v 0.1 2024-01-20 01:21:36
 */
public interface UserService {

    /**
     * 查询用户信息对象
     *
     * @param id 主键
     * @result 返回结果
     */
    UserVO selectById(Integer id);

    /**
     * 删除用户信息对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加用户信息
     *
     * @param user 注册用户信息
     * @return 返回结果
     */
    UserVO create(UserRegister user);

    void createRelatedAccounts4Distributor(String nick, Integer distributorMasterUserId, Integer salesUserId);

    RelatedAccounts queryRelatedAccounts(Integer userId);

    // 创建虚拟测试手机号，2开头，11位数字
    String genTestMobile();

    /**
     * 后台修改用户信息对象
     *
     * @param user 对象参数
     */
    void updateBack(UserUpdate user);

    /**
     * 前台修改用户信息对象
     *
     * @param user 对象参数
     */
    void updateFront(UserUpdate user);

    /**
     * 带条件查询用户信息数量
     *
     * @param query 查询条件
     */
    Long queryUserCount(UserQuery query);

    void updateByIdSelective(UserVO userVO);

    /**
     * 带条件分页查询用户信息
     *
     * @param query 查询条件
     */
    PageInfo<UserVO> queryUserByPage(UserQuery query);

    /**
     * 查询用户
     *
     * @param query
     * @return
     */
    List<UserVO> queryUsers(UserQuery query);

    // 查询所有vip和paid用户
    List<UserVO> queryAllVIPOrPaidCustomer();


    /**
     * 查询 3999以上的用户列表
     *
     * @return 用户列表
     */
    List<UserVO> queryAll3999VIPOrPaidCustomer();

    /**
     * 查询指定周期内 3999 以上的用户列表
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 用户列表
     */
    List<UserVO> queryAll3999VIPOrPaidCustomerWithCreateTime(String startDate, String endDate);

    /**
     * 密码重置
     *
     * @param mobile  手机号
     * @param newPswd 新密码
     */
    void resetPassword(String mobile, String newPswd);

    /**
     * 密码重置
     *
     * @param userId  用户id
     * @param newPswd 新密码
     */
    void resetPassword(Integer userId, String newPswd);

    /**
     * 根据账密登录
     *
     * @param loginId 登录id
     * @param pswd    密码
     * @return 结果
     */
    Result<UserVO> loginByPswd(String loginId, String pswd);

    /**
     * 短信登录
     *
     * @param request 登录请求
     * @return 结果
     */
    Result<UserVO> loginBySms(SmsLoginRequest request);

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 用户信息
     */
    Result<UserVO> register(RegisterRequest request);

    /**
     * 微信手机号授权登录
     *
     * @param appId 微信小程序id
     * @param code  授权码
     * @return 结果
     */
    Result<UserVO> loginByMobileAuth(String appId, String code);

    /**
     * 检查手机号是否白名单内的用户
     *
     * @param mobile
     * @return
     */
    boolean loginMobileCheck(String mobile);

    /**
     * 检查手机号是否已存在
     *
     * @param mobile 手机号
     */
    void checkMobileExists(String mobile);

    /**
     * 根据id列表批量查询用户信息
     *
     * @param userIdList id列表
     * @return 结果
     */
    List<UserVO> batchQueryById(List<Integer> userIdList);

    /**
     * 查询所有主账号id和昵称信息
     *
     * @param roleTypes 角色类型
     * @return 主账号id和昵称信息
     */
    List<UserVO> queryAllMasterMetaInfo(List<String> roleTypes);


    /**
     * 根据类型获取所有用户
     *
     * @param roleTypes 角色类型
     * @return 主账号id和昵称信息
     */
    List<UserVO> queryAllByRoleTypes(List<String> roleTypes);

    /**
     * 根据条件查询所有userId列表
     *
     * @param query 查询条件
     * @return userId列表
     */
    List<Integer> queryIdList(UserQuery query);

    void assignCustomer2Distributor(String userCorpName, Integer userId, Integer distributorUserId, Integer distributorSalesUserId, boolean notify);

    void assignCustomer2Distributor(String userCorpName, Integer userId, List<Integer> distributorOrgIdPath, boolean notify);

    /**
     * 判断是否vip客户
     *
     * @param userId 用户id
     * @return true 是vip客户
     */
    boolean isVip(Integer userId);

    /**
     * 判断是否vip客户,排除后台用户
     *
     * @param userId 用户id
     * @return true 是vip客户
     */
    boolean isVipWithoutBackUser(Integer userId);

    /**
     * 判断是否客户
     *
     * @param userId 用户id
     * @return true 是客户
     */
    boolean isCustomer(Integer userId);

    /**
     * 是否体验用户
     *
     * @param userId 用户id
     * @return true 体验用户
     */
    boolean isTrialAccount(Integer userId);

    /**
     * 根据组织id查询渠道管理员, 包括一级渠道, 二级渠道
     * @param orgIds 组织id列表
     * @return 结果
     */
    List<UserVO> batchQueryChannelAdminByOrgId(List<Integer> orgIds);

    /**
     * 根据组织id查询渠道管理员, 包括一级渠道, 二级渠道
     * @param orgId 组织id
     * @return 结果
     */
    UserVO getChannelAdminByOrgId(Integer orgId);

    /**
     * 查询用户是否在60天之内未充值过
     *
     * @return 用户列表
     */
    List<SalesInfoVO> queryBefore60Days(String endDate);

    /**
     * 查询活跃的用户
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 用户列表
     */
    List<ActiveUserVO> queryActiveUser(String startDate, String endDate);

    /**
     * 获取3999以上的用户id列表
     *
     * @param userId 销售id
     * @return 用户id列表
     */
    List<Integer> fetch3999UserIdList(Integer userId);

    /**
     * 查询运营用户是否在60天之内未充值过
     *
     * @param endDate 结束日期
     * @return 用户列表
     */
    List<SalesInfoVO> queryBefore60DaysByOperate(String endDate);

}