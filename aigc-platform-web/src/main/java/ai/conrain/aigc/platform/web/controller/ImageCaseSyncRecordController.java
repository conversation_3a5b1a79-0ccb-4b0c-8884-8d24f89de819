package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.ImageCaseSyncRecordDO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseSyncRecordVO;
import ai.conrain.aigc.platform.service.model.query.ImageCaseSyncRecordQuery;
import ai.conrain.aigc.platform.service.component.ImageCaseSyncRecordService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ImageCaseSyncRecord控制器
 *
 * <AUTHOR>
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
@Slf4j
@Api("")
@Validated
@RestController
@RequestMapping("/imageCaseSyncRecord")
public class ImageCaseSyncRecordController {

	/** imageCaseSyncRecordService */
	@Autowired
	private ImageCaseSyncRecordService imageCaseSyncRecordService;
	
	@GetMapping("/getById/{id}")
	@ApiOperation(value = "根据id查询", notes = "根据id查询[imageCaseSyncRecord]")
	public Result<ImageCaseSyncRecordVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageCaseSyncRecordService.selectById(id));
	}
	
	@PostMapping("/create")
	@ApiOperation(value = "新建", notes = "新建[imageCaseSyncRecord]")
	public Result<Integer> create(@Valid @RequestBody ImageCaseSyncRecordVO imageCaseSyncRecord){
		try {
			ImageCaseSyncRecordVO data = imageCaseSyncRecordService.insert(imageCaseSyncRecord);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	@ApiOperation(value = "删除", notes = "删除")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageCaseSyncRecordService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	@ApiOperation(value = "更新", notes = "修改[imageCaseSyncRecord]，id必填，所有字段按请求直接更新")
	public Result<?> updateById(@Valid @RequestBody ImageCaseSyncRecordVO imageCaseSyncRecord){
		imageCaseSyncRecordService.updateById(imageCaseSyncRecord);
		return Result.success();
	}

	@PostMapping("/findAll")
	@ApiOperation(value = "全量查询", notes = "全量查询")
	public Result<List<ImageCaseSyncRecordVO>> findAll(){
		return Result.success(imageCaseSyncRecordService.findAll());
	}

}