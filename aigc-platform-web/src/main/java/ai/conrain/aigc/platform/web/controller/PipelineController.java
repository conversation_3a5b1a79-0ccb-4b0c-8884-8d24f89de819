package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.component.dispatch.ServerCache;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.*;

import com.alibaba.fastjson.JSONObject;

/**
 * Pipeline控制器
 *
 * <AUTHOR>
 * @version PipelineService.java v 0.1 2024-06-15 05:51:42
 */
@Slf4j
@Api("服务管道")
@Validated
@RestController
@RequestMapping("/pipeline")
public class PipelineController {

    /** pipelineService */
    @Autowired
    private PipelineService pipelineService;
    @Autowired
    private ServerHelper serverHelper;

    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据id查询服务管道", notes = "根据id查询服务管道[pipeline]")
    public Result<PipelineVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(pipelineService.selectById(id));
    }

    @PostMapping("/create")
    @ApiOperation(value = "新建服务管道", notes = "新建服务管道[pipeline]")
    public Result<Integer> create(@Valid @RequestBody PipelineVO pipeline) {
        try {
            if (StringUtils.isNotBlank(pipeline.getUserRelationStr())) {
                pipeline.setUserRelation(JSONObject.parseObject(pipeline.getUserRelationStr()));
            }
            PipelineVO data = pipelineService.insert(pipeline);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加服务管道失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建服务管道失败");
    }

    @PostMapping("/deleteById")
    @ApiOperation(value = "删除服务管道", notes = "删除服务管道")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        pipelineService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    @ApiOperation(value = "更新服务管道", notes = "修改服务管道[pipeline]，id必填，所有字段按请求直接更新")
    public Result<?> updateById(@Valid @RequestBody PipelineVO pipeline) {
        if (StringUtils.isNotBlank(pipeline.getUserRelationStr())) {
            pipeline.setUserRelation(JSONObject.parseObject(pipeline.getUserRelationStr()));
        }
        pipelineService.updateById(pipeline);
        return Result.success();
    }

    @GetMapping("/all")
    @ApiOperation(value = "全量查询服务管道", notes = "全量查询服务管道")
    public Result<List<PipelineVO>> findAll() {
        List<PipelineVO> result = pipelineService.queryAll();
        fillLatestStatus(result);
        return Result.success(result);
    }

    /**
     * 填充最新的状态
     *
     * @param result 列表
     */
    private void fillLatestStatus(List<PipelineVO> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        for (PipelineVO pipeline : result) {
            if (CollectionUtils.isEmpty(pipeline.getServers())) {
                continue;
            }

            pipeline.getServers().forEach(server -> {
                if (CollectionUtils.isNotEmpty(server.getChildren())) {
                    server.getChildren().forEach(child -> {
                        ServerCache cache = serverHelper.getFromTair(child.getId());
                        if (cache != null && cache.getStatus() != null) {
                            child.setRealtimeStatus(cache.getStatus());
                            child.setRealtimeTaskId(cache.getTaskId());
                        }
                    });
                }
            });
        }
    }

}