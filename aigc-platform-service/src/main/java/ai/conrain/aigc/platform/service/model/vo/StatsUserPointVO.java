package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * StatsUserPointVO
 *
 * @version StatsUserPointService.java v 0.1 2025-04-11 10:09:06
 */
@Data
public class StatsUserPointVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty(name = "id", value = "id")
	private Integer id;

	/** 用户id */
	@ApiModelProperty(name = "userId", value = "用户id")
	private Integer userId;

	/** 用户昵称 */
	@ApiModelProperty(name = "nickName", value = "用户昵称")
	private String nickName;

	/** 所属渠道商 */
	@ApiModelProperty(name = "distributorCorpName", value = "所属渠道商")
	private String distributorCorpName;

	/** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
	@ApiModelProperty(name = "statsType", value = "统计类型：DAILY/WEEKLY/MONTHLY/TOTAL")
	private StatsPeriodEnum statsType;

	/** 统计日期: 格式为yyyy-MM-dd */
	@ApiModelProperty(name = "statsDate", value = "统计日期: 格式为yyyy-MM-dd")
	private String statsDate;

	/** 消耗的算力点 */
	@ApiModelProperty(name = "pointConsumed", value = "消耗的算力点")
	private Integer pointConsumed;

	/** 消耗的赠送点 */
	@ApiModelProperty(name = "givePointConsumed", value = "消耗的赠送点")
	private Integer givePointConsumed;

	/** 消耗的体验点 */
	@ApiModelProperty(name = "expPointConsumed", value = "消耗的体验点")
	private Integer expPointConsumed;

	/** 消耗的套内点 */
	@ApiModelProperty(name = "modelPointConsumed", value = "消耗的套内点")
	private Integer modelPointConsumed;

	/** 充值金额 */
	@ApiModelProperty(name = "rechargeAmount", value = "充值金额")
	private BigDecimal rechargeAmount;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "createTime", value = "创建时间")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@ApiModelProperty(name = "modifyTime", value = "修改时间")
	private Date modifyTime;

}