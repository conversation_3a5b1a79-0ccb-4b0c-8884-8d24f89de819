/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 * @version : UserStatusEnum.java, v 0.1 2024/1/18 16:23 renxiao.wu Exp $
 */
@Getter
public enum UserStatusEnum {
    UNDER_REVIEW("UNDER_REVIEW", "审核中"),
    REJECT("REJECT", "审核拒绝"),
    ENABLED("ENABLED", "正常"),
    DISABLED("DISABLED", "停用"),
    ;

    /** 枚举码 */
    private String code;

    /** 枚举描述 */
    private String desc;

    private UserStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static UserStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (UserStatusEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
