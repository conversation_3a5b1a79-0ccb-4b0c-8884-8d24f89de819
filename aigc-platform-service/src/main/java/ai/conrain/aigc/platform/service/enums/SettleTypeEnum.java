/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

/**
 * 结算类型枚举
 *
 * <AUTHOR>
 * @version : SettleType.java, v 0.1 2023/9/2 09:15 renxiao.wu Exp $
 */
public enum SettleTypeEnum {
    /** 系统结算 */
    SYSTEM_SETTLE(1, "系统结算"),
    /** 手动结算 */
    MANUAL_SETTLE(2, "手动结算"),

    ;

    /** 枚举码 */
    private Integer code;

    /** 枚举描述 */
    private String desc;

    private SettleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static SettleTypeEnum getByCode(Integer code) {
        if (null == code) {
            return null;
        }

        for (SettleTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public Integer getCode() {
        return code;
    }
}
