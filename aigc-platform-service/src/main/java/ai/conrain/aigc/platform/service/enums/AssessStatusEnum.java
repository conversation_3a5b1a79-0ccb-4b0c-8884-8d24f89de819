package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum AssessStatusEnum {
    ASSESS_INIT("ASSESS_INIT", "未启用"),
    ASSESS_WAITING("ASSESS_WAITING", "未开始"),
    ASSESS_ACTIVE("ASSESS_ACTIVE", "考核中"),
    ASSESS_PASSED("ASSESS_PASSED", "已通过"),
    ASSESS_FAILED("ASSESS_FAILED", "未通过"),
    ASSESS_REVIEWED("ASSESS_REVIEWED", "已审核"),
    ASSESS_FINISHED("ASSESS_FINISHED", "已完成"),
    ASSESS_CANCELLED("ASSESS_CANCELLED", "已取消"),
    ;

    private String code;

    private String desc;

    AssessStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AssessStatusEnum getByCode(String code) {
        for (AssessStatusEnum statusEnum : AssessStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public boolean isCompleted() {
        return this.code.equals(ASSESS_PASSED.getCode()) || this.code.equals(ASSESS_FAILED.getCode());
    }

    public boolean unCompleted() {
        return !this.isCompleted();
    }
}
