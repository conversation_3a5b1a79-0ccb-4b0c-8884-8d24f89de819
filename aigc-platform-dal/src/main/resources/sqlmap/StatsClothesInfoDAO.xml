<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.StatsClothesInfoDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="stats_type" jdbcType="VARCHAR" property="statsType" />
    <result column="stats_date" jdbcType="VARCHAR" property="statsDate" />
    <result column="vip_clothes_count" jdbcType="INTEGER" property="vipClothesCount" />
    <result column="auto_train_count" jdbcType="INTEGER" property="autoTrainCount" />
    <result column="manual_delivery_count" jdbcType="INTEGER" property="manualDeliveryCount" />
    <result column="auto_train_and_delivery_count" jdbcType="INTEGER" property="autoTrainAndDeliveryCount" />
    <result column="retry_matting_count" jdbcType="INTEGER" property="retryMattingCount" />
    <result column="update_prompt_count" jdbcType="INTEGER" property="updatePromptCount" />
    <result column="copy_count" jdbcType="INTEGER" property="copyCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, stats_type, stats_date, vip_clothes_count, auto_train_count, manual_delivery_count, 
    auto_train_and_delivery_count, retry_matting_count, update_prompt_count, copy_count, 
    create_time, modify_time
  </sql>
  <sql id="Blob_Column_List">
    ext_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.StatsClothesInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from stats_clothes_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.StatsClothesInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from stats_clothes_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from stats_clothes_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from stats_clothes_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stats_clothes_info (stats_type, stats_date, vip_clothes_count, 
      auto_train_count, manual_delivery_count, auto_train_and_delivery_count, 
      retry_matting_count, update_prompt_count, copy_count, 
      create_time, modify_time, ext_info
      )
    values (#{statsType,jdbcType=VARCHAR}, #{statsDate,jdbcType=VARCHAR}, #{vipClothesCount,jdbcType=INTEGER}, 
      #{autoTrainCount,jdbcType=INTEGER}, #{manualDeliveryCount,jdbcType=INTEGER}, #{autoTrainAndDeliveryCount,jdbcType=INTEGER}, 
      #{retryMattingCount,jdbcType=INTEGER}, #{updatePromptCount,jdbcType=INTEGER}, #{copyCount,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{extInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stats_clothes_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statsType != null">
        stats_type,
      </if>
      <if test="statsDate != null">
        stats_date,
      </if>
      <if test="vipClothesCount != null">
        vip_clothes_count,
      </if>
      <if test="autoTrainCount != null">
        auto_train_count,
      </if>
      <if test="manualDeliveryCount != null">
        manual_delivery_count,
      </if>
      <if test="autoTrainAndDeliveryCount != null">
        auto_train_and_delivery_count,
      </if>
      <if test="retryMattingCount != null">
        retry_matting_count,
      </if>
      <if test="updatePromptCount != null">
        update_prompt_count,
      </if>
      <if test="copyCount != null">
        copy_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statsType != null">
        #{statsType,jdbcType=VARCHAR},
      </if>
      <if test="statsDate != null">
        #{statsDate,jdbcType=VARCHAR},
      </if>
      <if test="vipClothesCount != null">
        #{vipClothesCount,jdbcType=INTEGER},
      </if>
      <if test="autoTrainCount != null">
        #{autoTrainCount,jdbcType=INTEGER},
      </if>
      <if test="manualDeliveryCount != null">
        #{manualDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="autoTrainAndDeliveryCount != null">
        #{autoTrainAndDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="retryMattingCount != null">
        #{retryMattingCount,jdbcType=INTEGER},
      </if>
      <if test="updatePromptCount != null">
        #{updatePromptCount,jdbcType=INTEGER},
      </if>
      <if test="copyCount != null">
        #{copyCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.StatsClothesInfoExample" resultType="java.lang.Long">
    select count(*) from stats_clothes_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stats_clothes_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.statsType != null">
        stats_type = #{record.statsType,jdbcType=VARCHAR},
      </if>
      <if test="record.statsDate != null">
        stats_date = #{record.statsDate,jdbcType=VARCHAR},
      </if>
      <if test="record.vipClothesCount != null">
        vip_clothes_count = #{record.vipClothesCount,jdbcType=INTEGER},
      </if>
      <if test="record.autoTrainCount != null">
        auto_train_count = #{record.autoTrainCount,jdbcType=INTEGER},
      </if>
      <if test="record.manualDeliveryCount != null">
        manual_delivery_count = #{record.manualDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="record.autoTrainAndDeliveryCount != null">
        auto_train_and_delivery_count = #{record.autoTrainAndDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="record.retryMattingCount != null">
        retry_matting_count = #{record.retryMattingCount,jdbcType=INTEGER},
      </if>
      <if test="record.updatePromptCount != null">
        update_prompt_count = #{record.updatePromptCount,jdbcType=INTEGER},
      </if>
      <if test="record.copyCount != null">
        copy_count = #{record.copyCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update stats_clothes_info
    set id = #{record.id,jdbcType=INTEGER},
      stats_type = #{record.statsType,jdbcType=VARCHAR},
      stats_date = #{record.statsDate,jdbcType=VARCHAR},
      vip_clothes_count = #{record.vipClothesCount,jdbcType=INTEGER},
      auto_train_count = #{record.autoTrainCount,jdbcType=INTEGER},
      manual_delivery_count = #{record.manualDeliveryCount,jdbcType=INTEGER},
      auto_train_and_delivery_count = #{record.autoTrainAndDeliveryCount,jdbcType=INTEGER},
      retry_matting_count = #{record.retryMattingCount,jdbcType=INTEGER},
      update_prompt_count = #{record.updatePromptCount,jdbcType=INTEGER},
      copy_count = #{record.copyCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stats_clothes_info
    set id = #{record.id,jdbcType=INTEGER},
      stats_type = #{record.statsType,jdbcType=VARCHAR},
      stats_date = #{record.statsDate,jdbcType=VARCHAR},
      vip_clothes_count = #{record.vipClothesCount,jdbcType=INTEGER},
      auto_train_count = #{record.autoTrainCount,jdbcType=INTEGER},
      manual_delivery_count = #{record.manualDeliveryCount,jdbcType=INTEGER},
      auto_train_and_delivery_count = #{record.autoTrainAndDeliveryCount,jdbcType=INTEGER},
      retry_matting_count = #{record.retryMattingCount,jdbcType=INTEGER},
      update_prompt_count = #{record.updatePromptCount,jdbcType=INTEGER},
      copy_count = #{record.copyCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    update stats_clothes_info
    <set>
      <if test="statsType != null">
        stats_type = #{statsType,jdbcType=VARCHAR},
      </if>
      <if test="statsDate != null">
        stats_date = #{statsDate,jdbcType=VARCHAR},
      </if>
      <if test="vipClothesCount != null">
        vip_clothes_count = #{vipClothesCount,jdbcType=INTEGER},
      </if>
      <if test="autoTrainCount != null">
        auto_train_count = #{autoTrainCount,jdbcType=INTEGER},
      </if>
      <if test="manualDeliveryCount != null">
        manual_delivery_count = #{manualDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="autoTrainAndDeliveryCount != null">
        auto_train_and_delivery_count = #{autoTrainAndDeliveryCount,jdbcType=INTEGER},
      </if>
      <if test="retryMattingCount != null">
        retry_matting_count = #{retryMattingCount,jdbcType=INTEGER},
      </if>
      <if test="updatePromptCount != null">
        update_prompt_count = #{updatePromptCount,jdbcType=INTEGER},
      </if>
      <if test="copyCount != null">
        copy_count = #{copyCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    update stats_clothes_info
    set stats_type = #{statsType,jdbcType=VARCHAR},
      stats_date = #{statsDate,jdbcType=VARCHAR},
      vip_clothes_count = #{vipClothesCount,jdbcType=INTEGER},
      auto_train_count = #{autoTrainCount,jdbcType=INTEGER},
      manual_delivery_count = #{manualDeliveryCount,jdbcType=INTEGER},
      auto_train_and_delivery_count = #{autoTrainAndDeliveryCount,jdbcType=INTEGER},
      retry_matting_count = #{retryMattingCount,jdbcType=INTEGER},
      update_prompt_count = #{updatePromptCount,jdbcType=INTEGER},
      copy_count = #{copyCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    update stats_clothes_info
    set stats_type = #{statsType,jdbcType=VARCHAR},
      stats_date = #{statsDate,jdbcType=VARCHAR},
      vip_clothes_count = #{vipClothesCount,jdbcType=INTEGER},
      auto_train_count = #{autoTrainCount,jdbcType=INTEGER},
      manual_delivery_count = #{manualDeliveryCount,jdbcType=INTEGER},
      auto_train_and_delivery_count = #{autoTrainAndDeliveryCount,jdbcType=INTEGER},
      retry_matting_count = #{retryMattingCount,jdbcType=INTEGER},
      update_prompt_count = #{updatePromptCount,jdbcType=INTEGER},
      copy_count = #{copyCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsertOrUpdateSelective" parameterType="java.util.List">
    INSERT INTO stats_clothes_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      stats_type, stats_date, 
      <if test="list[0].vipClothesCount != null">vip_clothes_count,</if>
      <if test="list[0].autoTrainCount != null">auto_train_count,</if>
      <if test="list[0].manualDeliveryCount != null">manual_delivery_count,</if>
      <if test="list[0].autoTrainAndDeliveryCount != null">auto_train_and_delivery_count,</if>
      <if test="list[0].retryMattingCount != null">retry_matting_count,</if>
      <if test="list[0].updatePromptCount != null">update_prompt_count,</if>
      <if test="list[0].copyCount != null">copy_count,</if>
      <if test="list[0].createTime != null">create_time,</if>
      <if test="list[0].modifyTime != null">modify_time,</if>
      <if test="list[0].extInfo != null">ext_info,</if>
    </trim>
    VALUES
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.statsType,jdbcType=VARCHAR},
        #{item.statsDate,jdbcType=VARCHAR},
        <if test="item.vipClothesCount != null">#{item.vipClothesCount,jdbcType=INTEGER},</if>
        <if test="item.autoTrainCount != null">#{item.autoTrainCount,jdbcType=INTEGER},</if>
        <if test="item.manualDeliveryCount != null">#{item.manualDeliveryCount,jdbcType=INTEGER},</if>
        <if test="item.autoTrainAndDeliveryCount != null">#{item.autoTrainAndDeliveryCount,jdbcType=INTEGER},</if>
        <if test="item.retryMattingCount != null">#{item.retryMattingCount,jdbcType=INTEGER},</if>
        <if test="item.updatePromptCount != null">#{item.updatePromptCount,jdbcType=INTEGER},</if>
        <if test="item.copyCount != null">#{item.copyCount,jdbcType=INTEGER},</if>
        <if test="item.createTime != null">#{item.createTime,jdbcType=TIMESTAMP},</if>
        <if test="item.modifyTime != null">#{item.modifyTime,jdbcType=TIMESTAMP},</if>
        <if test="item.extInfo != null">#{item.extInfo,jdbcType=LONGVARCHAR},</if>
      </trim>
    </foreach>
    ON DUPLICATE KEY UPDATE
    <trim suffixOverrides=",">
      <if test="list[0].vipClothesCount != null">vip_clothes_count = VALUES(vip_clothes_count),</if>
      <if test="list[0].autoTrainCount != null">auto_train_count = VALUES(auto_train_count),</if>
      <if test="list[0].manualDeliveryCount != null">manual_delivery_count = VALUES(manual_delivery_count),</if>
      <if test="list[0].autoTrainAndDeliveryCount != null">auto_train_and_delivery_count = VALUES(auto_train_and_delivery_count),</if>
      <if test="list[0].retryMattingCount != null">retry_matting_count = VALUES(retry_matting_count),</if>
      <if test="list[0].updatePromptCount != null">update_prompt_count = VALUES(update_prompt_count),</if>
      <if test="list[0].copyCount != null">copy_count = VALUES(copy_count),</if>
      <if test="list[0].modifyTime != null">modify_time = VALUES(modify_time),</if>
      <if test="list[0].extInfo != null">ext_info = VALUES(ext_info),</if>
    </trim>
  </insert>

  <select id="selectDailyStatsByWeek" resultType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    select * from stats_clothes_info
    where stats_type = 'DAILY' and stats_date between #{startDate} and #{endDate}
    order by stats_date
  </select>

  <select id="selectDailyStatsByMonth" resultType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO"> 
    select * from stats_clothes_info
    where stats_type = 'DAILY' and stats_date between #{startDate} and #{endDate}
    order by stats_date
  </select>
  <select id="selectStatsInfoByDateAndPeriod"
          resultType="ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO">
    select * from stats_clothes_info
    where stats_type = #{statsType} and stats_date = #{date}
  </select>

</mapper>