/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.model.vo.UserReviewInfo;
import ai.conrain.aigc.platform.service.validation.EnumValid;
import ai.conrain.aigc.platform.service.validation.Mobile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 注册用户
 *
 * <AUTHOR>
 * @version : UserRegister.java, v 0.1 2023/9/3 00:20 renxiao.wu Exp $
 */
@Data
public class UserRegister implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 手机号 */
    @Mobile
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    /** 昵称 */
    @NotBlank(message = "昵称不能为空")
    @Size(min = 2, max = 50, message = "昵称长度必须在2-50之间")
    @ApiModelProperty(name = "nickName", value = "昵称")
    private String nickName;

    /** 角色类型 */
    @EnumValid(value = RoleTypeEnum.class, nullable = true)
    @ApiModelProperty(name = "roleType", value = "角色类型")
    private String roleType;

    /** 企业名称，渠道商必填 */
    @ApiModelProperty(name = "corpName", value = "企业名称")
    private String corpName;

    /** 渠道商结算费率，非必填 */
    @ApiModelProperty(name = "settleRate", value = "渠道商结算费率")
    private BigDecimal settleRate;

    /** 备注 */
    @ApiModelProperty(name = "memo", value = "备注")
    private String memo;

    /** 注册来源 */
    @ApiModelProperty(name = "registerFrom", value = "注册来源")
    private String registerFrom;

    /** 自定义角色 */
    @ApiModelProperty(name = "customRole", value = "自定义角色")
    private String customRole;

    /** 是否只允许主账号创建子账号 */
    @ApiModelProperty(name = "onlyMasterCanCreate", value = "是否只允许主账号创建子账号")
    private boolean onlyMasterCanCreate = true;

    /** 用户状态 */
    @EnumValid(value = UserStatusEnum.class, nullable = true)
    @ApiModelProperty(name = "userStatus", value = "用户状态")
    private String userStatus = UserStatusEnum.ENABLED.getCode();

    /** 审核信息 */
    @ApiModelProperty(name = "userReviewInfo", value = "审核信息")
    private UserReviewInfo userReviewInfo;

    /** 是否需要初始化muse点（100点），Y|N，缺省为N */
    @ApiModelProperty(name = "needInitMusePoints", value = "是否需要初始化muse点")
    private String needInitMusePoints;

    /** 是否需要初始化分销商相关的账号，只对当前待分配的主账号是渠道商账号时有效 */
    @ApiModelProperty(name = "initDistributorRelatedAccounts", value = "是否需要初始化分销商相关的账号")
    private boolean initDistributorRelatedAccounts = false;

    /** 密码 */
    @ApiModelProperty(name = "password", value = "密码")
    private String password;

    /** 销售类型 */
    @ApiModelProperty(name = "salesType", value = "销售类型")
    private String salesType;
}
