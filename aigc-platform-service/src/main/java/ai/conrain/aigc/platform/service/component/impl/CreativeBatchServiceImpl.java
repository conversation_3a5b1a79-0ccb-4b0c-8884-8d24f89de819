package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.CreativeBatchDAO;
import ai.conrain.aigc.platform.dal.entity.CreativeBatchDO;
import ai.conrain.aigc.platform.dal.entity.StatsQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.StatsUserQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.UserCountDO;
import ai.conrain.aigc.platform.dal.example.CreativeBatchExample;
import ai.conrain.aigc.platform.dal.example.CreativeBatchExample.Criteria;
import ai.conrain.aigc.platform.integration.aliyun.AliyunTryonService;
import ai.conrain.aigc.platform.integration.aliyun.EraseService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.aliyun.model.ImageOperateOutputModal;
import ai.conrain.aigc.platform.integration.aliyun.model.TryonTaskOutputModel;
import ai.conrain.aigc.platform.integration.kling.KlingTaskParams;
import ai.conrain.aigc.platform.integration.kling.KlingTaskRet;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.CreativeBatchElementsService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.creative.CreativeServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.DispatchServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.TaskDispatch;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.TaskStatusEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.EraseBrushTaskHelper;
import ai.conrain.aigc.platform.service.helper.KlingVideoHelper;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.helper.RemoveWrinkleTaskHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper.WeakType;
import ai.conrain.aigc.platform.service.model.biz.CommonParamsWrapper;
import ai.conrain.aigc.platform.service.model.biz.TryonRefinerTaskParams;
import ai.conrain.aigc.platform.service.model.biz.TryonTaskParams;
import ai.conrain.aigc.platform.service.model.biz.VideoClipGenReq;
import ai.conrain.aigc.platform.service.model.biz.VideoClipTask;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.common.UserCountVO;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchElementsQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchElementsVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.BizConstants.BIZ_TAG;
import static ai.conrain.aigc.platform.service.constants.BizConstants.DOWNLOADED_IMGS;
import static ai.conrain.aigc.platform.service.constants.BizConstants.EXAMPLE_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ASSIGN_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CREATIVE_QUERY_LASTED_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DEMO_TAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DISLIKE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_END_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LATEST_ZIP_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LIKE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LIKE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MASK_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MIDDLE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REFINE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_COMMON_TASK;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_START_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEMP_VIDEO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEMP_VIDEO_TASK;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRANS_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ZIP_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.ONE_DAY_SECONDS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.CREATIVE_VIP_USERS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.IDLE_MAX_PER_CUSTOMER;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;

/**
 * CreativeBatchService实现
 *
 * <AUTHOR>
 * @version CreativeBatchService.java v 0.1 2024-05-08 03:35:56
 */
@Slf4j
@Service
public class CreativeBatchServiceImpl implements CreativeBatchService {
    private static final String LOCK_KEY_PREFIX = "_sync_batch_lock_";
    private static final int LOCK_EXPIRE_TIME = 5 * 60 * 1000;
    /** DAO */
    @Autowired
    private CreativeBatchDAO creativeBatchDAO;
    @Autowired
    private UserService userService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private CreativeBatchElementsService creativeBatchElementsService;
    @Autowired
    private UserPointService userPointService;
    @Lazy
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private TairService tairService;
    @Lazy
    @Autowired
    private CreativeServiceFactory creativeService;
    @Lazy
    @Autowired
    private TaskDispatch creativeTaskDispatch;
    @Lazy
    @Autowired
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    private WeakLockHelper weakLockHelper;
    @Autowired
    private OssService ossService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private KlingVideoHelper klingVideoHelper;

    @Autowired
    private CommonTaskService commonTaskService;

    @Autowired
    private AliyunTryonService aliyunTryonService;

    @Autowired
    private OssHelper ossHelper;

    private static final String VIDEO_CLIP_SUBMIT = "VIDEO_CLIP_SUBMIT";

    @Autowired
    private RemoveWrinkleTaskHelper removeWrinkleTaskHelper;
    @Autowired
    private EraseService eraseService;
    @Autowired
    private EraseBrushTaskHelper eraseBrushTaskHelper;
    @Autowired
    private DispatchServiceFactory dispatchServiceFactory;

    @Override
    public CreativeBatchVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        CreativeBatchVO ret = CreativeBatchConverter.do2VO(data);

        fillElements(ret);
        fillVideoClipTaskStatus(ret);

        return ret;
    }

    @Override
    public CreativeBatchVO getCreativeBatchByIdWithTask(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        CreativeBatchVO ret = CreativeBatchConverter.do2VO(data);

        fillElements(ret, true);
        fillVideoClipTaskStatus(ret);

        return ret;
    }

    @Override
    public CreativeBatchVO getAndSync(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.BIZ_FAIL, "CreativeBatch不存在");

        // 如果当前记录未结束，则进行一次同步状态
        if (!data.getStatus().isEnd()) {
            try {
                syncStatus(data);
                log.info("状态同步成功，当前状态为{}", data.getStatus());
            } catch (Exception e) {
                log.error("状态同步失败，直接返回之前的数据batchId=" + id, e);
            }
        }

        return data;
    }

    @Override
    public List<CreativeBatchVO> batchQueryByIds(List<Integer> ids, Boolean isSelectTask) {
        CreativeBatchExample example = new CreativeBatchExample();
        example.createCriteria().andIdIn(ids).andLogicalDeleted(false);

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<CreativeBatchVO> result = CreativeBatchConverter.doList2VOList(list);
        fillElementInfo(result, isSelectTask);
        return result;
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = creativeBatchDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeBatch失败");
    }

    @Override
    public <T extends CreativeRequest> CreativeBatchVO create(CreativeTypeEnum type, T request) throws IOException {
        return creativeService.create(type, request);
    }

    @Override
    public void updateOriginalImg4Video(Integer batchId, Integer index, String imageUrl) {
        AssertUtil.assertNotNull(batchId, ResultCode.PARAM_INVALID, "batchId is null");
        AssertUtil.assertNotNull(index, ResultCode.PARAM_INVALID, "index is null");
        AssertUtil.assertNotBlank(imageUrl, ResultCode.PARAM_INVALID, "imageUrl is null");

        CreativeBatchVO data = selectById(batchId);

        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "对应的图生视频批次不存在");
        AssertUtil.assertTrue(data.getType().isVideoCreative(), ResultCode.BIZ_FAIL, "目标任务不是图生视频任务");
        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        if (data.getExtInfo() == null || data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE) == null) {
            log.warn("当前视频任务数据不完整，可能是脏数据ext={}", data.getExtInfo());
            throw new BizException(ResultCode.BIZ_FAIL);
        }

        // 更新信息
        String originImage = data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).getString(index);
        data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).set(index, imageUrl);
        data.getExtInfo().remove(KEY_TEMP_VIDEO + index);
        data.getExtInfo().remove(KEY_TEMP_VIDEO_TASK + index);

        log.info("updateOriginalImg4Video monitor,batchId={},index={},originImage={},updated to imageUrl={}", batchId,
            index, originImage, imageUrl);

        // 同时清空result images，防止影响展示（否则重置创作状态时会有展示问题）
        data.setResultImages(new ArrayList<>());

        if (data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).size() == 1) {
            data.setShowImage(imageUrl);
        }

        updateByIdSelective(data);
    }

    @Override
    public void updateByIdSelective(CreativeBatchVO creativeBatch) {
        AssertUtil.assertNotNull(creativeBatch, ResultCode.PARAM_INVALID, "creativeBatch is null");
        AssertUtil.assertTrue(creativeBatch.getId() != null, ResultCode.PARAM_INVALID, "creativeBatch.id is null");

        // 如果任务完结时，释放占用的服务和端口
        if (creativeBatch.getStatus() != null && creativeBatch.getStatus().isEnd()) {
            // 释放分发服务
            DispatchTypeEnum dispatchTypeEnum = DispatchTypeEnum.getByServerType(creativeBatch.getType());
            dispatchServiceFactory.release(dispatchTypeEnum, creativeBatch);

            //creativeTaskDispatch.release(creativeBatch);
        }

        // 修改时间必须更新
        creativeBatch.setModifyTime(new Date());
        CreativeBatchDO data = CreativeBatchConverter.vo2DO(creativeBatch);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = creativeBatchDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CreativeBatch失败，影响行数:" + n);
    }

    @Override
    public List<CreativeBatchVO> queryCreativeBatchList(CreativeBatchQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CreativeBatchExample example = CreativeBatchConverter.query2Example(query);

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);
        List<CreativeBatchVO> ret = CreativeBatchConverter.doList2VOList(list);
        if (CollectionUtils.isNotEmpty(ret)) {
            fillElements(ret);
        }

        return ret;
    }

    /**
     * 带条件分页查询创作批次
     */
    @Override
    public PageInfo<CreativeBatchVO> queryCreativeBatchByPage(CreativeBatchQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<CreativeBatchVO> page = new PageInfo<>();

        CreativeBatchExample example = CreativeBatchConverter.query2Example(query);
        long totalCount = creativeBatchDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);
        page.setList(CreativeBatchConverter.doList2VOList(list));
        if (CollectionUtils.isNotEmpty(page.getList())) {
            List<CreativeBatchVO> vos = page.getList();
            fillElements(vos);
            page.setList(vos);
        }

        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Async
    @Override
    public void syncStatus(CreativeBatchVO data) {
        log.info("[syncStatus]异步执行状态同步开始,batchId={}, batchType={}", data.getId(), data.getType());
        if (!weakLockHelper.lock(WeakType.BATCH, data.getId())) {
            // 3秒内已执行过同步，直接返回，减少数据库压力
            log.info("3秒内任务已经进行过同步，直接返回当前数据，不需要同步状态,id={},uid={}", data.getId(),
                data.getOperatorId());
            return;
        }

        if (data.getType() == CreativeTypeEnum.CREATE_VIDEO) {
            log.info("【syncStatus】，进入视频创作流程={}={}", data.getId(), data.getType());

            // 视频任务，如果有请求外部视频创作api时，需要同步状态
            CreativeBatchVO target = selectById(data.getId());
            List<VideoClipTask> unCompletedVideoClipTasks = target.getUnCompletedVideoClipTasks();
            if (CollectionUtils.isNotEmpty(unCompletedVideoClipTasks)) {
                log.info("【syncStatus】当前视频创作中，有关联到外部api生成，需要同步状态batchId={}", target.getId());
                for (VideoClipTask each : unCompletedVideoClipTasks) {
                    syncEachUnCompletedVideoClipTask(each, target);
                }
                this.updateByIdSelective(target);
            }

            return;
        }

        // 衣服去皱（走咻图外部api发起的任务，不走comfyui，通过查询oss去判断任务状态）
        if (data.getType() == CreativeTypeEnum.REMOVE_WRINKLE) {
            CreativeBatchVO targetBatch = selectById(data.getId());
            if (!targetBatch.getStatus().isEnd()) {
                syncStatus4RemoveWrinkleTask(targetBatch);
            }
            return;
        }

        if (data.getType().isManual()) {
            log.info("【syncStatus】当前批次id={}为手动任务type={}，无需调度，直接返回查询到的结果即可", data.getId(),
                data.getType());
            return;
        }

        log.info("【syncStatus】，id={},uid={},opid={},status={}", data.getId(), data.getUserId(), data.getOperatorId(),
            data.getStatus());

        long t1 = System.currentTimeMillis();

        String lockKey = LOCK_KEY_PREFIX + data.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.info("任务正在处理中，直接返回当前数据，不需要同步状态,id={},uid={}", data.getId(), data.getOperatorId());
            return;
        }

        CreativeBatchVO target = selectById(data.getId());
        try {
            // 分布式锁之后，再查一遍数据库
            AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "data is null");
            if (target.getStatus().isEnd()) {
                log.info("任务已经结束，无需同步,id={},uid={}", target.getId(), target.getOperatorId());
                return;
            }

            DispatchTypeEnum dispatchTypeEnum = DispatchTypeEnum.getByServerType(target.getType());
            String serverUrl = dispatchServiceFactory.dispatch(dispatchTypeEnum, target);

            if (StringUtils.isBlank(serverUrl)) {
                log.warn("当前可用服务为0，直接返回，operator={},batchId={}", target.getOperatorId(), target.getId());
                return;
            }

            List<CreativeTaskVO> taskList = creativeTaskService.queryTaskByBatchId(target.getId());
            AssertUtil.assertTrue(CollectionUtils.isNotEmpty(taskList), ResultCode.BIZ_FAIL, "任务列表为空");

            List<CreativeTaskVO> unfinishedTasks = taskList.stream().filter(
                task -> task.getStatus() != CreativeStatusEnum.FINISHED).collect(Collectors.toList());

            target.setStatus(CreativeStatusEnum.PROCESSING);

            // 查询进行中的comfyui任务状态
            if (CollectionUtils.isNotEmpty(unfinishedTasks)) {
                Integer serverId = target.getExtValue(KEY_SERVER_ID, Integer.class);
                creativeTaskService.batchSyncStatus(unfinishedTasks, serverUrl, serverId);
            }

            AtomicInteger finishedCnt = new AtomicInteger(0);
            final BigDecimal[] taskSchedule = {BigDecimalUtils.newZero()};

            // 遍历任务列表，更新状态和进度
            taskList.forEach(task -> {

                if (task.getStatus() != CreativeStatusEnum.FINISHED) {
                    // 更新状态
                    target.setStatus(task.getStatus().getOrder() > target.getStatus().getOrder() ? task.getStatus()
                        : target.getStatus());

                    if (task.getStatus() == CreativeStatusEnum.PROCESSING) {
                        BigDecimal item = task.getExtInfo() != null ? task.getExtInfo().getBigDecimal(KEY_SCHEDULE)
                            : null;
                        item = item != null ? item : BigDecimalUtils.newZero();
                        taskSchedule[0] = BigDecimalUtils.greaterThan(item, taskSchedule[0]) ? item : taskSchedule[0];
                    }
                } else {
                    target.addResultImage(task.getResultImages());
                    finishedCnt.set(finishedCnt.get() + 1);
                }
            });

            if (target.getStatus() == CreativeStatusEnum.PROCESSING) {
                JSONObject json = new JSONObject();
                json.put("finished", finishedCnt);
                json.put("subSchedule", CommonConstants.CATTY_DECIMAL_FORMAT.format(taskSchedule[0]));
                target.addExtInfo(CommonConstants.KEY_SCHEDULE, json);

                if (StringUtils.isBlank(target.getExtValue(KEY_START_TIME, String.class))) {
                    target.addExtInfo(KEY_START_TIME, DateUtils.formatFullTime(new Date()));
                }
            }

            if (finishedCnt.get() == target.getBatchCnt()) {
                log.info("{}当前所有子任务都已经完成，设置状态为finished", target.getId());
                target.setStatus(CreativeStatusEnum.FINISHED);

                if (StringUtils.isBlank(target.getExtValue(KEY_END_TIME, String.class))) {
                    target.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
                }

                // 结束的时候将翻译结果回填到batch中，方便问题排查
                String trans = target.getExtValue(KEY_TRANS_CLOTH_COLLOCATION, String.class);
                if (StringUtils.isBlank(trans) && StringUtils.isNotBlank(
                    target.getExtValue(KEY_ORIGIN_CLOTH_COLLOCATION, String.class))) {

                    target.addExtInfo(KEY_TRANS_CLOTH_COLLOCATION,
                        taskList.get(0).getExtValue(KEY_TRANS_CLOTH_COLLOCATION, String.class));
                }
            }

            // 图片生成则更新本次创作的show image为结果图第一张图
            if (CollectionUtils.isNotEmpty(target.getResultImages()) && !target.getType().isVideoCreative()) {
                target.setShowImage(target.getResultImages().get(0));
            }
        } catch (Exception e) {
            log.error("syncStatus异常,batchId=" + data.getId(), e);
        } finally {

            if (target != null) {
                updateByIdSelective(target);
            }
            // 释放锁
            tairService.releaseLock(lockKey);

            long t2 = System.currentTimeMillis();
            log.info("syncStatus耗时：{} ms, id:{}", t2 - t1, data.getId());
        }
    }

    private void syncStatus4RemoveWrinkleTask(CreativeBatchVO targetBatch) {
        log.info("[syncStatus]衣服去皱任务id={}", targetBatch.getId());

        CommonTaskVO removeWrinkleTask = commonTaskService.selectById(
            targetBatch.getExtValue(CommonConstants.KEY_RELATED_COMMON_TASK, Integer.class));
        AssertUtil.assertNotNull(removeWrinkleTask, ResultCode.BIZ_FAIL, "关联的去皱任务不存在");

        String targetOssObjectName = removeWrinkleTask.getStringFromExtInfo(CommonConstants.KEY_TARGET_OSS_OBJECT_NAME);
        AssertUtil.assertNotBlank(targetOssObjectName, ResultCode.BIZ_FAIL,
            "衣服去皱任务的字段缺失：" + CommonConstants.KEY_TARGET_OSS_OBJECT_NAME + ",taskId:"
            + removeWrinkleTask.getId());

        // 判断文件存在，则去皱任务成功
        if (ossService.checkFileExists(targetOssObjectName)) {
            String retUrl = ossService.getSignedFileUrl(targetOssObjectName);
            AssertUtil.assertNotBlank(retUrl, ResultCode.BIZ_FAIL,
                "衣服去皱任务成功但获取文件url失败：" + targetOssObjectName + ",taskId:" + removeWrinkleTask.getId());

            // 更新common task
            JSONObject retDetail = new JSONObject();
            retDetail.put(CommonConstants.KEY_RESULT_IMG_URL, retUrl);
            removeWrinkleTask.setRetDetail(retDetail.toJSONString());

            removeWrinkleTask.setOutTaskStatus(CommonTaskEnums.TaskStatus.COMPLETED.name());
            removeWrinkleTask.setTaskStatus(CommonTaskEnums.TaskStatus.COMPLETED.name());
            removeWrinkleTask.setTaskEndTime(new Date());
            commonTaskService.updateByIdSelective(removeWrinkleTask);

            // 更新targetBatch
            targetBatch.addResultImage(retUrl);
            targetBatch.setStatus(CreativeStatusEnum.FINISHED);
            targetBatch.setShowImage(retUrl);
            targetBatch.addExtInfo(KEY_MIDDLE_IMAGE, retUrl);
            targetBatch.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));

            // 文件不存在，判断轮询的时间是否已经超时，超时需要重试
        } else {
            Date createTime = removeWrinkleTask.getCreateTime();

            // 判断当前时间是否已经超过30秒
            if (DateUtils.diffNowSecond(createTime) > 120) {
                // 超时，更新状态为失败
                removeWrinkleTask.setOutTaskStatus("TIMEOUT");
                removeWrinkleTask.setTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
                commonTaskService.updateByIdSelective(removeWrinkleTask);

                // 重试，创建新任务
                log.error("[syncStatus]衣服去皱任务id={}超时未完成，需要重试", targetBatch.getId());
                DingTalkNoticeHelper.sendMsg2DevGroup(
                    String.format("衣服去皱任务超时失败重试\nbatchId:%s, commonTaskId:%s\ntraceId:%s",
                        targetBatch.getId(), removeWrinkleTask.getId(), MDC.get("traceId")));

                String originImgUrl = removeWrinkleTask.getStringFromExtInfo(KEY_ORIGIN_IMAGE);
                AssertUtil.assertNotBlank(originImgUrl, ResultCode.BIZ_FAIL, "原始图片为空");

                CommonTaskVO newRemoveWrinkleTask = removeWrinkleTaskHelper.createRemoveWrinkleTask(originImgUrl,
                    targetBatch);

                targetBatch.addExtInfo(KEY_RELATED_COMMON_TASK, newRemoveWrinkleTask.getId());
                targetBatch.setStatus(CreativeStatusEnum.PROCESSING);

                // 没超时，没结果，需要继续轮询
            } else {
                targetBatch.setStatus(CreativeStatusEnum.PROCESSING);
            }
        }

        this.updateByIdSelective(targetBatch);
    }

    // 同步消除笔任务状态
    private void syncStatus4EraseBrushTask(CreativeBatchVO targetBatch) {
        log.info("[图片擦除任务同步]batchId={}", targetBatch.getId());
        CommonTaskVO eraseBrushTask = commonTaskService.selectById(
            targetBatch.getExtValue(KEY_RELATED_COMMON_TASK, Integer.class));
        AssertUtil.assertNotNull(eraseBrushTask, ResultCode.BIZ_FAIL, "[图片擦除任务同步]任务不存在");

        String outTaskId = eraseBrushTask.getOutTaskId();
        AssertUtil.assertNotNull(outTaskId, ResultCode.BIZ_FAIL, "[图片擦除任务同步]字段缺失");

        ImageOperateOutputModal response = eraseService.queryEraseTask(outTaskId);
        if (Objects.nonNull(response)) {
            log.info("[图片擦除任务同步]同步状态查询成功, response={}", response);
            switch (response.getTaskStatus()) {
                case "SUCCEEDED":
                    eraseBrushTask.setTaskStatus(CommonTaskEnums.TaskStatus.COMPLETED.name());
                    eraseBrushTask.setRetDetail(JSONObject.toJSONString(response));
                    eraseBrushTask.setTaskEndTime(new Date());
                    commonTaskService.updateByIdSelective(eraseBrushTask);
                    targetBatch.setStatus(CreativeStatusEnum.FINISHED);
                    targetBatch.setShowImage(response.getOutputImageUrl());
                    targetBatch.addResultImage(response.getOutputImageUrl());
                    targetBatch.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
                    break;
                case "RUNNING":
                    eraseBrushTask.setTaskStatus(CommonTaskEnums.TaskStatus.RUNNING.name());
                    commonTaskService.updateByIdSelective(eraseBrushTask);
                    targetBatch.setStatus(CreativeStatusEnum.PROCESSING);
                    break;
                case "FAILED":
                    eraseBrushTask.setTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
                    eraseBrushTask.setRetDetail(JSONObject.toJSONString(response));
                    eraseBrushTask.setTaskEndTime(new Date());
                    commonTaskService.updateByIdSelective(eraseBrushTask);
                    targetBatch.setStatus(CreativeStatusEnum.FAILED);
                    targetBatch.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
                    break;
                default:
                    break;
            }
        } else {
            log.info("[图片擦除任务同步]同步状态查询失败, batchId={}, commonTaskId={}", targetBatch.getId(),
                eraseBrushTask.getId());
            Date createTime = eraseBrushTask.getCreateTime();
            // 超时重试
            if (DateUtils.diffNowSecond(createTime) > 180) {
                eraseBrushTask.setOutTaskStatus("TIMEOUT");
                eraseBrushTask.setTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
                commonTaskService.updateByIdSelective(eraseBrushTask);

                // 创建新任务重试
                log.error("[图片擦除任务同步]commonTaskId={}超时未完成,开始重试", eraseBrushTask.getId());
                DingTalkNoticeHelper.sendMsg2DevGroup(
                    String.format("[图片擦除任务同步]超时失败\nbatchId:%s, commonTaskId:%s\ntraceId:%s",
                        targetBatch.getId(), eraseBrushTask.getId(), MDC.get("traceId")));
                String originImgUrl = eraseBrushTask.getStringFromExtInfo(KEY_ORIGIN_IMAGE);
                AssertUtil.assertNotNull(originImgUrl, ResultCode.BIZ_FAIL, "[图片擦除任务同步]原始图片为空");
                String maskImgUrl = eraseBrushTask.getStringFromExtInfo(KEY_MASK_IMAGE);
                AssertUtil.assertNotNull(maskImgUrl, ResultCode.BIZ_FAIL, "[图片擦除任务同步]蒙版图片为空");
                CommonTaskVO newEraseBrushTask = eraseBrushTaskHelper.createEraseBrushTask(originImgUrl, maskImgUrl,
                    targetBatch);
                AssertUtil.assertNotNull(newEraseBrushTask, ResultCode.BIZ_FAIL, "[图片擦除任务同步]任务创建失败");
                targetBatch.addExtInfo(KEY_RELATED_COMMON_TASK, newEraseBrushTask.getId());
                targetBatch.setStatus(CreativeStatusEnum.PROCESSING);
            } else {
                targetBatch.setStatus(CreativeStatusEnum.PROCESSING);
            }
        }
        this.updateByIdSelective(targetBatch);
    }

    private void syncEachUnCompletedVideoClipTask(VideoClipTask clipTaskView, CreativeBatchVO targetBatch) {

        if (clipTaskView.getTaskId() == null) {
            log.warn("clipTaskView.taskId为空，预期之外的脏数据，忽略:{}", clipTaskView);
            return;
        }

        CommonTaskVO task = commonTaskService.lockById(clipTaskView.getTaskId());
        if (task == null) {
            log.error("当前task不存在，忽略，common_task id={}", clipTaskView.getTaskId());
            return;
        }

        // 外部任务id
        String outTaskId = task.getOutTaskId();

        // 任务待提交
        if (StringUtils.isBlank(outTaskId)) {

            // 控频次，3秒内只提交一个创建任务
            boolean canSubmit = tairService.acquireLock(VIDEO_CLIP_SUBMIT, 100);
            if (canSubmit) {
                // 请求可灵生成任务
                KlingTaskParams klingTaskParams = new KlingTaskParams();
                klingTaskParams.setPrompt(clipTaskView.getPrompt().trim());
                klingTaskParams.setDuration(clipTaskView.getDuration());

                String originalImageUrl = getOriginalImgUrlByIndex(targetBatch, clipTaskView.getIndex());
                AssertUtil.assertNotBlank(originalImageUrl, ResultCode.PARAM_INVALID, "视频片段原图地址不能为空");
                klingTaskParams.setImageUrl(originalImageUrl);

                String klingTaskId = null;
                try {
                    klingTaskId = klingVideoHelper.createVideoTask(klingTaskParams);
                } catch (Exception e) {
                    log.error("[syncStatus]创建视频任务失败", e);
                }

                // 提交创建失败
                if (StringUtils.isBlank(klingTaskId)) {

                    tairService.releaseLock(VIDEO_CLIP_SUBMIT);

                    log.error("创建视频任务失败，等下次重试，commonTask.id={}", task.getId());

                    DingTalkNoticeHelper.sendMsg2DevGroup(
                        "创建视频任务失败，等下次重试\n任务batchId=" + targetBatch.getId() + "\ntraceId=" + MDC.get(
                            "traceId"));

                    // 提交创建成功
                } else {

                    clipTaskView.setOutTaskId(klingTaskId);
                    clipTaskView.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());

                    // 更新batch
                    targetBatch.addExtInfo(KEY_TEMP_VIDEO_TASK + clipTaskView.getIndex(), clipTaskView);

                    // 更新common task
                    task.setReqBizParams(CommonParamsWrapper.wrap(klingTaskParams));
                    task.setOutTaskId(klingTaskId);
                    task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());
                    commonTaskService.updateByIdSelective(task);
                }

            } else {
                log.info("需要提交视频切片任务，但没请求到锁，等下次调度 CommonTask.id:{}", clipTaskView.getTaskId());
            }

            // 已经有任务，且任务状态没有结束时，查询任务状态
        } else if (!CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus()) || !CommonTaskEnums.TaskStatus.isEnd(
            clipTaskView.getTaskStatus())) {
            KlingTaskRet klingTaskRet = klingVideoHelper.getVideoTask(outTaskId);

            // 查询失败（网络超时的情况）
            if (klingTaskRet == null || klingTaskRet.getStatus() == null) {
                log.info("查询视频结果失败，下次查询再重试。返回：{}", klingTaskRet);

                // 查询到结果，再看是否生成成功
            } else {
                String taskStatus = CommonTaskEnums.TaskStatus.fromKlingTaskStatus(klingTaskRet.getStatus()).name();
                clipTaskView.setTaskStatus(taskStatus);

                // 视频生成成功
                if (StringUtils.isNotBlank(klingTaskRet.getOutVideoUrl()) && StringUtils.isNotBlank(
                    klingTaskRet.getOssVideoUrl())) {

                    clipTaskView.setOutVideoUrl(klingTaskRet.getOutVideoUrl());

                    // 更新oss url
                    targetBatch.addExtInfo(KEY_TEMP_VIDEO + clipTaskView.getIndex(), klingTaskRet.getOssVideoUrl());
                    clipTaskView.setOssVideoUrl(klingTaskRet.getOssVideoUrl());
                }

                // 更新batch
                targetBatch.addExtInfo(KEY_TEMP_VIDEO_TASK + clipTaskView.getIndex(), clipTaskView);

                // 更新common task
                task.setTaskStatus(taskStatus);
                task.setOutTaskStatus(klingTaskRet.getStatus().name());
                if (task.getTaskStartTime() == null) {
                    task.setTaskStartTime(new Date());
                }
                if (CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus())) {
                    task.setTaskEndTime(new Date());
                }
                task.setRetDetail(JSONObject.toJSONString(klingTaskRet));

                commonTaskService.updateByIdSelective(task);

                // 视频任务失败情况，（重试，或通知人工处理）
                if (StringUtils.equals(taskStatus, CommonTaskEnums.TaskStatus.FAILED.name())) {
                    CommonTaskQuery ctq = new CommonTaskQuery();
                    ctq.setRelatedBizType(CommonTaskEnums.RelatedBizType.CREATE_VIDEO_CLIP.getCode());
                    ctq.setRelatedBizId(clipTaskView.getId() + "_" + clipTaskView.getIndex());
                    List<CommonTaskVO> list = commonTaskService.queryCommonTaskList(ctq);
                    if (list == null || list.size() <= 5) {
                        log.warn("视频任务失败，进行自动重试，batchId:{}, index:{}", clipTaskView.getId(),
                            clipTaskView.getIndex());

                        DingTalkNoticeHelper.sendMsg2DevGroup(
                            "视频任务失败，系统自动重试\n重试次数=" + (list == null ? 1 : list.size()) + "\ntraceId="
                            + MDC.get("traceId"));

                        VideoClipGenReq retry = new VideoClipGenReq();
                        retry.setId(clipTaskView.getId());
                        retry.setIndex(clipTaskView.getIndex());
                        retry.setPrompt(clipTaskView.getPrompt());
                        retry.setDuration(clipTaskView.getDuration());

                        this.apply2GenVideoClip(retry);

                    } else {
                        log.error("视频任务失败，尝试{}次后仍然失败，需要页面手工处理，返回：{}", list.size(),
                            klingTaskRet);
                        DingTalkNoticeHelper.sendMsg2DevGroup(
                            "视频任务失败，需要页面上重试\ntraceId=" + MDC.get("traceId"));
                    }
                }
            }
        } else {
            log.info("当前视频切片任务需要人工处理，状态:{}，Batch.id:{}, CommonTask.id:{}", task.getTaskStatus(),
                clipTaskView.getId(), clipTaskView.getTaskId());
        }
    }

    private String getOriginalImgUrlByIndex(CreativeBatchVO batch, Integer index) {
        if (batch.getExtInfo() != null && batch.getExtInfo().containsKey(KEY_ORIGIN_IMAGE)
            && batch.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE) != null && batch.getExtInfo().getJSONArray(
            KEY_ORIGIN_IMAGE).size() > index) {

            return batch.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).getString(index);
        }
        return null;
    }

    @Override
    public List<CreativeBatchVO> queryActive(List<String> types, ModelTypeEnum modelType) {
        CreativeBatchExample example = new CreativeBatchExample();
        Criteria criteria = example.createCriteria();
        criteria.andOperatorIdEqualTo(OperationContextHolder.getOperatorUserId()).andStatusIn(
            CreativeStatusEnum.getUncompleteStatusList()).andDeletedEqualTo(false).andTypeIn(types);
        if (modelType != null) {
            criteria.andModelTypeEqualTo(modelType.getCode());
        }
        // 正序，取最早的那个任务
        example.setOrderByClause("id");

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);
        return CreativeBatchConverter.doList2VOList(list);
    }

    /**
     * 查询今天的创作批次列表
     *
     * @param types     创作类型列表，筛选特定类型的创作批次
     * @param modelType 模型类型，筛选特定模型类型的创作批次
     * @param isSelect  是否查询任务列表，默认不查询
     * @return 符合条件的创作批次VO列表
     */
    @Override
    public List<CreativeBatchVO> queryTodayList(List<String> types, ModelTypeEnum modelType, boolean isSelect,
                                                CreativeBizTypeEnum bizType) {
        // 1、构建查询条件  设置基础查询条件：未删除且类型在指定列表中
        CreativeBatchExample example = new CreativeBatchExample();
        Criteria criteria = example.createCriteria().andDeletedEqualTo(false).andTypeIn(types);

        if (bizType != null && bizType != CreativeBizTypeEnum.ALL) {
            criteria.andBizTypeEqualTo(bizType.getCode());
        }

        // 2、根据角色设置不同的查询条件
        // 普通商家和运营角色
        if (!OperationContextHolder.isDistributorRole()) {
            criteria.andOperatorIdOrBizTagEqualTo(OperationContextHolder.getOperatorUserId(), EXAMPLE_IMAGES);
            // 渠道商角色
        } else {
            criteria.andOperatorIdEqualTo(OperationContextHolder.getOperatorUserId());
        }

        // 3、非后台角色且无特殊权限时的模型权限过滤
        if (!OperationContextHolder.isBackRole() && !systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
            OperationContextHolder.getOperatorUserId())) {
            if (OperationContextHolder.isDistributorRole()) {
                criteria.andEnabledModel();
            } else {
                criteria.andEnabledModel(OperationContextHolder.getMasterUserId());
            }
        }

        // 4、设置用户ID过滤，只查询当前主账号的数据
        criteria.andUserIdEqualTo(OperationContextHolder.getMasterUserId());

        // 5、设置模型类型过滤(如果指定)
        if (modelType != null) {
            criteria.andModelTypeEqualTo(modelType.getCode());
        }

        // 6、设置时间范围过滤(昨天之后的数据)
        criteria.andCreateTimeGreaterThan(DateUtils.getYesterday());

        // 7、增量查询处理：如果有上次查询时间，则只查询该时间之后的修改数据
        Long lastedTime = tairService.getObject(getQueryLastedTimeKey(types, modelType), Long.class);
        if (lastedTime != null) {
            criteria.andModifyTimeGreaterThanOrEqualTo(new Date(lastedTime));
        }

        // 8、设置排序(按ID倒序)
        example.setOrderByClause("id desc");

        // 9、执行查询
        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);

        // 10、 转换结果并填充额外信息
        List<CreativeBatchVO> result = CreativeBatchConverter.doList2VOList(list);
        fillElementInfo(result, isSelect);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Integer id) {
        CreativeBatchVO data = selectById(id);
        if (data == null) {
            log.info("取消任务时发现任务不存在，直接跳过，id:{}", id);
            return;
        }

        AssertUtil.assertOperatePermission(data.getUserId());

        AssertUtil.assertTrue(data.getStatus() == CreativeStatusEnum.QUEUE, ResultCode.NOT_QUEUE_CANNOT_CANCEL,
            "当前批次任务正在处理中，不允许取消" + id);

        String lockKey = LOCK_KEY_PREFIX + data.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.error("任务正在处理中，无法取消,id={},uid={}", data.getId(), data.getOperatorId());
            throw new BizException(ResultCode.NOT_QUEUE_CANNOT_CANCEL, "当前批次任务正在处理中，不允许取消" + id);
        }

        try {
            CreativeBatchDO batch = creativeBatchDAO.lockByPrimaryKey(data.getId());
            AssertUtil.assertNotNull(batch, ResultCode.BIZ_FAIL, "任务不存在" + id);

            // 执行删除动作
            deleteById(id);

            List<CreativeTaskVO> tasks = creativeTaskService.queryTaskByBatchId(id);
            for (CreativeTaskVO each : tasks) {
                AssertUtil.assertTrue(each.getStatus() == CreativeStatusEnum.INIT, ResultCode.NOT_QUEUE_CANNOT_CANCEL,
                    "当前任务正在处理中，不允许取消" + id);
                creativeTaskService.deleteById(each.getId());
            }

            // 恢复用户点数(手部修复暂时免费)
            if (BigDecimalUtils.greaterThanZero(data.getType().getConsumeMusePoints())
                && !OperationContextHolder.isDistributorRole() && !OperationContextHolder.isAdmin()) {
                userPointService.revertByImage(data);
            }
        } finally {
            tairService.releaseLock(lockKey);
        }
    }

    @Override
    public void clear(List<String> types, ModelTypeEnum modelType) {
        // 增加缓存
        tairService.setObject(getQueryLastedTimeKey(types, modelType), System.currentTimeMillis(), ONE_DAY_SECONDS);
    }

    /**
     * 查询当前登录用户所有有创作历史的模型(创作记录的服装下拉菜单)
     */
    @Override
    public List<LoraOption> queryModels4HistoryTasks() {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setOrderBy("id desc");

        switch (OperationContextHolder.getRoleType()) {
            // 管理员和运营可以看全部服装
            case ADMIN:
                break;
            case OPERATOR:
                break;

            // 渠道商要进一步分角色（渠道管理，创作记录-服装下拉菜单）
            // 管理员/二级管理员，可以看到自己和下级和会员的所有服装
            case DISTRIBUTOR: {
                List<DistributorCustomerVO> customers
                    = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(true);
                if (CollectionUtils.isEmpty(customers)) {
                    return new ArrayList<>();
                }

                query.setUserIds(customers.stream().map(DistributorCustomerVO::getCustomerMasterUserId)
                    .collect(Collectors.toList()));
                break;
            }
            // 商家只能看自己的
            case MERCHANT: {
                if (OperationContextHolder.isMasterUser()) {
                    query.setUserId(OperationContextHolder.getOperatorUserId());
                } else {
                    query.setOperatorId(OperationContextHolder.getOperatorUserId());
                }
                break;
            }
        }

        query.setMaterialType(MaterialType.cloth.name());

        List<MaterialModelVO> models = materialModelService.queryMaterialModelList(query);
        if (CollectionUtils.isNotEmpty(models)) {
            return models.stream().map(m -> {
                LoraOption option = new LoraOption();
                option.setId(m.getId());
                option.setName(m.getName());
                return option;
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void imageLike(Integer taskId, Integer batchId, Boolean like) {
        CreativeTaskVO task = creativeTaskService.selectById(taskId);

        if (OperationContextHolder.isBackRole() && userService.isCustomer(task.getUserId())) {
            log.info("后台用户不能对前台用户的图片进行点赞/踩，currentUserId={}，直接跳过,batchId={}",
                OperationContextHolder.getOperatorUserId(), task.getBatchId());
            return;
        }

        String likeStatus = like ? KEY_LIKE_STATUS : KEY_DISLIKE_STATUS;

        // 如果是精选图时，可能taskId是不存在的
        if (task != null) {
            CreativeTaskVO target = new CreativeTaskVO();
            target.setId(taskId);
            target.setExtInfo(task.getExtInfo());
            target.addExtInfo(KEY_LIKE, likeStatus);
            creativeTaskService.updateByIdSelective(target);

            if (batchId == null) {
                batchId = task.getBatchId();
            }
        }

        if (batchId == null) {
            log.error("taskId={}，点赞/踩异常{}，batchId为空", taskId, like);
            return;
        }

        // 同时更新batch中的状态，减少前端查询压力
        CreativeBatchVO batch = selectById(batchId);
        CreativeBatchVO target = new CreativeBatchVO();
        target.setId(batchId);
        target.setExtInfo(batch.getExtInfo());

        JSONObject likeObj = target.getExtValue(KEY_LIKE, JSONObject.class);
        if (likeObj == null) {
            likeObj = new JSONObject();
            target.getExtInfo().put(KEY_LIKE, likeObj);
        }

        likeObj.put(String.valueOf(taskId), likeStatus);
        updateByIdSelective(target);
    }

    @Override
    public List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, null, null, true);
    }

    @Override
    public List<CreativeBatchVO> queryUnProcessedTopUser(Integer pipelineId, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, null, null, false);
    }

    @Override
    public List<CreativeBatchVO> queryUnProcessedTopUser(Integer pipelineId, List<String> exceptTypeList,
                                                         List<String> includeTypeList, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, exceptTypeList, includeTypeList, false);
    }

    @Override
    public List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, List<String> exceptTypeList,
                                                         List<String> includeTypeList, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, exceptTypeList, includeTypeList, true);
    }

    @Override
    public CreativeBatchVO insert(CreativeBatchVO data) {
        CreativeBatchDO target = CreativeBatchConverter.vo2DO(data);
        target.setCreateTime(new Date());
        target.setDeleted(false);
        int cnt = creativeBatchDAO.insertSelective(target);
        AssertUtil.assertTrue(cnt == 1 && target.getId() != null, ResultCode.BIZ_FAIL, "创建创作批次失败");
        data.setId(target.getId());
        return data;
    }

    @Override
    public void changeExampleImages(Integer modelId, Integer userId, List<String> exampleImages) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(exampleImages), ResultCode.PARAM_INVALID,
            "exampleImages is null");

        CreativeBatchVO data = queryExampleImagesItem(modelId);
        if (data == null) {
            data = CreativeBatchConverter.buildExampleImagesVO(modelId, userId, exampleImages);
            insert(data);
            return;
        }

        data.setResultImages(exampleImages);
        data.setShowImage(exampleImages.get(0));
        data.setBatchCnt(exampleImages.size());
        updateByIdSelective(data);
    }

    @Override
    public void clearExampleImages(Integer modelId, Integer userId) {
        CreativeBatchExample example = new CreativeBatchExample();
        example.createCriteria().andModelIdEqualTo(modelId).andUserIdEqualTo(userId).andBizTagEqualTo(EXAMPLE_IMAGES);
        creativeBatchDAO.logicalDeleteByExample(example);
    }

    @Override
    public List<String> queryExampleImages(Integer modelId) {
        CreativeBatchVO data = queryExampleImagesItem(modelId);

        if (data == null) {
            return null;
        }
        return data.getResultImages();
    }

    @Override
    public void assignExampleImages(Integer modelId, Integer userId) {
        CreativeBatchVO data = queryExampleImagesItem(modelId);
        if (data == null) {
            return;
        }

        data.setUserId(userId);
        data.setOperatorId(userId);

        updateByIdSelective(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setToFail(Integer id) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次状态错误" + data.getStatus());

        String lockKey = LOCK_KEY_PREFIX + data.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.error("任务正在处理中，无法设置为失败,id={},uid={}", data.getId(), data.getOperatorId());
            throw new BizException(ResultCode.NOT_QUEUE_CANNOT_CANCEL, "当前批次任务正在处理中，无法设置为失败" + id);
        }

        try {
            data.setStatus(CreativeStatusEnum.FAILED);
            updateByIdSelective(data);

            // 设置关联的任务状态为失败
            List<CreativeTaskVO> tasks = creativeTaskService.queryTaskByBatchId(id);
            tasks.forEach(task -> {
                task.setStatus(CreativeStatusEnum.FAILED);
                creativeTaskService.updateByIdSelective(task);
            });
        } finally {
            tairService.releaseLock(lockKey);
        }
    }

    @Override
    public void uploadVideo(Integer id, List<String> videos) {
        CreativeBatchVO data = selectById(id);

        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getType() == CreativeTypeEnum.CREATE_VIDEO, ResultCode.BIZ_FAIL,
            "创作类型错误" + data.getType());

        // 添加前，先清理历史的数据
        if (CollectionUtils.isNotEmpty(data.getResultImages())) {
            data.getResultImages().clear();
        }

        data.addResultImage(videos);
        data.setStatus(CreativeStatusEnum.FINISHED);

        updateByIdSelective(data);
    }

    @Override
    public void removeFixFace(Integer id, Integer index) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        data.getExtInfo().remove(KEY_RELATED + index);
        updateByIdSelective(data);
    }

    @Override
    public void assignVideoOperator(Integer id, String mobile) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getType() == CreativeTypeEnum.CREATE_VIDEO, ResultCode.BIZ_FAIL,
            "创作类型错误" + data.getType());

        data.addExtInfo(KEY_RELATED_OPERATOR, mobile);
        updateByIdSelective(data);
    }

    @Override
    public long queryCount(CreativeBatchQuery query) {
        CreativeBatchExample example = CreativeBatchConverter.query2Example(query);
        return creativeBatchDAO.countByExample(example);
    }

    @Override
    public void applyRefine(Integer id) {
        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        CreativeBatchVO target = CreativeBatchConverter.do2VO(data);

        String bizTag = target.getExtValue(BIZ_TAG, String.class);
        AssertUtil.assertTrue(StringUtils.equals(bizTag, EXAMPLE_IMAGES), ResultCode.BIZ_FAIL, "当前批次非精选图");

        TaskStatusEnum taskStatus = target.getExtValue(KEY_REFINE_STATUS, TaskStatusEnum.class);

        if (taskStatus == TaskStatusEnum.COMPLETED) {
            log.error("精修任务{}已完成{}，但仍请求精修，理论上不存在", id, taskStatus);
            throw new BizException(ResultCode.BIZ_FAIL);
        }
        target.addExtInfo(KEY_REFINE_STATUS, TaskStatusEnum.INIT);

        updateByIdSelective(target);
    }

    @Override
    public void completeRefine(Integer id) {
        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        CreativeBatchVO target = CreativeBatchConverter.do2VO(data);

        String bizTag = target.getExtValue(BIZ_TAG, String.class);
        AssertUtil.assertTrue(StringUtils.equals(bizTag, EXAMPLE_IMAGES), ResultCode.BIZ_FAIL, "当前批次非精选图");

        TaskStatusEnum taskStatus = target.getExtValue(KEY_REFINE_STATUS, TaskStatusEnum.class);
        AssertUtil.assertNotNull(taskStatus, ResultCode.BIZ_FAIL, "精修任务状态为空");

        target.addExtInfo(KEY_REFINE_STATUS, TaskStatusEnum.COMPLETED);

        updateByIdSelective(target);
    }

    @Override
    public void changeTempVideo(Integer id, Integer index, String videoUrl) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");

        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        if (StringUtils.isNotBlank(videoUrl)) {
            data.addExtInfo(KEY_TEMP_VIDEO + index, videoUrl);
        } else {
            data.getExtInfo().remove(KEY_TEMP_VIDEO + index);
            data.getExtInfo().remove(KEY_TEMP_VIDEO_TASK + index);

            // 同时清空result images，防止影响展示（否则重置创作状态时会有展示问题）
            data.setResultImages(new ArrayList<>());
        }

        updateByIdSelective(data);
    }

    /**
     * 申请创建视频片段
     *
     * @param req
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void apply2GenVideoClip(VideoClipGenReq req) {
        CreativeBatchVO batch = selectById(req.getId());
        AssertUtil.assertNotNull(batch, ResultCode.PARAM_INVALID, "创作批次不存在");

        AssertUtil.assertTrue(!batch.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        AssertUtil.assertNotNull(req.getIndex(), ResultCode.PARAM_INVALID, "视频片段索引不能为空");
        AssertUtil.assertNotBlank(req.getPrompt(), ResultCode.PARAM_INVALID, "视频片段描述不能为空");
        AssertUtil.assertNotNull(req.getDuration(), ResultCode.PARAM_INVALID, "视频片段时长不能为空");

        if (batch.getExtInfo() != null && batch.getExtInfo().containsKey(KEY_TEMP_VIDEO_TASK + req.getIndex())) {
            VideoClipTask task = batch.getExtValue(KEY_TEMP_VIDEO_TASK + req.getIndex(), VideoClipTask.class);
            if (task != null && !StringUtils.equals(task.getTaskStatus(), CommonTaskEnums.TaskStatus.FAILED.name())) {
                log.info("当前视频切片任务已经存在，且其状态为{}，不允许重试", task.getTaskStatus());
                return;
            }
        }

        // 保存切片任务记录
        CommonTaskVO task = new CommonTaskVO();
        {
            task.setUserId(OperationContextHolder.getMasterUserId());
            task.setOperatorId(OperationContextHolder.getOperatorUserId());
            task.setTaskType(CommonTaskEnums.TaskType.VIDEO_GENERATION.name());
            task.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());
            task.setRelatedBizType(CommonTaskEnums.RelatedBizType.CREATE_VIDEO_CLIP.getCode());
            task.setRelatedBizId(batch.getId() + "_" + req.getIndex());

            task = commonTaskService.insert(task);
        }

        // 保存切片任务关联到批次
        VideoClipTask batchClipView = new VideoClipTask();
        {
            batchClipView.setId(req.getId());
            batchClipView.setIndex(req.getIndex());
            batchClipView.setPrompt(req.getPrompt());
            batchClipView.setDuration(req.getDuration());
            batchClipView.setTaskId(task.getId());
            batchClipView.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());
            batchClipView.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        }

        batch.addExtInfo(KEY_TEMP_VIDEO_TASK + req.getIndex(), batchClipView);
        updateByIdSelective(batch);
    }

    @Override
    public void resetProcessing(Integer id) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getType() == CreativeTypeEnum.CREATE_VIDEO, ResultCode.BIZ_FAIL,
            "重置为生成中仅支持视频创作");

        if (!data.getStatus().isEnd()) {
            log.warn("当前状态为" + data.getStatus() + "，无需重置");
            return;
        }
        CreativeBatchVO target = new CreativeBatchVO();
        target.setId(id);
        target.setStatus(CreativeStatusEnum.PROCESSING);
        updateByIdSelective(target);
    }

    @Override
    public String downloadAll(Integer id, List<String> imageUrls) throws IOException {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        List<String> resultImages = data.getResultImages();
        List<String> images = resultImages.stream().filter(imageUrls::contains).collect(Collectors.toList());
        // 保底默认下载全部
        if (CollectionUtils.isEmpty(images)) {
            images = resultImages;
        }
        boolean selectAll = images.size() == resultImages.size();
        boolean updated = false;

        try {
            // 如果已经在扩展信息中存在，则直接返回结果
            String zipUrl = data.getExtValue(KEY_ZIP_URL, String.class);
            String latestZipTime = data.getExtValue(KEY_LATEST_ZIP_TIME, String.class);

            //已经存在zip地址，切最近更新时间一致，则直接返回
            if (selectAll && StringUtils.isNotBlank(zipUrl) && StringUtils.equals(latestZipTime,
                DateUtils.formatTime(data.getModifyTime()))) {
                return zipUrl;
            }

            updated = true;

            String url = ossHelper.createZipFromUrlsAndUpload(images,
                FileUtils.confoundFileName(data.getModelName() + "_" + id), false);

            // 缓存全选的zip文件
            if (selectAll) {
                data.addExtInfo(KEY_ZIP_URL, url);
                data.addExtInfo(KEY_LATEST_ZIP_TIME, DateUtils.formatTime(data.getModifyTime()));
            }

            return url;
        } finally {
            if (OperationContextHolder.getRoleType() == RoleTypeEnum.MERCHANT) {
                // noinspection unchecked
                List<String> downloadImages = data.getExtValue(DOWNLOADED_IMGS, List.class);
                Set<String> downloadImagesSet = downloadImages != null ? new HashSet<>(downloadImages)
                    : new HashSet<>();
                downloadImagesSet.addAll(images);
                data.addExtInfo(DOWNLOADED_IMGS, new ArrayList<>(downloadImagesSet));
                updated = true;
            }

            if (updated) {
                updateByIdSelective(data);
            }
        }
    }

    @Override
    public void assignTo(Integer batchId, Integer userId) {
        CreativeBatchVO data = selectById(batchId);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getStatus() == CreativeStatusEnum.FINISHED, ResultCode.BIZ_FAIL, "创作批次未完成");

        data.setOperatorId(userId);
        data.setUserId(userId);
        data.setCreateTime(new Date());
        data.setModifyTime(new Date());
        data.addExtInfo(KEY_ASSIGN_OPERATOR, OperationContextHolder.getOperatorUserId());

        updateByIdSelective(data);
    }

    @Override
    public List<CreativeBatchVO> queryUnCompletedExternal() {
        if (CollectionUtils.isEmpty(CreativeTypeEnum.getExternalTypes())) {
            return Collections.emptyList();
        }
        CreativeBatchExample example = new CreativeBatchExample();
        // 优先处理较早任务
        example.setOrderByClause("modify_time ASC");

        example.createCriteria().andStatusIn(CreativeStatusEnum.getUncompleteStatusList()).andTypeIn(
            CreativeTypeEnum.getExternalTypes()).andDeletedEqualTo(false);

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExample(example);
        return list.stream().map(CreativeBatchConverter::do2VO).collect(Collectors.toList());
    }

    @Override
    public List<String> queryImagesByElement(Integer elementId, Integer userId, Boolean testFlag, Integer limit) {
        // 获取元素及其子元素的ID列表
        List<Integer> elementIds = creativeElementService.getElementIdsWithChildren(elementId);

        // 从批次表查询
        List<CreativeBatchDO> dataList = creativeBatchDAO.selectByElements(elementIds, userId, testFlag, limit);
        List<CreativeBatchVO> list = CreativeBatchConverter.doList2VOList(dataList);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(CreativeBatchVO::getResultImages).filter(Objects::nonNull).flatMap(List::stream)
            .collect(Collectors.toList());
    }

    @Override
    public PageInfo<String> queryImagesByElementWithPage(CreativeBatchQuery query) {
        // 获取元素及其子元素的ID列表
        List<Integer> elementIds = creativeElementService.getElementIdsWithChildren(query.getElementId());
        if (CollectionUtils.isNotEmpty(elementIds)) {
            query.setElementIds(elementIds);
        }

        PageInfo<CreativeBatchVO> data = queryCreativeBatchByPage(query);

        PageInfo<String> result = new PageInfo<>();
        result.setTotalCount(data.getTotalCount());
        result.setSize(data.getSize());
        result.setHasNextPage(data.isHasNextPage());
        result.setExtInfo(data.getExtInfo());

        if (CollectionUtils.isNotEmpty(data.getList())) {
            List<String> collect = data.getList().stream().map(CreativeBatchVO::getResultImages).filter(
                Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
            result.setList(collect);
        }

        return result;
    }

    @Override
    public Integer createAliyunTryonTask(TryonTaskParams params) {

        AssertUtil.assertNotNull(params.getTopUrl(), ResultCode.PARAM_INVALID, "topUrl不能为空");

        String aliyunTaskId = aliyunTryonService.createTryonTask(params.getTopUrl(), params.getBottomUrl(),
            params.getPersonImgUrl());

        AssertUtil.assertNotBlank(aliyunTaskId, ResultCode.BIZ_FAIL, "创建try-on任务失败");

        CommonTaskVO task = new CommonTaskVO();
        {
            task.setUserId(OperationContextHolder.getMasterUserId());
            task.setOperatorId(OperationContextHolder.getOperatorUserId());
            task.setTaskType(CommonTaskEnums.TaskType.TRY_ON.name());
            task.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.ALIYUN.name());
            task.setOutTaskId(aliyunTaskId);
            task.setReqBizParams(CommonParamsWrapper.wrap(params));

            task = commonTaskService.insert(task);
        }

        return task.getId();
    }

    @Override
    public Integer createTryonRefinerTask(Integer tryonTaskId, String gender) {
        AssertUtil.assertNotNull(tryonTaskId, ResultCode.PARAM_INVALID, "任务ID不能为空");
        AssertUtil.assertNotBlank(gender, ResultCode.PARAM_INVALID, "性别不能为空");
        AssertUtil.assertTrue("woman".equals(gender) || "man".equals(gender), ResultCode.PARAM_INVALID,
            "性别只能为'woman'或'man'");

        CommonTaskVO tryonTask = commonTaskService.selectById(tryonTaskId);
        AssertUtil.assertNotNull(tryonTask, ResultCode.PARAM_INVALID, "任务不存在");

        AssertUtil.assertTrue(CommonTaskEnums.TaskStatus.isEnd(tryonTask.getTaskStatus()) && StringUtils.isNotBlank(
            tryonTask.getRetDetail()), ResultCode.PARAM_INVALID, "任务未完结，无法精修");

        TryonTaskParams params = CommonParamsWrapper.unwrap(tryonTask.getReqBizParams());
        AssertUtil.assertNotNull(params, ResultCode.PARAM_INVALID, "任务请求参数异常");

        TryonTaskOutputModel outputModel = JSONObject.parseObject(tryonTask.getRetDetail(), TryonTaskOutputModel.class);
        AssertUtil.assertTrue(outputModel != null && StringUtils.isNotBlank(outputModel.getImageUrl()),
            ResultCode.PARAM_INVALID, "任务结果参数异常");

        String tryonResultImgUrl = outputModel.getImageUrl();

        String refinerTaskId = aliyunTryonService.createTryonRefinerTask(params.getTopUrl(), params.getBottomUrl(),
            params.getPersonImgUrl(), tryonResultImgUrl, gender);

        CommonTaskVO refinerTask = new CommonTaskVO();
        {
            refinerTask.setUserId(OperationContextHolder.getMasterUserId());
            refinerTask.setOperatorId(OperationContextHolder.getOperatorUserId());
            refinerTask.setTaskType(CommonTaskEnums.TaskType.TRY_ON_REFINER.name());
            refinerTask.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            refinerTask.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.ALIYUN.name());
            refinerTask.setOutTaskId(refinerTaskId);

            TryonRefinerTaskParams refinerTaskParams = new TryonRefinerTaskParams();
            {
                refinerTaskParams.setTopUrl(params.getTopUrl());
                refinerTaskParams.setBottomUrl(params.getBottomUrl());
                refinerTaskParams.setPersonImgUrl(params.getPersonImgUrl());
                refinerTaskParams.setTryonTaskResultImgUrl(tryonResultImgUrl);
                refinerTaskParams.setGender(gender);
            }
            refinerTask.setReqBizParams(CommonParamsWrapper.wrap(refinerTaskParams));

            refinerTask = commonTaskService.insert(refinerTask);
        }

        return refinerTask.getId();
    }

    @Override
    public CommonTaskVO queryAliyunTryonTask(Integer taskId) {
        CommonTaskVO task = commonTaskService.selectById(taskId);
        AssertUtil.assertNotNull(task, ResultCode.PARAM_INVALID, "任务不存在");

        syncActiveTryonTaskStatus(task);
        return task;
    }

    private void syncActiveTryonTaskStatus(CommonTaskVO task) {
        if (!CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus())) {
            TryonTaskOutputModel tryonTaskOutput = aliyunTryonService.queryTask(task.getOutTaskId());
            AssertUtil.assertNotNull(tryonTaskOutput, ResultCode.BIZ_FAIL, "查询try-on任务状态异常");
            CommonTaskEnums.TaskStatus status = CommonTaskEnums.TaskStatus.fromAliyunTaskStatus(
                tryonTaskOutput.getTaskStatus());
            task.setTaskStatus(status.name());
            task.setOutTaskStatus(tryonTaskOutput.getTaskStatus());

            if (task.getTaskStartTime() == null) {
                task.setTaskStartTime(new Date());
            }
            if (CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus())) {
                task.setTaskEndTime(new Date());
            }
            task.setRetDetail(JSONObject.toJSONString(tryonTaskOutput));

            commonTaskService.updateByIdSelective(task);
        }
    }

    @Override
    public PageInfo<CommonTaskVO> queryAliyunTryonTasksByPage(CommonTaskQuery query) {
        if (StringUtils.isBlank(query.getTaskType()) && CollectionUtils.isEmpty(query.getTaskTypeList())) {
            query.setTaskTypeList(
                Arrays.asList(CommonTaskEnums.TaskType.TRY_ON.name(), CommonTaskEnums.TaskType.TRY_ON_REFINER.name()));
        }

        if (!OperationContextHolder.isAdmin()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        PageInfo<CommonTaskVO> page = commonTaskService.queryCommonTaskByPage(query);
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (CommonTaskVO each : page.getList()) {
                syncActiveTryonTaskStatus(each);
            }
        }

        return page;
    }

    @Override
    public void addDemoTag(Integer id) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "任务不存在");

        String demoTag = data.getExtValue(KEY_DEMO_TAG, String.class);
        String target = !StringUtils.equals(YES, demoTag) ? YES : NO;

        data.addExtInfo(KEY_DEMO_TAG, target);
        updateByIdSelective(data);
    }

    @Override
    public int queryCreateImageCntByModelId(Integer modelId) {
        MaterialModelVO modelVO = materialModelService.selectById(modelId);
        AssertUtil.assertNotNull(modelVO, ResultCode.PARAM_INVALID, "模型不存在");

        Long cnt = creativeBatchDAO.queryCreateImageCntByModelId(modelId, modelVO.getUserId(), false);
        return cnt == null ? 0 : cnt.intValue();
    }

    @Override
    public int queryFinishedSysGenImageCntByModelId(Integer modelId) {
        Long cnt = creativeBatchDAO.queryCreateImageCntByModelId(modelId,
            CommonUtil.mockAutoCreativeContext().getMasterUser(), true);
        return cnt == null ? 0 : cnt.intValue();
    }

    @Override
    public List<UserCountVO> statsQueuedUser() {
        CreativeBatchExample example = new CreativeBatchExample();
        Criteria criteria = example.createCriteria();
        criteria.andStatusIn(Arrays.asList(CreativeStatusEnum.INIT.getCode(), CreativeStatusEnum.QUEUE.getCode()));
        criteria.andDeletedEqualTo(false).andTypeNotIn(CreativeTypeEnum.getManualTypes());
        if (CollectionUtils.isNotEmpty(CreativeTypeEnum.getExternalTypes())) {
            criteria.andTypeNotIn(CreativeTypeEnum.getExternalTypes());
        }
        criteria.andUserTypeIsCustomer();

        List<UserCountDO> doList = creativeBatchDAO.queryQueuedUser(example);
        return CreativeBatchConverter.userCount2VO(doList);
    }

    @Override
    public StatsQueuedCreativeDO statsQueuedCreative() {
        return creativeBatchDAO.statsQueuedCreative();
    }

    @Override
    public List<StatsUserQueuedCreativeDO> statsCustomerQueuedCreative() {
        return creativeBatchDAO.statsCustomerQueue();
    }

    /**
     * 基于用户维度查询未完成的批次
     *
     * @param pipelineId        集群id
     * @param limit             查询条数
     * @param exceptTypeList    排除列表
     * @param includeTypeList   包含列表
     * @param includeProcessing 是否包含处理中的
     * @return 批次列表
     */
    private List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, Integer limit,
                                                          List<String> exceptTypeList, List<String> includeTypeList,
                                                          boolean includeProcessing) {
        Map<String, Object> params = new HashMap<>();
        params.put("pipelineId", pipelineId);
        params.put("limit", limit);

        int maxPerUser = 3; // 默认3
        Calendar calendar = Calendar.getInstance();
        int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
        // 判断当前时间是否在 21:00 ~ 7:00 之间 最大数调整
        if (hourOfDay >= 21 || hourOfDay < 7) {
            maxPerUser = systemConfigService.queryIntValue(IDLE_MAX_PER_CUSTOMER, 8);
        }
        params.put("maxPerUser", maxPerUser);
        params.put("maxAutoCreateSize", 10);

        List<String> exceptTypes = new ArrayList<>();
        exceptTypes.addAll(CreativeTypeEnum.getManualTypes());
        exceptTypes.addAll(CreativeTypeEnum.getExternalTypes());
        if (CollectionUtils.isNotEmpty(exceptTypeList)) {
            exceptTypes.addAll(exceptTypeList);
        }
        if (CollectionUtils.isNotEmpty(exceptTypes)) {
            params.put("exceptTypes", exceptTypes);
        }

        // 添加包含类型
        if (CollectionUtils.isNotEmpty(includeTypeList)) {
            params.put("includeTypes", includeTypeList);
        }

        if (includeProcessing) {
            params.put("includeProcessing", true);
        }

        JSONArray vipUserList = systemConfigService.queryJsonArrValue(CREATIVE_VIP_USERS);
        if (CollectionUtils.isNotEmpty(vipUserList)) {
            params.put("vipUserList", vipUserList.toJavaList(Integer.class));
        }

        List<CreativeBatchDO> list = creativeBatchDAO.selectUnCompletedTopUser(params);
        log.info("queryUnCompletedTopUser: ids={}", list.stream().map(CreativeBatchDO::getId));
        return CreativeBatchConverter.doList2VOList(list);
    }

    /**
     * 查询示例图创作批次
     *
     * @param modelId 模型id
     * @return 创作批次
     */
    private CreativeBatchVO queryExampleImagesItem(Integer modelId) {
        CreativeBatchQuery query = new CreativeBatchQuery();
        query.setModelId(modelId);

        if (!OperationContextHolder.isBackRole() && !OperationContextHolder.isDistributorRole()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }

        query.setBizTag(EXAMPLE_IMAGES);
        query.setPageSize(1);
        query.setPageNum(1);

        List<CreativeBatchVO> list = queryCreativeBatchList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    private String getQueryLastedTimeKey(List<String> types, ModelTypeEnum modelType) {
        // 按types的字母顺序排序
        types.sort(Comparator.naturalOrder());

        // 遍历types，拼接成以竖线分隔的字符串
        StringBuilder sb = new StringBuilder();
        for (String type : types) {
            sb.append(type).append("|");
        }

        return KEY_CREATIVE_QUERY_LASTED_TIME + "_" + modelType + "_" + sb + "_"
               + OperationContextHolder.getOperatorUserId();
    }

    /**
     * 填充元素信息
     *
     * @param result       批次列表
     * @param isSelectTask 是否查询批次对应任务信息
     */
    private void fillElementInfo(List<CreativeBatchVO> result, boolean isSelectTask) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 获取批次id列表
        List<Integer> batchIds = result.stream().map(CreativeBatchVO::getId).collect(Collectors.toList());

        // 批量查询批次元素
        Map<Integer, List<CreativeElementVO>> elementMap = creativeBatchElementsService.batchQueryBatchElements(
            batchIds);

        // 批量查询对应任务列表数据
        final Map<Integer, List<CreativeTaskVO>> taskMap = isSelectTask ? creativeTaskService.batchQueryCreativeTask(
            batchIds) : new HashMap<>();

        // 填充元素信息
        result.forEach(item -> {
            // 如果为 true 则查询批次对应任务列表
            if (isSelectTask && taskMap != null) {
                List<CreativeTaskVO> tasksLIst = taskMap.get(item.getId());
                if (CollectionUtils.isNotEmpty(tasksLIst)) {
                    item.setCreativeTasksList(tasksLIst);
                }
            }

            // 填充元素信息
            List<CreativeElementVO> elements = elementMap.get(item.getId());
            if (CollectionUtils.isNotEmpty(elements)) {
                elements.forEach(element -> {
                    if (StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                        item.setFaceName(element.getName());
                    }
                    if (StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                        item.setSceneName(element.getName());
                    }
                });
            }
        });
    }

    /**
     * 用于前端的'历史任务'详情页面：创作的脸和场景名
     *
     * @param ret          结果模型
     * @param isSelectTask 是否查询任务列表
     */
    private void fillElements(CreativeBatchVO ret, Boolean... isSelectTask) {
        fillElements(Collections.singletonList(ret), isSelectTask);
    }

    /**
     * 用于前端的'历史任务'详情页面：创作的脸和场景名
     *
     * @param batchList    结果模型
     * @param isSelectTask 是否查询任务列表
     */
    private void fillElements(List<CreativeBatchVO> batchList, Boolean... isSelectTask) {
        if (CollectionUtils.isEmpty(batchList)) {
            return;
        }
        Map<Integer, List<CreativeElementVO>> elementMap = creativeBatchElementsService.batchQueryBatchElements(
            batchList.stream().map(CreativeBatchVO::getId).collect(Collectors.toList()));

        if (MapUtils.isNotEmpty(elementMap)) {
            for (CreativeBatchVO batch : batchList) {
                List<CreativeElementVO> batchElements = elementMap.get(batch.getId());
                if (CollectionUtils.isEmpty(batchElements)) {
                    continue;
                }

                batchElements.forEach(e -> {
                    if (StringUtils.equalsIgnoreCase(e.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                        batch.setFaceName(e.getName());
                    } else if (StringUtils.equalsIgnoreCase(e.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                        batch.setSceneName(e.getName());
                    }
                });

                if (StringUtils.isBlank(batch.getFaceName())) {
                    batch.setFaceName(batch.getStringFromExtInfo(CommonConstants.KEY_SNAPSHOT_FACE_NAME));
                }
                if (StringUtils.isBlank(batch.getSceneName())) {
                    batch.setSceneName(batch.getStringFromExtInfo(CommonConstants.KEY_SNAPSHOT_SCENE_NAME));
                }
            }
        }

        // 填充任务列表
        batchList.forEach(batch -> {
            if (isSelectTask != null && isSelectTask.length > 0 && isSelectTask[0]) {
                List<CreativeTaskVO> creativeTaskVOS = creativeTaskService.queryTaskByBatchId(batch.getId());
                batch.setCreativeTasksList(creativeTaskVOS);
            }

            // 逻辑删除时的补偿：快照里没有，查数据库，如果是逻辑删除状态，则返回，同时以快照填充到ext info
            if (StringUtils.isBlank(batch.getFaceName()) || StringUtils.isBlank(batch.getSceneName())) {
                CreativeBatchElementsQuery query = new CreativeBatchElementsQuery();
                query.setBatchId(batch.getId());
                List<CreativeBatchElementsVO> batchElements
                    = creativeBatchElementsService.queryCreativeBatchElementsList(query);

                for (CreativeBatchElementsVO e : batchElements) {
                    fetchElementFromDB(batch, e);
                }
            }
        });
    }

    private void fetchElementFromDB(CreativeBatchVO ret, CreativeBatchElementsVO e) {

        // 这里用selectById，因为可能存在删除的元素deleted=1（脸、场景）
        CreativeElementVO eleLogicDeleted = creativeElementService.selectById(e.getElementId());
        if (eleLogicDeleted != null) {

            if (StringUtils.equalsIgnoreCase(eleLogicDeleted.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                ret.setFaceName(eleLogicDeleted.getName());

                // 快照字段：脸名
                if (ret.getExtInfo() != null && !ret.getExtInfo().containsKey(CommonConstants.KEY_SNAPSHOT_FACE_NAME)) {
                    saveNameSnapshot(ret, CommonConstants.KEY_SNAPSHOT_FACE_NAME, eleLogicDeleted, e);
                }
            }

            if (StringUtils.equalsIgnoreCase(eleLogicDeleted.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                ret.setSceneName(eleLogicDeleted.getName());

                // 快照字段：场景名
                if (ret.getExtInfo() != null && !ret.getExtInfo().containsKey(
                    CommonConstants.KEY_SNAPSHOT_SCENE_NAME)) {
                    saveNameSnapshot(ret, CommonConstants.KEY_SNAPSHOT_SCENE_NAME, eleLogicDeleted, e);
                }
            }
        }
    }

    private void saveNameSnapshot(CreativeBatchVO ret, String keySnapshotFaceName, CreativeElementVO eleLogicDeleted,
                                  CreativeBatchElementsVO e) {
        CreativeBatchVO target = new CreativeBatchVO();
        target.setId(ret.getId());
        JSONObject extInfo = new JSONObject();
        if (ret.getExtInfo() != null) {
            extInfo.putAll(ret.getExtInfo());
        }
        extInfo.put(keySnapshotFaceName, eleLogicDeleted.getName());
        target.setExtInfo(extInfo);

        log.warn("当前创作记录id={}使用的元素id={},name={}被逻辑删除，保存到extInfo作为快照", ret.getId(),
            e.getElementId(), eleLogicDeleted.getName());
        this.updateByIdSelective(target);
    }

    private void fillVideoClipTaskStatus(CreativeBatchVO data) {
        List<VideoClipTask> videoClipTasks = data.getVideoClipGenTasks();
        if (CollectionUtils.isNotEmpty(videoClipTasks)) {
            CommonTaskQuery query = new CommonTaskQuery();
            query.setIdList(videoClipTasks.stream().map(VideoClipTask::getTaskId).collect(Collectors.toList()));
            List<CommonTaskVO> tasks = commonTaskService.queryCommonTaskList(query);
            Map<Integer, CommonTaskVO> map = tasks.stream().collect(
                Collectors.toMap(CommonTaskVO::getId, Function.identity()));
            for (VideoClipTask batchClip : videoClipTasks) {
                CommonTaskVO task = map.get(batchClip.getTaskId());
                if (task != null) {
                    batchClip.setStartTime(task.getTaskStartTime());
                    batchClip.setEndTime(task.getTaskEndTime());
                }
            }

            data.setVideoClipGenTasks(videoClipTasks);
        }
    }

    @Override
    public List<CreativeBatchVO> getLatestCreativeBatchByPoseIds(List<Integer> poseIdList) {
        if (CollectionUtils.isEmpty(poseIdList)) {
            return new ArrayList<>();
        }

        // 调用DAO层方法，使用窗口函数在数据库层面进行优化查询
        List<CreativeBatchDO> doList = creativeBatchDAO.selectLatestByPoseIds(poseIdList);
        
        if (CollectionUtils.isEmpty(doList)) {
            return new ArrayList<>();
        }

        // 转换为VO对象
        List<CreativeBatchVO> result = CreativeBatchConverter.doList2VOList(doList);
        
        // 填充元素信息（可选）
        // if (CollectionUtils.isNotEmpty(result)) {
        //     fillElements(result);
        // }
        
        return result;
    }
}