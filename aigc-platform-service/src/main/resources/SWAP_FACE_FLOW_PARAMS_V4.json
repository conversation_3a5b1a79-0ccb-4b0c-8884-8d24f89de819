{"client_id": "${clientId}", "prompt": {"1102": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "1222": {"inputs": {"samples": ["1224", 0], "vae": ["1102", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1224": {"inputs": {"noise": ["1484", 0], "guider": ["1227", 0], "sampler": ["1225", 0], "sigmas": ["1226", 0], "latent_image": ["1491", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "1225": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1226": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": "${denoised?number}", "model": ["1592", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "1227": {"inputs": {"model": ["1488", 0], "conditioning": ["1531", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "1484": {"inputs": {"noise_seed": "${seed?number}"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1488": {"inputs": {"model": ["1592", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "1489": {"inputs": {"text": ["1568", 0], "clip": ["1592", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "1490": {"inputs": {"guidance": 3.5, "conditioning": ["1489", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "1491": {"inputs": {"noise_mask": true, "positive": ["1490", 0], "negative": ["1492", 0], "vae": ["1102", 0], "pixels": ["1611", 0], "mask": ["1603", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "1492": {"inputs": {"text": "", "clip": ["1592", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "1530": {"inputs": {"switch_1": "On", "controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_1": 0.6, "start_percent_1": 0, "end_percent_1": 0.2, "switch_2": "On", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_2": 0.1, "start_percent_2": 0.2, "end_percent_2": 0.5, "switch_3": "Off", "controlnet_3": "None", "controlnet_strength_3": 1, "start_percent_3": 0, "end_percent_3": 1, "image_1": ["1532", 0], "image_2": ["1532", 0]}, "class_type": "CR Multi-ControlNet Stack", "_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}}, "1531": {"inputs": {"switch": "On", "base_positive": ["1491", 0], "base_negative": ["1492", 0], "controlnet_stack": ["1530", 0]}, "class_type": "CR Apply Multi-ControlNet", "_meta": {"title": "🕹️ CR Apply Multi-ControlNet"}}, "1532": {"inputs": {"resolution": 1024, "image": ["1611", 0]}, "class_type": "Zoe-DepthMapPreprocessor", "_meta": {"title": "<PERSON>"}}, "1565": {"inputs": {"image": "${targetImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1568": {"inputs": {"prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 991}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "1590": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "1591": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "1592": {"inputs": {"lora_name": "${FACE.extInfo.faceLora}", "strength_model": "1", "strength_clip": "1", "model": ["1591", 0], "clip": ["1590", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "1601": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "1602": {"inputs": {"output_path": ["1601", 0], "filename_prefix": ["1601", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["1222", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "1603": {"inputs": {"expand": 2, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": true, "mask": ["1647", 4]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "1611": {"inputs": {"width": ["1622", 0], "height": ["1628", 0], "position": "top-left", "x_offset": 0, "y_offset": 0, "image": ["1644", 0]}, "class_type": "ImageCrop+", "_meta": {"title": "🔧 Image Crop"}}, "1616": {"inputs": {"image": ["1644", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "1622": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["1616", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "1628": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["1616", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "1644": {"inputs": {"width": 1536, "height": 1536, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 0, "image": ["1565", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "1646": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "1647": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["1646", 0], "image": ["1611", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 1647, "last_link_id": 2167, "nodes": [{"id": 1102, "type": "VAELoader", "pos": [-8946, 2337], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1999], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 1490, "type": "FluxGuidance", "pos": [-8027.03125, 3369.019775390625], "size": [211.60000610351562, 58], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1897}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1900], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 1531, "type": "CR Apply Multi-ControlNet", "pos": [-6867, 3179], "size": [274.56842041015625, 98], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "base_positive", "type": "CONDITIONING", "link": 1962}, {"name": "base_negative", "type": "CONDITIONING", "link": 1963}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": 1954}], "outputs": [{"name": "base_pos", "type": "CONDITIONING", "links": [1964], "slot_index": 0, "shape": 3}, {"name": "base_neg", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "widgets_values": ["On"]}, {"id": 1551, "type": "Reroute", "pos": [-8519, 2329], "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1999}], "outputs": [{"name": "", "type": "VAE", "links": [2004, 2005], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#322", "bgcolor": "#533"}, {"id": 1590, "type": "DualCLIPLoader", "pos": [-8947, 2143], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [2070], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 1591, "type": "UNETLoader", "pos": [-8950, 1970], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2069], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 1592, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-8507, 1979], "size": [339.9307556152344, 126], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2069}, {"name": "clip", "type": "CLIP", "link": 2070}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2085], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [2086], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["${FACE.extInfo.faceLora}", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 1492, "type": "CLIPTextEncode", "pos": [-8017, 3469], "size": [210, 125.07953643798828], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2081}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1904, 1963], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1489, "type": "CLIPTextEncode", "pos": [-8333, 3422], "size": [210, 125.07953643798828], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2082}, {"name": "text", "type": "STRING", "link": 2067, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1897], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1488, "type": "DifferentialDiffusion", "pos": [-8321, 3349], "size": [210, 26], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2083}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1895], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 1593, "type": "Reroute", "pos": [-8756, 3211], "size": [75, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2085}], "outputs": [{"name": "", "type": "MODEL", "links": [2083, 2084], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#223", "bgcolor": "#335"}, {"id": 1594, "type": "Reroute", "pos": [-8752, 3338], "size": [75, 26], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2086}], "outputs": [{"name": "", "type": "CLIP", "links": [2081, 2082], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 1225, "type": "KSamplerSelect", "pos": [-6244, 3448], "size": [314.0994873046875, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1503], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 1227, "type": "BasicGuider", "pos": [-6245, 3322], "size": [263.1893615722656, 46], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1895, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1964, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1502], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 1530, "type": "CR Multi-ControlNet Stack", "pos": [-7114, 3629], "size": [563.9595947265625, 454], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "image_1", "type": "IMAGE", "link": 1951, "shape": 7}, {"name": "image_2", "type": "IMAGE", "link": 2076, "shape": 7}, {"name": "image_3", "type": "IMAGE", "link": null, "shape": 7}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": null, "shape": 7}], "outputs": [{"name": "CONTROLNET_STACK", "type": "CONTROL_NET_STACK", "links": [1954], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.1, 0.2, 0.5, "Off", "None", 1, 0, 1]}, {"id": 1226, "type": "BasicScheduler", "pos": [-6252, 3587], "size": [309.76611328125, 106], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2084, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [2024], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, "${denoised?number}"], "color": "#232", "bgcolor": "#353"}, {"id": 1491, "type": "InpaintModelConditioning", "pos": [-7426, 3182], "size": [216.59999084472656, 138], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1900}, {"name": "negative", "type": "CONDITIONING", "link": 1904}, {"name": "vae", "type": "VAE", "link": 2004}, {"name": "pixels", "type": "IMAGE", "link": 1920}, {"name": "mask", "type": "MASK", "link": 2094}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1962], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1908], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 1224, "type": "SamplerCustomAdvanced", "pos": [-5836, 3193], "size": [314.0994873046875, 106], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1881, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1502, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1503, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 2024, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1908, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1497], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1510, "type": "Reroute", "pos": [-7989, 3556], "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2029}], "outputs": [{"name": "", "type": "IMAGE", "links": [1920, 2012], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1532, "type": "Zoe-DepthMapPreprocessor", "pos": [-7770, 3658], "size": [210, 58], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2012}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1951, 1965, 2076], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "widgets_values": [1024]}, {"id": 1222, "type": "VAEDecode", "pos": [-5430, 3233], "size": [190.54541015625, 46], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1497}, {"name": "vae", "type": "VAE", "link": 2005}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2020, 2091], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1628, "type": "ConrainPythonExecutor", "pos": [-8330, 2750], "size": [354.08526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 2130, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2131], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1566, "type": "Reroute", "pos": [-7915, 2967], "size": [75, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2111}], "outputs": [{"name": "", "type": "IMAGE", "links": [2029, 2056], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1622, "type": "ConrainPythonExecutor", "pos": [-8330, 2670], "size": [354.08526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 2125, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2128], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1642, "type": "Reroute", "pos": [-8528, 2465], "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2163}], "outputs": [{"name": "", "type": "IMAGE", "links": [2151, 2152], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1601, "type": "Text String", "pos": [-6240, 3864], "size": [315, 190], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [2089], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [2090], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 1568, "type": "ConrainRandomPrompts", "pos": [-8985, 3539], "size": [602.1065673828125, 245.0613250732422], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [2067], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 991, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 1484, "type": "RandomNoise", "pos": [-6249, 3175], "size": [255.33419799804688, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1881], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed?number}", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 1519, "type": "PreviewImage", "pos": [-7427, 3478], "size": [210, 246], "flags": {}, "order": 29, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1965}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1616, "type": "GetImageSize+", "pos": [-8581, 2653], "size": [214.20001220703125, 66], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2151}], "outputs": [{"name": "width", "type": "INT", "links": [2125], "slot_index": 0}, {"name": "height", "type": "INT", "links": [2130], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 1565, "type": "LoadImage", "pos": [-8948, 2488], "size": [315, 314], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2162], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${targetImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 1611, "type": "ImageCrop+", "pos": [-8079, 2479], "size": [316.08935546875, 239.64944458007812], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2152}, {"name": "width", "type": "INT", "link": 2128, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 2131, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2111], "slot_index": 0}, {"name": "x", "type": "INT", "links": null}, {"name": "y", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 1644, "type": "ImageResize+", "pos": [-8753, 2941], "size": [315, 218], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2162}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2163], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 0]}, {"id": 1587, "type": "Reroute", "pos": [-7624.0634765625, 1965.79296875], "size": [75, 26], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2056}], "outputs": [{"name": "", "type": "IMAGE", "links": [2166], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1646, "type": "Sapiens<PERSON><PERSON>der", "pos": [-7584, 2101], "size": [659.2952270507812, 298], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [2165], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 1647, "type": "SapiensSampler", "pos": [-6796, 2111], "size": [315, 258], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 2165}, {"name": "image", "type": "IMAGE", "link": 2166}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [2167], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 1603, "type": "ConrainGrowMaskWithBlur", "pos": [-7513, 2612], "size": [340.20001220703125, 246], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2167}], "outputs": [{"name": "mask", "type": "MASK", "links": [2093, 2094], "slot_index": 0}, {"name": "mask_inverted", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [2, 0, true, false, 10, 1, 1, true]}, {"id": 1588, "type": "MaskPreview+", "pos": [-6836, 2612], "size": [210, 246], "flags": {}, "order": 32, "mode": 4, "inputs": [{"name": "mask", "type": "MASK", "link": 2093}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 1560, "type": "PreviewImage", "pos": [-5708, 3351], "size": [423.0299377441406, 418.9241027832031], "flags": {}, "order": 38, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 2020}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1602, "type": "ConrainImageSave", "pos": [-5711, 3842], "size": [320, 266], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2091, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 2089, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 2090, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}], "links": [[1497, 1224, 0, 1222, 0, "LATENT"], [1502, 1227, 0, 1224, 1, "GUIDER"], [1503, 1225, 0, 1224, 2, "SAMPLER"], [1881, 1484, 0, 1224, 0, "NOISE"], [1895, 1488, 0, 1227, 0, "MODEL"], [1897, 1489, 0, 1490, 0, "CONDITIONING"], [1900, 1490, 0, 1491, 0, "CONDITIONING"], [1904, 1492, 0, 1491, 1, "CONDITIONING"], [1908, 1491, 2, 1224, 4, "LATENT"], [1920, 1510, 0, 1491, 3, "IMAGE"], [1951, 1532, 0, 1530, 0, "IMAGE"], [1954, 1530, 0, 1531, 2, "CONTROL_NET_STACK"], [1962, 1491, 0, 1531, 0, "CONDITIONING"], [1963, 1492, 0, 1531, 1, "CONDITIONING"], [1964, 1531, 0, 1227, 1, "CONDITIONING"], [1965, 1532, 0, 1519, 0, "IMAGE"], [1999, 1102, 0, 1551, 0, "*"], [2004, 1551, 0, 1491, 2, "VAE"], [2005, 1551, 0, 1222, 1, "VAE"], [2012, 1510, 0, 1532, 0, "IMAGE"], [2020, 1222, 0, 1560, 0, "IMAGE"], [2024, 1226, 0, 1224, 3, "SIGMAS"], [2029, 1566, 0, 1510, 0, "*"], [2056, 1566, 0, 1587, 0, "*"], [2067, 1568, 0, 1489, 1, "STRING"], [2069, 1591, 0, 1592, 0, "MODEL"], [2070, 1590, 0, 1592, 1, "CLIP"], [2076, 1532, 0, 1530, 1, "IMAGE"], [2081, 1594, 0, 1492, 0, "CLIP"], [2082, 1594, 0, 1489, 0, "CLIP"], [2083, 1593, 0, 1488, 0, "MODEL"], [2084, 1593, 0, 1226, 0, "MODEL"], [2085, 1592, 0, 1593, 0, "*"], [2086, 1592, 1, 1594, 0, "*"], [2089, 1601, 0, 1602, 1, "STRING"], [2090, 1601, 1, 1602, 2, "STRING"], [2091, 1222, 0, 1602, 0, "IMAGE"], [2093, 1603, 0, 1588, 0, "MASK"], [2094, 1603, 0, 1491, 4, "MASK"], [2111, 1611, 0, 1566, 0, "*"], [2125, 1616, 0, 1622, 0, "*"], [2128, 1622, 0, 1611, 1, "INT"], [2130, 1616, 1, 1628, 0, "*"], [2131, 1628, 0, 1611, 2, "INT"], [2151, 1642, 0, 1616, 0, "IMAGE"], [2152, 1642, 0, 1611, 0, "IMAGE"], [2162, 1565, 0, 1644, 0, "IMAGE"], [2163, 1644, 0, 1642, 0, "*"], [2165, 1646, 0, 1647, 0, "MODEL_SAPIEN"], [2166, 1587, 0, 1647, 1, "IMAGE"], [2167, 1647, 4, 1603, 0, "MASK"]], "groups": [{"id": 2, "title": "TEXT | GENERATION", "bounding": [-9014, 3105, 3862.244384765625, 1043.0714111328125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "", "bounding": [-9015.4423828125, 1898.2938232421875, 1295.358154296875, 1176.14306640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-7670.44873046875, 1903.500732421875, 1559.69091796875, 1167.5008544921875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3380916604057103, "offset": [10362.875337249128, -1250.3904512264594]}}, "version": 0.4, "widget_idx_map": {"1225": {"sampler_name": 0}, "1226": {"scheduler": 0}, "1484": {"noise_seed": 0}, "1568": {"seed": 1}}, "seed_widgets": {"1484": 0, "1568": 1}}}}}