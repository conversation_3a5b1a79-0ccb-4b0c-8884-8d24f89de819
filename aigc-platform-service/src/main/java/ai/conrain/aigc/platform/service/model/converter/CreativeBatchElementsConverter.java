package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.CreativeBatchElementsDO;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchElementsQuery;
import ai.conrain.aigc.platform.dal.example.CreativeBatchElementsExample;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchElementsVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * CreativeBatchElementsConverter
 *
 * @version CreativeBatchElementsService.java v 0.1 2024-05-08 03:35:57
 */
public class CreativeBatchElementsConverter {

    /**
     * DO -> VO
     */
    public static CreativeBatchElementsVO do2VO(CreativeBatchElementsDO from) {
        CreativeBatchElementsVO to = new CreativeBatchElementsVO();
        to.setId(from.getId());
        to.setBatchId(from.getBatchId());
        to.setElementId(from.getElementId());
        to.setUserId(from.getUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CreativeBatchElementsDO vo2DO(CreativeBatchElementsVO from) {
        CreativeBatchElementsDO to = new CreativeBatchElementsDO();
        to.setId(from.getId());
        to.setBatchId(from.getBatchId());
        to.setElementId(from.getElementId());
        to.setElementKey(from.getElementKey());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static CreativeBatchElementsQuery do2Query(CreativeBatchElementsDO from) {
        CreativeBatchElementsQuery to = new CreativeBatchElementsQuery();
        to.setId(from.getId());
        to.setBatchId(from.getBatchId());
        to.setElementId(from.getElementId());
        to.setUserId(from.getUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static CreativeBatchElementsDO query2DO(CreativeBatchElementsQuery from) {
        CreativeBatchElementsDO to = new CreativeBatchElementsDO();
        to.setId(from.getId());
        to.setBatchId(from.getBatchId());
        to.setElementId(from.getElementId());
        to.setUserId(from.getUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static CreativeBatchElementsExample query2Example(CreativeBatchElementsQuery from) {
        CreativeBatchElementsExample to = new CreativeBatchElementsExample();
        CreativeBatchElementsExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getBatchId())) {
            c.andBatchIdEqualTo(from.getBatchId());
        }
        if (!ObjectUtils.isEmpty(from.getElementId())) {
            c.andElementIdEqualTo(from.getElementId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getElementKey())) {
            c.andElementKeyEqualTo(from.getElementKey());
        }
        if (!ObjectUtils.isEmpty(from.getIncludesTypes())) {
            c.andElementIncludesTypes(from.getIncludesTypes());
        }
        if (!ObjectUtils.isEmpty(from.getExcludesTypes())) {
            c.andElementExcludesTypes(from.getExcludesTypes());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<CreativeBatchElementsVO> doList2VOList(List<CreativeBatchElementsDO> list) {
        return CommonUtil.listConverter(list, CreativeBatchElementsConverter::do2VO);
    }
}