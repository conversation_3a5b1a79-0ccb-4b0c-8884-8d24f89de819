/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 创作任务触发事件
 *
 * <AUTHOR>
 * @version : CreativeTriggerEvent.java, v 0.1 2024/9/11 16:04 renxiao.wu Exp $
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class CreativeTriggerEvent extends BaseEvent {
    private static final long serialVersionUID = -3353606262717518957L;
    private Integer id;

    public CreativeTriggerEvent() {
        super();
    }
}
