package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import lombok.Data;

@Data
public class ContractModel {

    /** 用户id */
    private Integer userId;

    /** 签署日期 */
    private String beginDate;

    /**
     * 设置合同日期, 格式 yyyy-MM-dd
     * @param beginDate 日期
     */
    public void setBeginDate(String beginDate) {
        AssertUtil.assertTrue(DateUtils.isValidDate(beginDate, "yyyy-MM-dd"), ResultCode.BIZ_FAIL, "合同日期格式不正确");
        this.beginDate = beginDate;
    }
}
