package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * comfyui模板激活版本表
 * 对应数据表：comfyui_workflow_template_active_version
 */
public class ComfyuiWorkflowTemplateActiveVersionDO implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 模板key
     */
    private String templateKey;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 模板版本，如20250610.1
     */
    private String activeVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 创建人id
     */
    private Integer createBy;

    /**
     * 修改人id
     */
    private Integer modifyBy;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey == null ? null : templateKey.trim();
    }

    public String getTemplateDesc() {
        return templateDesc;
    }

    public void setTemplateDesc(String templateDesc) {
        this.templateDesc = templateDesc == null ? null : templateDesc.trim();
    }

    public String getActiveVersion() {
        return activeVersion;
    }

    public void setActiveVersion(String activeVersion) {
        this.activeVersion = activeVersion == null ? null : activeVersion.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    public Integer getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(Integer modifyBy) {
        this.modifyBy = modifyBy;
    }
}