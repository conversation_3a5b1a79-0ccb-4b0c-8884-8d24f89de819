package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum SettleStrategyTypeEnum {

    AGENT_A("AGENT_A", "A级费率"),
    AGENT_B("AGENT_B", "B级费率"),
    // key account sales, 大客户销售
    SALES_KA("SALES_KA", "大客户销售费率"),
    // industrial cluster sales , 产业带销售
    SALES_IC("SALES_IC", "产业带销售费率"),
    SALES_LEADER("SALES_LEADER", "销售主管费率"),
    SALES_SUB("SALES_SUB", "销售业务员费率"),
    SALES_DEPT("SALES_DEPT", "销售子渠道费率"),
    CUSTOM("CUSTOM", "自定义费率"),
    ;

    private String code;
    private String desc;

    SettleStrategyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SettleStrategyTypeEnum getByCode(String code) {
        for (SettleStrategyTypeEnum item : SettleStrategyTypeEnum.values()) {
            if (StringUtils.equals(item.code, code)) {
                return item;
            }
        }
        return null;
    }
}