package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * ComfyuiTaskVO
 *
 * @version ComfyuiTaskService.java v 0.1 2024-05-30 04:05:20
 */
@Data
public class ComfyuiTaskVO implements IExtModel, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** 模型训练任务id */
    @ApiModelProperty(name = "id", value = "模型训练任务id")
    private Integer id;

    /** 归属主账号id */
    @ApiModelProperty(name = "userId", value = "归属主账号id")
    private Integer userId;

    /** 操作人账号id */
    @ApiModelProperty(name = "operatorId", value = "操作人账号id")
    private Integer operatorId;

    /** 任务类型, cutout/mark-label/lora */
    @ApiModelProperty(name = "taskType", value = "任务类型, cutout/label/lora")
    /**
     * @see ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum
     */
    private ComfyuiTaskTypeEnum taskType;

    /** 任务状态, QUEUED/RUNNING/COMPLETED/FAILED/UNKNOWN/NONE */
    @ApiModelProperty(name = "taskStatus", value = "任务状态, QUEUED/RUNNING/COMPLETED/FAILED/UNKNOWN/NONE")
    private QueueResult.QueueCodeEnum taskStatus;

    /** 自定义请求参数 */
    @ApiModelProperty(name = "reqParams", value = "自定义请求参数")
    private JSONObject reqParams;

    /** ComfyUI返回的唯一标识 */
    @ApiModelProperty(name = "promptId", value = "ComfyUI返回的唯一标识")
    private String promptId;

    /** 扩展 */
    @ApiModelProperty(name = "extInfo", value = "扩展")
    private JSONObject extInfo;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "modifyTime", value = "修改时间")
    private Date modifyTime;

    /** comfyui api请求报文 */
    @ApiModelProperty(name = "comfyuiRequest", value = "comfyui api请求报文")
    private String comfyuiRequest;

    /** 结果详情 */
    @ApiModelProperty(name = "retDetail", value = "结果详情")
    private String retDetail;

    @Override
    public boolean isProcessing() {
        return QueueCodeEnum.COMPLETED != taskStatus;
    }
}